{"ast": null, "code": "import { InternSet } from \"internmap\";\nexport default function disjoint(values, other) {\n  const iterator = other[Symbol.iterator](),\n    set = new InternSet();\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n    while ({\n      value,\n      done\n    } = iterator.next()) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n  return true;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}