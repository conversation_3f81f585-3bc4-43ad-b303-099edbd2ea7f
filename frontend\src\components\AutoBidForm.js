import React, { useState, useEffect } from "react";
import axiosInstance from "../api/axiosInstance";

function AutoBidForm({ auctionId, token }) {
  const [maxBid, setMaxBid] = useState("");
  const [message, setMessage] = useState("");

  // Optionally fetch existing autobid on mount
  useEffect(() => {
    if (!token) return;

    axiosInstance
      .get(`autobids/?auction=${auctionId}`)
      .then((response) => {
        const data = response.data.results || response.data || [];
        if (data.length > 0) {
          setMaxBid(data[0].max_bid);
        }
      })
      .catch(() => {});
  }, [auctionId, token]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    const payload = { auction: auctionId, max_bid: parseFloat(maxBid) };

    try {
      // Check if autobid exists
      const response = await axiosInstance.get(
        `autobids/?auction=${auctionId}`
      );
      const data = response.data.results || response.data || [];

      if (data.length > 0) {
        // Update existing
        await axiosInstance.patch(`autobids/${data[0].id}/`, payload);
      } else {
        // Create new
        await axiosInstance.post("autobids/", payload);
      }
      setMessage("AutoBid set successfully!");
    } catch (error) {
      console.error("AutoBid error:", error);
      setMessage("Failed to set AutoBid");
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <label>
        Max AutoBid Amount:
        <input
          type="number"
          step="0.01"
          min="0"
          value={maxBid}
          onChange={(e) => setMaxBid(e.target.value)}
          required
        />
      </label>
      <button type="submit" className="btn btn-primary ms-2">
        Set AutoBid
      </button>
      {message && <p>{message}</p>}
    </form>
  );
}

export default AutoBidForm;
