/* Category Flip Card Styles */
.category-flip-card {
  background-color: transparent;
  width: 100%;
  height: 280px;
  perspective: 1000px;
  cursor: pointer;
  margin-bottom: 1rem;
}

.category-flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
  border-radius: 16px;
  box-shadow: 0 8px 32px var(--card-shadow);
}

.category-flip-card:hover .category-flip-card-inner {
  transform: rotateY(180deg);
}

.category-flip-card-front,
.category-flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 16px;
  overflow: hidden;
  border: 2px solid var(--card-border);
}

.category-flip-card-front {
  background: var(--card-bg);
  color: var(--card-text);
}

.category-flip-card-back {
  background: linear-gradient(135deg, var(--primary-color), #667eea);
  color: white;
  transform: rotateY(180deg);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
}

/* Front Side Styles */
.category-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.category-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.category-flip-card:hover .category-image {
  transform: scale(1.1);
}

.category-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(52, 152, 219, 0.8) 100%
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s ease;
}

.category-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.category-icon {
  font-size: 1.5rem;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.category-flip-card:hover .category-icon {
  transform: translateX(5px);
  opacity: 1;
}

/* Back Side Styles */
.category-stats {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  text-align: center;
}

.stats-header {
  margin-bottom: 1rem;
}

.stats-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.stats-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.stats-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.stat-item {
  margin-bottom: 1.5rem;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  line-height: 1;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #ffffff, #f0f8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.popular-item {
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.popular-label {
  font-size: 0.8rem;
  opacity: 0.8;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.popular-title {
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.2;
}

.action-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.8;
  margin-top: auto;
}

.action-hint i {
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .category-flip-card {
    height: 240px;
  }
  
  .category-name {
    font-size: 1.2rem;
  }
  
  .stat-number {
    font-size: 2.5rem;
  }
  
  .stats-title {
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .category-flip-card {
    height: 200px;
  }
  
  .category-name {
    font-size: 1rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .stats-title {
    font-size: 1rem;
  }
  
  .category-flip-card-back {
    padding: 1rem;
  }
}

/* Animation for card entrance */
@keyframes flipCardFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) rotateX(-10deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotateX(0);
  }
}

.category-flip-card {
  animation: flipCardFadeIn 0.6s ease-out forwards;
}

/* Stagger animation for multiple cards */
.category-flip-card:nth-child(1) { animation-delay: 0.1s; }
.category-flip-card:nth-child(2) { animation-delay: 0.2s; }
.category-flip-card:nth-child(3) { animation-delay: 0.3s; }
.category-flip-card:nth-child(4) { animation-delay: 0.4s; }
.category-flip-card:nth-child(5) { animation-delay: 0.5s; }
.category-flip-card:nth-child(6) { animation-delay: 0.6s; }

/* Dark mode specific adjustments */
.dark-mode-bg .category-flip-card-front {
  background: var(--card-bg);
  border-color: var(--card-border);
}

.dark-mode-bg .category-overlay {
  background: linear-gradient(
    135deg,
    rgba(15, 23, 42, 0.8) 0%,
    rgba(52, 152, 219, 0.9) 100%
  );
}

.dark-mode-bg .popular-item {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Hover effects for better interactivity */
.category-flip-card:hover {
  transform: translateY(-5px);
}

.category-flip-card:active {
  transform: translateY(-2px);
}

/* Loading state */
.category-flip-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.category-flip-card.loading .category-flip-card-inner {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
