import React, { useEffect, useState, useCallback } from "react";
import { useAuth } from "../context/AuthContext";
import Swal from "sweetalert2";
import { formatAuctionPrice } from "../utils/currency";

function Profile() {
  const { user } = useAuth();
  const [userAuctions, setUserAuctions] = useState([]);
  const [userBids, setUserBids] = useState([]);
  const [myBiddedAuctions, setMyBiddedAuctions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  const fetchUserData = useCallback(async () => {
    if (!user || !user.token) {
      setError("Please log in to view your profile");
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError("");

      // Fetch user's auctions
      const auctionsResponse = await fetch(
        `http://127.0.0.1:8000/api/auctions/?owner=${user.username}&page_size=100`,
        {
          headers: {
            Authorization: `Bearer ${user.token}`,
          },
        }
      );

      if (auctionsResponse.ok) {
        const auctionsData = await auctionsResponse.json();
        setUserAuctions(auctionsData.results || []);
      }

      // Fetch user's bids
      const bidsResponse = await fetch("http://127.0.0.1:8000/api/bids/", {
        headers: {
          Authorization: `Bearer ${user.token}`,
        },
      });

      if (bidsResponse.ok) {
        const bidsData = await bidsResponse.json();
        const allBids = bidsData.results || [];

        // Filter bids by current user
        const myBids = allBids.filter(
          (bid) => bid.user === user.id || bid.user === user.username
        );
        setUserBids(myBids);

        // Get unique auction IDs that user has bid on
        const biddedAuctionIds = [...new Set(myBids.map((bid) => bid.auction))];

        // Fetch auctions that user has bid on
        if (biddedAuctionIds.length > 0) {
          const auctionPromises = biddedAuctionIds.map((auctionId) =>
            fetch(`http://127.0.0.1:8000/api/auctions/${auctionId}/`)
              .then((res) => (res.ok ? res.json() : null))
              .catch(() => null)
          );

          const auctionResults = await Promise.all(auctionPromises);
          const validAuctions = auctionResults.filter(
            (auction) => auction !== null
          );
          setMyBiddedAuctions(validAuctions);
        }
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
      setError("Failed to load profile data");
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchUserData();
    // REMOVED: Auto-refresh interval to prevent excessive API calls
    // const interval = setInterval(fetchUserData, 30000);
    // return () => clearInterval(interval);
  }, [fetchUserData]);

  const totalBidsReceived = userBids.length;

  const getUserBidsForAuction = (auctionId) => {
    return userBids.filter((bid) => bid.auction === auctionId);
  };

  const getHighestBidForAuction = (auctionId) => {
    const auctionBids = getUserBidsForAuction(auctionId);
    if (auctionBids.length === 0) return null;
    return auctionBids.reduce((highest, current) =>
      parseFloat(current.amount) > parseFloat(highest.amount)
        ? current
        : highest
    );
  };

  const isWinning = (auctionId) => {
    const auction = myBiddedAuctions.find((a) => a.id === auctionId);
    if (!auction) return false;

    const myHighestBid = getHighestBidForAuction(auctionId);
    if (!myHighestBid) return false;

    return parseFloat(myHighestBid.amount) >= parseFloat(auction.current_bid);
  };

  const handleDelete = async (auctionId) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This auction will be permanently deleted!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        const response = await fetch(
          `http://127.0.0.1:8000/api/auctions/${auctionId}/`,
          {
            method: "DELETE",
            headers: {
              Authorization: `Bearer ${user.token}`,
            },
          }
        );

        if (response.ok) {
          await fetchUserData();
          Swal.fire("Deleted!", "Your auction has been deleted.", "success");
        } else {
          Swal.fire("Error!", "Failed to delete auction.", "error");
        }
      } catch (error) {
        console.error("Delete error:", error);
        Swal.fire("Error!", "Failed to delete auction.", "error");
      }
    }
  };

  if (loading) {
    return (
      <div className="container mt-5 text-center">
        <div className="loading-spinner"></div>
        <p>Loading your profile...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mt-5">
        <div className="alert alert-danger text-center">
          <h4>Error Loading Profile</h4>
          <p>{error}</p>
          <button className="btn btn-primary" onClick={fetchUserData}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mt-5">
      <h2 className="mb-4 text-center">My Profile Dashboard</h2>

      {/* Dashboard Cards */}
      <div className="row mb-5 text-center">
        <div className="col-md-3 mb-3">
          <div className="card shadow-sm">
            <div className="card-body">
              <h5 className="card-title">Total Auctions</h5>
              <p className="display-6">{userAuctions.length}</p>
            </div>
          </div>
        </div>
        <div className="col-md-3 mb-3">
          <div className="card shadow-sm">
            <div className="card-body">
              <h5 className="card-title">Total Bids Placed</h5>
              <p className="display-6">{totalBidsReceived}</p>
            </div>
          </div>
        </div>
        <div className="col-md-3 mb-3">
          <div className="card shadow-sm">
            <div className="card-body">
              <h5 className="card-title">Auctions I'm Bidding On</h5>
              <p className="display-6">{myBiddedAuctions.length}</p>
            </div>
          </div>
        </div>
        <div className="col-md-3 mb-3">
          <div className="card shadow-sm">
            <div className="card-body">
              <h5 className="card-title">Currently Winning</h5>
              <p className="display-6 text-success">
                {
                  myBiddedAuctions.filter((auction) => isWinning(auction.id))
                    .length
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Refresh Button */}
      <div className="text-end mb-4">
        <button onClick={fetchUserData} className="btn btn-outline-primary">
          🔄 Refresh Data
        </button>
      </div>

      {/* User Auctions */}
      <h4 className="mb-4 text-center">My Auction Listings</h4>
      {userAuctions.length === 0 ? (
        <p className="text-center">You have not created any auctions yet.</p>
      ) : (
        userAuctions.map((auction) => (
          <div key={auction.id} className="card mb-3">
            <div className="row g-0">
              <div className="col-md-3">
                <img
                  src={auction.image || "/placeholder-image.svg"}
                  className="img-fluid rounded-start h-100"
                  alt={auction.title}
                  style={{ objectFit: "contain", minHeight: "200px" }}
                />
              </div>
              <div className="col-md-9">
                <div className="card-body">
                  <h5 className="card-title">{auction.title}</h5>
                  <p className="card-text">{auction.description}</p>
                  <div className="row">
                    <div className="col-md-6">
                      <p>
                        <strong>Starting Bid:</strong>{" "}
                        {formatAuctionPrice(auction.starting_bid)}
                      </p>
                      <p>
                        <strong>Current Bid:</strong>{" "}
                        {formatAuctionPrice(auction.current_bid)}
                      </p>
                      <p>
                        <strong>Category:</strong> {auction.category}
                      </p>
                    </div>
                    <div className="col-md-6">
                      <p>
                        <strong>Status:</strong>
                        <span
                          className={`badge ms-2 ${
                            auction.approved ? "bg-success" : "bg-warning"
                          }`}
                        >
                          {auction.approved ? "Approved" : "Pending"}
                        </span>
                      </p>
                      <p>
                        <strong>End Time:</strong>{" "}
                        {new Date(auction.end_time).toLocaleString()}
                      </p>
                      {auction.image && (
                        <p>
                          <strong>Image URL:</strong>{" "}
                          <small className="text-muted">
                            {auction.image.length > 50
                              ? `${auction.image.substring(0, 50)}...`
                              : auction.image}
                          </small>
                        </p>
                      )}
                    </div>
                  </div>

                  <button
                    className="btn btn-danger mt-3"
                    onClick={() => handleDelete(auction.id)}
                  >
                    Delete Auction
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))
      )}

      {/* My Bidded Auctions */}
      <div className="mt-5">
        <h4 className="text-center mb-4">Auctions I Placed Bids On</h4>
        {myBiddedAuctions.length === 0 ? (
          <p className="text-center">You have not placed any bids yet.</p>
        ) : (
          myBiddedAuctions.map((auction) => {
            const myBids = getUserBidsForAuction(auction.id);
            const myHighestBid = getHighestBidForAuction(auction.id);
            const winning = isWinning(auction.id);

            return (
              <div key={auction.id} className="card mb-3">
                <div className="row g-0">
                  <div className="col-md-3">
                    <img
                      src={
                        auction.image && auction.image.trim() !== ""
                          ? auction.image
                          : "/placeholder-image.svg"
                      }
                      className="img-fluid rounded-start h-100"
                      alt={auction.title}
                      style={{ objectFit: "contain", minHeight: "200px" }}
                      onError={(e) => {
                        e.target.src = "/placeholder-image.svg";
                      }}
                    />
                  </div>
                  <div className="col-md-9">
                    <div className="card-body">
                      <h5 className="card-title">{auction.title}</h5>
                      <p className="card-text">{auction.description}</p>
                      <div className="row">
                        <div className="col-md-6">
                          <p>
                            <strong>Current Bid:</strong>{" "}
                            {formatAuctionPrice(auction.current_bid)}
                          </p>
                          <p>
                            <strong>My Highest Bid:</strong>{" "}
                            {formatAuctionPrice(myHighestBid?.amount || 0)}
                          </p>
                          <p>
                            <strong>Total Bids Placed:</strong> {myBids.length}
                          </p>
                        </div>
                        <div className="col-md-6">
                          <p>
                            <strong>Status:</strong>
                            <span
                              className={`badge ms-2 ${
                                winning ? "bg-success" : "bg-secondary"
                              }`}
                            >
                              {winning ? "🏆 Winning" : "📊 Bidding"}
                            </span>
                          </p>
                          <p>
                            <strong>End Time:</strong>{" "}
                            {new Date(auction.end_time).toLocaleString()}
                          </p>
                        </div>
                      </div>

                      {/* My Bid History */}
                      <div className="mt-3">
                        <h6>My Bid History:</h6>
                        <div className="list-group">
                          {myBids
                            .sort(
                              (a, b) =>
                                parseFloat(b.amount) - parseFloat(a.amount)
                            )
                            .map((bid, index) => (
                              <div
                                key={index}
                                className="list-group-item d-flex justify-content-between align-items-center"
                              >
                                <div>
                                  <strong>
                                    {formatAuctionPrice(bid.amount)}
                                  </strong>
                                  <small className="text-muted ms-2">
                                    {new Date(bid.created_at).toLocaleString()}
                                  </small>
                                </div>
                                {parseFloat(bid.amount) ===
                                  parseFloat(myHighestBid?.amount || 0) && (
                                  <span className="badge bg-primary">
                                    Highest
                                  </span>
                                )}
                              </div>
                            ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
}

export default Profile;
