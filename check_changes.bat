@echo off
echo ========================================
echo REPOSITORY CHANGE DETECTION
echo ========================================
echo.

echo Current branch:
git branch --show-current
echo.

echo Overall repository status:
git status --short
echo.

echo Checking each folder for potential changes...
echo.

:: Function to check folder status
:check_folder
set folder=%1
set description=%2

if exist "%folder%" (
    echo [%folder%] %description%
    git status --porcelain "%folder%/" | find /v "" >nul
    if %errorlevel% equ 0 (
        echo   Status: HAS CHANGES
        git status --porcelain "%folder%/"
    ) else (
        echo   Status: No changes
    )
) else (
    echo [%folder%] Folder does not exist
)
echo.
goto :eof

:: Check each major folder
call :check_folder "backend" "Django backend application"
call :check_folder "backend/OnlineAuctionSystem" "Django project settings"
call :check_folder "backend/auction" "Main auction app"
call :check_folder "backend/auctions" "Additional auction services"
call :check_folder "backend/templates" "Email templates"
call :check_folder "frontend" "React frontend application"
call :check_folder "frontend/src" "React source code"
call :check_folder "frontend/src/components" "React components"
call :check_folder "frontend/src/pages" "React pages"
call :check_folder "frontend/src/context" "React context"
call :check_folder "frontend/src/styles" "CSS styles"
call :check_folder "testing_suite" "Testing suite"
call :check_folder "scripts" "Utility scripts"

echo ========================================
echo SUMMARY
echo ========================================

:: Count untracked files
for /f %%i in ('git status --porcelain ^| find "??" /c') do set untracked=%%i
for /f %%i in ('git status --porcelain ^| find "M " /c') do set modified=%%i
for /f %%i in ('git status --porcelain ^| find "A " /c') do set added=%%i
for /f %%i in ('git status --porcelain ^| find "D " /c') do set deleted=%%i

echo Untracked files: %untracked%
echo Modified files: %modified%
echo Added files: %added%
echo Deleted files: %deleted%
echo.

if %untracked% gtr 0 (
    echo Untracked files found:
    git status --porcelain | findstr "??"
    echo.
)

if %modified% gtr 0 (
    echo Modified files found:
    git status --porcelain | findstr "M "
    echo.
)

echo Use 'smart_commit.bat' to commit changes folder by folder
echo Or use 'commit_by_folders.bat' for basic folder commits
echo.
pause
