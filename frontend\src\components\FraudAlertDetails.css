/* Fraud <PERSON><PERSON> Details Styling */
.fraud-alert-details {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.fraud-alert-details .card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.fraud-alert-details .card-header {
  background: linear-gradient(135deg, var(--bs-warning) 0%, var(--bs-danger) 100%);
  border: none;
  padding: 1rem 1.25rem;
}

.fraud-alert-details .card-header.bg-danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.fraud-alert-details .card-header.bg-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: #212529 !important;
}

.fraud-alert-details .card-header.bg-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.fraud-alert-details .card-header h5 {
  margin: 0;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.fraud-alert-details .progress {
  height: 8px;
  border-radius: 4px;
  background-color: #e9ecef;
}

.fraud-alert-details .progress-bar {
  border-radius: 4px;
  transition: width 0.6s ease;
}

.fraud-alert-details .detection-details .alert {
  border-radius: 6px;
  border: none;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.fraud-alert-details .detection-details .alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.fraud-alert-details .detection-details .alert-warning {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
  border-left: 4px solid #ffc107;
}

.fraud-alert-details .detection-details .alert-info {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
  border-left: 4px solid #17a2b8;
}

.fraud-alert-details .detection-details .alert-light {
  background: linear-gradient(135deg, #fefefe 0%, #f8f9fa 100%);
  color: #495057;
  border-left: 4px solid #6c757d;
}

.fraud-alert-details .badge {
  font-size: 0.8rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.fraud-alert-details .text-muted {
  color: #6c757d !important;
}

.fraud-alert-details .card-body {
  padding: 1.25rem;
}

/* Risk Score Animation */
.fraud-alert-details .progress-bar {
  animation: progressFill 1s ease-in-out;
}

@keyframes progressFill {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width);
  }
}

/* Hover Effects */
.fraud-alert-details .detection-details .alert:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

/* Icon Styling */
.fraud-alert-details .card-header svg,
.fraud-alert-details .card-body svg {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Responsive Design */
@media (max-width: 768px) {
  .fraud-alert-details .card-header {
    padding: 0.75rem 1rem;
  }
  
  .fraud-alert-details .card-body {
    padding: 1rem;
  }
  
  .fraud-alert-details .detection-details .alert {
    font-size: 0.85rem;
    padding: 0.4rem 0.6rem;
  }
}

/* Print Styles */
@media print {
  .fraud-alert-details .card {
    box-shadow: none;
    border: 1px solid #dee2e6;
  }
  
  .fraud-alert-details .card-header {
    background: #f8f9fa !important;
    color: #212529 !important;
  }
  
  .fraud-alert-details .detection-details .alert {
    background: #f8f9fa !important;
    color: #212529 !important;
    border: 1px solid #dee2e6;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .fraud-alert-details .card {
    background-color: #2d3748;
    color: #e2e8f0;
  }
  
  .fraud-alert-details .text-muted {
    color: #a0aec0 !important;
  }
  
  .fraud-alert-details .detection-details .alert {
    color: #e2e8f0;
  }
}
