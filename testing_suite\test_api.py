"""
API Tests for Auction System
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from decimal import Decimal
from datetime import timed<PERSON><PERSON>

from auction.models import Auction, Bid, Payment


class AuthenticationAPITest(APITestCase):
    """Test authentication endpoints"""
    
    def setUp(self):
        self.register_url = reverse('register')
        self.login_url = reverse('login')
        self.user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'password2': 'TestPass123!',
            'first_name': 'Test',
            'last_name': 'User'
        }
    
    def test_user_registration(self):
        """Test user registration"""
        response = self.client.post(self.register_url, self.user_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(User.objects.filter(username='testuser').exists())
    
    def test_user_registration_invalid_password(self):
        """Test registration with weak password"""
        invalid_data = self.user_data.copy()
        invalid_data['password'] = '123'
        invalid_data['password2'] = '123'
        
        response = self.client.post(self.register_url, invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_user_registration_password_mismatch(self):
        """Test registration with password mismatch"""
        invalid_data = self.user_data.copy()
        invalid_data['password2'] = 'DifferentPass123!'
        
        response = self.client.post(self.register_url, invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_user_login(self):
        """Test user login"""
        # Create user first
        User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='TestPass123!'
        )
        
        login_data = {
            'username': 'testuser',
            'password': 'TestPass123!'
        }
        
        response = self.client.post(self.login_url, login_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
    
    def test_user_login_invalid_credentials(self):
        """Test login with invalid credentials"""
        login_data = {
            'username': 'nonexistent',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(self.login_url, login_data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class AuctionAPITest(APITestCase):
    """Test auction endpoints"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='auctioneer',
            email='<EMAIL>',
            password='TestPass123!'
        )
        self.client = APIClient()
        
        # Get JWT token
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        self.auction_data = {
            'title': 'Test Auction',
            'description': 'A test auction item',
            'starting_bid': '100.00',
            'category': 'Electronics',
            'condition': 'Excellent',
            'auction_type': 'standard',
            'end_time': (timezone.now() + timedelta(days=7)).isoformat()
        }
    
    def test_create_auction(self):
        """Test auction creation"""
        url = reverse('auction-list')
        response = self.client.post(url, self.auction_data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Auction.objects.filter(title='Test Auction').exists())
    
    def test_create_auction_unauthorized(self):
        """Test auction creation without authentication"""
        self.client.credentials()  # Remove authentication
        url = reverse('auction-list')
        response = self.client.post(url, self.auction_data)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_list_auctions(self):
        """Test listing auctions"""
        # Create test auction
        Auction.objects.create(
            title='Test Auction',
            description='Test description',
            starting_bid=Decimal('100.00'),
            owner=self.user,
            end_time=timezone.now() + timedelta(days=7),
            approved=True
        )
        
        url = reverse('auction-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
    
    def test_auction_detail(self):
        """Test auction detail view"""
        auction = Auction.objects.create(
            title='Test Auction',
            description='Test description',
            starting_bid=Decimal('100.00'),
            owner=self.user,
            end_time=timezone.now() + timedelta(days=7),
            approved=True
        )
        
        url = reverse('auction-detail', kwargs={'pk': auction.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Test Auction')
    
    def test_update_own_auction(self):
        """Test updating own auction"""
        auction = Auction.objects.create(
            title='Test Auction',
            description='Test description',
            starting_bid=Decimal('100.00'),
            owner=self.user,
            end_time=timezone.now() + timedelta(days=7)
        )
        
        url = reverse('auction-detail', kwargs={'pk': auction.pk})
        update_data = {'title': 'Updated Auction'}
        response = self.client.patch(url, update_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        auction.refresh_from_db()
        self.assertEqual(auction.title, 'Updated Auction')
    
    def test_cannot_update_others_auction(self):
        """Test cannot update another user's auction"""
        other_user = User.objects.create_user(
            username='other',
            email='<EMAIL>',
            password='TestPass123!'
        )
        
        auction = Auction.objects.create(
            title='Other User Auction',
            description='Test description',
            starting_bid=Decimal('100.00'),
            owner=other_user,
            end_time=timezone.now() + timedelta(days=7)
        )
        
        url = reverse('auction-detail', kwargs={'pk': auction.pk})
        update_data = {'title': 'Hacked Auction'}
        response = self.client.patch(url, update_data)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class BidAPITest(APITestCase):
    """Test bidding endpoints"""
    
    def setUp(self):
        self.owner = User.objects.create_user(
            username='owner',
            email='<EMAIL>',
            password='TestPass123!'
        )
        self.bidder = User.objects.create_user(
            username='bidder',
            email='<EMAIL>',
            password='TestPass123!'
        )
        
        self.auction = Auction.objects.create(
            title='Test Auction',
            description='Test auction for bidding',
            starting_bid=Decimal('100.00'),
            current_bid=Decimal('100.00'),
            owner=self.owner,
            end_time=timezone.now() + timedelta(days=7),
            approved=True
        )
        
        self.client = APIClient()
        refresh = RefreshToken.for_user(self.bidder)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    def test_place_bid(self):
        """Test placing a valid bid"""
        url = reverse('bid-list')
        bid_data = {
            'auction': self.auction.pk,
            'amount': '150.00'
        }
        
        response = self.client.post(url, bid_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Bid.objects.filter(
            auction=self.auction,
            user=self.bidder,
            amount=Decimal('150.00')
        ).exists())
    
    def test_place_bid_too_low(self):
        """Test placing bid lower than current bid"""
        url = reverse('bid-list')
        bid_data = {
            'auction': self.auction.pk,
            'amount': '50.00'  # Lower than starting bid
        }
        
        response = self.client.post(url, bid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_owner_cannot_bid(self):
        """Test auction owner cannot bid on own auction"""
        # Switch to owner credentials
        refresh = RefreshToken.for_user(self.owner)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        url = reverse('bid-list')
        bid_data = {
            'auction': self.auction.pk,
            'amount': '150.00'
        }
        
        response = self.client.post(url, bid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_bid_on_ended_auction(self):
        """Test cannot bid on ended auction"""
        # Create ended auction
        ended_auction = Auction.objects.create(
            title='Ended Auction',
            description='This auction has ended',
            starting_bid=Decimal('100.00'),
            owner=self.owner,
            end_time=timezone.now() - timedelta(days=1),
            approved=True
        )
        
        url = reverse('bid-list')
        bid_data = {
            'auction': ended_auction.pk,
            'amount': '150.00'
        }
        
        response = self.client.post(url, bid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class SearchAPITest(APITestCase):
    """Test search endpoints"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='seller',
            email='<EMAIL>',
            password='TestPass123!'
        )
        
        # Create test auctions
        self.auction1 = Auction.objects.create(
            title='iPhone 13 Pro',
            description='Latest iPhone in excellent condition',
            starting_bid=Decimal('800.00'),
            category='Electronics',
            condition='Excellent',
            owner=self.user,
            end_time=timezone.now() + timedelta(days=7),
            approved=True
        )
        
        self.auction2 = Auction.objects.create(
            title='Samsung Galaxy S21',
            description='Android smartphone in good condition',
            starting_bid=Decimal('600.00'),
            category='Electronics',
            condition='Good',
            owner=self.user,
            end_time=timezone.now() + timedelta(days=5),
            approved=True
        )
        
        self.auction3 = Auction.objects.create(
            title='Vintage Camera',
            description='Classic film camera for collectors',
            starting_bid=Decimal('200.00'),
            category='Collectibles',
            condition='Fair',
            owner=self.user,
            end_time=timezone.now() + timedelta(days=3),
            approved=True
        )
    
    def test_basic_search(self):
        """Test basic text search"""
        url = reverse('search-advanced')
        response = self.client.get(url, {'q': 'iPhone'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['title'], 'iPhone 13 Pro')
    
    def test_category_filter(self):
        """Test filtering by category"""
        url = reverse('search-advanced')
        response = self.client.get(url, {'category': 'Electronics'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
    
    def test_price_range_filter(self):
        """Test filtering by price range"""
        url = reverse('search-advanced')
        response = self.client.get(url, {
            'min_price': '500',
            'max_price': '900'
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
    
    def test_condition_filter(self):
        """Test filtering by condition"""
        url = reverse('search-advanced')
        response = self.client.get(url, {'condition': 'Excellent'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['condition'], 'Excellent')
    
    def test_combined_filters(self):
        """Test combining multiple filters"""
        url = reverse('search-advanced')
        response = self.client.get(url, {
            'q': 'phone',
            'category': 'Electronics',
            'min_price': '500'
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should find both phones but only those above $500
        self.assertEqual(len(response.data['results']), 2)
    
    def test_search_suggestions(self):
        """Test search suggestions endpoint"""
        url = reverse('search-suggestions')
        response = self.client.get(url, {'q': 'phone'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('suggestions', response.data)
        self.assertGreater(len(response.data['suggestions']), 0)
