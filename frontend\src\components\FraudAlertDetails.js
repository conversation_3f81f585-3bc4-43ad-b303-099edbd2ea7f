import React from "react";
import { <PERSON>, Badge, <PERSON>, <PERSON>, <PERSON><PERSON>, ProgressBar } from "react-bootstrap";
import "./FraudAlertDetails.css";
import {
  FaExclamationTriangle,
  FaRobot,
  FaCreditCard,
  FaUserSecret,
  FaEye,
  FaShieldAlt,
  FaClock,
  FaChartLine,
  FaUser,
  FaGavel,
} from "react-icons/fa";

const FraudAlertDetails = ({ fraud }) => {
  if (!fraud) return null;

  // Get fraud type icon and color
  const getFraudTypeInfo = (type) => {
    const typeMap = {
      suspicious_bidding: {
        icon: <FaGavel className="me-2" />,
        color: "warning",
        title: "Suspicious Bidding Pattern",
        description: "Unusual bidding behavior detected",
      },
      bot_activity: {
        icon: <FaRobot className="me-2" />,
        color: "danger",
        title: "Bot Activity Detected",
        description: "Automated behavior patterns identified",
      },
      payment_fraud: {
        icon: <FaCreditCard className="me-2" />,
        color: "danger",
        title: "Payment Fraud",
        description: "Suspicious payment activity detected",
      },
      account_takeover: {
        icon: <FaUserSecret className="me-2" />,
        color: "danger",
        title: "Account Takeover",
        description: "Unauthorized account access suspected",
      },
      fake_listing: {
        icon: <FaEye className="me-2" />,
        color: "warning",
        title: "Fake Listing",
        description: "Potentially fraudulent auction listing",
      },
      shill_bidding: {
        icon: <FaChartLine className="me-2" />,
        color: "warning",
        title: "Shill Bidding",
        description: "Coordinated bidding to inflate prices",
      },
    };
    return (
      typeMap[type] || {
        icon: <FaExclamationTriangle className="me-2" />,
        color: "secondary",
        title: type.replace("_", " ").toUpperCase(),
        description: "Security alert detected",
      }
    );
  };

  // Get risk level info
  const getRiskLevel = (score) => {
    if (score >= 90)
      return { level: "CRITICAL", color: "danger", variant: "danger" };
    if (score >= 80)
      return { level: "HIGH", color: "warning", variant: "warning" };
    if (score >= 60) return { level: "MEDIUM", color: "info", variant: "info" };
    return { level: "LOW", color: "success", variant: "success" };
  };

  // Format detection details in a user-friendly way
  const formatDetails = (details, fraudType) => {
    if (!details || typeof details !== "object") return null;

    const formatters = {
      suspicious_bidding: (d) => [
        d.rapid_consecutive_bids && {
          icon: "⚡",
          text: "Multiple rapid bids detected",
          severity: "high",
        },
        d.unusual_timing_pattern && {
          icon: "⏰",
          text: "Unusual bidding timing pattern",
          severity: "medium",
        },
        d.bid_count_in_last_hour && {
          icon: "📊",
          text: `${d.bid_count_in_last_hour} bids in last hour`,
          severity: "high",
        },
        d.bid_increment_pattern === "suspicious" && {
          icon: "📈",
          text: "Suspicious bid increment pattern",
          severity: "medium",
        },
        d.time_between_bids_seconds && {
          icon: "⏱️",
          text: `Consistent timing: ${d.time_between_bids_seconds.join(
            ", "
          )}s intervals`,
          severity: "high",
        },
      ],
      bot_activity: (d) => [
        d.automated_behavior && {
          icon: "🤖",
          text: "Automated behavior detected",
          severity: "critical",
        },
        d.consistent_response_time && {
          icon: "⚡",
          text: "Consistent response times (non-human)",
          severity: "high",
        },
        d.no_human_delays && {
          icon: "🚫",
          text: "No natural human delays",
          severity: "high",
        },
        d.captcha_failures && {
          icon: "🔒",
          text: `${d.captcha_failures} CAPTCHA failures`,
          severity: "medium",
        },
        d.user_agent_suspicious && {
          icon: "🌐",
          text: "Suspicious browser/device signature",
          severity: "medium",
        },
        d.bid_frequency_per_minute && {
          icon: "📊",
          text: `${d.bid_frequency_per_minute} actions per minute`,
          severity: "critical",
        },
      ],
      payment_fraud: (d) => [
        d.stolen_card_indicators && {
          icon: "💳",
          text: "Stolen card indicators detected",
          severity: "critical",
        },
        d.billing_address_mismatch && {
          icon: "📍",
          text: "Billing address mismatch",
          severity: "high",
        },
        d.multiple_failed_payments && {
          icon: "❌",
          text: "Multiple failed payment attempts",
          severity: "high",
        },
        d.velocity_check_failed && {
          icon: "🚨",
          text: "Velocity check failed",
          severity: "high",
        },
        d.cvv_failures && {
          icon: "🔢",
          text: `${d.cvv_failures} CVV verification failures`,
          severity: "medium",
        },
        d.high_risk_country && {
          icon: "🌍",
          text: "Transaction from high-risk location",
          severity: "medium",
        },
      ],
      account_takeover: (d) => [
        d.login_from_new_location && {
          icon: "📍",
          text: "Login from new/unusual location",
          severity: "high",
        },
        d.unusual_bidding_behavior && {
          icon: "🎯",
          text: "Unusual bidding behavior for this user",
          severity: "medium",
        },
        d.password_recently_changed && {
          icon: "🔑",
          text: "Password recently changed",
          severity: "medium",
        },
        d.different_payment_method && {
          icon: "💳",
          text: "New payment method added",
          severity: "medium",
        },
        d.suspicious_ip && {
          icon: "🌐",
          text: `Suspicious IP: ${d.suspicious_ip}`,
          severity: "high",
        },
        d.login_time_unusual && {
          icon: "⏰",
          text: "Login at unusual time",
          severity: "low",
        },
      ],
      fake_listing: (d) => [
        d.stock_photos_detected && {
          icon: "📸",
          text: "Stock photos detected",
          severity: "high",
        },
        d.price_too_low_for_item && {
          icon: "💰",
          text: "Price significantly below market value",
          severity: "high",
        },
        d.vague_description && {
          icon: "📝",
          text: "Vague or generic description",
          severity: "medium",
        },
        d.new_seller_account && {
          icon: "👤",
          text: "New seller account",
          severity: "medium",
        },
        d.no_seller_history && {
          icon: "📊",
          text: "No previous selling history",
          severity: "medium",
        },
        d.similar_listings_found && {
          icon: "🔍",
          text: `${d.similar_listings_found} similar listings found elsewhere`,
          severity: "high",
        },
      ],
      shill_bidding: (d) => [
        d.shill_bidding_pattern && {
          icon: "🎭",
          text: "Shill bidding pattern detected",
          severity: "high",
        },
        d.bidding_on_same_seller_items && {
          icon: "🔄",
          text: "Repeatedly bidding on same seller items",
          severity: "high",
        },
        d.artificial_price_inflation && {
          icon: "📈",
          text: "Artificial price inflation detected",
          severity: "high",
        },
        d.coordinated_bidding && {
          icon: "🤝",
          text: "Coordinated bidding activity",
          severity: "critical",
        },
        d.seller_connection_suspected && {
          icon: "🔗",
          text: "Connection to seller suspected",
          severity: "high",
        },
        d.bid_timing_coordination && {
          icon: "⏰",
          text: "Coordinated bid timing",
          severity: "medium",
        },
      ],
    };

    const formatter = formatters[fraudType];
    if (!formatter) return null;

    return formatter(details).filter(Boolean);
  };

  const typeInfo = getFraudTypeInfo(fraud.fraud_type);
  const riskInfo = getRiskLevel(fraud.risk_score);
  const detailItems = formatDetails(fraud.details, fraud.fraud_type);

  return (
    <div className="fraud-alert-details">
      {/* Header */}
      <Card className={`border-${typeInfo.color} mb-3`}>
        <Card.Header className={`bg-${typeInfo.color} text-white`}>
          <Row className="align-items-center">
            <Col>
              <h5 className="mb-0">
                {typeInfo.icon}
                {typeInfo.title}
              </h5>
              <small>{typeInfo.description}</small>
            </Col>
            <Col xs="auto">
              <Badge bg={riskInfo.variant} className="fs-6">
                {riskInfo.level} RISK
              </Badge>
            </Col>
          </Row>
        </Card.Header>

        <Card.Body>
          {/* Risk Score */}
          <Row className="mb-3">
            <Col md={6}>
              <div className="d-flex align-items-center mb-2">
                <FaShieldAlt className="me-2 text-danger" />
                <strong>Risk Score: {fraud.risk_score}/100</strong>
              </div>
              <ProgressBar
                variant={riskInfo.variant}
                now={fraud.risk_score}
                label={`${fraud.risk_score}%`}
                className="mb-2"
              />
            </Col>
            <Col md={6}>
              <div className="d-flex align-items-center mb-2">
                <FaUser className="me-2 text-primary" />
                <strong>User: {fraud.user?.username || fraud.user}</strong>
              </div>
              <div className="d-flex align-items-center">
                <FaClock className="me-2 text-muted" />
                <small className="text-muted">
                  Detected:{" "}
                  {new Date(
                    fraud.detected_at || fraud.created_at
                  ).toLocaleString()}
                </small>
              </div>
            </Col>
          </Row>

          {/* Detection Details */}
          {detailItems && detailItems.length > 0 && (
            <>
              <h6 className="mb-3">
                <FaExclamationTriangle className="me-2 text-warning" />
                Detection Details
              </h6>
              <div className="detection-details">
                {detailItems.map((item, index) => (
                  <Alert
                    key={index}
                    variant={
                      item.severity === "critical"
                        ? "danger"
                        : item.severity === "high"
                        ? "warning"
                        : item.severity === "medium"
                        ? "info"
                        : "light"
                    }
                    className="py-2 mb-2"
                  >
                    <span className="me-2">{item.icon}</span>
                    {item.text}
                  </Alert>
                ))}
              </div>
            </>
          )}

          {/* Confidence Level */}
          {fraud.details?.confidence_level && (
            <div className="mt-3">
              <small className="text-muted">
                <strong>Detection Confidence:</strong>{" "}
                {fraud.details.confidence_level.replace("_", " ").toUpperCase()}
              </small>
            </div>
          )}

          {/* Detection Reason */}
          {fraud.details?.detection_reason && (
            <div className="mt-2">
              <small className="text-muted">
                <strong>Reason:</strong> {fraud.details.detection_reason}
              </small>
            </div>
          )}
        </Card.Body>
      </Card>
    </div>
  );
};

export default FraudAlertDetails;
