import React, { createContext, useContext, useState } from "react";
import axiosInstance from "../api/axiosInstance";

const AuctionContext = createContext();

export const useAuction = () => useContext(AuctionContext);

export const AuctionProvider = ({ children }) => {
  const [auctions, setAuctions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch auctions from backend
  const fetchAuctions = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.get("auctions/");
      setAuctions(response.data.results || response.data);
    } catch (error) {
      console.error("Error fetching auctions:", error);
      setError("Failed to load auctions");
      setAuctions([]);
    } finally {
      setLoading(false);
    }
  };

  // Removed automatic fetching to prevent double API calls
  // Components should call refreshAuctions() when they need data
  // useEffect(() => {
  //   fetchAuctions();
  // }, []);

  const addAuction = async (newAuction) => {
    try {
      setLoading(true);
      const response = await axiosInstance.post("auctions/", newAuction);
      setAuctions((prev) => [...prev, response.data]);
      return response.data;
    } catch (error) {
      console.error("Error creating auction:", error);
      setError("Failed to create auction");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateAuction = async (auctionId, updateData) => {
    try {
      setLoading(true);
      const response = await axiosInstance.patch(
        `auctions/${auctionId}/`,
        updateData
      );
      setAuctions((prev) =>
        prev.map((auction) =>
          auction.id === auctionId ? response.data : auction
        )
      );
      return response.data;
    } catch (error) {
      console.error("Error updating auction:", error);
      setError("Failed to update auction");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const deleteAuction = async (auctionId) => {
    try {
      setLoading(true);
      await axiosInstance.delete(`auctions/${auctionId}/`);
      setAuctions((prev) => prev.filter((auction) => auction.id !== auctionId));
    } catch (error) {
      console.error("Error deleting auction:", error);
      setError("Failed to delete auction");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const refreshAuctions = () => {
    fetchAuctions();
  };

  return (
    <AuctionContext.Provider
      value={{
        auctions,
        loading,
        error,
        addAuction,
        updateAuction,
        deleteAuction,
        refreshAuctions,
      }}
    >
      {children}
    </AuctionContext.Provider>
  );
};
