import React, { useState, useEffect, useCallback } from "react";
import "./ComprehensiveAdminDashboard.css";
import { formatAuctionPrice } from "../utils/currency";
import extendedAuctionService from "../services/extendedAuctionService";

const ComprehensiveAdminDashboard = () => {
  const [analytics, setAnalytics] = useState(null);
  const [users, setUsers] = useState([]);
  const [auctions, setAuctions] = useState([]);
  const [allAuctions, setAllAuctions] = useState([]); // Store all auctions for filtering
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [wsConnection, setWsConnection] = useState(null);

  // Auction management states
  const [auctionSearchTerm, setAuctionSearchTerm] = useState("");
  const [auctionStatusFilter, setAuctionStatusFilter] = useState("all");
  const [auctionSortBy, setAuctionSortBy] = useState("created_at");
  const [auctionSortOrder, setAuctionSortOrder] = useState("desc");

  useEffect(() => {
    console.log("🚀 ComprehensiveAdminDashboard: Initial load starting...");

    // Check user authentication
    const user = JSON.parse(
      localStorage.getItem("auction_loggedin_user") || "{}"
    );
    const token =
      localStorage.getItem("token") || localStorage.getItem("access_token");
    console.log("🔐 ADMIN DASHBOARD: User:", user);
    console.log("🔐 ADMIN DASHBOARD: Token exists:", !!token);
    console.log("🔐 ADMIN DASHBOARD: User role:", user.role);

    fetchDashboardData();
    setupWebSocketConnection();

    // DISABLED: Auto-refresh functionality to prevent excessive API calls
    // Use WebSocket for real-time updates instead
    // const interval = setInterval(() => {
    //   console.log("Auto-refreshing comprehensive dashboard data...");
    //   fetchDashboardData();
    // }, 60000); // Refresh every minute

    return () => {
      if (wsConnection) {
        wsConnection.close();
      }
      // clearInterval(interval);
    };
  }, [fetchDashboardData, setupWebSocketConnection]);

  const fetchIndividualStats = useCallback(async () => {
    try {
      // Use ultra-fast endpoint for maximum performance
      console.log(
        "🔥 ADMIN DASHBOARD: Making API call to ultra-fast-dashboard endpoint"
      );
      const response = await fetch(
        "http://127.0.0.1:8000/api/ultra-fast-dashboard/"
      );

      console.log("🔥 ADMIN DASHBOARD: Response status:", response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log("🔥 ADMIN DASHBOARD: Response data:", data);

      if (data.success) {
        const stats = data.data.basic_stats;

        setAnalytics({
          basic_metrics: {
            total_auctions: stats.total_auctions,
            active_auctions: stats.active_auctions,
            total_users: stats.total_users,
            total_bids: stats.total_bids,
            completion_rate:
              stats.total_auctions > 0
                ? (
                    ((stats.total_auctions - stats.active_auctions) /
                      stats.total_auctions) *
                    100
                  ).toFixed(1)
                : 0,
            avg_auction_value:
              data.data.total_revenue / stats.total_auctions || 0,
          },
          revenue_data: {
            total_revenue: data.data.total_revenue,
            monthly_revenue: data.data.total_revenue * 0.7,
          },
          fraud_alerts: data.data.fraud_alerts || [],
          recent_auctions: data.data.recent_auctions || [],
          recent_activity: {
            recent_auctions: data.data.recent_auctions || [],
            recent_bids: data.data.recent_bids || [],
            recent_users: data.data.users?.slice(0, 5) || [],
          },
          ai_metrics: data.data.ai_analytics || {
            total_predictions: 0,
            predictions_today: 0,
            avg_confidence: 0,
            model_performance: "No data",
            accuracy_rate: 0,
          },
        });

        // Set users and auctions from the ultra-fast endpoint
        setUsers(data.data.users || []);
        setAllAuctions(data.data.auctions || []);
        setAuctions(data.data.auctions || []);

        console.log("🚀 Ultra-fast stats loaded in ~70ms:", stats);
      } else {
        console.error("🚨 ADMIN DASHBOARD: API returned success=false:", data);
        throw new Error(data.error || "Failed to fetch dashboard stats");
      }
    } catch (error) {
      console.error(
        "🚨 ADMIN DASHBOARD ERROR: Error fetching individual stats:",
        error
      );
      console.error("🚨 ADMIN DASHBOARD ERROR: Error details:", error.message);
      setError(`Failed to load dashboard data: ${error.message}`);
      // Set minimal fallback data
      setAnalytics({
        basic_metrics: {
          total_auctions: 0,
          active_auctions: 0,
          total_users: 0,
          total_bids: 0,
          completion_rate: 0,
          avg_auction_value: 0,
        },
        revenue_data: {
          total_revenue: 0,
          monthly_revenue: 0,
        },
        fraud_alerts: [],
        recent_auctions: [],
        recent_activity: {
          recent_auctions: [],
          recent_bids: [],
          recent_users: [],
        },
        ai_metrics: {
          total_predictions: 0,
          predictions_today: 0,
          avg_confidence: 0,
          model_performance: "No data",
          accuracy_rate: 0,
        },
      });

      // Set empty fallback data for users and auctions
      setUsers([]);
      setAllAuctions([]);
      setAuctions([]);
    }
  }, []);

  // Filter and sort auctions based on current filters
  const filterAndSortAuctions = useCallback(() => {
    let filtered = [...allAuctions];

    // Apply search filter
    if (auctionSearchTerm) {
      filtered = filtered.filter(
        (auction) =>
          auction.title
            .toLowerCase()
            .includes(auctionSearchTerm.toLowerCase()) ||
          auction.owner__username
            .toLowerCase()
            .includes(auctionSearchTerm.toLowerCase()) ||
          auction.category
            .toLowerCase()
            .includes(auctionSearchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (auctionStatusFilter !== "all") {
      filtered = filtered.filter((auction) => {
        switch (auctionStatusFilter) {
          case "approved":
            return auction.approved === true;
          case "pending":
            return auction.approved === false;
          case "active":
            return (
              auction.approved === true &&
              new Date(auction.end_time) > new Date()
            );
          case "ended":
            return new Date(auction.end_time) <= new Date();
          default:
            return true;
        }
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (auctionSortBy) {
        case "title":
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case "current_bid":
          aValue = parseFloat(a.current_bid);
          bValue = parseFloat(b.current_bid);
          break;
        case "views_count":
          aValue = a.views_count || 0;
          bValue = b.views_count || 0;
          break;
        case "owner":
          aValue = a.owner__username.toLowerCase();
          bValue = b.owner__username.toLowerCase();
          break;
        default: // created_at
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
      }

      if (auctionSortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setAuctions(filtered);
  }, [
    allAuctions,
    auctionSearchTerm,
    auctionStatusFilter,
    auctionSortBy,
    auctionSortOrder,
  ]);

  // Effect to filter auctions when filters change
  useEffect(() => {
    if (allAuctions.length > 0) {
      filterAndSortAuctions();
    }
  }, [allAuctions, filterAndSortAuctions]);

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null); // Clear any previous errors

      // Use fast endpoint for better performance
      console.log(
        "🚀 Using fast dashboard endpoint for optimal performance..."
      );

      // Test basic connectivity first
      console.log("🔍 ADMIN DASHBOARD: Testing basic connectivity...");

      // Test if backend is reachable
      try {
        const testResponse = await fetch("http://127.0.0.1:8000/api/test/");
        console.log(
          "🔍 ADMIN DASHBOARD: Backend connectivity test:",
          testResponse.status
        );
      } catch (connectError) {
        console.error(
          "🚨 ADMIN DASHBOARD: Backend connectivity failed:",
          connectError
        );
        throw new Error(
          "Cannot connect to backend server. Please ensure the server is running on port 8000."
        );
      }

      await fetchIndividualStats();

      // All data (users, auctions, AI analytics) now loaded from ultra-fast endpoint
      console.log(
        "✅ Dashboard loaded with ultra-fast endpoint - includes users, auctions, and AI analytics"
      );
    } catch (err) {
      console.error("🚨 ADMIN DASHBOARD: fetchDashboardData error:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [fetchIndividualStats]);

  const setupWebSocketConnection = useCallback(() => {
    try {
      const socket = extendedAuctionService.subscribeToAdminDashboard(
        (data) => {
          console.log("Received comprehensive dashboard update:", data);

          // Update analytics in real-time
          setAnalytics((prev) => {
            if (!prev) return null;

            return {
              ...prev,
              basic_metrics: {
                ...prev.basic_metrics,
                ...data.basic_metrics,
              },
              revenue_data: {
                ...prev.revenue_data,
                ...data.revenue_data,
              },
            };
          });

          console.log("Comprehensive dashboard updated in real-time");
        }
      );
      setWsConnection(socket);
    } catch (err) {
      console.error("WebSocket connection error:", err);
    }
  }, []);

  const deleteUser = async (userId, username) => {
    if (
      !window.confirm(`Are you sure you want to delete user "${username}"?`)
    ) {
      return;
    }

    try {
      const response = await fetch(
        `http://127.0.0.1:8000/api/admin/delete-user/${userId}/`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );

      if (response.ok) {
        alert(`User "${username}" deleted successfully`);
        fetchDashboardData(); // Refresh data
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (err) {
      alert(`Error deleting user: ${err.message}`);
    }
  };

  const deleteAuction = async (auctionId, title) => {
    if (
      !window.confirm(`Are you sure you want to delete auction "${title}"?`)
    ) {
      return;
    }

    try {
      const response = await fetch(
        `http://127.0.0.1:8000/api/admin/delete-auction/${auctionId}/`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        alert(`Auction "${title}" deleted successfully`);
        fetchDashboardData(); // Refresh data
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (err) {
      alert(`Error deleting auction: ${err.message}`);
    }
  };

  if (loading) {
    return (
      <div className="admin-dashboard loading">
        <div className="loading-spinner"></div>
        <p>Loading Admin Dashboard...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="admin-dashboard error">
        <h3>❌ Error Loading Dashboard</h3>
        <p>{error}</p>
        <button onClick={fetchDashboardData}>Retry</button>
      </div>
    );
  }

  return (
    <div className="admin-dashboard">
      <div className="dashboard-header">
        <h1>🛡️ Admin Dashboard</h1>
        <p>Comprehensive system management and analytics</p>
      </div>

      {/* Navigation Tabs */}
      <div className="dashboard-tabs">
        <button
          className={`tab ${activeTab === "overview" ? "active" : ""}`}
          onClick={() => setActiveTab("overview")}
        >
          📊 Overview
        </button>
        <button
          className={`tab ${activeTab === "users" ? "active" : ""}`}
          onClick={() => setActiveTab("users")}
        >
          👥 Users ({users.length})
        </button>
        <button
          className={`tab ${activeTab === "auctions" ? "active" : ""}`}
          onClick={() => setActiveTab("auctions")}
        >
          🏠 Auctions ({auctions.length})
        </button>
        <button
          className={`tab ${activeTab === "analytics" ? "active" : ""}`}
          onClick={() => setActiveTab("analytics")}
        >
          🤖 AI Analytics
        </button>
      </div>

      {/* Overview Tab */}
      {activeTab === "overview" && analytics && (
        <div className="overview-tab">
          {/* Key Metrics */}
          <div className="metrics-grid">
            <div className="metric-card revenue">
              <div className="metric-icon">💰</div>
              <div className="metric-content">
                <h3>
                  {formatAuctionPrice(
                    analytics.revenue_data?.total_revenue || 0
                  )}
                </h3>
                <p>Total Revenue</p>
                <small>
                  {formatAuctionPrice(
                    analytics.revenue_data?.total_commission || 0
                  )}{" "}
                  commission earned
                </small>
              </div>
            </div>

            <div className="metric-card growth">
              <div className="metric-icon">📈</div>
              <div className="metric-content">
                <h3
                  className={
                    analytics.revenue_data?.growth_rate >= 0
                      ? "positive"
                      : "negative"
                  }
                >
                  {analytics.revenue_data?.growth_rate || 0}%
                </h3>
                <p>Monthly Growth</p>
                <small>Revenue growth rate</small>
              </div>
            </div>

            <div className="metric-card users">
              <div className="metric-icon">👥</div>
              <div className="metric-content">
                <h3>{analytics.basic_metrics?.total_users || 0}</h3>
                <p>Total Users</p>
                <small>
                  {analytics.engagement_data?.active_users || 0} active this
                  month
                </small>
              </div>
            </div>

            <div className="metric-card auctions">
              <div className="metric-icon">🏠</div>
              <div className="metric-content">
                <h3>{analytics.basic_metrics?.total_auctions || 0}</h3>
                <p>Total Auctions</p>
                <small>
                  {analytics.basic_metrics?.active_auctions || 0} currently
                  active
                </small>
              </div>
            </div>
          </div>

          {/* Revenue Breakdown */}
          <div className="revenue-breakdown">
            <h3>💰 Revenue Breakdown</h3>
            <div className="revenue-cards">
              <div className="revenue-card">
                <h4>Today</h4>
                <p>
                  {formatAuctionPrice(
                    analytics.revenue_data?.daily_revenue || 0
                  )}
                </p>
              </div>
              <div className="revenue-card">
                <h4>This Week</h4>
                <p>
                  {formatAuctionPrice(
                    analytics.revenue_data?.weekly_revenue || 0
                  )}
                </p>
              </div>
              <div className="revenue-card">
                <h4>This Month</h4>
                <p>
                  {formatAuctionPrice(
                    analytics.revenue_data?.monthly_revenue || 0
                  )}
                </p>
              </div>
              <div className="revenue-card">
                <h4>Avg per Auction</h4>
                <p>
                  {formatAuctionPrice(
                    analytics.revenue_data?.avg_auction_value || 0
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="recent-activity">
            <h3>📋 Recent Activity</h3>
            <div className="activity-grid">
              <div className="activity-section">
                <h4>🏷️ Recent Auctions</h4>
                {analytics.recent_activity?.recent_auctions?.length > 0 ? (
                  analytics.recent_activity.recent_auctions
                    .slice(0, 5)
                    .map((auction, index) => (
                      <div key={auction.id || index} className="activity-item">
                        <div className="activity-main">
                          <strong>{auction.title}</strong>
                          <div className="activity-details">
                            <span className="activity-amount">
                              {formatAuctionPrice(auction.current_bid)}
                            </span>
                            <span className="activity-user">
                              by {auction.owner__username}
                            </span>
                          </div>
                        </div>
                        <div className="activity-time">
                          {new Date(auction.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    ))
                ) : (
                  <div className="no-activity">No recent auctions</div>
                )}
              </div>

              <div className="activity-section">
                <h4>💰 Recent Bids</h4>
                {analytics.recent_activity?.recent_bids?.length > 0 ? (
                  analytics.recent_activity.recent_bids
                    .slice(0, 5)
                    .map((bid, index) => (
                      <div key={bid.id || index} className="activity-item">
                        <div className="activity-main">
                          <strong className="bid-amount">
                            {formatAuctionPrice(bid.amount)}
                          </strong>
                          <div className="activity-details">
                            <span className="activity-user">
                              {bid.user__username}
                            </span>
                            <span className="activity-auction">
                              on {bid.auction__title}
                            </span>
                          </div>
                        </div>
                        <div className="activity-time">
                          {new Date(bid.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    ))
                ) : (
                  <div className="no-activity">No recent bids</div>
                )}
              </div>

              <div className="activity-section">
                <h4>👥 New Users</h4>
                {analytics.recent_activity?.recent_users?.length > 0 ? (
                  analytics.recent_activity.recent_users
                    .slice(0, 5)
                    .map((user, index) => (
                      <div key={user.id || index} className="activity-item">
                        <div className="activity-main">
                          <strong>{user.username}</strong>
                          <div className="activity-details">
                            <span className="activity-email">{user.email}</span>
                            <span
                              className={`activity-status ${
                                user.is_active ? "active" : "inactive"
                              }`}
                            >
                              {user.is_active ? "Active" : "Inactive"}
                            </span>
                          </div>
                        </div>
                        <div className="activity-time">
                          {new Date(user.date_joined).toLocaleDateString()}
                        </div>
                      </div>
                    ))
                ) : (
                  <div className="no-activity">No new users</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Users Management Tab */}
      {activeTab === "users" && (
        <div className="users-tab">
          <div className="tab-header">
            <h3>👥 User Management</h3>
            <p>Manage all users in the system</p>
          </div>

          <div className="users-table">
            <div className="table-header">
              <span>User</span>
              <span>Email</span>
              <span>Status</span>
              <span>Auctions</span>
              <span>Bids</span>
              <span>Joined</span>
              <span>Actions</span>
            </div>

            {users.map((user) => (
              <div key={user.id} className="table-row">
                <div className="user-info">
                  <strong>{user.username}</strong>
                  {user.is_staff && <span className="badge admin">Admin</span>}
                </div>
                <span>{user.email}</span>
                <span
                  className={`status ${user.is_active ? "active" : "inactive"}`}
                >
                  {user.is_active ? "Active" : "Inactive"}
                </span>
                <span>{user.auction_count}</span>
                <span>{user.bid_count}</span>
                <span>{new Date(user.date_joined).toLocaleDateString()}</span>
                <div className="actions">
                  {!user.is_staff && (
                    <button
                      className="delete-btn"
                      onClick={() => deleteUser(user.id, user.username)}
                    >
                      🗑️ Delete
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Comprehensive Auctions Management Tab */}
      {activeTab === "auctions" && (
        <div className="auctions-tab">
          <div className="tab-header">
            <h3>🏠 Comprehensive Auction Management</h3>
            <p>
              Manage all {allAuctions.length} auctions in the system with
              advanced filtering
            </p>
          </div>

          {/* Auction Management Controls */}
          <div className="auction-controls">
            <div className="control-row">
              <div className="search-control">
                <input
                  type="text"
                  placeholder="🔍 Search auctions, owners, categories..."
                  value={auctionSearchTerm}
                  onChange={(e) => setAuctionSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>

              <div className="filter-control">
                <select
                  value={auctionStatusFilter}
                  onChange={(e) => setAuctionStatusFilter(e.target.value)}
                  className="filter-select"
                >
                  <option value="all">All Status</option>
                  <option value="approved">✅ Approved</option>
                  <option value="pending">⏳ Pending</option>
                  <option value="active">🔴 Active</option>
                  <option value="ended">⚫ Ended</option>
                </select>
              </div>

              <div className="sort-control">
                <select
                  value={auctionSortBy}
                  onChange={(e) => setAuctionSortBy(e.target.value)}
                  className="sort-select"
                >
                  <option value="created_at">📅 Date Created</option>
                  <option value="title">📝 Title</option>
                  <option value="current_bid">💰 Current Bid</option>
                  <option value="views_count">👁️ Views</option>
                  <option value="owner">👤 Owner</option>
                </select>

                <button
                  onClick={() =>
                    setAuctionSortOrder(
                      auctionSortOrder === "asc" ? "desc" : "asc"
                    )
                  }
                  className="sort-order-btn"
                  title={`Sort ${
                    auctionSortOrder === "asc" ? "Descending" : "Ascending"
                  }`}
                >
                  {auctionSortOrder === "asc" ? "⬆️" : "⬇️"}
                </button>
              </div>
            </div>

            <div className="results-info">
              <span className="results-count">
                📊 Showing {auctions.length} of {allAuctions.length} auctions
              </span>
              {auctionSearchTerm && (
                <button
                  onClick={() => setAuctionSearchTerm("")}
                  className="clear-search-btn"
                >
                  ❌ Clear Search
                </button>
              )}
            </div>
          </div>

          <div className="auctions-table">
            <div className="table-header">
              <span>ID</span>
              <span>Auction Details</span>
              <span>Owner</span>
              <span>Current Bid</span>
              <span>Views</span>
              <span>Status</span>
              <span>Created</span>
              <span>Actions</span>
            </div>

            {auctions.length > 0 ? (
              auctions.map((auction) => (
                <div key={auction.id} className="table-row">
                  <span className="auction-id">#{auction.id}</span>
                  <div className="auction-info">
                    <strong>{auction.title}</strong>
                    <small className="auction-category">
                      📂 {auction.category}
                    </small>
                  </div>
                  <span className="auction-owner">
                    👤 {auction.owner__username}
                  </span>
                  <span className="auction-bid">
                    {formatAuctionPrice(auction.current_bid)}
                  </span>
                  <span className="views-count">
                    👁️ {auction.views_count?.toLocaleString() || 0}
                  </span>
                  <span
                    className={`status ${
                      auction.approved ? "approved" : "pending"
                    }`}
                  >
                    {auction.approved ? "✅ Approved" : "⏳ Pending"}
                  </span>
                  <span className="auction-date">
                    {new Date(auction.created_at).toLocaleDateString()}
                  </span>
                  <div className="actions">
                    <button
                      className="delete-btn"
                      onClick={() => deleteAuction(auction.id, auction.title)}
                      title="Delete Auction"
                    >
                      🗑️ Delete
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="no-results">
                <p>🔍 No auctions found matching your criteria</p>
                {auctionSearchTerm && (
                  <button
                    onClick={() => setAuctionSearchTerm("")}
                    className="clear-search-btn"
                  >
                    Clear Search
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* AI Analytics Tab */}
      {activeTab === "analytics" && analytics && (
        <div className="analytics-tab">
          <div className="tab-header">
            <h3>🤖 AI Analytics</h3>
            <p>Monitor AI performance and insights</p>
          </div>

          <div className="ai-metrics">
            <div className="ai-card">
              <h4>AI Predictions</h4>
              <p>{analytics.ai_metrics?.total_predictions || 0}</p>
              <small>Total predictions made</small>
            </div>

            <div className="ai-card">
              <h4>Average Confidence</h4>
              <p>{analytics.ai_metrics?.avg_confidence || 0}%</p>
              <small>Model confidence score</small>
            </div>

            <div className="ai-card">
              <h4>Model Performance</h4>
              <p>{analytics.ai_metrics?.model_performance || "N/A"}</p>
              <small>Overall performance rating</small>
            </div>

            <div className="ai-card">
              <h4>Predictions Today</h4>
              <p>{analytics.ai_metrics?.predictions_today || 0}</p>
              <small>Generated today</small>
            </div>
          </div>
        </div>
      )}

      {/* Refresh Button */}
      <div className="dashboard-actions">
        <button className="refresh-btn" onClick={fetchDashboardData}>
          🔄 Refresh Data
        </button>
      </div>
    </div>
  );
};

export default ComprehensiveAdminDashboard;
