"""
Reporting and analytics views for auctions
"""

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Count, Sum, Avg, Q
from datetime import timedelta, datetime
import json

from auction.models import Auction, Bid, Category, User


@api_view(['GET'])
@permission_classes([IsAdminUser])
def admin_dashboard_stats(request):
    """Get comprehensive statistics for admin dashboard"""
    try:
        now = timezone.now()
        last_30_days = now - timedelta(days=30)
        last_7_days = now - timedelta(days=7)

        # Basic counts
        total_users = User.objects.count()
        total_auctions = Auction.objects.count()
        active_auctions = Auction.objects.filter(
            end_time__gt=now,
            is_closed=False,
            approved=True
        ).count()
        total_bids = Bid.objects.count()

        # Revenue calculations
        completed_auctions = Auction.objects.filter(
            is_closed=True,
            current_bid__isnull=False
        )
        total_revenue = completed_auctions.aggregate(
            total=Sum('current_bid')
        )['total'] or 0

        # Recent activity
        recent_users = User.objects.filter(
            date_joined__gte=last_30_days
        ).count()
        
        recent_auctions = Auction.objects.filter(
            created_at__gte=last_30_days
        ).count()

        recent_bids = Bid.objects.filter(
            created_at__gte=last_7_days
        ).count()

        # Category statistics - using string values since category is a CharField
        from django.db.models import Case, When, Value, CharField
        category_stats = Auction.objects.values('category').annotate(
            auction_count=Count('id'),
            active_count=Count('id', filter=Q(
                end_time__gt=now,
                is_closed=False,
                approved=True
            ))
        ).values('category', 'auction_count', 'active_count')

        # Top sellers
        top_sellers = User.objects.annotate(
            auction_count=Count('auctions'),
            total_sales=Sum('auctions__current_bid', filter=Q(auctions__is_closed=True))
        ).filter(
            auction_count__gt=0
        ).order_by('-total_sales')[:5]

        top_sellers_data = []
        for seller in top_sellers:
            top_sellers_data.append({
                'username': seller.username,
                'auction_count': seller.auction_count,
                'total_sales': float(seller.total_sales or 0)
            })

        stats = {
            'overview': {
                'total_users': total_users,
                'total_auctions': total_auctions,
                'active_auctions': active_auctions,
                'total_bids': total_bids,
                'total_revenue': float(total_revenue)
            },
            'recent_activity': {
                'new_users_30d': recent_users,
                'new_auctions_30d': recent_auctions,
                'bids_7d': recent_bids
            },
            'categories': list(category_stats),
            'top_sellers': top_sellers_data
        }

        return Response({
            'success': True,
            'data': stats
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def revenue_report(request):
    """Generate revenue report for specified period"""
    try:
        # Get date range from query parameters
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        
        if not start_date or not end_date:
            # Default to last 30 days
            end_date = timezone.now()
            start_date = end_date - timedelta(days=30)
        else:
            start_date = datetime.fromisoformat(start_date)
            end_date = datetime.fromisoformat(end_date)

        # Get completed auctions in date range
        completed_auctions = Auction.objects.filter(
            is_active=False,
            end_time__gte=start_date,
            end_time__lte=end_date,
            final_price__isnull=False
        )

        # Calculate metrics
        total_revenue = completed_auctions.aggregate(
            total=Sum('final_price')
        )['total'] or 0

        average_sale_price = completed_auctions.aggregate(
            avg=Avg('final_price')
        )['avg'] or 0

        total_sales = completed_auctions.count()

        # Revenue by category
        category_revenue = completed_auctions.values(
            'category__name'
        ).annotate(
            revenue=Sum('final_price'),
            sales_count=Count('id')
        ).order_by('-revenue')

        # Daily revenue breakdown
        daily_revenue = {}
        for auction in completed_auctions:
            date_key = auction.end_time.date().isoformat()
            if date_key not in daily_revenue:
                daily_revenue[date_key] = 0
            daily_revenue[date_key] += float(auction.final_price)

        report = {
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'summary': {
                'total_revenue': float(total_revenue),
                'average_sale_price': float(average_sale_price),
                'total_sales': total_sales
            },
            'category_breakdown': list(category_revenue),
            'daily_revenue': daily_revenue
        }

        return Response({
            'success': True,
            'data': report
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def user_activity_report(request):
    """Generate user activity report"""
    try:
        now = timezone.now()
        last_30_days = now - timedelta(days=30)

        # Active users (users who bid or created auctions in last 30 days)
        active_users = User.objects.filter(
            Q(bid__created_at__gte=last_30_days) |
            Q(auctions__created_at__gte=last_30_days)
        ).distinct()

        # User registration trends
        registration_data = {}
        for i in range(30):
            date = (now - timedelta(days=i)).date()
            count = User.objects.filter(
                date_joined__date=date
            ).count()
            registration_data[date.isoformat()] = count

        # Top bidders
        top_bidders = User.objects.annotate(
            bid_count=Count('bid'),
            total_bid_amount=Sum('bid__amount')
        ).filter(
            bid_count__gt=0
        ).order_by('-bid_count')[:10]

        top_bidders_data = []
        for bidder in top_bidders:
            top_bidders_data.append({
                'username': bidder.username,
                'bid_count': bidder.bid_count,
                'total_bid_amount': float(bidder.total_bid_amount or 0)
            })

        report = {
            'active_users_30d': active_users.count(),
            'total_users': User.objects.count(),
            'registration_trend': registration_data,
            'top_bidders': top_bidders_data
        }

        return Response({
            'success': True,
            'data': report
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def export_user_data(request):
    """Export user's auction and bid data"""
    try:
        user = request.user
        
        # User's auctions
        user_auctions = Auction.objects.filter(seller=user)
        auctions_data = []
        
        for auction in user_auctions:
            auctions_data.append({
                'title': auction.title,
                'starting_bid': float(auction.starting_bid),
                'current_bid': float(auction.current_bid),
                'final_price': float(auction.final_price or 0),
                'created_at': auction.created_at.isoformat(),
                'end_time': auction.end_time.isoformat(),
                'is_active': auction.is_active,
                'category': auction.category.name if auction.category else None
            })

        # User's bids
        user_bids = Bid.objects.filter(bidder=user)
        bids_data = []
        
        for bid in user_bids:
            bids_data.append({
                'auction_title': bid.auction.title,
                'bid_amount': float(bid.amount),
                'timestamp': bid.timestamp.isoformat(),
                'is_winning': bid.auction.current_bid == bid.amount
            })

        export_data = {
            'user_info': {
                'username': user.username,
                'email': user.email,
                'date_joined': user.date_joined.isoformat()
            },
            'auctions': auctions_data,
            'bids': bids_data,
            'export_date': timezone.now().isoformat()
        }

        return Response({
            'success': True,
            'data': export_data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
