import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Form, Alert, Spinner } from "react-bootstrap";
import axiosInstance from "../api/axiosInstance";

const AuctionImageUpdater = ({ show, onHide, auction, onUpdate }) => {
  const [imageUrl, setImageUrl] = useState("");
  const [additionalImages, setAdditionalImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  useEffect(() => {
    if (auction) {
      setImageUrl(auction.image || "");
      setAdditionalImages(auction.additional_images || []);
    }
  }, [auction]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess("");

    try {
      // Filter out empty additional images
      const filteredAdditionalImages = additionalImages.filter(
        (img) => img.trim() !== ""
      );

      const response = await axiosInstance.patch(`auctions/${auction.id}/`, {
        image: imageUrl,
        additional_images: filteredAdditionalImages,
      });

      setSuccess("Images updated successfully!");
      onUpdate(response.data);

      // Close modal after 2 seconds
      setTimeout(() => {
        onHide();
        setSuccess("");
      }, 2000);
    } catch (err) {
      setError(
        err.response?.data?.detail ||
          err.response?.data?.image?.[0] ||
          err.response?.data?.additional_images?.[0] ||
          "Failed to update images"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setImageUrl(auction?.image || "");
    setAdditionalImages(auction?.additional_images || []);
    setError("");
    setSuccess("");
    onHide();
  };

  const handleAdditionalImageChange = (index, value) => {
    const newImages = [...additionalImages];
    newImages[index] = value;
    setAdditionalImages(newImages);
  };

  const handleAdditionalImageAdd = () => {
    if (additionalImages.length < 5) {
      setAdditionalImages([...additionalImages, ""]);
    }
  };

  const handleAdditionalImageRemove = (index) => {
    const newImages = additionalImages.filter((_, i) => i !== index);
    setAdditionalImages(newImages);
  };

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Update Auction Image</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {auction && (
          <>
            <div className="mb-3">
              <h6>Auction: {auction.title}</h6>
              <small className="text-muted">ID: {auction.id}</small>
            </div>

            {auction.image && (
              <div className="mb-3">
                <label className="form-label">Current Image:</label>
                <div className="text-center">
                  <img
                    src={auction.image}
                    alt="Current auction"
                    style={{
                      maxWidth: "100%",
                      maxHeight: "200px",
                      objectFit: "contain",
                      border: "1px solid #ddd",
                      borderRadius: "4px",
                    }}
                    onError={(e) => {
                      e.target.style.display = "none";
                    }}
                  />
                </div>
              </div>
            )}

            {error && <Alert variant="danger">{error}</Alert>}
            {success && <Alert variant="success">{success}</Alert>}

            <Form onSubmit={handleSubmit}>
              <Form.Group className="mb-3">
                <Form.Label>New Image URL</Form.Label>
                <Form.Control
                  type="url"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  placeholder="https://example.com/image.jpg"
                  required
                />
                <Form.Text className="text-muted">
                  Enter a valid image URL (jpg, png, gif, webp)
                </Form.Text>
              </Form.Group>

              {imageUrl && imageUrl !== auction?.image && (
                <div className="mb-3">
                  <label className="form-label">Main Image Preview:</label>
                  <div className="text-center">
                    <img
                      src={imageUrl}
                      alt="Preview"
                      style={{
                        maxWidth: "100%",
                        maxHeight: "200px",
                        objectFit: "contain",
                        border: "1px solid #ddd",
                        borderRadius: "4px",
                      }}
                      onError={(e) => {
                        e.target.style.display = "none";
                      }}
                    />
                  </div>
                </div>
              )}

              {/* Additional Images Section */}
              <div className="mb-3">
                <Form.Label>Additional Images (Optional)</Form.Label>
                {additionalImages.map((imageUrl, index) => (
                  <div key={index} className="input-group mb-2">
                    <Form.Control
                      type="url"
                      placeholder={`Additional Image ${index + 1} URL`}
                      value={imageUrl}
                      onChange={(e) =>
                        handleAdditionalImageChange(index, e.target.value)
                      }
                    />
                    <Button
                      variant="outline-danger"
                      onClick={() => handleAdditionalImageRemove(index)}
                    >
                      Remove
                    </Button>
                  </div>
                ))}
                {additionalImages.length < 5 && (
                  <Button
                    variant="outline-primary"
                    size="sm"
                    onClick={handleAdditionalImageAdd}
                  >
                    + Add Another Image
                  </Button>
                )}
                <Form.Text className="text-muted d-block mt-1">
                  You can add up to 5 additional images
                </Form.Text>
              </div>

              {/* Additional Images Preview */}
              {additionalImages.some((img) => img.trim() !== "") && (
                <div className="mb-3">
                  <label className="form-label">
                    Additional Images Preview:
                  </label>
                  <div className="row">
                    {additionalImages
                      .filter((img) => img.trim() !== "")
                      .map((img, index) => (
                        <div key={index} className="col-6 col-md-4 mb-2">
                          <img
                            src={img}
                            alt={`Additional ${index + 1}`}
                            style={{
                              width: "100%",
                              height: "100px",
                              objectFit: "contain",
                              border: "1px solid #ddd",
                              borderRadius: "4px",
                            }}
                            onError={(e) => {
                              e.target.style.display = "none";
                            }}
                          />
                        </div>
                      ))}
                  </div>
                </div>
              )}

              <div className="d-flex justify-content-end gap-2">
                <Button variant="secondary" onClick={handleClose}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  disabled={
                    loading ||
                    !imageUrl ||
                    (imageUrl === auction?.image &&
                      JSON.stringify(additionalImages) ===
                        JSON.stringify(auction?.additional_images || []))
                  }
                >
                  {loading ? (
                    <>
                      <Spinner size="sm" className="me-2" />
                      Updating...
                    </>
                  ) : (
                    "Update Images"
                  )}
                </Button>
              </div>
            </Form>
          </>
        )}
      </Modal.Body>
    </Modal>
  );
};

export default AuctionImageUpdater;
