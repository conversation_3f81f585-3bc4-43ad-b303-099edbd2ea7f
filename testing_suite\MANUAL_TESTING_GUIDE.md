# Manual Testing Execution Guide

## Pre-Testing Setup

### 1. Environment Preparation
```bash
# Start the application
cd C:\Users\<USER>\OneDrive\Desktop\Online_Auction_System
start_complete.bat

# Verify services are running
# Frontend: http://localhost:3000
# Backend: http://localhost:8000
# Database: PostgreSQL should be running
```

### 2. Test Data Setup
Create the following test accounts before starting:

**Admin Account:**
- Username: `aisha_admin`
- Email: `<EMAIL>`
- Password: `admin123`
- Role: Admin

**Seller Account:**
- Username: `test_seller`
- Email: `<EMAIL>`
- Password: `seller123`
- Role: Seller

**Bidder Account:**
- Username: `test_bidder`
- Email: `<EMAIL>`
- Password: `bidder123`
- Role: Bidder

**Both Role Account:**
- Username: `test_both`
- Email: `<EMAIL>`
- Password: `both123`
- Role: Both

### 3. Sample Auction Data
Create 3-5 sample auctions with:
- Different categories (Electronics, Fashion, etc.)
- Various price ranges
- Different auction types
- Some active, some ended
- Multiple images for carousel testing

---

## Testing Execution Order

### Phase 1: Core Functionality (Priority: High)
Execute these test cases first as they are critical:

1. **TC_001 - TC_004**: User Authentication
2. **TC_005 - TC_006**: Auction Creation & Access Control
3. **TC_007 - TC_008**: Basic Bidding
4. **TC_021**: Admin Dashboard Access

### Phase 2: User Features (Priority: Medium)
5. **TC_010 - TC_012**: Search & Filter
6. **TC_013 - TC_018**: Navigation & User Dashboard
7. **TC_032**: Watchlist
8. **TC_033**: Currency Display

### Phase 3: Advanced Features (Priority: Medium)
9. **TC_019 - TC_020**: Real-time Features
10. **TC_024 - TC_025**: Payment System
11. **TC_026 - TC_027**: AI Features
12. **TC_028**: Notifications

### Phase 4: UI/UX & Edge Cases (Priority: Low)
13. **TC_029 - TC_030**: UI/UX Testing
14. **TC_031**: Auction End Time
15. **TC_034 - TC_035**: Media & Contact

---

## Testing Best Practices

### Before Each Test Case:
1. Clear browser cache if needed
2. Ensure you're logged in with the correct user role
3. Take a screenshot of the initial state
4. Note the current time for time-sensitive tests

### During Testing:
1. Follow test steps exactly as written
2. Record actual results immediately
3. Take screenshots of any failures
4. Note any unexpected behavior
5. Check browser console for errors

### After Each Test Case:
1. Mark the status (Pass/Fail) in the results template
2. Add detailed notes for failures
3. Clean up test data if needed
4. Log out and log back in for role-based tests

---

## Common Test Scenarios

### Testing User Roles:
```
Bidder Only: Can view auctions, place bids, cannot create auctions
Seller Only: Can create auctions, view own auctions, cannot bid
Both: Can do everything (create auctions and bid)
Admin: Full access to all features + admin panel
```

### Testing Real-time Features:
1. Open two browser windows/tabs
2. Login as different users in each
3. Perform actions in one window
4. Verify updates appear in the other window

### Testing Responsive Design:
1. Desktop: 1920x1080
2. Tablet: 768x1024
3. Mobile: 375x667
4. Test all major functions on each size

---

## Troubleshooting Common Issues

### Application Won't Start:
- Check if PostgreSQL is running
- Verify Redis is running
- Check port conflicts (3000, 8000)
- Review console logs for errors

### Database Issues:
- Ensure PostgreSQL service is running
- Check database connection settings
- Verify test data exists

### WebSocket Issues:
- Check browser console for WebSocket errors
- Verify backend WebSocket service is running
- Test with different browsers

### Payment Testing:
- Use Stripe test card numbers
- Don't use real payment information
- Check Stripe dashboard for test transactions

---

## Test Data Management

### Creating Test Auctions:
```
Title: "Test Auction - [Category]"
Description: "This is a test auction for manual testing"
Starting Bid: ₹100
End Time: Set 1-2 hours from current time
Category: Vary across different categories
Images: Use placeholder images or test images
```

### Bid Testing Amounts:
```
Starting Bid: ₹100
Valid Bid: ₹150
Invalid Bid: ₹50 (should fail)
Auto-bid Max: ₹500
```

---

## Reporting Issues

### For Each Bug Found:
1. **Bug ID**: Unique identifier
2. **Severity**: Critical/High/Medium/Low
3. **Module**: Which part of the system
4. **Steps to Reproduce**: Exact steps
5. **Expected Result**: What should happen
6. **Actual Result**: What actually happened
7. **Screenshot**: Visual evidence
8. **Browser/OS**: Environment details

### Severity Guidelines:
- **Critical**: System crash, data loss, security issues
- **High**: Major functionality broken, blocking user tasks
- **Medium**: Minor functionality issues, workarounds available
- **Low**: Cosmetic issues, minor inconveniences

---

## Post-Testing Activities

### 1. Results Compilation:
- Fill out the results template completely
- Calculate pass/fail percentages
- Identify patterns in failures
- Prioritize issues by severity

### 2. Test Data Cleanup:
- Remove test accounts (except admin)
- Delete test auctions
- Clear test bids and payments
- Reset database to clean state

### 3. Documentation:
- Update test cases based on findings
- Document any new test scenarios discovered
- Create bug reports for development team
- Archive test results with timestamps

---

## Quick Commands Reference

```bash
# Start application
start_complete.bat

# Stop application
stop.bat

# Check running processes
netstat -an | findstr :3000
netstat -an | findstr :8000

# Clear browser data
Ctrl+Shift+Delete (Chrome)

# Open developer tools
F12 (Chrome)
```

---

## Contact Information

**For Technical Issues:**
- Check application logs in backend console
- Review browser developer console
- Contact development team if needed

**For Test Process Issues:**
- Review this guide
- Check test case documentation
- Escalate to test lead if needed
