{"ast": null, "code": "import { slice } from \"./array.js\";\nimport bisect from \"./bisect.js\";\nimport constant from \"./constant.js\";\nimport extent from \"./extent.js\";\nimport identity from \"./identity.js\";\nimport nice from \"./nice.js\";\nimport ticks, { tickIncrement } from \"./ticks.js\";\nimport sturges from \"./threshold/sturges.js\";\nexport default function bin() {\n  var value = identity,\n    domain = extent,\n    threshold = sturges;\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n    var i,\n      n = data.length,\n      x,\n      step,\n      values = new Array(n);\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n    var xz = domain(values),\n      x0 = xz[0],\n      x1 = xz[1],\n      tz = threshold(values, x0, x1);\n\n    // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n    if (!Array.isArray(tz)) {\n      const max = x1,\n        tn = +tz;\n      if (domain === extent) [x0, x1] = nice(x0, x1, tn);\n      tz = ticks(x0, x1, tn);\n\n      // If the domain is aligned with the first tick (which it will by\n      // default), then we can use quantization rather than bisection to bin\n      // values, which is substantially faster.\n      if (tz[0] <= x0) step = tickIncrement(x0, x1, tn);\n\n      // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === extent) {\n          const step = tickIncrement(x0, x1, tn);\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    }\n\n    // Remove any thresholds outside the domain.\n    // Be careful not to mutate an array owned by the user!\n    var m = tz.length,\n      a = 0,\n      b = m;\n    while (tz[a] <= x0) ++a;\n    while (tz[b - 1] > x1) --b;\n    if (a || b < m) tz = tz.slice(a, b), m = b - a;\n    var bins = new Array(m + 1),\n      bin;\n\n    // Initialize bins.\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    }\n\n    // Assign data to bins by value, ignoring any outside the domain.\n    if (isFinite(step)) {\n      if (step > 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            bins[Math.min(m, Math.floor((x - x0) / step))].push(data[i]);\n          }\n        }\n      } else if (step < 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            const j = Math.floor((x0 - x) * step);\n            bins[Math.min(m, j + (tz[j] <= x))].push(data[i]); // handle off-by-one due to rounding\n          }\n        }\n      }\n    } else {\n      for (i = 0; i < n; ++i) {\n        if ((x = values[i]) != null && x0 <= x && x <= x1) {\n          bins[bisect(tz, x, 0, m)].push(data[i]);\n        }\n      }\n    }\n    return bins;\n  }\n  histogram.value = function (_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(_), histogram) : value;\n  };\n  histogram.domain = function (_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : constant([_[0], _[1]]), histogram) : domain;\n  };\n  histogram.thresholds = function (_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : constant(Array.isArray(_) ? slice.call(_) : _), histogram) : threshold;\n  };\n  return histogram;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}