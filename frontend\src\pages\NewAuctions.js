import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../api/axiosInstance";
import NewAdvancedFilter from "../components/NewAdvancedFilter";
import AuctionCard from "../components/AuctionCard";
import LoadingSpinner from "../components/LoadingSpinner";
import "./NewAuctions.css";

const NewAuctions = () => {
  const navigate = useNavigate();
  const [auctions, setAuctions] = useState([]);
  const [filteredAuctions, setFilteredAuctions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isFiltering, setIsFiltering] = useState(false);

  // Fetch all auctions on component mount
  useEffect(() => {
    fetchAllAuctions();
  }, []);

  const fetchAllAuctions = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axiosInstance.get("/auctions/?page_size=100");
      const auctionData = response.data.results || response.data;
      setAuctions(auctionData);
      setFilteredAuctions(auctionData);
    } catch (err) {
      console.error("Failed to fetch auctions:", err);
      setError("Failed to load auctions. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleFilterResults = (results) => {
    setIsFiltering(true);
    setFilteredAuctions(results);
    setIsFiltering(false);
  };

  const handleClearResults = () => {
    setFilteredAuctions(auctions);
  };

  const handleAuctionClick = (auctionId) => {
    navigate(`/auctions/${auctionId}`);
  };

  if (loading) {
    return (
      <div className="new-auctions-page">
        <div className="container">
          <LoadingSpinner />
          <p className="loading-text">Loading auctions...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="new-auctions-page">
        <div className="container">
          <div className="error-message">
            <h3>⚠️ Error</h3>
            <p>{error}</p>
            <button onClick={fetchAllAuctions} className="retry-btn">
              🔄 Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="new-auctions-page">
      <div className="container">
        {/* Page Header */}
        <div className="page-header">
          <h1>🏛️ Browse Auctions</h1>
          <p className="page-subtitle">
            Discover amazing items and place your bids
          </p>
        </div>

        {/* Advanced Filter Component */}
        <NewAdvancedFilter
          onFilterResults={handleFilterResults}
          onClearResults={handleClearResults}
        />

        {/* Results Summary */}
        <div className="results-summary">
          <div className="results-info">
            <span className="results-count">
              {isFiltering ? (
                "🔄 Filtering..."
              ) : (
                <>
                  📊 Showing {filteredAuctions.length} of {auctions.length} auctions
                </>
              )}
            </span>
          </div>
        </div>

        {/* Auctions Grid */}
        {filteredAuctions.length > 0 ? (
          <div className="auctions-grid">
            {filteredAuctions.map((auction) => (
              <div
                key={auction.id}
                className="auction-card-wrapper"
                onClick={() => handleAuctionClick(auction.id)}
              >
                <AuctionCard auction={auction} />
              </div>
            ))}
          </div>
        ) : (
          <div className="no-results">
            <div className="no-results-content">
              <h3>🔍 No auctions found</h3>
              <p>
                {auctions.length === 0
                  ? "No auctions are currently available."
                  : "Try adjusting your search filters to find more auctions."}
              </p>
              {auctions.length > 0 && (
                <button onClick={handleClearResults} className="clear-filters-btn">
                  🗑️ Clear Filters
                </button>
              )}
            </div>
          </div>
        )}

        {/* Load More Button (if needed) */}
        {filteredAuctions.length > 0 && filteredAuctions.length >= 20 && (
          <div className="load-more-section">
            <button
              onClick={() => {
                // Implement load more functionality if needed
                console.log("Load more auctions");
              }}
              className="load-more-btn"
            >
              📄 Load More Auctions
            </button>
          </div>
        )}

        {/* Quick Stats */}
        <div className="quick-stats">
          <div className="stat-item">
            <span className="stat-number">{auctions.length}</span>
            <span className="stat-label">Total Auctions</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">
              {auctions.filter((a) => a.is_active).length}
            </span>
            <span className="stat-label">Active Auctions</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">
              {new Set(auctions.map((a) => a.category)).size}
            </span>
            <span className="stat-label">Categories</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewAuctions;
