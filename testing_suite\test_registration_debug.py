#!/usr/bin/env python3
"""
Debug script to test user registration endpoint
"""

import requests
import json

def test_registration():
    """Test the registration endpoint with various scenarios"""
    
    base_url = "http://127.0.0.1:8000/api"
    
    # Test data
    test_cases = [
        {
            "name": "Valid Registration",
            "data": {
                "username": "test_user_debug",
                "email": "<EMAIL>",
                "password": "testpass123",
                "user_role": "bidder"
            }
        },
        {
            "name": "Missing user_role",
            "data": {
                "username": "test_user_debug2",
                "email": "<EMAIL>",
                "password": "testpass123"
            }
        },
        {
            "name": "Invalid user_role",
            "data": {
                "username": "test_user_debug3",
                "email": "<EMAIL>",
                "password": "testpass123",
                "user_role": "invalid_role"
            }
        }
    ]
    
    print("🔍 Testing Registration Endpoint")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print(f"📤 Request Data: {json.dumps(test_case['data'], indent=2)}")
        
        try:
            response = requests.post(
                f"{base_url}/register/",
                json=test_case['data'],
                headers={"Content-Type": "application/json"}
            )
            
            print(f"📊 Status Code: {response.status_code}")
            
            try:
                response_data = response.json()
                print(f"📥 Response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"📥 Response Text: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        print("-" * 30)

def test_endpoint_availability():
    """Test if the endpoint is available"""
    print("\n🌐 Testing Endpoint Availability")
    print("=" * 50)
    
    endpoints = [
        "http://127.0.0.1:8000/api/",
        "http://127.0.0.1:8000/api/register/",
        "http://127.0.0.1:8000/api/login/"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=5)
            print(f"✅ {endpoint} - Status: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - Error: {str(e)}")

if __name__ == "__main__":
    test_endpoint_availability()
    test_registration()
