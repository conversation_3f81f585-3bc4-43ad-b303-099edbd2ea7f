from django.contrib import admin

from .models import (Auction, AuditTrail, AutoBid, Bid, ChatMessage, ChatRoom,
                     Notification, Payment, PricePrediction,
                     PricePredictionHistory, PrivateMessage, Review)


class AuctionAdmin(admin.ModelAdmin):
    list_display = (
        "title",
        "starting_bid",
        "current_bid",
        "created_at",
        "end_time",
        "owner",
        "approved",
    )
    list_filter = ("approved",)
    list_display_links = ("title",)
    ordering = ("-created_at",)


class BidAdmin(admin.ModelAdmin):
    list_display = ("user", "auction", "amount", "created_at")
    search_fields = ("user__username", "auction__title")
    list_filter = ("created_at",)


class PaymentAdmin(admin.ModelAdmin):
    list_display = ("user", "auction", "amount", "payment_status", "transaction_id")
    list_filter = ("payment_status",)


admin.site.register(Payment, PaymentAdmin)
admin.site.register(Auction, AuctionAdmin)
admin.site.register(Bid, BidAdmin)
admin.site.register(Notification)
admin.site.register(Review)
admin.site.register(AutoBid)
admin.site.register(AuditTrail)


# AI Price Prediction Admin
class PricePredictionAdmin(admin.ModelAdmin):
    list_display = (
        "auction",
        "predicted_price",
        "confidence_score",
        "model_version",
        "created_at",
    )
    list_filter = ("model_version", "created_at")
    search_fields = ("auction__title",)
    readonly_fields = ("created_at", "updated_at")


class PricePredictionHistoryAdmin(admin.ModelAdmin):
    list_display = (
        "auction",
        "predicted_price",
        "actual_price",
        "accuracy_score",
        "confidence_score",
        "created_at",
    )
    list_filter = ("model_version", "created_at")
    search_fields = ("auction__title",)
    readonly_fields = ("created_at",)


# Chat System Admin
class ChatRoomAdmin(admin.ModelAdmin):
    list_display = (
        "auction",
        "participants_count",
        "is_active",
        "created_at",
        "updated_at",
    )
    list_filter = ("is_active", "created_at")
    search_fields = ("auction__title",)
    readonly_fields = ("created_at", "updated_at")

    def participants_count(self, obj):
        return obj.participants.count()

    participants_count.short_description = "Participants"


class ChatMessageAdmin(admin.ModelAdmin):
    list_display = (
        "sender",
        "room",
        "message_preview",
        "message_type",
        "timestamp",
        "is_read",
    )
    list_filter = ("message_type", "is_read", "timestamp")
    search_fields = ("sender__username", "message", "room__auction__title")
    readonly_fields = ("timestamp",)

    def message_preview(self, obj):
        return obj.message[:50] + "..." if len(obj.message) > 50 else obj.message

    message_preview.short_description = "Message"


class PrivateMessageAdmin(admin.ModelAdmin):
    list_display = (
        "sender",
        "recipient",
        "subject",
        "message_preview",
        "timestamp",
        "is_read",
    )
    list_filter = ("is_read", "timestamp")
    search_fields = ("sender__username", "recipient__username", "subject", "message")
    readonly_fields = ("timestamp",)

    def message_preview(self, obj):
        return obj.message[:50] + "..." if len(obj.message) > 50 else obj.message

    message_preview.short_description = "Message"


# Register new models
admin.site.register(PricePrediction, PricePredictionAdmin)
admin.site.register(PricePredictionHistory, PricePredictionHistoryAdmin)
admin.site.register(ChatRoom, ChatRoomAdmin)
admin.site.register(ChatMessage, ChatMessageAdmin)
admin.site.register(PrivateMessage, PrivateMessageAdmin)
