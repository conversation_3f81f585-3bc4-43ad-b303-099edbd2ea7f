{"ast": null, "code": "import { tickIncrement } from \"./ticks.js\";\nexport default function nice(start, stop, count) {\n  let prestep;\n  while (true) {\n    const step = tickIncrement(start, stop, count);\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n    prestep = step;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}