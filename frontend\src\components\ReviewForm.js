import React, { useState } from "react";
import { <PERSON>dal, <PERSON>ton, Form, Alert, Card, Row, Col } from "react-bootstrap";
import { FaStar, FaRegStar, FaUser, FaCheckCircle } from "react-icons/fa";
import axiosInstance from "../api/axiosInstance";
import { useAuth } from "../context/AuthContext";
import "./ReviewForm.css";

const ReviewForm = ({
  show,
  onHide,
  auctionId,
  revieweeId,
  revieweeName,
  auctionTitle,
  onReviewSubmitted,
}) => {
  const { user } = useAuth();
  const [rating, setRating] = useState(5);
  const [comment, setComment] = useState("");
  const [reviewType, setReviewType] = useState("general");
  const [hoveredRating, setHoveredRating] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!user) {
      setError("Please log in to submit a review");
      return;
    }

    if (comment.trim().length < 10) {
      setError("Review comment must be at least 10 characters long");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const reviewData = {
        auction: auctionId,
        reviewee: revieweeId,
        rating: parseInt(rating),
        comment: comment.trim(),
        review_type: reviewType,
      };

      const response = await axiosInstance.post("/reviews/", reviewData);

      setSuccess(true);
      setTimeout(() => {
        onReviewSubmitted && onReviewSubmitted(response.data);
        handleClose();
      }, 2000);
    } catch (err) {
      console.error("Review submission error:", err);
      if (err.response?.data?.detail) {
        setError(err.response.data.detail);
      } else if (err.response?.data?.non_field_errors) {
        setError(err.response.data.non_field_errors[0]);
      } else {
        setError("Failed to submit review. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setRating(5);
    setComment("");
    setReviewType("general");
    setError("");
    setSuccess(false);
    setHoveredRating(0);
    onHide();
  };

  const renderStars = () => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <span
          key={i}
          className={`star ${i <= (hoveredRating || rating) ? "filled" : ""}`}
          onClick={() => setRating(i)}
          onMouseEnter={() => setHoveredRating(i)}
          onMouseLeave={() => setHoveredRating(0)}
        >
          {i <= (hoveredRating || rating) ? <FaStar /> : <FaRegStar />}
        </span>
      );
    }
    return stars;
  };

  const getRatingText = (rating) => {
    const texts = {
      1: "Poor",
      2: "Fair",
      3: "Good",
      4: "Very Good",
      5: "Excellent",
    };
    return texts[rating] || "";
  };

  return (
    <Modal show={show} onHide={handleClose} size="lg" centered>
      <Modal.Header closeButton>
        <Modal.Title>
          <FaUser className="me-2" />
          Write a Review
        </Modal.Title>
      </Modal.Header>

      <Modal.Body>
        {success ? (
          <Alert variant="success" className="text-center">
            <FaCheckCircle className="me-2" />
            Review submitted successfully! Thank you for your feedback.
          </Alert>
        ) : (
          <>
            <Card className="mb-3">
              <Card.Body>
                <h6 className="text-muted mb-1">Reviewing:</h6>
                <h5 className="mb-1">{auctionTitle}</h5>
                <p className="text-muted mb-0">
                  <FaUser className="me-1" />
                  {revieweeName}
                </p>
              </Card.Body>
            </Card>

            {error && (
              <Alert variant="danger" className="mb-3">
                {error}
              </Alert>
            )}

            <Form onSubmit={handleSubmit}>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Review Type</Form.Label>
                    <Form.Select
                      value={reviewType}
                      onChange={(e) => setReviewType(e.target.value)}
                    >
                      <option value="general">General Review</option>
                      <option value="seller">Seller Review</option>
                      <option value="buyer">Buyer Review</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Rating</Form.Label>
                    <div className="rating-container">
                      <div className="stars-wrapper">{renderStars()}</div>
                      <span className="rating-text ms-2">
                        {rating}/5 - {getRatingText(rating)}
                      </span>
                    </div>
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mb-3">
                <Form.Label>Your Review</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={4}
                  placeholder="Share your experience with this auction and seller. Your review helps other buyers make informed decisions..."
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  maxLength={1000}
                  required
                />
                <Form.Text className="text-muted">
                  {comment.length}/1000 characters (minimum 10 required)
                </Form.Text>
              </Form.Group>

              <div className="review-guidelines">
                <h6>Review Guidelines:</h6>
                <ul className="small text-muted">
                  <li>Be honest and fair in your assessment</li>
                  <li>Focus on your experience with the item and seller</li>
                  <li>Avoid personal attacks or inappropriate language</li>
                  <li>Include details that would help other buyers</li>
                </ul>
              </div>
            </Form>
          </>
        )}
      </Modal.Body>

      {!success && (
        <Modal.Footer>
          <Button variant="secondary" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            disabled={loading || comment.trim().length < 10}
          >
            {loading ? "Submitting..." : "Submit Review"}
          </Button>
        </Modal.Footer>
      )}
    </Modal>
  );
};

export default ReviewForm;
