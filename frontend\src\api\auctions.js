import axiosInstance from "./axiosInstance";
import globalApiManager from "../utils/globalApiManager";

export async function fetchAuctions(params = {}) {
  try {
    // Add page_size=100 to show all auctions by default
    const defaultParams = { page_size: 100, ...params };
    const queryParams = new URLSearchParams(defaultParams).toString();
    const url = `auctions/?${queryParams}`;

    console.log("🌐 Fetching auctions from:", url);
    const res = await axiosInstance.get(url);

    // Django REST Framework returns paginated data with 'results' array
    const auctions = res.data.results || res.data || [];
    console.log(`📦 Received ${auctions.length} auctions from API`);

    return auctions;
  } catch (error) {
    console.error("❌ fetchAuctions API error:", error);

    // Provide more specific error information
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response data:", error.response.data);
      throw new Error(
        `Server error: ${error.response.status} - ${
          error.response.data?.detail || "Unknown error"
        }`
      );
    } else if (error.request) {
      console.error("No response received:", error.request);
      throw new Error("Network error: Unable to connect to server");
    } else {
      console.error("Request setup error:", error.message);
      throw new Error(`Request error: ${error.message}`);
    }
  }
}

export async function fetchAuction(id) {
  const res = await axiosInstance.get(`auctions/${id}/`);
  return res.data;
}

export async function incrementAuctionViews(id) {
  try {
    console.log(`👁️ Incrementing views for auction ID: ${id}`);
    const res = await axiosInstance.post(`auctions/${id}/increment_views/`);
    console.log(
      `✅ Successfully incremented views for auction ID: ${id}`,
      res.data
    );
    return res.data;
  } catch (error) {
    console.error(`❌ incrementAuctionViews API error for ID ${id}:`, error);
    // Don't throw error for views increment - it's not critical
    return { success: false, error: error.message };
  }
}

export async function fetchFeaturedAuctions() {
  try {
    // Use global API manager with caching and circuit breaker
    const data = await globalApiManager.makeRequest(
      axiosInstance,
      "auctions/featured/",
      {
        cacheTime: 5 * 60 * 1000, // Cache for 5 minutes
        maxRetries: 2,
      }
    );
    return data || [];
  } catch (error) {
    console.error("Error fetching featured auctions:", error);
    try {
      // Single fallback to alternative endpoint with global manager
      const data = await globalApiManager.makeRequest(
        axiosInstance,
        "featured-auctions/",
        {
          cacheTime: 5 * 60 * 1000,
          maxRetries: 1,
        }
      );
      return data || [];
    } catch (fallbackError) {
      console.error(
        "Featured auctions endpoints failed, returning empty array"
      );
      return []; // Return empty array instead of trying more endpoints
    }
  }
}

export async function fetchCategories() {
  try {
    console.log("🔄 Fetching categories with auction counts from API...");
    const categories =
      (await globalApiManager.makeRequest(
        axiosInstance,
        "categories-with-counts/",
        {
          cacheTime: 10 * 60 * 1000, // Cache for 10 minutes
          maxRetries: 2,
        }
      )) || [];
    console.log(
      `✅ Fetched ${categories.length} categories with counts from API`
    );

    // Transform backend categories to frontend format with auction counts
    const transformedCategories = categories.map((cat) => ({
      name: cat.name,
      slug: cat.slug,
      image:
        cat.image ||
        `https://picsum.photos/300/200?random=${Math.floor(
          Math.random() * 1000
        )}`,
      description: cat.description || "",
      auction_count: cat.auction_count || 0,
      popular_auction: cat.popular_item
        ? {
            title: cat.popular_item,
            bid_count: cat.popular_item_bids || 0,
          }
        : null,
    }));

    return transformedCategories;
  } catch (error) {
    console.error("❌ Error fetching categories from API:", error);

    // Fallback to default categories with mock auction counts
    console.log("🔄 Using fallback categories...");
    const fallbackCategories = [
      {
        name: "Electronics",
        image:
          "https://img.freepik.com/premium-photo/8k-realistic-smartphone-accessories-white-canvas_893571-33631.jpg",
        slug: "electronics",
        description: "Smartphones, laptops, gadgets, and electronic devices",
        auction_count: 8,
        popular_auction: { title: "iPhone 14 Pro Max" },
      },
      {
        name: "Fashion",
        image:
          "https://images.unsplash.com/photo-1445205170230-053b83016050?w=500",
        slug: "fashion",
        description: "Clothing, shoes, accessories, and fashion items",
        auction_count: 5,
        popular_auction: { title: "Designer Handbag" },
      },
      {
        name: "Art",
        image:
          "https://th.bing.com/th/id/OIP.XXwcURFvVIUMwbkxTazACQHaEz?rs=1&pid=ImgDetMain",
        slug: "art",
        description:
          "Paintings, sculptures, artwork, and collectible art pieces",
        auction_count: 4,
        popular_auction: { title: "Vintage Painting" },
      },
      {
        name: "Collectibles",
        image:
          "https://img.freepik.com/psd-gratuit/vase-porcelaine-antique-fleurs-peintes-isolees-fond-transparent_191095-23323.jpg",
        slug: "collectibles",
        description:
          "Antiques, vintage items, rare collectibles, and memorabilia",
        auction_count: 6,
        popular_auction: { title: "Antique Vase" },
      },
      {
        name: "Jewelry",
        image:
          "https://i.pinimg.com/originals/1e/14/c5/1e14c5229b10f256d44aea92e47f57e5.jpg",
        slug: "jewelry",
        description: "Rings, necklaces, watches, and precious jewelry",
        auction_count: 3,
        popular_auction: { title: "Diamond Ring" },
      },
      {
        name: "Home & Garden",
        image:
          "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500",
        slug: "home_garden",
        description: "Furniture, home decor, garden tools, and household items",
        auction_count: 7,
        popular_auction: { title: "Garden Furniture Set" },
      },
    ];

    console.log(`✅ Using ${fallbackCategories.length} fallback categories`);
    return fallbackCategories;
  }
}

export async function fetchTrendingAuctions() {
  try {
    const data = await globalApiManager.makeRequest(
      axiosInstance,
      "auctions/",
      {
        params: { ordering: "-views_count", page_size: 4 },
        cacheTime: 3 * 60 * 1000, // Cache for 3 minutes
        maxRetries: 2,
      }
    );
    return data.results || [];
  } catch (error) {
    console.error("Error fetching trending auctions:", error);
    try {
      // Use mock data when backend is not available
      const mockResponse = await fetch("/mock-data.json");
      const mockData = await mockResponse.json();
      return mockData.trending_auctions || [];
    } catch (mockError) {
      console.error("Error loading mock data:", mockError);
      return [];
    }
  }
}

export async function searchAuctions(query) {
  try {
    const data = await globalApiManager.makeRequest(
      axiosInstance,
      "auctions/",
      {
        params: { search: query },
        cacheTime: 2 * 60 * 1000, // Cache for 2 minutes
        maxRetries: 2,
      }
    );
    return data.results || [];
  } catch (error) {
    console.error("Error searching auctions:", error);
    return [];
  }
}
