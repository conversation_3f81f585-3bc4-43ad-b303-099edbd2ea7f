from django.urls import re_path

from . import consumers

websocket_urlpatterns = [
    re_path(r"^ws/auction/(?P<auction_id>\d+)/$", consumers.BidConsumer.as_asgi()),
    re_path(r"^ws/chat/(?P<room_id>\d+)/$", consumers.ChatConsumer.as_asgi()),
    # Fallback route for invalid chat room IDs
    re_path(
        r"^ws/chat/(?P<room_id>undefined|null)/$",
        consumers.InvalidChatConsumer.as_asgi(),
    ),
    # Auctions list updates
    re_path(r"^ws/auctions/$", consumers.AuctionsListConsumer.as_asgi()),

    # Extended WebSocket endpoints
    re_path(r"^ws/auction_updates/$", consumers.ChatConsumer.as_asgi()),  # For trending/ending soon
    re_path(r"^ws/auction_(?P<auction_id>\d+)_analytics/$", consumers.ChatConsumer.as_asgi()),  # Auction analytics
    re_path(r"^ws/admin_dashboard/$", consumers.ChatConsumer.as_asgi()),  # Admin dashboard updates
    re_path(r"^ws/user_(?P<user_id>\d+)_stats/$", consumers.ChatConsumer.as_asgi()),  # User stats updates
]
