import React from "react";
import ReactDOM from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import App from "./App";
import "bootstrap/dist/css/bootstrap.min.css";

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>
);

// Register service worker for PWA functionality (only in production)
if ("serviceWorker" in navigator && process.env.NODE_ENV === "production") {
  window.addEventListener("load", () => {
    navigator.serviceWorker
      .register("/sw.js")
      .then((registration) => {
        console.log("SW registered: ", registration);

        // Check for updates
        registration.addEventListener("updatefound", () => {
          const newWorker = registration.installing;
          newWorker.addEventListener("statechange", () => {
            if (
              newWorker.state === "installed" &&
              navigator.serviceWorker.controller
            ) {
              // New content is available, show update notification
              console.log("New version available! Please refresh to update.");
              // Don't auto-reload in development
              // if (window.confirm("New version available! Reload to update?")) {
              //   window.location.reload();
              // }
            }
          });
        });
      })
      .catch((registrationError) => {
        console.log("SW registration failed: ", registrationError);
      });
  });
} else if (process.env.NODE_ENV === "development") {
  console.log("Service Worker disabled in development mode");
}

// Request notification permission only if not already decided
if ("Notification" in window && "serviceWorker" in navigator) {
  // Only request permission if it hasn't been decided yet
  if (Notification.permission === "default") {
    Notification.requestPermission().then((permission) => {
      if (permission === "granted") {
        console.log("Notification permission granted");
      } else {
        console.log("Notification permission denied");
      }
    });
  } else if (Notification.permission === "denied") {
    console.log(
      "Notification permission was previously denied. User can enable it in browser settings."
    );
  } else if (Notification.permission === "granted") {
    console.log("Notification permission already granted");
  }
}
