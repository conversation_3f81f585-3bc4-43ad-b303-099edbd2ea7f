import React, { useState, useEffect } from "react";
import {
  Con<PERSON><PERSON>,
  Row,
  Col,
  Card,
  Table,
  Button,
  Form,
  Alert,
  Badge,
  Spinner,
} from "react-bootstrap";
import { FaTrash, FaImage, FaSearch } from "react-icons/fa";
import axiosInstance from "../api/axiosInstance";
import AuctionImageUpdater from "../components/AuctionImageUpdater";
import { useAuth } from "../context/AuthContext";
import { formatAuctionPrice } from "../utils/currency";

const AdminAuctionManager = () => {
  const { user } = useAuth();
  const [auctions, setAuctions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedAuction, setSelectedAuction] = useState(null);
  const [showImageUpdater, setShowImageUpdater] = useState(false);

  useEffect(() => {
    if (user?.is_staff) {
      fetchAuctions();
    }
  }, [user]);

  const fetchAuctions = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get("auctions/?page_size=100");
      setAuctions(response.data.results || []);
    } catch (err) {
      setError("Failed to fetch auctions");
      console.error("Error fetching auctions:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpdate = (updatedAuction) => {
    setAuctions((prev) =>
      prev.map((auction) =>
        auction.id === updatedAuction.id ? updatedAuction : auction
      )
    );
  };

  const handleDeleteAuction = async (auctionId) => {
    if (
      !window.confirm(
        "Are you sure you want to delete this auction? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      setLoading(true);
      await axiosInstance.delete(`auctions/${auctionId}/`);

      // Update local state immediately
      setAuctions((prev) => prev.filter((auction) => auction.id !== auctionId));

      // Refresh the entire list to ensure sync
      await fetchAuctions();

      // Show success message
      alert("Auction deleted successfully!");
    } catch (err) {
      setError("Failed to delete auction");
      console.error("Error deleting auction:", err);
      alert("Failed to delete auction. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const filteredAuctions = auctions.filter(
    (auction) =>
      auction.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      auction.seller_username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (auction) => {
    const now = new Date();
    const endTime = new Date(auction.end_time);

    if (endTime < now) {
      return <Badge bg="secondary">Ended</Badge>;
    } else {
      return <Badge bg="success">Active</Badge>;
    }
  };

  if (!user?.is_staff) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">
          Access denied. This page is only available to administrators.
        </Alert>
      </Container>
    );
  }

  return (
    <Container className="mt-4">
      <Row>
        <Col>
          <h2 className="mb-4">🛠️ Admin Auction Manager</h2>
          {error && <Alert variant="danger">{error}</Alert>}
        </Col>
      </Row>

      <Row className="mb-4">
        <Col md={6}>
          <Form.Group>
            <Form.Label>Search Auctions</Form.Label>
            <div className="position-relative">
              <Form.Control
                type="text"
                placeholder="Search by title or seller..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <FaSearch className="position-absolute top-50 end-0 translate-middle-y me-3 text-muted" />
            </div>
          </Form.Group>
        </Col>
        <Col md={6} className="d-flex align-items-end">
          <Button variant="outline-primary" onClick={fetchAuctions}>
            🔄 Refresh
          </Button>
        </Col>
      </Row>

      <Card>
        <Card.Header>
          <h5 className="mb-0">
            Auction Management ({filteredAuctions.length} auctions)
          </h5>
        </Card.Header>
        <Card.Body>
          {loading ? (
            <div className="text-center py-4">
              <Spinner animation="border" />
              <p className="mt-2">Loading auctions...</p>
            </div>
          ) : (
            <Table responsive striped hover>
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Image</th>
                  <th>Title</th>
                  <th>Seller</th>
                  <th>Current Bid</th>
                  <th>Status</th>
                  <th>End Time</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredAuctions.map((auction) => (
                  <tr key={auction.id}>
                    <td>{auction.id}</td>
                    <td>
                      {auction.image ? (
                        <img
                          src={auction.image}
                          alt={auction.title}
                          style={{
                            width: "50px",
                            height: "50px",
                            objectFit: "contain",
                            borderRadius: "4px",
                          }}
                          onError={(e) => {
                            e.target.style.display = "none";
                          }}
                        />
                      ) : (
                        <div
                          style={{
                            width: "50px",
                            height: "50px",
                            backgroundColor: "#f8f9fa",
                            border: "1px solid #dee2e6",
                            borderRadius: "4px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <FaImage className="text-muted" />
                        </div>
                      )}
                    </td>
                    <td>
                      <strong>{auction.title}</strong>
                      <br />
                      <small className="text-muted">
                        {auction.description?.substring(0, 50)}...
                      </small>
                    </td>
                    <td>{auction.seller_username}</td>
                    <td>{formatAuctionPrice(auction.current_bid)}</td>
                    <td>{getStatusBadge(auction)}</td>
                    <td>
                      {new Date(auction.end_time).toLocaleDateString()}{" "}
                      {new Date(auction.end_time).toLocaleTimeString()}
                    </td>
                    <td>
                      <div className="d-flex gap-1">
                        <Button
                          size="sm"
                          variant="outline-primary"
                          onClick={() => {
                            setSelectedAuction(auction);
                            setShowImageUpdater(true);
                          }}
                          title="Update Image"
                        >
                          <FaImage />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline-danger"
                          onClick={() => handleDeleteAuction(auction.id)}
                          title="Delete Auction"
                        >
                          <FaTrash />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}

          {!loading && filteredAuctions.length === 0 && (
            <div className="text-center py-4">
              <p className="text-muted">No auctions found.</p>
            </div>
          )}
        </Card.Body>
      </Card>

      <AuctionImageUpdater
        show={showImageUpdater}
        onHide={() => setShowImageUpdater(false)}
        auction={selectedAuction}
        onUpdate={handleImageUpdate}
      />
    </Container>
  );
};

export default AdminAuctionManager;
