# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DB_NAME=online_auction_db
DB_USER=auction_user
DB_PASSWORD=auction_password_2024
DB_HOST=localhost
DB_PORT=5432

# Email Configuration
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
ADMIN_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
SITE_URL=http://localhost:3000
DEFAULT_FROM_EMAIL=AuctionStore <<EMAIL>>

# Redis Configuration
REDIS_URL=redis://127.0.0.1:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://127.0.0.1:6379/0
CELERY_RESULT_BACKEND=redis://127.0.0.1:6379/0

# Stripe Payment Gateway Configuration (Primary)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here
STRIPE_CURRENCY=usd

# Currency Settings
DEFAULT_CURRENCY=USD
CURRENCY_SYMBOL=$

# Payment URLs
PAYMENT_SUCCESS_URL=http://localhost:3001/payment/success/
PAYMENT_FAILURE_URL=http://localhost:3001/payment/failure/

# Security Settings (for production)
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
