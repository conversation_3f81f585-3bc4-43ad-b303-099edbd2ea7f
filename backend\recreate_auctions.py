#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to recreate all auctions with sequential IDs starting from 1
After deleting <PERSON><PERSON>'s auctions (she's bidder only)
"""

import os
import sys
import django
from datetime import timed<PERSON>ta
import random

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from auction.models import Auction, Category
from django.utils import timezone

def main():
    print("🔄 RECREATING ALL AUCTIONS WITH SEQUENTIAL IDs")
    print("=" * 50)
    
    # Get users
    amina = User.objects.get(username='Amina_admin')
    azam = User.objects.get(username='Azam_k')
    arshitha = User.objects.get(username='Arshitha_T')
    
    # All auction data in the order they should be created
    all_auctions = [
        # Amina_admin's auctions (IDs 1-30)
        {
            "title": "iPhone 14 Pro Max 256GB - Space Black",
            "description": "Brand new iPhone 14 Pro Max in Space Black. 256GB storage, unlocked, comes with original box and accessories. Never used, still in plastic wrap.",
            "starting_bid": 899.99,
            "category": "Electronics",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500",
            "days_to_end": 7,
            "owner": amina
        },
        {
            "title": "MacBook Air M2 13-inch - Silver",
            "description": "2022 MacBook Air with M2 chip, 8GB RAM, 256GB SSD. Excellent condition, barely used. Includes original charger and box.",
            "starting_bid": 999.99,
            "category": "Electronics", 
            "condition": "Like New",
            "image": "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500",
            "days_to_end": 5,
            "owner": amina
        },
        {
            "title": "Sony WH-1000XM4 Wireless Headphones",
            "description": "Premium noise-canceling headphones in black. Excellent sound quality, 30-hour battery life. Includes carrying case and cables.",
            "starting_bid": 199.99,
            "category": "Electronics",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=500",
            "days_to_end": 3,
            "owner": amina
        },
        {
            "title": "Samsung 65\" 4K Smart TV - QLED",
            "description": "Samsung 65-inch QLED 4K Smart TV with HDR. Crystal clear picture quality, smart features, and sleek design. Perfect for home entertainment.",
            "starting_bid": 799.99,
            "category": "Electronics",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=500",
            "days_to_end": 6,
            "owner": amina
        },
        {
            "title": "Nintendo Switch OLED Console",
            "description": "Nintendo Switch OLED model with vibrant 7-inch screen. Includes Joy-Con controllers, dock, and original accessories. Great for gaming on the go.",
            "starting_bid": 299.99,
            "category": "Electronics",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=500",
            "days_to_end": 4,
            "owner": amina
        },
        {
            "title": "Vintage Leather Jacket - Brown",
            "description": "Authentic vintage brown leather jacket from the 1980s. Genuine leather, excellent craftsmanship, perfect for collectors or fashion enthusiasts.",
            "starting_bid": 149.99,
            "category": "Fashion & Clothing",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1551028719-00167b16eac5?w=500",
            "days_to_end": 5,
            "owner": amina
        },
        {
            "title": "Designer Handbag - Luxury Style",
            "description": "Elegant designer handbag in classic pattern. High-quality materials, spacious interior, perfect for everyday use or special occasions.",
            "starting_bid": 299.99,
            "category": "Fashion & Clothing",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=500",
            "days_to_end": 6,
            "owner": amina
        },
        {
            "title": "Men's Formal Suit - Navy Blue",
            "description": "Tailored navy blue suit, size 42R. Wool blend fabric, modern fit, includes jacket and trousers. Perfect for business or formal events.",
            "starting_bid": 199.99,
            "category": "Fashion & Clothing",
            "condition": "Like New",
            "image": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500",
            "days_to_end": 4,
            "owner": amina
        },
        {
            "title": "Women's Designer Dress - Black Evening Gown",
            "description": "Elegant black evening gown, size 8. Perfect for formal events, weddings, or special occasions. Beautiful silhouette and high-quality fabric.",
            "starting_bid": 179.99,
            "category": "Fashion & Clothing",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1566479179817-c0b5b4b4b1e5?w=500",
            "days_to_end": 7,
            "owner": amina
        },
        {
            "title": "Sneaker Collection - Limited Edition",
            "description": "Rare limited edition sneakers, size 10. Collector's item, never worn, comes with original box and authentication certificate.",
            "starting_bid": 249.99,
            "category": "Fashion & Clothing",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=500",
            "days_to_end": 3,
            "owner": amina
        },
        # Continue with more Amina's auctions (11-30)
        {
            "title": "Antique Wooden Dining Table",
            "description": "Beautiful antique oak dining table from the 1920s. Seats 6 people comfortably. Restored to excellent condition, perfect centerpiece for any dining room.",
            "starting_bid": 599.99,
            "category": "Home & Garden",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500",
            "days_to_end": 8,
            "owner": amina
        },
        {
            "title": "Modern Coffee Machine - Espresso Maker",
            "description": "Professional-grade espresso machine with built-in grinder. Perfect for coffee enthusiasts. Includes all accessories and instruction manual.",
            "starting_bid": 399.99,
            "category": "Home & Garden",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=500",
            "days_to_end": 5,
            "owner": amina
        },
        {
            "title": "Garden Tool Set - Professional Grade",
            "description": "Complete set of professional gardening tools. Includes spades, pruners, watering can, and storage bag. Perfect for serious gardeners.",
            "starting_bid": 89.99,
            "category": "Home & Garden",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=500",
            "days_to_end": 4,
            "owner": amina
        },
        {
            "title": "Luxury Bedding Set - Egyptian Cotton",
            "description": "Premium Egyptian cotton bedding set. King size, 1000 thread count. Includes sheets, pillowcases, and duvet cover. Hotel-quality comfort.",
            "starting_bid": 199.99,
            "category": "Home & Garden",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=500",
            "days_to_end": 6,
            "owner": amina
        },
        {
            "title": "Vintage Persian Rug - Handwoven",
            "description": "Authentic handwoven Persian rug, 8x10 feet. Beautiful intricate patterns, excellent condition. Perfect for living room or dining area.",
            "starting_bid": 799.99,
            "category": "Home & Garden",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1506439773649-6e0eb8cfb237?w=500",
            "days_to_end": 9,
            "owner": amina
        },
        # Continue with Sports & Recreation (16-20)
        {
            "title": "Professional Tennis Racket - Wilson Pro",
            "description": "Professional-grade tennis racket used by tournament players. Excellent balance and power. Includes protective case and extra grip tape.",
            "starting_bid": 189.99,
            "category": "Sports & Recreation",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=500",
            "days_to_end": 5,
            "owner": amina
        },
        {
            "title": "Mountain Bike - Full Suspension",
            "description": "High-performance mountain bike with full suspension. 21-speed gear system, disc brakes, perfect for trail riding and outdoor adventures.",
            "starting_bid": 899.99,
            "category": "Sports & Recreation",
            "condition": "Like New",
            "image": "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=500",
            "days_to_end": 8,
            "owner": amina
        },
        {
            "title": "Golf Club Set - Complete Beginner Set",
            "description": "Complete golf club set perfect for beginners. Includes driver, irons, putter, and golf bag. Everything needed to start playing golf.",
            "starting_bid": 299.99,
            "category": "Sports & Recreation",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1535131749006-b7f58c99034b?w=500",
            "days_to_end": 6,
            "owner": amina
        },
        {
            "title": "Yoga Mat Set - Premium Quality",
            "description": "Premium yoga mat set with blocks, strap, and carrying bag. Non-slip surface, eco-friendly materials. Perfect for home or studio practice.",
            "starting_bid": 79.99,
            "category": "Sports & Recreation",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1506629905607-d9c8e5e8b3c8?w=500",
            "days_to_end": 4,
            "owner": amina
        },
        {
            "title": "Fishing Rod and Reel Combo",
            "description": "Professional fishing rod and reel combo. Suitable for both freshwater and saltwater fishing. Includes tackle box with assorted lures.",
            "starting_bid": 149.99,
            "category": "Sports & Recreation",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1445112098124-3e76dd67983c?w=500",
            "days_to_end": 7,
            "owner": amina
        },
        # Art & Collectibles (21-25)
        {
            "title": "Original Oil Painting - Landscape",
            "description": "Beautiful original oil painting of mountain landscape. Signed by artist, framed and ready to hang. Perfect for art collectors or home decoration.",
            "starting_bid": 399.99,
            "category": "Art & Collectibles",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500",
            "days_to_end": 10,
            "owner": amina
        },
        {
            "title": "Vintage Vinyl Record Collection",
            "description": "Collection of 50 vintage vinyl records from the 1960s-1980s. Includes classic rock, jazz, and blues albums. Great condition, some rare finds.",
            "starting_bid": 299.99,
            "category": "Art & Collectibles",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=500",
            "days_to_end": 9,
            "owner": amina
        },
        {
            "title": "Antique Pocket Watch - Gold Plated",
            "description": "Beautiful antique pocket watch from the early 1900s. Gold-plated case, mechanical movement, still keeping accurate time. Collector's item.",
            "starting_bid": 249.99,
            "category": "Art & Collectibles",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1509048191080-d2e2678e3449?w=500",
            "days_to_end": 8,
            "owner": amina
        },
        {
            "title": "Ceramic Vase Set - Handmade",
            "description": "Set of 3 handmade ceramic vases in different sizes. Beautiful glazed finish, perfect for flowers or as decorative pieces. Artist signed.",
            "starting_bid": 129.99,
            "category": "Art & Collectibles",
            "condition": "New",
            "image": "https://images.unsplash.com/photo-1578749556568-bc2c40e68b61?w=500",
            "days_to_end": 6,
            "owner": amina
        },
        {
            "title": "Comic Book Collection - Marvel Classics",
            "description": "Collection of 25 classic Marvel comic books from the 1980s-1990s. Includes Spider-Man, X-Men, and Avengers. Well-preserved condition.",
            "starting_bid": 199.99,
            "category": "Art & Collectibles",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=500",
            "days_to_end": 5,
            "owner": amina
        },
        # Final Amina auctions (26-30)
        {
            "title": "Rare Book Collection - First Editions",
            "description": "Collection of 10 first edition books including classics and modern literature. Excellent condition, some signed copies. Perfect for book collectors.",
            "starting_bid": 499.99,
            "category": "Books & Media",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500",
            "days_to_end": 12,
            "owner": amina
        },
        {
            "title": "Professional Camera Equipment",
            "description": "Canon DSLR camera with multiple lenses and accessories. Perfect for photography enthusiasts or professionals. Includes camera bag and tripod.",
            "starting_bid": 799.99,
            "category": "Electronics",
            "condition": "Like New",
            "image": "https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=500",
            "days_to_end": 7,
            "owner": amina
        },
        {
            "title": "Cookbook Collection - International Cuisine",
            "description": "Collection of 20 cookbooks featuring international cuisine. Includes recipes from Italy, France, Asia, and more. Perfect for culinary enthusiasts.",
            "starting_bid": 89.99,
            "category": "Books & Media",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=500",
            "days_to_end": 4,
            "owner": amina
        },
        {
            "title": "Board Game Collection - Strategy Games",
            "description": "Collection of 15 strategy board games including Settlers of Catan, Risk, and Monopoly. Perfect for family game nights and gatherings.",
            "starting_bid": 149.99,
            "category": "Toys & Games",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?w=500",
            "days_to_end": 5,
            "owner": amina
        },
        {
            "title": "Jewelry Box - Vintage Wooden",
            "description": "Beautiful vintage wooden jewelry box with multiple compartments and mirror. Perfect for storing and organizing jewelry collection. Excellent craftsmanship.",
            "starting_bid": 79.99,
            "category": "Jewelry & Watches",
            "condition": "Very Good",
            "image": "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=500",
            "days_to_end": 6,
            "owner": amina
        }
    ]

    # Import remaining auctions
    from remaining_auctions_data import get_remaining_auctions
    remaining_auctions = get_remaining_auctions(azam, arshitha)
    all_auctions.extend(remaining_auctions)
    
    # Create auctions
    created_count = 0
    print("✨ Creating auctions with sequential IDs...")
    
    for auction_info in all_auctions:
        try:
            category = Category.objects.filter(name=auction_info["category"]).first()
            if not category:
                print(f"❌ Category '{auction_info['category']}' not found")
                continue
                
            end_time = timezone.now() + timedelta(days=auction_info["days_to_end"])
            
            auction = Auction.objects.create(
                title=auction_info["title"],
                description=auction_info["description"],
                starting_bid=auction_info["starting_bid"],
                current_bid=auction_info["starting_bid"],
                end_time=end_time,
                category=category,
                condition=auction_info["condition"],
                image=auction_info["image"],
                owner=auction_info["owner"],
                approved=True,
                featured=random.choice([True, False])
            )
            
            created_count += 1
            print(f"   ✅ ID:{auction.id} - {auction.title[:40]}... (Owner: {auction.owner.username})")
            
        except Exception as e:
            print(f"❌ Error creating auction '{auction_info['title']}': {e}")
    
    print(f"\n🎯 CREATED {created_count} AUCTIONS")
    print(f"📊 Total auctions in system: {Auction.objects.count()}")

if __name__ == "__main__":
    main()
