#!/usr/bin/env python3
"""
Test script to verify registration and chat functionality fixes
"""

import requests
import json

def test_registration_fix():
    """Test the registration with username containing spaces"""
    
    base_url = "http://127.0.0.1:8000/api"
    
    print("🔍 Testing Registration Fix")
    print("=" * 50)
    
    # Test with username containing spaces (should be auto-fixed)
    test_data = {
        "username": "Test User 123",  # Contains spaces
        "email": "<EMAIL>",
        "password": "testpass123",
        "user_role": "both"
    }
    
    print(f"📤 Request Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(
            f"{base_url}/register/",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"📊 Status Code: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"📥 Response: {json.dumps(response_data, indent=2)}")
            
            if response.status_code == 201:
                print("✅ Registration successful!")
                return response_data.get('username')
            else:
                print("❌ Registration failed")
                return None
                
        except:
            print(f"📥 Response Text: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def test_chat_room_creation():
    """Test chat room creation for an auction"""
    
    base_url = "http://127.0.0.1:8000/api"
    
    print("\n🔍 Testing Chat Room Creation")
    print("=" * 50)
    
    # First, get an auction ID
    try:
        auctions_response = requests.get(f"{base_url}/auctions/")
        if auctions_response.status_code == 200:
            auctions_data = auctions_response.json()
            if auctions_data.get('results') and len(auctions_data['results']) > 0:
                auction_id = auctions_data['results'][0]['id']
                print(f"📋 Using auction ID: {auction_id}")
                
                # Test chat room creation
                chat_data = {
                    "auction": auction_id,
                    "is_active": True
                }
                
                print(f"📤 Chat Room Data: {json.dumps(chat_data, indent=2)}")
                
                chat_response = requests.post(
                    f"{base_url}/chat-rooms/",
                    json=chat_data,
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"📊 Chat Room Status Code: {chat_response.status_code}")
                
                try:
                    chat_response_data = chat_response.json()
                    print(f"📥 Chat Room Response: {json.dumps(chat_response_data, indent=2)}")
                    
                    if chat_response.status_code in [200, 201]:
                        print("✅ Chat room creation/retrieval successful!")
                        return True
                    else:
                        print("❌ Chat room creation failed")
                        return False
                        
                except:
                    print(f"📥 Chat Room Response Text: {chat_response.text}")
                    return False
            else:
                print("❌ No auctions found to test with")
                return False
        else:
            print(f"❌ Failed to get auctions: {auctions_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_endpoints_availability():
    """Test if endpoints are available"""
    print("\n🌐 Testing Endpoint Availability")
    print("=" * 50)
    
    endpoints = [
        "http://127.0.0.1:8000/api/",
        "http://127.0.0.1:8000/api/auctions/",
        "http://127.0.0.1:8000/api/chat-rooms/",
        "http://127.0.0.1:8000/api/register/"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=5)
            print(f"✅ {endpoint} - Status: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Testing Registration and Chat Fixes")
    print("=" * 60)
    
    # Test endpoint availability
    test_endpoints_availability()
    
    # Test registration fix
    username = test_registration_fix()
    
    # Test chat room creation
    chat_success = test_chat_room_creation()
    
    print("\n📊 Test Summary")
    print("=" * 30)
    print(f"Registration: {'✅ PASS' if username else '❌ FAIL'}")
    print(f"Chat Room: {'✅ PASS' if chat_success else '❌ FAIL'}")
    
    if username:
        print(f"✅ Username was cleaned to: {username}")
    
    print("\n🎯 Next Steps:")
    print("1. Try registering with 'Arshitha T' - should become 'Arshitha_T'")
    print("2. Test chat functionality on auction detail pages")
    print("3. Check that axios requests go to correct backend URL")
