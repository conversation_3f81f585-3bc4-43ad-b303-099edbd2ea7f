#!/usr/bin/env python3
"""
Comprehensive Email SMTP Testing Script
Tests all email functionality in the auction system
"""

import os
import sys
import django
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from django.core.mail import send_mail, EmailMessage
from django.conf import settings
from django.template.loader import render_to_string
from django.contrib.auth.models import User
from auction.models import Auction, Bid, Category
import smtplib
import socket

class EmailSMTPTester:
    def __init__(self):
        self.test_results = []
        self.test_email = "<EMAIL>"  # User's preferred test email
        
    def check_email_configuration(self):
        """Check email configuration settings"""
        print("📧 CHECKING EMAIL CONFIGURATION")
        print("="*40)
        
        config_items = {
            'EMAIL_BACKEND': settings.EMAIL_BACKEND,
            'EMAIL_HOST': settings.EMAIL_HOST,
            'EMAIL_PORT': settings.EMAIL_PORT,
            'EMAIL_USE_TLS': settings.EMAIL_USE_TLS,
            'EMAIL_HOST_USER': settings.EMAIL_HOST_USER,
            'EMAIL_HOST_PASSWORD': '***' if settings.EMAIL_HOST_PASSWORD else 'NOT SET',
            'DEFAULT_FROM_EMAIL': settings.DEFAULT_FROM_EMAIL,
        }
        
        print("📋 Current Configuration:")
        for key, value in config_items.items():
            status = "✅" if value else "❌"
            print(f"   {status} {key}: {value}")
            
        # Check if all required settings are present
        required_settings = ['EMAIL_HOST_USER', 'EMAIL_HOST_PASSWORD']
        missing_settings = [setting for setting in required_settings 
                          if not getattr(settings, setting, None)]
        
        if missing_settings:
            print(f"\n❌ Missing required settings: {', '.join(missing_settings)}")
            return False
        else:
            print(f"\n✅ All required email settings are configured")
            return True
    
    def test_smtp_connection(self):
        """Test SMTP server connection"""
        print(f"\n🔗 TESTING SMTP CONNECTION")
        print("-" * 30)
        
        try:
            # Test basic socket connection
            print(f"📡 Testing socket connection to {settings.EMAIL_HOST}:{settings.EMAIL_PORT}")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((settings.EMAIL_HOST, settings.EMAIL_PORT))
            sock.close()
            
            if result == 0:
                print(f"   ✅ Socket connection successful")
            else:
                print(f"   ❌ Socket connection failed: {result}")
                return False
            
            # Test SMTP connection with authentication
            print(f"🔐 Testing SMTP authentication...")
            server = smtplib.SMTP(settings.EMAIL_HOST, settings.EMAIL_PORT)
            server.starttls()
            server.login(settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD)
            server.quit()
            
            print(f"   ✅ SMTP authentication successful")
            self.test_results.append({
                'test': 'SMTP Connection',
                'status': 'SUCCESS',
                'details': f'Connected to {settings.EMAIL_HOST}:{settings.EMAIL_PORT}'
            })
            return True
            
        except smtplib.SMTPAuthenticationError as e:
            print(f"   ❌ SMTP authentication failed: {e}")
            self.test_results.append({
                'test': 'SMTP Connection',
                'status': 'FAILED',
                'error': f'Authentication error: {e}'
            })
            return False
        except Exception as e:
            print(f"   ❌ SMTP connection failed: {e}")
            self.test_results.append({
                'test': 'SMTP Connection',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def test_basic_email_sending(self):
        """Test basic email sending functionality"""
        print(f"\n📤 TESTING BASIC EMAIL SENDING")
        print("-" * 35)
        
        try:
            subject = f"Test Email from Auction System - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            message = f"""
Hello!

This is a test email from the Online Auction System to verify SMTP functionality.

Test Details:
- Sent at: {datetime.now()}
- From: {settings.DEFAULT_FROM_EMAIL}
- To: {self.test_email}
- SMTP Host: {settings.EMAIL_HOST}

If you receive this email, the SMTP configuration is working correctly!

Best regards,
Online Auction System
            """
            
            print(f"📧 Sending test email to: {self.test_email}")
            
            result = send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[self.test_email],
                fail_silently=False
            )
            
            if result == 1:
                print(f"   ✅ Basic email sent successfully")
                self.test_results.append({
                    'test': 'Basic Email',
                    'status': 'SUCCESS',
                    'details': f'Email sent to {self.test_email}'
                })
                return True
            else:
                print(f"   ❌ Email sending failed (result: {result})")
                return False
                
        except Exception as e:
            print(f"   ❌ Email sending failed: {e}")
            self.test_results.append({
                'test': 'Basic Email',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def test_html_email_sending(self):
        """Test HTML email sending"""
        print(f"\n🎨 TESTING HTML EMAIL SENDING")
        print("-" * 32)
        
        try:
            subject = f"HTML Test Email - Auction System"
            
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
                    .content {{ padding: 20px; background-color: #f8f9fa; }}
                    .footer {{ background-color: #6c757d; color: white; padding: 10px; text-align: center; }}
                    .success {{ color: #28a745; font-weight: bold; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>🏆 Online Auction System</h1>
                    <p>SMTP Test Email</p>
                </div>
                <div class="content">
                    <h2>HTML Email Test</h2>
                    <p>This is a <strong>HTML formatted email</strong> to test the email system.</p>
                    
                    <h3>Test Information:</h3>
                    <ul>
                        <li><strong>Sent at:</strong> {datetime.now()}</li>
                        <li><strong>From:</strong> {settings.DEFAULT_FROM_EMAIL}</li>
                        <li><strong>To:</strong> {self.test_email}</li>
                        <li><strong>SMTP Host:</strong> {settings.EMAIL_HOST}</li>
                    </ul>
                    
                    <p class="success">✅ If you can see this formatted email, HTML emails are working!</p>
                </div>
                <div class="footer">
                    <p>© 2025 Online Auction System - Email Test</p>
                </div>
            </body>
            </html>
            """
            
            print(f"🎨 Sending HTML email to: {self.test_email}")
            
            email = EmailMessage(
                subject=subject,
                body=html_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[self.test_email]
            )
            email.content_subtype = 'html'
            result = email.send()
            
            if result == 1:
                print(f"   ✅ HTML email sent successfully")
                self.test_results.append({
                    'test': 'HTML Email',
                    'status': 'SUCCESS',
                    'details': f'HTML email sent to {self.test_email}'
                })
                return True
            else:
                print(f"   ❌ HTML email sending failed")
                return False
                
        except Exception as e:
            print(f"   ❌ HTML email sending failed: {e}")
            self.test_results.append({
                'test': 'HTML Email',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def test_auction_notification_emails(self):
        """Test auction-specific notification emails"""
        print(f"\n🏷️ TESTING AUCTION NOTIFICATION EMAILS")
        print("-" * 42)
        
        try:
            # Create test data
            test_user, created = User.objects.get_or_create(
                username='email_test_user',
                defaults={
                    'email': self.test_email,
                    'first_name': 'Email',
                    'last_name': 'Tester'
                }
            )
            
            category, _ = Category.objects.get_or_create(
                name='Email Test Category',
                defaults={'description': 'Category for email testing'}
            )
            
            # Test auction ending reminder
            subject = "🔔 Auction Ending Soon - Test Notification"
            message = f"""
Dear {test_user.first_name},

This is a test auction ending reminder email.

Auction Details:
- Title: Test Auction for Email
- Current Bid: $299.99
- Ending: Soon
- Your Status: Highest Bidder

This email confirms that auction notification emails are working correctly.

Best regards,
Online Auction System Team
            """
            
            print(f"🔔 Sending auction notification to: {self.test_email}")
            
            result = send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[self.test_email],
                fail_silently=False
            )
            
            if result == 1:
                print(f"   ✅ Auction notification email sent successfully")
                self.test_results.append({
                    'test': 'Auction Notification',
                    'status': 'SUCCESS',
                    'details': f'Notification sent to {self.test_email}'
                })
                return True
            else:
                print(f"   ❌ Auction notification failed")
                return False
                
        except Exception as e:
            print(f"   ❌ Auction notification failed: {e}")
            self.test_results.append({
                'test': 'Auction Notification',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print(f"\n" + "="*60)
        print("📊 EMAIL SMTP TEST REPORT")
        print("="*60)
        
        print(f"\n📧 EMAIL CONFIGURATION:")
        print(f"   Host: {settings.EMAIL_HOST}:{settings.EMAIL_PORT}")
        print(f"   User: {settings.EMAIL_HOST_USER}")
        print(f"   From: {settings.DEFAULT_FROM_EMAIL}")
        print(f"   Test Email: {self.test_email}")
        
        print(f"\n🧪 TEST RESULTS:")
        success_count = len([r for r in self.test_results if r.get('status') == 'SUCCESS'])
        total_tests = len(self.test_results)
        
        print(f"   Total Tests: {total_tests}")
        print(f"   Successful: {success_count}")
        print(f"   Failed: {total_tests - success_count}")
        print(f"   Success Rate: {(success_count/total_tests*100):.1f}%" if total_tests > 0 else "   Success Rate: 0%")
        
        print(f"\n📋 DETAILED RESULTS:")
        for result in self.test_results:
            status_icon = "✅" if result.get('status') == 'SUCCESS' else "❌"
            print(f"   {status_icon} {result.get('test')}: {result.get('status')}")
            if result.get('details'):
                print(f"      Details: {result['details']}")
            if result.get('error'):
                print(f"      Error: {result['error']}")
        
        print(f"\n🎯 EMAIL SYSTEM STATUS:")
        if success_count >= total_tests * 0.8:  # 80% success rate
            print("   ✅ Email system is working well!")
            print("   📧 Ready to send notifications to users")
            print("   🚀 All email functionality operational")
        else:
            print("   ⚠️ Email system has some issues")
            print("   🔧 Requires attention before production")
            
        print(f"\n📬 CHECK YOUR EMAIL: {self.test_email}")
        print(f"💌 You should receive test emails if SMTP is working")

def main():
    """Main execution function"""
    print("🚀 STARTING COMPREHENSIVE EMAIL SMTP TEST")
    print("="*60)
    
    tester = EmailSMTPTester()
    
    try:
        # Run all tests
        config_ok = tester.check_email_configuration()
        if not config_ok:
            print("❌ Email configuration incomplete. Please check settings.")
            return
        
        smtp_ok = tester.test_smtp_connection()
        if smtp_ok:
            tester.test_basic_email_sending()
            tester.test_html_email_sending()
            tester.test_auction_notification_emails()
        
        tester.generate_test_report()
        
    except Exception as e:
        print(f"❌ Error during email testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
