import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { Container, <PERSON>, <PERSON>, <PERSON><PERSON>, Card } from "react-bootstrap";
import {
  FaGavel,
  FaShieldAlt,
  FaRocket,
  FaUsers,
  FaClock,
  FaChartLine,
  FaHeart,
  FaSearch,
} from "react-icons/fa";
import axiosInstance from "../api/axiosInstance";
import apiCallManager from "../utils/apiCallManager";
import "./LandingPage.css";

// Global singleton to prevent multiple API calls
class LandingDataManager {
  constructor() {
    this.data = null;
    this.loading = false;
    this.lastFetch = 0;
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
    this.subscribers = new Set();
    this.requestPromise = null;
    this.isDestroyed = false; // Flag to prevent calls after destruction
    this.maxRetries = 3;
    this.retryCount = 0;
    this.backoffDelay = 1000; // Start with 1 second
  }

  subscribe(callback) {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  notify() {
    this.subscribers.forEach((callback) => callback(this.data, this.loading));
  }

  async fetchData() {
    // Prevent calls if manager is destroyed
    if (this.isDestroyed) {
      console.log("🚫 LandingDataManager is destroyed - preventing API call");
      return this.data;
    }

    const now = Date.now();

    // Return cached data if still valid
    if (this.data && now - this.lastFetch < this.cacheExpiry) {
      console.log("📦 Using cached landing data (singleton)");
      this.retryCount = 0; // Reset retry count on successful cache hit
      return this.data;
    }

    // If already fetching, return the existing promise
    if (this.requestPromise) {
      console.log("🔄 Landing data request already in progress (singleton)");
      return this.requestPromise;
    }

    // Check retry limit
    if (this.retryCount >= this.maxRetries) {
      console.log("🚫 Max retries reached - using cached data or defaults");
      return this.data;
    }

    // Start new request
    this.loading = true;
    this.notify();

    this.requestPromise = this.performFetch();

    try {
      const result = await this.requestPromise;
      this.retryCount = 0; // Reset on success
      return result;
    } catch (error) {
      this.retryCount++;
      console.error(
        `❌ Fetch attempt ${this.retryCount}/${this.maxRetries} failed:`,
        error
      );
      throw error;
    } finally {
      this.requestPromise = null;
    }
  }

  async performFetch() {
    try {
      // Double-check if destroyed before making request
      if (this.isDestroyed) {
        console.log("🚫 Manager destroyed during fetch - aborting");
        return this.data;
      }

      console.log("🌐 Fetching fresh landing data (singleton)...");

      // Add timeout and abort controller for better control
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await axiosInstance.get("/landing/data/", {
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.data.success) {
        this.data = response.data.data;
        this.lastFetch = Date.now();
        localStorage.setItem("landingData", JSON.stringify(this.data));
        localStorage.setItem("landingData_time", this.lastFetch.toString());
        console.log(
          "✅ Landing data fetched and cached successfully (singleton)"
        );
      } else {
        console.warn("⚠️ API returned success=false:", response.data);
      }

      return this.data;
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("⏰ Landing data request timed out");
      } else {
        console.error(
          "❌ Failed to fetch landing page data (singleton):",
          error
        );
      }

      // Implement exponential backoff for retries
      if (this.retryCount < this.maxRetries) {
        const delay = this.backoffDelay * Math.pow(2, this.retryCount);
        console.log(`⏳ Retrying in ${delay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }

      throw error;
    } finally {
      this.loading = false;
      this.notify();
    }
  }

  // Initialize with cached data
  initializeFromCache() {
    if (this.isDestroyed) {
      console.log("🚫 Manager destroyed - skipping cache initialization");
      return false;
    }

    try {
      const cachedData = localStorage.getItem("landingData");
      const cachedTime = localStorage.getItem("landingData_time");

      if (cachedData && cachedTime) {
        const age = Date.now() - parseInt(cachedTime);
        if (age < this.cacheExpiry) {
          this.data = JSON.parse(cachedData);
          this.lastFetch = parseInt(cachedTime);
          console.log("📦 Initialized with cached landing data (singleton)");
          return true;
        } else {
          console.log("📦 Cached data expired, will fetch fresh data");
        }
      }
    } catch (error) {
      console.error("❌ Error loading cached data:", error);
    }
    return false;
  }

  // Method to destroy the manager and prevent further API calls
  destroy() {
    console.log("🗑️ Destroying LandingDataManager");
    this.isDestroyed = true;
    this.subscribers.clear();
    this.requestPromise = null;
    this.data = null;
  }

  // Method to reset the manager (for testing or recovery)
  reset() {
    console.log("🔄 Resetting LandingDataManager");
    this.isDestroyed = false;
    this.retryCount = 0;
    this.requestPromise = null;
    this.loading = false;
  }
}

// Global instance
const landingDataManager = new LandingDataManager();

const LandingPage = () => {
  const [currentFeature, setCurrentFeature] = useState(0);
  const [landingData, setLandingData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const navigate = useNavigate();
  const componentId = useRef(`landing-${Date.now()}-${Math.random()}`); // Unique component ID for debugging

  // Check for dark mode from body class
  useEffect(() => {
    const checkDarkMode = () => {
      const isDark = document.body.classList.contains("dark-mode-bg");
      setIsDarkMode(isDark);
    };

    // Initial check
    checkDarkMode();

    // Listen for class changes on body
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ["class"],
    });

    return () => observer.disconnect();
  }, []);

  const features = [
    {
      icon: <FaGavel className="feature-icon" />,
      title: "Live Auctions",
      description:
        "Participate in real-time bidding with instant updates and notifications",
    },
    {
      icon: <FaShieldAlt className="feature-icon" />,
      title: "Secure Payments",
      description:
        "Safe and secure transactions with Stripe payment integration",
    },
    {
      icon: <FaRocket className="feature-icon" />,
      title: "AI Price Prediction",
      description:
        "Get intelligent price predictions powered by advanced AI algorithms",
    },
    {
      icon: <FaUsers className="feature-icon" />,
      title: "Community Driven",
      description:
        "Join thousands of buyers and sellers in our trusted marketplace",
    },
    {
      icon: <FaClock className="feature-icon" />,
      title: "24/7 Availability",
      description: "Bid anytime, anywhere with our always-available platform",
    },
    {
      icon: <FaChartLine className="feature-icon" />,
      title: "Market Analytics",
      description:
        "Track trends and make informed decisions with detailed analytics",
    },
  ];

  // Default stats (will be replaced by API data)
  const defaultStats = [
    { number: "10,000+", label: "Active Users" },
    { number: "50,000+", label: "Auctions Completed" },
    { number: "99.9%", label: "Uptime" },
    { number: "$2M+", label: "Total Sales" },
  ];

  // Use API data if available, otherwise use defaults
  const stats = landingData?.stats
    ? [
        {
          number: `${landingData.stats.total_users.toLocaleString()}+`,
          label: "Active Users",
        },
        {
          number: `${landingData.stats.total_auctions.toLocaleString()}+`,
          label: "Auctions Completed",
        },
        { number: landingData.stats.uptime, label: "Uptime" },
        {
          number: `$${(landingData.stats.total_sales / 1000000).toFixed(1)}M+`,
          label: "Total Sales",
        },
      ]
    : defaultStats;

  // Initialize and subscribe to landing data manager
  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates after unmount

    console.log(`🔧 LandingPage component ${componentId.current} mounting`);

    // Initialize from cache first
    const cacheInitialized = landingDataManager.initializeFromCache();

    // Set initial state only if component is still mounted
    if (isMounted) {
      setLandingData(landingDataManager.data);
      setLoading(landingDataManager.loading);
    }

    // Subscribe to updates
    const unsubscribe = landingDataManager.subscribe((data, loading) => {
      if (isMounted) {
        console.log(`📡 LandingPage ${componentId.current} received update:`, {
          data: !!data,
          loading,
        });
        setLandingData(data);
        setLoading(loading);
      }
    });

    // Fetch fresh data if needed (only if cache wasn't sufficient)
    if (!cacheInitialized || !landingDataManager.data) {
      landingDataManager.fetchData().catch((error) => {
        if (isMounted) {
          console.error(
            `❌ LandingPage ${componentId.current} fetch error:`,
            error
          );
          // Set loading to false on error to prevent infinite loading state
          setLoading(false);
        }
      });
    }

    return () => {
      console.log(`🔧 LandingPage component ${componentId.current} unmounting`);
      isMounted = false; // Prevent further state updates
      unsubscribe();
    };
  }, []); // Empty dependency array - only run once on mount

  // Auto-rotate features
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []); // Remove features.length dependency since features array is constant

  const handleGetStarted = () => {
    navigate("/home");
  };

  const handleLearnMore = () => {
    // Scroll to features section
    document.getElementById("features-section")?.scrollIntoView({
      behavior: "smooth",
    });
  };

  return (
    <div className="landing-page">
      {/* Hero Section */}
      <section className="hero-section">
        <Container>
          <Row className="align-items-center min-vh-100">
            <Col lg={6} className="hero-content">
              <div className="logo-section mb-4">
                <div className="logo-container">
                  <FaGavel className="logo-icon" />
                  <h1 className="logo-text">AuctionStore</h1>
                </div>
                <p className="tagline">Where Every Bid Tells a Story</p>
              </div>

              <h2 className="hero-title">
                Discover, Bid, and Win
                <span className="highlight"> Amazing Items</span>
              </h2>

              <p className="hero-description">
                Join the world's most trusted online auction platform. From rare
                collectibles to everyday treasures, find exactly what you're
                looking for or discover something unexpected.
              </p>

              <div className="hero-buttons">
                <Button
                  size="lg"
                  className="cta-button primary"
                  onClick={handleGetStarted}
                >
                  <FaRocket className="me-2" />
                  Get Started
                </Button>
                <Button
                  size="lg"
                  variant="outline-light"
                  className="cta-button secondary"
                  onClick={handleLearnMore}
                >
                  Learn More
                </Button>
              </div>

              <div className="trust-indicators">
                <div className="trust-item">
                  <FaShieldAlt className="trust-icon" />
                  <span>Secure & Trusted</span>
                </div>
                <div className="trust-item">
                  <FaUsers className="trust-icon" />
                  <span>10,000+ Users</span>
                </div>
                <div className="trust-item">
                  <FaHeart className="trust-icon" />
                  <span>99% Satisfaction</span>
                </div>
              </div>
            </Col>

            <Col lg={6} className="hero-visual">
              <div className="feature-showcase">
                <div className="feature-card active">
                  {features[currentFeature].icon}
                  <h3>{features[currentFeature].title}</h3>
                  <p>{features[currentFeature].description}</p>
                </div>

                <div className="feature-dots">
                  {features.map((_, index) => (
                    <button
                      key={index}
                      className={`dot ${
                        index === currentFeature ? "active" : ""
                      }`}
                      onClick={() => setCurrentFeature(index)}
                    />
                  ))}
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Stats Section */}
      <section className="stats-section">
        <Container>
          <Row>
            {stats.map((stat, index) => (
              <Col md={3} key={index} className="text-center">
                <div className="stat-item">
                  <h3 className="stat-number">{stat.number}</h3>
                  <p className="stat-label">{stat.label}</p>
                </div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* Features Section */}
      <section id="features-section" className="features-section">
        <Container>
          <Row>
            <Col lg={12} className="text-center mb-5">
              <h2 className="section-title">Why Choose AuctionStore?</h2>
              <p className="section-subtitle">
                Experience the future of online auctions with our cutting-edge
                features
              </p>
            </Col>
          </Row>

          <Row>
            {features.map((feature, index) => (
              <Col md={4} key={index} className="mb-4">
                <Card className="feature-card h-100">
                  <Card.Body className="text-center">
                    {feature.icon}
                    <h4>{feature.title}</h4>
                    <p>{feature.description}</p>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <Container>
          <Row>
            <Col lg={12} className="text-center">
              <h2 style={{ color: "white" }}>
                Ready to Start Your Auction Journey?
              </h2>
              <p style={{ color: "white" }}>
                Join thousands of satisfied users and discover amazing deals
                today!
              </p>
              <div className="cta-buttons">
                <Button
                  size="lg"
                  className="cta-button primary me-3"
                  onClick={handleGetStarted}
                >
                  <FaSearch className="me-2" />
                  Explore Auctions
                </Button>
                <Link
                  to="/register"
                  className="btn btn-outline-light btn-lg cta-button"
                >
                  Sign Up Free
                </Link>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Footer */}
      <footer className="landing-footer">
        <Container>
          <Row>
            <Col lg={12} className="text-center">
              <div className="footer-logo">
                <FaGavel className="footer-logo-icon" />
                <span>AuctionStore</span>
              </div>
              <p>&copy; 2024 AuctionStore. All rights reserved.</p>
              <div className="footer-links">
                <Link to="/home">Home</Link>
                <Link to="/auctions">Auctions</Link>
                <Link to="/login">Login</Link>
                <Link to="/register">Register</Link>
              </div>
            </Col>
          </Row>
        </Container>
      </footer>
    </div>
  );
};

export default React.memo(LandingPage);
