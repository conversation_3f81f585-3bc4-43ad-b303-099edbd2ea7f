"""
WebSocket URL Configuration for Real-time Features
"""

from django.http import HttpResponse
from django.urls import path


def websocket_placeholder(request, auction_id):
    """Placeholder for WebSocket endpoint"""
    return HttpResponse("WebSocket endpoint not yet implemented", status=501)


urlpatterns = [
    path("auction/<int:auction_id>/", websocket_placeholder, name="auction_websocket"),
]
