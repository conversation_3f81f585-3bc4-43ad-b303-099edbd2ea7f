from django.utils import timezone

from ..models import AutoBid, Bid
from .notifications import notify_user


def evaluate_auto_bids(auction):
    autobids = AutoBid.objects.filter(auction=auction)
    current_bid = auction.current_bid
    highest_bid = None

    for autobid in autobids:
        user = autobid.user
        max_bid = autobid.max_bid

        if max_bid > current_bid:
            increment = 1.0  # Define your increment rule
            new_bid = current_bid + increment
            if new_bid <= max_bid:
                # Create bid
                bid = Bid.objects.create(auction=auction, user=user, amount=new_bid)
                # Update auction
                auction.current_bid = new_bid
                auction.save()

                # Notify user
                notify_user(
                    user,
                    f"Your auto-bid of ${new_bid} was placed on '{auction.title}'.",
                    "AutoBid Confirmation",
                )

                current_bid = new_bid
                highest_bid = bid
    return highest_bid
