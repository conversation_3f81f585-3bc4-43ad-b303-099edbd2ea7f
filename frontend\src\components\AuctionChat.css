.auction-chat {
  background: var(--white);
  border-radius: var(--radius-auction);
  box-shadow: var(--shadow-lg);
  border: 2px solid var(--border-light);
  overflow: hidden;
  height: 500px;
  display: flex;
  flex-direction: column;
  margin: var(--space-lg) 0;
  transition: var(--transition-normal);
}

.auction-chat:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-xl);
}

.chat-header {
  background: var(--gradient-nav);
  color: var(--text-light);
  padding: var(--space-lg) var(--space-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  border-bottom: 2px solid var(--secondary-color);
}

.chat-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-auction);
}

.chat-header h3 {
  margin: 0;
  font-size: 1.2em;
  font-weight: 600;
}

.connection-status {
  font-size: 0.85em;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator.connected {
  color: #4CAF50;
}

.status-indicator.disconnected {
  color: #f44336;
}

.reconnect-btn, .refresh-btn, .clear-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: 4px;
}

.reconnect-btn:hover, .refresh-btn:hover, .clear-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.refresh-btn, .clear-btn {
  font-size: 0.9rem;
  padding: 6px 10px;
  border-radius: 6px;
}

.refresh-btn:active, .clear-btn:active {
  transform: scale(0.95);
}

/* Clear button specific styles */
.clear-btn:hover {
  background: rgba(255, 0, 0, 0.3);
  border-color: rgba(255, 0, 0, 0.5);
  color: #ff6b6b;
}

.clear-btn:active {
  background: rgba(255, 0, 0, 0.4);
}

.chat-loading, .chat-error {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
  text-align: center;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chat-error button {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  margin-top: 10px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background: #f8f9fa;
  min-height: 200px;
}

.no-messages {
  text-align: center;
  color: #666;
  padding: 40px 20px;
  font-style: italic;
}

.message {
  margin-bottom: 15px;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message.system {
  text-align: center;
}

.system-message {
  background: #e3f2fd;
  color: #1976d2;
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-block;
  font-size: 0.85em;
  font-style: italic;
}

.user-message {
  max-width: 70%;
  background: white;
  border-radius: 12px;
  padding: 12px 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.message.own-message .user-message {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-left: auto;
  border: none;
}

.message.own-message {
  display: flex;
  justify-content: flex-end;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  font-size: 0.8em;
}

.sender-name {
  font-weight: 600;
  color: #667eea;
}

.message.own-message .sender-name {
  color: rgba(255, 255, 255, 0.9);
}

.message-time {
  color: #999;
  font-size: 0.75em;
}

.message.own-message .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.message-text {
  line-height: 1.4;
  word-wrap: break-word;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #666;
  font-style: italic;
  font-size: 0.85em;
  padding: 10px 15px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 20px;
  margin-bottom: 10px;
  animation: fadeIn 0.3s ease-in;
}

.typing-dots {
  display: flex;
  gap: 3px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background: #667eea;
  border-radius: 50%;
  animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input {
  border-top: 1px solid #e0e0e0;
  background: white;
  padding: 15px;
}

.login-prompt {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.login-prompt p {
  margin: 0 0 15px 0;
  color: #495057;
  font-weight: 500;
}

.login-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.login-btn, .register-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.login-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.register-btn {
  background: #6c757d;
  color: white;
}

.register-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.user-info {
  margin-bottom: 8px;
  padding: 5px 0;
}

.logged-in-as {
  font-size: 0.85em;
  color: #667eea;
  font-weight: 500;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-container > div:last-child {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.input-container textarea {
  flex: 1;
  border: 2px solid #e0e0e0;
  border-radius: 20px;
  padding: 10px 15px;
  resize: none;
  font-family: inherit;
  font-size: 0.9em;
  outline: none;
  transition: border-color 0.3s ease;
  max-height: 80px;
}

.input-container textarea:focus {
  border-color: #667eea;
}

.input-container textarea:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1em;
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .auction-chat {
    height: 400px;
    margin: 15px 0;
  }

  .chat-header {
    padding: 12px 15px;
  }

  .chat-header h3 {
    font-size: 1.1em;
  }

  .chat-messages {
    padding: 10px;
  }

  .user-message {
    max-width: 85%;
    padding: 10px 12px;
  }

  .chat-input {
    padding: 12px;
  }

  .input-container textarea {
    padding: 8px 12px;
    font-size: 0.85em;
  }

  .send-button {
    width: 36px;
    height: 36px;
    font-size: 1em;
  }
}

@media (max-width: 480px) {
  .auction-chat {
    height: 350px;
  }

  .user-message {
    max-width: 90%;
  }

  .message-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }
}

/* AI Message Styles */
.message.ai-message {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-left: 4px solid #2196f3;
  margin: 15px 0;
  border-radius: 12px;
  box-shadow: 0 3px 12px rgba(33, 150, 243, 0.15);
  border: 1px solid rgba(33, 150, 243, 0.2);
}

.ai-message-content {
  padding: 15px 18px;
}

.ai-message-content .message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.ai-sender {
  color: #1976d2 !important;
  font-weight: 700;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.ai-text {
  color: #333;
  line-height: 1.7;
  font-size: 0.95rem;
  white-space: pre-wrap;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-text strong {
  color: #1976d2;
  font-weight: 600;
}

.ai-text div {
  margin-bottom: 6px;
}

/* AI Message Animation */
.message.ai-message {
  animation: slideInAI 0.6s ease-out;
}

@keyframes slideInAI {
  from {
    opacity: 0;
    transform: translateX(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Dark mode support for AI messages */
.dark-mode-bg .message.ai-message {
  background: linear-gradient(135deg, #1a237e 0%, #4a148c 100%);
  border-left-color: #3f51b5;
  color: #ffffff;
  border-color: rgba(63, 81, 181, 0.3);
}

.dark-mode-bg .ai-sender {
  color: #90caf9 !important;
}

.dark-mode-bg .ai-text {
  color: #e3f2fd;
}

.dark-mode-bg .ai-text strong {
  color: #90caf9;
}
