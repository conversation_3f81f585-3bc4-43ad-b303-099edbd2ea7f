from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.core.paginator import Paginator
from django.http import JsonResponse
from .new_search_service import NewAdvancedSearchService
from .serializers import AuctionSerializer
import logging

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([AllowAny])
def new_advanced_search(request):
    """
    New Advanced Search API endpoint
    Handles precise filtering with Apply Filters functionality
    """
    try:
        # Get search parameters from request
        filters = {
            'q': request.GET.get('q', ''),
            'category': request.GET.get('category', ''),
            'min_price': request.GET.get('min_price', ''),
            'max_price': request.GET.get('max_price', ''),
            'condition': request.GET.get('condition', ''),
            'status': request.GET.get('status', ''),
            'sort_by': request.GET.get('sort_by', 'created_at'),
            'sort_order': request.GET.get('sort_order', 'desc'),
        }
        
        # Log the search request
        logger.info(f"New advanced search request: {filters}")
        
        # Initialize search service
        search_service = NewAdvancedSearchService()
        
        # Perform search
        queryset = search_service.search_auctions(filters)
        
        # Pagination
        page_size = int(request.GET.get('page_size', 20))
        page_number = int(request.GET.get('page', 1))
        
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page_number)
        
        # Serialize results
        serializer = AuctionSerializer(page_obj.object_list, many=True)
        
        # Prepare response
        response_data = {
            'results': serializer.data,
            'count': paginator.count,
            'total_pages': paginator.num_pages,
            'current_page': page_number,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous(),
            'filters_applied': {k: v for k, v in filters.items() if v},
        }
        
        logger.info(f"Search completed: {paginator.count} total results")
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in new_advanced_search: {str(e)}")
        return Response(
            {'error': 'Search failed', 'message': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([AllowAny])
def new_search_filters(request):
    """
    Get available filter options for the new advanced search
    """
    try:
        search_service = NewAdvancedSearchService()
        filter_options = search_service.get_filter_options()
        
        logger.info("Filter options retrieved successfully")
        return Response(filter_options, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error getting filter options: {str(e)}")
        return Response(
            {'error': 'Failed to get filter options', 'message': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([AllowAny])
def new_search_suggestions(request):
    """
    Get search suggestions for autocomplete
    """
    try:
        query = request.GET.get('q', '').strip()
        limit = int(request.GET.get('limit', 5))
        
        if not query or len(query) < 2:
            return Response({'suggestions': []}, status=status.HTTP_200_OK)
        
        search_service = NewAdvancedSearchService()
        suggestions = search_service.get_search_suggestions(query, limit)
        
        return Response({'suggestions': suggestions}, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error getting search suggestions: {str(e)}")
        return Response(
            {'error': 'Failed to get suggestions', 'message': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([AllowAny])
def new_filter_counts(request):
    """
    Get counts for each filter option based on current search
    """
    try:
        # Get current filters
        base_filters = {
            'q': request.GET.get('q', ''),
            'category': request.GET.get('category', ''),
            'min_price': request.GET.get('min_price', ''),
            'max_price': request.GET.get('max_price', ''),
            'condition': request.GET.get('condition', ''),
            'status': request.GET.get('status', ''),
        }
        
        search_service = NewAdvancedSearchService()
        filter_counts = search_service.get_filter_counts(base_filters)
        
        return Response(filter_counts, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error getting filter counts: {str(e)}")
        return Response(
            {'error': 'Failed to get filter counts', 'message': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([AllowAny])
def save_search_query(request):
    """
    Save a search query for analytics (optional feature)
    """
    try:
        search_data = request.data
        
        # Log search query for analytics
        logger.info(f"Search query saved: {search_data}")
        
        # Here you could save to a SearchQuery model for analytics
        # For now, just return success
        
        return Response(
            {'message': 'Search query saved successfully'},
            status=status.HTTP_201_CREATED
        )
        
    except Exception as e:
        logger.error(f"Error saving search query: {str(e)}")
        return Response(
            {'error': 'Failed to save search query', 'message': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

# Helper function for quick search (used by other views)
def quick_search_auctions(query, limit=10):
    """
    Quick search function for internal use
    """
    try:
        search_service = NewAdvancedSearchService()
        filters = {'q': query}
        queryset = search_service.search_auctions(filters)
        
        # Limit results
        limited_queryset = queryset[:limit]
        
        # Serialize
        serializer = AuctionSerializer(limited_queryset, many=True)
        return serializer.data
        
    except Exception as e:
        logger.error(f"Error in quick_search_auctions: {str(e)}")
        return []
