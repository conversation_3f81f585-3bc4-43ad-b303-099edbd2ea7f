﻿import React, { createContext, useContext, useState, useEffect } from "react";
import jwt_Decode from "jwt-decode";

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);

  // Load user from localStorage on mount
  useEffect(() => {
    const storedUser = JSON.parse(
      localStorage.getItem("auction_loggedin_user")
    );
    console.log("Loading stored user:", storedUser);
    if (storedUser) {
      setUser(storedUser);
    }
  }, []);

  // âœ… Signup function â€” hits Django /api/register/ and logs in with username
  const signup = async (username, email, password, userRole = "bidder") => {
    console.log("🔍 Signup attempt:", { username, email, userRole });

    try {
      const res = await fetch("http://127.0.0.1:8000/api/register/", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          username,
          password,
          email,
          user_role: userRole,
        }),
      });

      console.log("📊 Registration response status:", res.status);

      if (!res.ok) {
        const errorData = await res.json();
        console.error("❌ Registration error:", errorData);

        // Handle different types of error responses
        if (errorData.username) {
          throw new Error(errorData.username[0] || "Username error");
        }
        if (errorData.email) {
          throw new Error(errorData.email[0] || "Email error");
        }
        if (errorData.user_role) {
          throw new Error(errorData.user_role[0] || "User role error");
        }
        if (errorData.password) {
          throw new Error(errorData.password[0] || "Password error");
        }

        throw new Error(
          errorData.error || errorData.message || "Registration failed"
        );
      }

      const responseData = await res.json();
      console.log("✅ Registration successful:", responseData);

      return login(username, password); // Auto-login
    } catch (error) {
      console.error("❌ Signup error:", error);
      throw error;
    }
  };

  const login = async (username, password) => {
    try {
      const response = await fetch("http://127.0.0.1:8000/api/login/", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) throw new Error("Login failed");

      const data = await response.json();

      // âœ… Save the token (using both keys for compatibility)
      localStorage.setItem("token", data.access);
      localStorage.setItem("access_token", data.access);

      // âœ… Decode token
      const decoded = jwtDecode(data.access);
      const userData = {
        id: decoded.user_id, // Add user ID
        username: decoded.username,
        email: decoded.email,
        role: decoded.role,
        user_role: decoded.user_role || "bidder",
        can_create_auctions: decoded.can_create_auctions || false,
        can_bid_on_auctions: decoded.can_bid_on_auctions || true,
        is_staff: decoded.role === "admin",
        token: data.access,
      };

      // âœ… Save user info
      localStorage.setItem("auction_loggedin_user", JSON.stringify(userData));
      setUser(userData);
      console.log("User logged in successfully:", userData);

      return userData;
    } catch (err) {
      console.error("Login error:", err);
      throw err;
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem("auction_loggedin_user");
    localStorage.removeItem("token");
    localStorage.removeItem("access_token");
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, signup }}>
      {children}
    </AuthContext.Provider>
  );
};
