import React, { useEffect, useState, useCallback } from "react";
import { Link, useNavigate } from "react-router-dom";
import {
  fetchFeaturedAuctions,
  fetchCategories,
  fetchTrendingAuctions,
  incrementAuctionViews,
} from "../api/auctions";
import { useAuth } from "../context/AuthContext";
import { useMultipleApiCalls } from "../hooks/useApiCall";
import globalApiManager from "../utils/globalApiManager";
import axiosInstance from "../api/axiosInstance";

import NewAdvancedFilter from "../components/NewAdvancedFilter";
import CategoryFlipCard from "../components/CategoryFlipCard";
import { formatAuctionPrice } from "../utils/currency";

function Home() {
  const [featuredAuctions, setFeaturedAuctions] = useState([]);
  const [trendingAuctions, setTrendingAuctions] = useState([]);
  const [categories, setCategories] = useState([]);
  const [timers, setTimers] = useState({});
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalAuctions: 0,
    activeAuctions: 0,
    totalUsers: 0,
    totalBids: 0,
  });
  const [statsLoading, setStatsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  // Circuit breaker state for featured auctions
  const [featuredRetryCount, setFeaturedRetryCount] = useState(0);
  const [featuredLastFetch, setFeaturedLastFetch] = useState(null);

  // New state for filtered results
  const [filteredResults, setFilteredResults] = useState([]);
  const [isFiltered, setIsFiltered] = useState(false);
  const [filterLoading, setFilterLoading] = useState(false);

  const { user } = useAuth();
  const navigate = useNavigate();

  // Fetch statistics with real data using global API manager
  const fetchStats = useCallback(async () => {
    try {
      setStatsLoading(true);
      console.log("🔄 Fetching statistics...");

      // Try platform stats API first
      try {
        const statsData = await globalApiManager.makeRequest(
          axiosInstance,
          "landing/stats/",
          { cacheTime: 2 * 60 * 1000 } // Cache for 2 minutes
        );

        if (statsData.success && statsData.stats) {
          const stats = statsData.stats;
          const newStats = {
            totalAuctions: stats.total_auctions || 0,
            activeAuctions: stats.active_auctions || 0,
            totalUsers: stats.total_users || 0,
            totalBids: stats.total_bids || 0,
          };

          console.log("✅ Using real platform stats:", newStats);
          setStats(newStats);
          setLastUpdated(new Date());
          return;
        }
      } catch (apiError) {
        console.warn(
          "⚠️ Platform stats API failed, falling back to individual endpoints:",
          apiError
        );
      }

      // Fallback: Use multiple API calls with global manager

      const results = await Promise.allSettled([
        globalApiManager.makeRequest(axiosInstance, "auctions/", {
          params: { page_size: 1000 },
        }),
        globalApiManager.makeRequest(axiosInstance, "bids/", {
          params: { page_size: 1000 },
        }),
      ]);

      let totalAuctions = 0;
      let activeAuctions = 0;
      let totalUsers = 9; // Real user count from database
      let totalBids = 0;

      // Process results from global API manager
      results.forEach((result, index) => {
        if (result.status === "fulfilled") {
          const data = result.value;
          if (index === 0) {
            // auctions
            console.log("📊 Auctions data:", data);
            totalAuctions = data.count || data.results?.length || 0;
            const auctions = data.results || [];
            activeAuctions = auctions.filter(
              (a) => new Date(a.end_time) > new Date()
            ).length;
            console.log(
              `📈 Total auctions: ${totalAuctions}, Active: ${activeAuctions}`
            );
          } else if (index === 1) {
            // bids
            console.log("💰 Bids data:", data);
            totalBids = data.count || data.results?.length || 0;
            console.log(`💰 Total bids: ${totalBids}`);
          }
        } else {
          const dataType = index === 0 ? "auctions" : "bids";
          console.warn(`❌ Failed to fetch ${dataType}:`, result.reason);
        }
      });

      console.log("👥 Using real user count:", totalUsers);

      const newStats = {
        totalAuctions,
        activeAuctions,
        totalUsers,
        totalBids,
      };

      console.log("📊 Final stats:", newStats);
      setStats(newStats);
      setLastUpdated(new Date());
    } catch (error) {
      console.error("❌ Error fetching stats:", error);
      // Set realistic fallback stats based on actual database counts
      setStats({
        totalAuctions: featuredAuctions.length || 30,
        activeAuctions:
          featuredAuctions.filter((a) => new Date(a.end_time) > new Date())
            .length || 27,
        totalUsers: 9, // Real user count from database
        totalBids: 0, // Real bid count from database
      });
      setLastUpdated(new Date());
    } finally {
      setStatsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // FIXED: Remove dependencies to prevent infinite loop

  // Default categories fallback
  const defaultCategories = [
    {
      name: "Art",
      image:
        "https://th.bing.com/th/id/OIP.XXwcURFvVIUMwbkxTazACQHaEz?rs=1&pid=ImgDetMain",
      slug: "art",
    },
    {
      name: "Electronics",
      image:
        "https://img.freepik.com/premium-photo/8k-realistic-smartphone-accessories-white-canvas_893571-33631.jpg",
      slug: "electronics",
    },
    {
      name: "Antiques",
      image:
        "https://img.freepik.com/psd-gratuit/vase-porcelaine-antique-fleurs-peintes-isolees-fond-transparent_191095-23323.jpg",
      slug: "collectibles",
    },
    {
      name: "Jewelry",
      image:
        "https://i.pinimg.com/originals/1e/14/c5/1e14c5229b10f256d44aea92e47f57e5.jpg",
      slug: "jewelry",
    },
    {
      name: "Fashion",
      image:
        "https://images.unsplash.com/photo-1445205170230-053b83016050?w=500",
      slug: "fashion",
    },
    {
      name: "Sports",
      image:
        "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500",
      slug: "sports",
    },
  ];

  // Fetch all data on component mount using global API manager
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        console.log("🏠 Loading home page data with global API manager...");

        // Use the existing API functions but with circuit breaker protection
        const dataPromises = [];

        // Featured auctions with circuit breaker
        if (featuredRetryCount < 3) {
          dataPromises.push(
            fetchFeaturedAuctions()
              .then((data) => ({ type: "featured", data }))
              .catch((error) => ({ type: "featured", error }))
          );
        }

        // Trending auctions
        dataPromises.push(
          fetchTrendingAuctions()
            .then((data) => ({ type: "trending", data }))
            .catch((error) => ({ type: "trending", error }))
        );

        // Categories
        dataPromises.push(
          fetchCategories()
            .then((data) => ({ type: "categories", data }))
            .catch((error) => ({ type: "categories", error }))
        );

        // Execute all API calls with global manager protection
        const results = await Promise.allSettled(dataPromises);

        // Process results
        results.forEach((result, index) => {
          if (result.status === "fulfilled") {
            const { type, data, error } = result.value;

            if (error) {
              console.error(`❌ Error loading ${type}:`, error);
              if (type === "featured") {
                setFeaturedRetryCount((prev) => prev + 1);
                setFeaturedAuctions([]);
              } else if (type === "categories") {
                setCategories(defaultCategories);
              }
            } else {
              if (type === "featured") {
                setFeaturedAuctions(data || []);
                setFeaturedRetryCount(0);
                setFeaturedLastFetch(Date.now());
              } else if (type === "trending") {
                setTrendingAuctions(data || []);
              } else if (type === "categories") {
                console.log(
                  "✅ Categories loaded:",
                  data?.length || 0,
                  "categories"
                );
                setCategories(data?.length > 0 ? data : defaultCategories);
              }
            }
          }
        });

        // Fetch stats separately
        await fetchStats();
      } catch (error) {
        console.error("❌ Error loading home page data:", error);
        setCategories(defaultCategories);
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // DISABLED: Periodic refresh to prevent infinite API polling
    // Set up periodic refresh only if polling is enabled
    const isPollingEnabled = false; // FORCE DISABLED to stop infinite polling

    if (isPollingEnabled) {
      // Set up periodic refresh every 10 minutes for stats (reduced frequency)
      const refreshInterval = setInterval(() => {
        fetchStats();
      }, 10 * 60 * 1000); // 10 minutes

      return () => clearInterval(refreshInterval);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // FIXED: Remove fetchStats dependency to prevent infinite loop

  // Refresh stats when user changes (login/logout)
  useEffect(() => {
    if (user) {
      fetchStats(); // Refresh stats when user logs in
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]); // FIXED: Remove fetchStats dependency to prevent infinite loop

  // Timer effect for countdown
  useEffect(() => {
    const interval = setInterval(() => {
      const updatedTimers = {};
      const allAuctions = isFiltered
        ? filteredResults
        : [...featuredAuctions, ...trendingAuctions];

      allAuctions.forEach((auction) => {
        const now = new Date();
        const end = new Date(auction.end_time || auction.endTime);
        const diff = end - now;

        if (diff <= 0) {
          updatedTimers[auction.id] = "Ended";
        } else {
          const days = Math.floor(diff / (1000 * 60 * 60 * 24));
          const hours = Math.floor(
            (diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
          );
          const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

          if (days > 0) {
            updatedTimers[auction.id] = `${days}d ${hours}h ${minutes}m`;
          } else if (hours > 0) {
            updatedTimers[auction.id] = `${hours}h ${minutes}m`;
          } else {
            updatedTimers[auction.id] = `${minutes}m`;
          }
        }
      });

      setTimers(updatedTimers);
    }, 1000);

    return () => clearInterval(interval);
  }, [featuredAuctions, trendingAuctions, filteredResults, isFiltered]);

  const handleCategoryClick = (categorySlug) => {
    console.log("🔄 Category clicked:", categorySlug);

    // Handle slug normalization (convert hyphens to underscores for auction filtering)
    const normalizedSlug = categorySlug.replace(/-/g, "_");

    console.log("🔄 Normalized slug:", normalizedSlug);
    console.log("🔄 Navigating to:", `/auctions/category/${normalizedSlug}`);

    // Use route-based navigation for better SEO and user experience
    navigate(`/auctions/category/${normalizedSlug}`);
  };

  // Handle search results from NewAdvancedFilter component
  const handleSearchResults = (searchResults) => {
    setFilterLoading(true);

    if (searchResults && searchResults.length > 0) {
      // Show filtered results on home page
      setFilteredResults(searchResults);
      setIsFiltered(true);
    } else if (searchResults === null) {
      // Clear search - show original content
      setFilteredResults([]);
      setIsFiltered(false);
    } else {
      // Empty results - show no results message
      setFilteredResults([]);
      setIsFiltered(true);
    }

    setFilterLoading(false);
  };

  // Handle clear results from NewAdvancedFilter component
  const handleClearResults = () => {
    // Clear filtered results and show original content
    setFilteredResults([]);
    setIsFiltered(false);
    setFilterLoading(false);
  };

  const handleViewDetails = async (auctionId) => {
    // Increment views count when view details is clicked
    await incrementAuctionViews(auctionId);
  };

  // Manual refresh function
  const handleRefresh = async () => {
    setLoading(true);
    try {
      // Refresh all data
      const [featured, trending, categoriesData] = await Promise.all([
        fetchFeaturedAuctions(),
        fetchTrendingAuctions(),
        fetchCategories(),
      ]);

      setFeaturedAuctions(featured);
      setTrendingAuctions(trending);
      setCategories(
        categoriesData.length > 0 ? categoriesData : defaultCategories
      );

      // Refresh stats
      await fetchStats();
    } catch (error) {
      console.error("Error refreshing data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Use the imported currency utility function
  const formatCurrency = formatAuctionPrice;

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="col-md-4 mb-4">
      <div className="card skeleton-card skeleton"></div>
    </div>
  );

  return (
    <div>
      {/* Hero Section */}
      <section className="hero-section">
        <div className="container">
          <div className="hero-content text-center">
            <h1 className="display-3 fw-bold mb-4 fade-in-up">
              Welcome to <span className="text-warning">AuctionStore</span>
            </h1>
            <p
              className="lead mb-5 fade-in-up"
              style={{
                animationDelay: "0.2s",
                color: "white",
              }}
            >
              Discover rare collectibles, bid for your dream items, and sell
              your treasures easily
            </p>

            {/* Advanced Search Section */}
            <div
              className="search-section mx-auto"
              style={{ maxWidth: "800px" }}
            >
              <NewAdvancedFilter
                onFilterResults={handleSearchResults}
                onClearResults={handleClearResults}
              />
            </div>
          </div>
        </div>
      </section>

      <div className="container">
        {/* Stats Section */}
        <section className="stats-section">
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h3 className="mb-0">
              <i className="fas fa-chart-bar me-2"></i>Live Statistics
            </h3>
            <div className="d-flex align-items-center">
              {lastUpdated && (
                <small className="text-muted me-3">
                  Last updated: {lastUpdated.toLocaleTimeString()}
                </small>
              )}
              <button
                className="btn btn-outline-primary btn-sm"
                onClick={handleRefresh}
                disabled={loading || statsLoading}
              >
                <i
                  className={`fas fa-sync-alt me-2 ${
                    loading || statsLoading ? "fa-spin" : ""
                  }`}
                ></i>
                {loading || statsLoading ? "Refreshing..." : "Refresh"}
              </button>
            </div>
          </div>
          <div className="row">
            <div className="col-md-3">
              <div className="stat-item">
                <div className="stat-number">
                  {statsLoading ? (
                    <i className="fas fa-spinner fa-spin"></i>
                  ) : (
                    stats.totalAuctions
                  )}
                </div>
                <div className="stat-label">Total Auctions</div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="stat-item">
                <div className="stat-number">
                  {statsLoading ? (
                    <i className="fas fa-spinner fa-spin"></i>
                  ) : (
                    stats.activeAuctions
                  )}
                </div>
                <div className="stat-label">Active Auctions</div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="stat-item">
                <div className="stat-number">
                  {statsLoading ? (
                    <i className="fas fa-spinner fa-spin"></i>
                  ) : (
                    stats.totalUsers
                  )}
                </div>
                <div className="stat-label">Registered Users</div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="stat-item">
                <div className="stat-number">
                  {statsLoading ? (
                    <i className="fas fa-spinner fa-spin"></i>
                  ) : (
                    stats.totalBids
                  )}
                </div>
                <div className="stat-label">Total Bids</div>
              </div>
            </div>
          </div>
        </section>

        {/* Quick Actions */}
        <div className="text-center mb-5">
          <div
            className="d-flex justify-content-center align-items-center flex-wrap"
            style={{ gap: "1rem" }}
          >
            <Link
              to="/auctions"
              className="btn btn-lg btn-primary px-5 py-3"
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                border: "none",
                borderRadius: "50px",
                fontWeight: "600",
                textTransform: "uppercase",
                letterSpacing: "1px",
                boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                transition: "all 0.3s ease",
              }}
            >
              <i className="fas fa-gavel me-2"></i>Explore All Auctions
            </Link>
            {user && (user.can_create_auctions || user.is_staff) ? (
              <Link
                to="/create-auction"
                className="btn btn-lg btn-outline-primary px-5 py-3"
                style={{
                  borderColor: "#667eea",
                  color: "#667eea",
                  borderRadius: "50px",
                  fontWeight: "600",
                  textTransform: "uppercase",
                  letterSpacing: "1px",
                  borderWidth: "2px",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = "#667eea";
                  e.target.style.color = "white";
                  e.target.style.transform = "translateY(-2px)";
                  e.target.style.boxShadow =
                    "0 6px 20px rgba(102, 126, 234, 0.4)";
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = "transparent";
                  e.target.style.color = "#667eea";
                  e.target.style.transform = "translateY(0)";
                  e.target.style.boxShadow = "none";
                }}
              >
                <i className="fas fa-plus me-2"></i>Create Auction
              </Link>
            ) : user ? (
              <div
                className="btn btn-lg btn-outline-secondary px-5 py-3"
                style={{
                  borderColor: "#6c757d",
                  color: "#6c757d",
                  borderRadius: "50px",
                  fontWeight: "600",
                  textTransform: "uppercase",
                  letterSpacing: "1px",
                  borderWidth: "2px",
                  cursor: "not-allowed",
                }}
                title="Your account type doesn't allow creating auctions"
              >
                <i className="fas fa-lock me-2"></i>Create Auction (Restricted)
              </div>
            ) : (
              <Link
                to="/login"
                className="btn btn-lg btn-outline-primary px-5 py-3"
                style={{
                  borderColor: "#667eea",
                  color: "#667eea",
                  borderRadius: "50px",
                  fontWeight: "600",
                  textTransform: "uppercase",
                  letterSpacing: "1px",
                  borderWidth: "2px",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = "#667eea";
                  e.target.style.color = "white";
                  e.target.style.transform = "translateY(-2px)";
                  e.target.style.boxShadow =
                    "0 6px 20px rgba(102, 126, 234, 0.4)";
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = "transparent";
                  e.target.style.color = "#667eea";
                  e.target.style.transform = "translateY(0)";
                  e.target.style.boxShadow = "none";
                }}
              >
                <i className="fas fa-sign-in-alt me-2"></i>Login to Create
              </Link>
            )}
          </div>
        </div>

        {/* Featured Auctions or Filtered Results */}
        <section className="mb-5">
          <h2 className="text-center mb-5 slide-in-left">
            {isFiltered ? (
              <>
                <i className="fas fa-search text-primary me-2"></i>
                Search Results
                <span className="badge bg-primary ms-2">
                  {filteredResults.length}{" "}
                  {filteredResults.length === 1 ? "result" : "results"}
                </span>
                <button
                  className="btn btn-outline-secondary btn-sm ms-3"
                  onClick={handleClearResults}
                >
                  <i className="fas fa-times me-1"></i>Clear Filters
                </button>
              </>
            ) : (
              <>
                <i className="fas fa-star text-warning me-2"></i>
                Featured Auctions
              </>
            )}
          </h2>

          <div className="row">
            {loading || filterLoading ? (
              // Loading skeletons
              Array.from({ length: 6 }).map((_, index) => (
                <LoadingSkeleton key={index} />
              ))
            ) : isFiltered ? (
              // Show filtered results
              filteredResults.length === 0 ? (
                <div className="col-12 text-center">
                  <div className="alert alert-warning">
                    <i className="fas fa-search me-2"></i>
                    No auctions found matching your search criteria.
                    <br />
                    <button
                      className="btn btn-primary mt-2"
                      onClick={handleClearResults}
                    >
                      <i className="fas fa-eye me-1"></i>View All Auctions
                    </button>
                  </div>
                </div>
              ) : (
                filteredResults.map((auction, index) => {
                  const isLive = timers[auction.id] !== "Ended";
                  const currentBid =
                    auction.current_bid ||
                    auction.currentBid ||
                    auction.starting_bid ||
                    auction.startingBid;

                  return (
                    <div
                      key={`filtered-${auction.id}-${index}`}
                      className="col-lg-4 col-md-6 mb-4 fade-in-up"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <div className="card auction-card h-100">
                        <div className="position-relative">
                          <img
                            src={
                              auction.image && auction.image.trim() !== ""
                                ? auction.image
                                : "/placeholder-image.svg"
                            }
                            className="card-img-top"
                            alt={auction.title}
                            style={{ height: "250px", objectFit: "contain" }}
                            onError={(e) => {
                              e.target.src = "/placeholder-image.svg";
                            }}
                          />

                          {/* Auction Type Badge */}
                          {auction.auction_type && (
                            <span className="badge bg-primary position-absolute top-0 start-0 m-2">
                              {auction.auction_type
                                .replace("_", " ")
                                .toUpperCase()}
                            </span>
                          )}

                          {/* Live Badge */}
                          {isLive && (
                            <span className="badge bg-danger blinking-badge position-absolute top-0 end-0 m-2">
                              <i className="fas fa-circle me-1"></i>LIVE
                            </span>
                          )}

                          {/* Search Result Badge */}
                          <span
                            className="badge bg-info position-absolute"
                            style={{ top: "40px", right: "8px" }}
                          >
                            <i className="fas fa-search me-1"></i>RESULT
                          </span>
                        </div>

                        <div className="card-body d-flex flex-column">
                          <h5
                            className="card-title text-truncate"
                            title={auction.title}
                          >
                            {auction.title}
                          </h5>

                          <p className="text-muted small mb-2">
                            by {auction.owner}
                            {auction.owner_rating > 0 && (
                              <span className="ms-1">
                                <i className="fas fa-star text-warning"></i>{" "}
                                {auction.owner_rating.toFixed(1)}
                              </span>
                            )}
                          </p>

                          <div className="mb-3">
                            <div className="d-flex justify-content-between align-items-center mb-2">
                              <span className="text-muted small">
                                Current Bid:
                              </span>
                              <span className="fw-bold text-success">
                                {formatCurrency(currentBid)}
                              </span>
                            </div>

                            {auction.reserve_price && (
                              <div className="d-flex justify-content-between align-items-center mb-2">
                                <span className="text-muted small">
                                  Reserve:
                                </span>
                                <span className="small">
                                  {formatCurrency(auction.reserve_price)}
                                </span>
                              </div>
                            )}

                            <div className="d-flex justify-content-between align-items-center">
                              <span className="text-muted small">Bids:</span>
                              <span className="badge bg-secondary">
                                {auction.total_bids || 0}
                              </span>
                            </div>
                          </div>

                          <div className="mb-3">
                            <div className="text-center p-2 bg-light rounded">
                              <small className="text-muted d-block">
                                Time Remaining
                              </small>
                              <span
                                className={`fw-bold ${
                                  isLive ? "text-danger" : "text-muted"
                                }`}
                              >
                                {timers[auction.id] || "Loading..."}
                              </span>
                            </div>
                          </div>

                          <div className="mt-auto">
                            <Link
                              to={`/auction/${auction.id}`}
                              className="btn btn-primary w-100"
                              onClick={() => handleViewDetails(auction.id)}
                            >
                              <i className="fas fa-eye me-2"></i>View Details
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })
              )
            ) : featuredAuctions.length === 0 ? (
              <div className="col-12 text-center">
                <div className="alert alert-info">
                  <i className="fas fa-info-circle me-2"></i>
                  No featured auctions available at the moment.
                </div>
              </div>
            ) : (
              featuredAuctions.map((auction, index) => {
                const isLive = timers[auction.id] !== "Ended";
                const currentBid =
                  auction.current_bid ||
                  auction.currentBid ||
                  auction.starting_bid ||
                  auction.startingBid;

                return (
                  <div
                    key={`featured-${auction.id}-${index}`}
                    className="col-lg-4 col-md-6 mb-4 fade-in-up"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className="card auction-card h-100">
                      <div className="position-relative">
                        <img
                          src={
                            auction.image && auction.image.trim() !== ""
                              ? auction.image
                              : "/placeholder-image.svg"
                          }
                          className="card-img-top"
                          alt={auction.title}
                          style={{ height: "250px", objectFit: "contain" }}
                          onError={(e) => {
                            e.target.src = "/placeholder-image.svg";
                          }}
                        />

                        {/* Auction Type Badge */}
                        {auction.auction_type && (
                          <span className="badge bg-primary position-absolute top-0 start-0 m-2">
                            {auction.auction_type
                              .replace("_", " ")
                              .toUpperCase()}
                          </span>
                        )}

                        {/* Live Badge */}
                        {isLive && (
                          <span className="badge bg-danger blinking-badge position-absolute top-0 end-0 m-2">
                            <i className="fas fa-circle me-1"></i>LIVE
                          </span>
                        )}

                        {/* Featured Badge */}
                        {auction.featured && (
                          <span
                            className="badge bg-warning position-absolute"
                            style={{ top: "40px", right: "8px" }}
                          >
                            <i className="fas fa-star me-1"></i>FEATURED
                          </span>
                        )}
                      </div>

                      <div className="card-body d-flex flex-column">
                        <h5
                          className="card-title text-truncate"
                          title={auction.title}
                        >
                          {auction.title}
                        </h5>

                        <p className="text-muted small mb-2">
                          by {auction.owner}
                          {auction.owner_rating > 0 && (
                            <span className="ms-1">
                              <i className="fas fa-star text-warning"></i>{" "}
                              {auction.owner_rating.toFixed(1)}
                            </span>
                          )}
                        </p>

                        <div className="mb-3">
                          <div className="d-flex justify-content-between align-items-center mb-2">
                            <span className="text-muted small">
                              Current Bid:
                            </span>
                            <span className="fw-bold text-success">
                              {formatCurrency(currentBid)}
                            </span>
                          </div>

                          {auction.reserve_price && (
                            <div className="d-flex justify-content-between align-items-center mb-2">
                              <span className="text-muted small">Reserve:</span>
                              <span className="small">
                                {formatCurrency(auction.reserve_price)}
                              </span>
                            </div>
                          )}

                          <div className="d-flex justify-content-between align-items-center">
                            <span className="text-muted small">Bids:</span>
                            <span className="badge bg-secondary">
                              {auction.total_bids || 0}
                            </span>
                          </div>
                        </div>

                        <div className="mb-3">
                          <div className="text-center p-2 bg-light rounded">
                            <small className="text-muted d-block">
                              Time Remaining
                            </small>
                            <span
                              className={`fw-bold ${
                                isLive ? "text-danger" : "text-muted"
                              }`}
                            >
                              {timers[auction.id] || "Loading..."}
                            </span>
                          </div>
                        </div>

                        <div className="mt-auto">
                          <Link
                            to={`/auction/${auction.id}`}
                            className="btn btn-primary w-100"
                            onClick={() => handleViewDetails(auction.id)}
                          >
                            <i className="fas fa-eye me-2"></i>View Details
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </section>

        {/* Categories Section with Flip Cards - Hide when filtering */}
        {!isFiltered && (
          <section className="mb-5">
            <h2 className="text-center mb-5 slide-in-left">
              <i className="fas fa-tags text-primary me-2"></i>
              Popular Categories
              <small
                className="text-muted d-block mt-2"
                style={{ fontSize: "0.6em" }}
              >
                Hover over cards to see auction counts
              </small>
            </h2>

            <div className="row">
              {categories.slice(0, 6).map((category, index) => (
                <div
                  key={category.slug || index}
                  className="col-lg-2 col-md-4 col-sm-6 mb-4"
                  style={{
                    animationDelay: `${index * 0.1}s`,
                    opacity: 0,
                    animation: `flipCardFadeIn 0.6s ease-out ${
                      index * 0.1
                    }s forwards`,
                  }}
                >
                  <CategoryFlipCard
                    category={category}
                    onClick={handleCategoryClick}
                  />
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Trending Auctions - Hide when filtering */}
        {!isFiltered && trendingAuctions.length > 0 && (
          <section className="mb-5">
            <h2 className="text-center mb-5 slide-in-left">
              <i className="fas fa-fire text-danger me-2"></i>
              Trending Auctions
            </h2>

            <div className="row">
              {trendingAuctions.map((auction, index) => {
                const isLive = timers[auction.id] !== "Ended";
                const currentBid =
                  auction.current_bid ||
                  auction.currentBid ||
                  auction.starting_bid ||
                  auction.startingBid;

                return (
                  <div
                    key={`trending-${auction.id}-${index}`}
                    className="col-lg-3 col-md-6 mb-4 fade-in-up"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className="card auction-card h-100">
                      <div className="position-relative">
                        <img
                          src={
                            auction.image && auction.image.trim() !== ""
                              ? auction.image
                              : "/placeholder-image.svg"
                          }
                          className="card-img-top"
                          alt={auction.title}
                          style={{ height: "200px", objectFit: "contain" }}
                          onError={(e) => {
                            e.target.src = "/placeholder-image.svg";
                          }}
                        />

                        <span className="badge bg-danger position-absolute top-0 start-0 m-2">
                          <i className="fas fa-fire me-1"></i>TRENDING
                        </span>

                        {isLive && (
                          <span className="badge bg-success position-absolute top-0 end-0 m-2">
                            <i className="fas fa-circle me-1"></i>LIVE
                          </span>
                        )}
                      </div>

                      <div className="card-body">
                        <h6
                          className="card-title text-truncate"
                          title={auction.title}
                        >
                          {auction.title}
                        </h6>

                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <span className="text-muted small">Current Bid:</span>
                          <span className="fw-bold text-success small">
                            {formatCurrency(currentBid)}
                          </span>
                        </div>

                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <span className="text-muted small">Views:</span>
                          <span className="badge bg-info">
                            {auction.views_count || 0}
                          </span>
                        </div>

                        <Link
                          to={`/auction/${auction.id}`}
                          className="btn btn-sm btn-outline-primary w-100"
                          onClick={() => handleViewDetails(auction.id)}
                        >
                          View Details
                        </Link>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </section>
        )}
      </div>
    </div>
  );
}

export default Home;
