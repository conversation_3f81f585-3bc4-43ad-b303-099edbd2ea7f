"""
Django Management Command: Check Payment Timeouts
Run this command periodically to handle payment timeouts and re-auctions
"""

import logging

from django.core.management.base import BaseCommand
from django.utils import timezone

from auction.payment_timeout_service import PaymentTimeoutService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Check for payment timeouts and handle re-auctions"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Run in dry-run mode (no actual changes)",
        )
        parser.add_argument(
            "--verbose",
            action="store_true",
            help="Enable verbose output",
        )

    def handle(self, *args, **options):
        """Main command handler"""
        dry_run = options["dry_run"]
        verbose = options["verbose"]

        if verbose:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Starting payment timeout check at {timezone.now()}"
                )
            )

        try:
            if dry_run:
                self.stdout.write(
                    self.style.WARNING(
                        "Running in DRY-RUN mode - no changes will be made"
                    )
                )
                self.dry_run_check()
            else:
                service = PaymentTimeoutService()
                service.run_payment_timeout_check()

                if verbose:
                    self.stdout.write(
                        self.style.SUCCESS(
                            "Payment timeout check completed successfully"
                        )
                    )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error during payment timeout check: {e}")
            )
            logger.error(f"Payment timeout check failed: {e}")
            raise

    def dry_run_check(self):
        """Perform dry-run check without making changes"""
        from datetime import timedelta

        from auction.models import Auction, Payment

        # Check ended auctions without payment deadline
        ended_auctions = Auction.objects.filter(
            is_closed=True, payment_deadline__isnull=True, bids__isnull=False
        ).distinct()

        self.stdout.write(
            f"Found {ended_auctions.count()} ended auctions needing payment deadline"
        )

        # Check auctions needing payment reminders
        reminder_time = timezone.now() - timedelta(hours=12)
        auctions_needing_reminder = Auction.objects.filter(
            payment_deadline__lte=reminder_time,
            payment_deadline__gt=timezone.now(),
            payment_reminder_sent=False,
            winner__isnull=False,
        )

        self.stdout.write(
            f"Found {auctions_needing_reminder.count()} auctions needing payment reminders"
        )

        # Check overdue payments
        overdue_auctions = Auction.objects.filter(
            payment_deadline__lt=timezone.now(),
            winner__isnull=False,
            payment_timeout_count=0,
        )

        self.stdout.write(
            f"Found {overdue_auctions.count()} auctions with overdue payments"
        )

        for auction in overdue_auctions:
            # Check if payment was actually made
            payment = Payment.objects.filter(
                auction=auction, user=auction.winner, payment_status="completed"
            ).first()

            if not payment:
                self.stdout.write(
                    self.style.WARNING(
                        f"Would re-list auction: {auction.title} (ID: {auction.id}) - "
                        f"Winner: {auction.winner.username}"
                    )
                )
