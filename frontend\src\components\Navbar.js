import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { useTheme } from "../context/ThemeContext";

function Navbar() {
  const { user, logout } = useAuth();
  const { darkMode, toggleDarkMode, theme } = useTheme();
  const [showAccountDropdown, setShowAccountDropdown] = useState(false);
  const dropdownRef = useRef(null);
  const timeoutRef = useRef(null);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowAccountDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Improved dropdown handlers
  const handleDropdownEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setShowAccountDropdown(true);
  };

  const handleDropdownLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setShowAccountDropdown(false);
    }, 150); // Small delay to prevent flickering
  };

  const handleLinkClick = () => {
    setShowAccountDropdown(false);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  return (
    <nav
      className={`navbar navbar-expand-lg ${
        darkMode ? "navbar-dark" : "navbar-dark"
      }`}
      style={{
        backgroundColor: darkMode ? "#1a1a1a" : "#2c3e50",
        borderBottom: `2px solid ${darkMode ? "#3498db" : "#3498db"}`,
        boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
        width: "100%",
        position: "relative",
      }}
    >
      <div className="container-fluid px-3">
        <Link
          className="navbar-brand fw-bold"
          to="/home"
          style={{
            color: "#3498db",
            fontSize: "1.1rem",
            textDecoration: "none",
          }}
        >
          🏆 AuctionStore
        </Link>
        <button
          className="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span className="navbar-toggler-icon"></span>
        </button>

        <div
          className="collapse navbar-collapse"
          id="navbarNav"
          style={{ overflow: "visible" }}
        >
          <ul
            className="navbar-nav me-auto d-flex align-items-center"
            style={{ flexWrap: "nowrap" }}
          >
            <li className="nav-item d-flex align-items-center">
              <Link
                className="nav-link d-flex align-items-center"
                to="/auctions"
                style={{
                  fontSize: "0.85rem",
                  padding: "0.3rem 0.6rem",
                  whiteSpace: "nowrap",
                }}
              >
                Auctions
              </Link>
            </li>
            {user && (
              <>
                {(user.can_create_auctions || user.is_staff) && (
                  <li className="nav-item d-flex align-items-center">
                    <Link
                      className="nav-link d-flex align-items-center"
                      to="/create-auction"
                      style={{
                        fontSize: "0.85rem",
                        padding: "0.3rem 0.6rem",
                        whiteSpace: "nowrap",
                      }}
                    >
                      Create Auction
                    </Link>
                  </li>
                )}
                <li
                  ref={dropdownRef}
                  className="nav-item dropdown d-flex align-items-center"
                  style={{ position: "relative" }}
                  onMouseEnter={handleDropdownEnter}
                  onMouseLeave={handleDropdownLeave}
                >
                  <button
                    className="nav-link dropdown-toggle d-flex align-items-center"
                    style={{
                      fontSize: "0.85rem",
                      padding: "0.3rem 0.6rem",
                      whiteSpace: "nowrap",
                      background: "none",
                      border: "none",
                      color: "#ecf0f1",
                      cursor: "pointer",
                      height: "auto",
                      lineHeight: "1.5",
                      display: "flex",
                      alignItems: "center",
                    }}
                    onClick={() => setShowAccountDropdown(!showAccountDropdown)}
                  >
                    My Account
                  </button>
                  {showAccountDropdown && (
                    <div
                      className="dropdown-menu show"
                      style={{
                        position: "absolute",
                        top: "100%",
                        left: "0",
                        backgroundColor: darkMode ? "#2c3e50" : "#34495e",
                        border: `1px solid ${darkMode ? "#3498db" : "#3498db"}`,
                        borderRadius: "5px",
                        boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
                        minWidth: "180px",
                        zIndex: 1000,
                      }}
                    >
                      <Link
                        className="dropdown-item"
                        to="/profile"
                        style={{
                          color: "#ecf0f1",
                          padding: "8px 16px",
                          textDecoration: "none",
                          display: "block",
                          fontSize: "0.85rem",
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = darkMode
                            ? "#3498db"
                            : "#2980b9";
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = "transparent";
                        }}
                        onClick={handleLinkClick}
                      >
                        📊 Profile Dashboard
                      </Link>
                      <Link
                        className="dropdown-item"
                        to="/user-profile"
                        style={{
                          color: "#ecf0f1",
                          padding: "8px 16px",
                          textDecoration: "none",
                          display: "block",
                          fontSize: "0.85rem",
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = darkMode
                            ? "#3498db"
                            : "#2980b9";
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = "transparent";
                        }}
                        onClick={handleLinkClick}
                      >
                        👤 Account Settings
                      </Link>
                      <Link
                        className="dropdown-item"
                        to="/user-profile?tab=auctions"
                        style={{
                          color: "#ecf0f1",
                          padding: "8px 16px",
                          textDecoration: "none",
                          display: "block",
                          fontSize: "0.85rem",
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = darkMode
                            ? "#3498db"
                            : "#2980b9";
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = "transparent";
                        }}
                        onClick={handleLinkClick}
                      >
                        🏷️ My Auctions
                      </Link>
                      <Link
                        className="dropdown-item"
                        to="/user-profile?tab=won"
                        style={{
                          color: "#ecf0f1",
                          padding: "8px 16px",
                          textDecoration: "none",
                          display: "block",
                          fontSize: "0.85rem",
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = darkMode
                            ? "#3498db"
                            : "#2980b9";
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = "transparent";
                        }}
                        onClick={handleLinkClick}
                      >
                        🏆 Won Auctions
                      </Link>
                      <Link
                        className="dropdown-item"
                        to="/payments"
                        style={{
                          color: "#ecf0f1",
                          padding: "8px 16px",
                          textDecoration: "none",
                          display: "block",
                          fontSize: "0.85rem",
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = darkMode
                            ? "#3498db"
                            : "#2980b9";
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = "transparent";
                        }}
                        onClick={handleLinkClick}
                      >
                        💳 Payment Center
                      </Link>
                      <Link
                        className="dropdown-item"
                        to="/watchlist"
                        style={{
                          color: "#ecf0f1",
                          padding: "8px 16px",
                          textDecoration: "none",
                          display: "block",
                          fontSize: "0.85rem",
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = darkMode
                            ? "#3498db"
                            : "#2980b9";
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = "transparent";
                        }}
                        onClick={handleLinkClick}
                      >
                        ❤️ My Watchlist
                      </Link>
                    </div>
                  )}
                </li>
                {(user?.role === "admin" || user?.is_staff) && (
                  <>
                    <li className="nav-item d-flex align-items-center">
                      <Link
                        className="nav-link d-flex align-items-center"
                        to="/admin-dashboard"
                        style={{
                          fontSize: "0.8rem",
                          padding: "0.3rem 0.5rem",
                          whiteSpace: "nowrap",
                        }}
                      >
                        Admin Dashboard
                      </Link>
                    </li>
                    <li className="nav-item d-flex align-items-center">
                      <Link
                        className="nav-link d-flex align-items-center"
                        to="/dashboard"
                        style={{
                          fontSize: "0.8rem",
                          padding: "0.3rem 0.5rem",
                          whiteSpace: "nowrap",
                        }}
                      >
                        Analytics
                      </Link>
                    </li>
                    <li className="nav-item d-flex align-items-center">
                      <Link
                        className="nav-link d-flex align-items-center"
                        to="/admin/auctions"
                        style={{
                          fontSize: "0.8rem",
                          padding: "0.3rem 0.5rem",
                          whiteSpace: "nowrap",
                        }}
                      >
                        Manage Auctions
                      </Link>
                    </li>
                  </>
                )}
              </>
            )}
          </ul>

          <ul
            className="navbar-nav d-flex align-items-center"
            style={{ flexWrap: "nowrap" }}
          >
            {user ? (
              <li
                className="nav-item d-flex align-items-center"
                style={{ gap: "0.25rem" }}
              >
                <span
                  style={{
                    fontSize: "0.7rem",
                    color: "#ecf0f1",
                    whiteSpace: "nowrap",
                    marginRight: "0.25rem",
                  }}
                >
                  Hi, {user.username}
                </span>
                <button
                  className="btn btn-sm"
                  onClick={logout}
                  style={{
                    backgroundColor: "#e74c3c",
                    color: "white",
                    border: "none",
                    borderRadius: "3px",
                    padding: "2px 6px",
                    fontSize: "0.65rem",
                    whiteSpace: "nowrap",
                    marginRight: "0.25rem",
                  }}
                >
                  Logout
                </button>
                <button
                  className="btn btn-sm"
                  onClick={toggleDarkMode}
                  style={{
                    backgroundColor: darkMode ? "#f39c12" : "#3498db",
                    color: "white",
                    border: "none",
                    borderRadius: "3px",
                    padding: "2px 6px",
                    fontSize: "0.65rem",
                    whiteSpace: "nowrap",
                  }}
                  title={`Switch to ${darkMode ? "Light" : "Dark"} Mode`}
                >
                  {darkMode ? "☀️" : "🌙"}
                </button>
              </li>
            ) : (
              <>
                <li className="nav-item d-flex align-items-center">
                  <Link
                    className="nav-link d-flex align-items-center"
                    to="/login"
                    style={{
                      fontSize: "0.85rem",
                      padding: "0.3rem 0.6rem",
                      whiteSpace: "nowrap",
                    }}
                  >
                    Login
                  </Link>
                </li>
                <li className="nav-item d-flex align-items-center">
                  <Link
                    className="nav-link d-flex align-items-center"
                    to="/register"
                    style={{
                      fontSize: "0.85rem",
                      padding: "0.3rem 0.6rem",
                      whiteSpace: "nowrap",
                    }}
                  >
                    Register
                  </Link>
                </li>
                <li className="nav-item d-flex align-items-center">
                  <Link
                    className="nav-link d-flex align-items-center"
                    to="/contact"
                    style={{
                      fontSize: "0.85rem",
                      padding: "0.3rem 0.6rem",
                      whiteSpace: "nowrap",
                    }}
                  >
                    Contact
                  </Link>
                </li>
              </>
            )}
          </ul>
        </div>
      </div>
    </nav>
  );
}

export default Navbar;
