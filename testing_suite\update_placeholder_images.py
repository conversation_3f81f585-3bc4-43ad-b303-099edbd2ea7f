#!/usr/bin/env python3
"""
Script to update placeholder images with working URLs
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from auction.models import Auction

def update_placeholder_images():
    """Update placeholder images with working URLs"""
    print("🔄 Updating placeholder images...")
    
    # Working placeholder URLs
    placeholder_urls = [
        "https://picsum.photos/400/300?random=1",
        "https://picsum.photos/400/300?random=2", 
        "https://picsum.photos/400/300?random=3",
        "https://picsum.photos/400/300?random=4",
        "https://picsum.photos/400/300?random=5",
    ]
    
    # Find auctions with placeholder images
    placeholder_auctions = Auction.objects.filter(
        image__icontains="via.placeholder.com"
    )
    
    updated_count = 0
    for i, auction in enumerate(placeholder_auctions):
        # Use different placeholder for each auction
        new_url = placeholder_urls[i % len(placeholder_urls)]
        auction.image = new_url
        auction.save()
        
        print(f"✅ Updated auction {auction.id}: {auction.title}")
        print(f"   New image: {new_url}")
        updated_count += 1
    
    print(f"\n🎉 Updated {updated_count} placeholder images!")

if __name__ == "__main__":
    update_placeholder_images()
