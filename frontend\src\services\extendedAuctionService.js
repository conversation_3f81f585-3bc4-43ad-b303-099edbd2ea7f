/**
 * Extended Auction Service - API calls for additional auction functionality
 */

import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/api/extended`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const extendedAuctionService = {
  // Auction Analytics
  async getTrendingAuctions() {
    try {
      const response = await apiClient.get('/auctions/trending/');
      return response.data;
    } catch (error) {
      console.error('Error fetching trending auctions:', error);
      throw error;
    }
  },

  async getEndingSoonAuctions() {
    try {
      const response = await apiClient.get('/auctions/ending-soon/');
      return response.data;
    } catch (error) {
      console.error('Error fetching ending soon auctions:', error);
      throw error;
    }
  },

  async getPopularCategories() {
    try {
      const response = await apiClient.get('/auctions/popular-categories/');
      return response.data;
    } catch (error) {
      console.error('Error fetching popular categories:', error);
      throw error;
    }
  },

  async getUserAuctionStats() {
    try {
      const response = await apiClient.get('/auctions/user-stats/');
      return response.data;
    } catch (error) {
      console.error('Error fetching user auction stats:', error);
      throw error;
    }
  },

  async getAuctionAnalytics(auctionId) {
    try {
      const response = await apiClient.get(`/auctions/${auctionId}/analytics/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching auction analytics:', error);
      throw error;
    }
  },

  async extendAuction(auctionId, hours) {
    try {
      const response = await apiClient.post(`/auctions/${auctionId}/extend/`, {
        hours: hours
      });
      return response.data;
    } catch (error) {
      console.error('Error extending auction:', error);
      throw error;
    }
  },

  // Admin Dashboard
  async getAdminDashboardStats() {
    try {
      const response = await apiClient.get('/admin/dashboard-stats/');
      return response.data;
    } catch (error) {
      console.error('Error fetching admin dashboard stats:', error);
      throw error;
    }
  },

  async getRevenueReport(startDate, endDate) {
    try {
      const params = {};
      if (startDate) params.start_date = startDate;
      if (endDate) params.end_date = endDate;
      
      const response = await apiClient.get('/admin/revenue-report/', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching revenue report:', error);
      throw error;
    }
  },

  async getUserActivityReport() {
    try {
      const response = await apiClient.get('/admin/user-activity/');
      return response.data;
    } catch (error) {
      console.error('Error fetching user activity report:', error);
      throw error;
    }
  },

  // User Data Export
  async exportUserData() {
    try {
      const response = await apiClient.get('/user/export/');
      return response.data;
    } catch (error) {
      console.error('Error exporting user data:', error);
      throw error;
    }
  },

  // WebSocket connection helpers
  createWebSocketConnection(endpoint, onMessage, onError) {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${wsProtocol}//${window.location.host}/ws/${endpoint}/`;
    
    const socket = new WebSocket(wsUrl);
    
    socket.onopen = () => {
      console.log(`WebSocket connected to ${endpoint}`);
    };
    
    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage(data);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };
    
    socket.onerror = (error) => {
      console.error(`WebSocket error on ${endpoint}:`, error);
      if (onError) onError(error);
    };
    
    socket.onclose = () => {
      console.log(`WebSocket disconnected from ${endpoint}`);
    };
    
    return socket;
  },

  // Real-time subscriptions
  subscribeToTrendingUpdates(callback) {
    return this.createWebSocketConnection('auction_updates', (data) => {
      if (data.type === 'trending_update') {
        callback(data.data);
      }
    });
  },

  subscribeToEndingSoonAlerts(callback) {
    return this.createWebSocketConnection('auction_updates', (data) => {
      if (data.type === 'ending_soon_alert') {
        callback(data.data);
      }
    });
  },

  subscribeToAuctionAnalytics(auctionId, callback) {
    return this.createWebSocketConnection(`auction_${auctionId}_analytics`, (data) => {
      if (data.type === 'analytics_update') {
        callback(data.data);
      }
    });
  },

  subscribeToAdminDashboard(callback) {
    return this.createWebSocketConnection('admin_dashboard', (data) => {
      if (data.type === 'dashboard_update') {
        callback(data.data);
      }
    });
  },

  subscribeToUserStats(userId, callback) {
    return this.createWebSocketConnection(`user_${userId}_stats`, (data) => {
      if (data.type === 'user_stats_update') {
        callback(data.data);
      }
    });
  }
};

export default extendedAuctionService;
