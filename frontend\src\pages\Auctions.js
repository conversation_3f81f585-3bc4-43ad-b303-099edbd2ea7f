import React, { useEffect, useState, useCallback, useMemo } from "react";
import {
  Container,
  <PERSON>,
  Col,
  Al<PERSON>,
  <PERSON>read<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "react-bootstrap";
import { useAuth } from "../context/AuthContext";
import { fetchAuctions } from "../api/auctions";
import axiosInstance from "../api/axiosInstance";
import AuctionCard from "../components/AuctionCard";
import NewAdvancedFilter from "../components/NewAdvancedFilter";
import {
  useLocation,
  useNavigate,
  useSearchParams,
  useParams,
} from "react-router-dom";
import { FaHome, FaGavel, FaTags } from "react-icons/fa";
import "../components/NewAdvancedFilter.css";

// Custom styles for NewAdvancedFilter in Auctions page
const auctionPageStyles = `
  .new-advanced-filter-container .new-advanced-filter {
    max-width: 100%;
    margin-bottom: 20px;
  }

  .new-advanced-filter-container .search-section {
    margin-bottom: 15px;
  }

  .new-advanced-filter-container .filters-panel {
    border-radius: 8px;
  }
`;

const ITEMS_PER_PAGE = 8;

// Utility function to remove duplicate auctions by ID
const removeDuplicateAuctions = (auctions) => {
  const seen = new Set();
  const duplicates = [];
  const unique = auctions.filter((auction) => {
    if (seen.has(auction.id)) {
      console.warn(
        `⚠️ Removing duplicate auction with ID: ${auction.id}, Title: ${auction.title}`
      );
      duplicates.push(auction);
      return false;
    }
    seen.add(auction.id);
    return true;
  });

  if (duplicates.length > 0) {
    console.warn(
      `🔍 Found ${duplicates.length} duplicate auctions:`,
      duplicates.map((a) => ({ id: a.id, title: a.title }))
    );
  }

  return unique;
};

function Auctions() {
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { category: routeCategory } = useParams();

  // Get search results from navigation state (from Home page search)
  const navigationSearchResults = location.state?.searchResults;
  const fromSearch = location.state?.fromSearch;
  const [allAuctions, setAllAuctions] = useState([]);
  const [filteredAuctions, setFilteredAuctions] = useState([]);
  const [watchlist, setWatchlist] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(true);
  const [successMessage, setSuccessMessage] = useState("");
  const [websocket, setWebsocket] = useState(null);
  const [isLoadingAuctions, setIsLoadingAuctions] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("");
  const [categoryInfo, setCategoryInfo] = useState(null);
  const [debugLogs, setDebugLogs] = useState([]);
  const [showDebug, setShowDebug] = useState(true);

  // Debug logging function
  const addDebugLog = useCallback((message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, // More unique ID
      timestamp,
      message,
      data: data ? JSON.stringify(data, null, 2) : null,
    };

    setDebugLogs((prev) => [...prev.slice(-19), logEntry]); // Keep last 20 logs
    console.log(`[${timestamp}] ${message}`, data || "");
  }, []);

  // Category display mapping
  const categoryDisplayMapping = useMemo(
    () => ({
      electronics: "Electronics",
      fashion: "Fashion",
      home_garden: "Home & Garden",
      sports: "Sports",
      books: "Books",
      art: "Art",
      collectibles: "Collectibles",
      automotive: "Vehicles",
      jewelry: "Jewelry",
      other: "Other",
    }),
    []
  );

  // Category matching function to handle special cases
  const matchesCategory = useCallback((auctionCategory, selectedCategory) => {
    if (!auctionCategory || !selectedCategory) return false;

    // Handle special case: home_garden -> "Home & Garden"
    if (selectedCategory.toLowerCase() === "home_garden") {
      return (
        auctionCategory.toLowerCase().includes("home") &&
        auctionCategory.toLowerCase().includes("garden")
      );
    }

    // Standard case-insensitive matching
    return auctionCategory.toLowerCase() === selectedCategory.toLowerCase();
  }, []);

  const loadAuctions = useCallback(
    async (forceRefresh = false) => {
      // Prevent concurrent auction loading
      if (isLoadingAuctions) {
        console.log("🔄 Auction loading already in progress, skipping...");
        return;
      }

      try {
        setIsLoadingAuctions(true);
        setLoading(true);
        setError(""); // Clear any previous errors

        // Add timestamp to bypass cache when force refreshing
        const params = forceRefresh ? { _t: Date.now() } : {};
        console.log("🔄 Loading auctions...", params);

        const auctions = await fetchAuctions(params);
        // Ensure auctions is always an array and remove duplicates
        const auctionsArray = Array.isArray(auctions) ? auctions : [];
        const uniqueAuctions = removeDuplicateAuctions(auctionsArray);

        // Debug: Show categories in loaded auctions
        const loadedCategories = [
          ...new Set(uniqueAuctions.map((a) => a.category)),
        ];
        console.log(
          `📊 Loaded auctions by category:`,
          loadedCategories.map((cat) => ({
            category: cat,
            count: uniqueAuctions.filter((a) => a.category === cat).length,
          }))
        );

        setAllAuctions(uniqueAuctions);
        setFilteredAuctions(uniqueAuctions);

        console.log(`✅ Successfully loaded ${auctionsArray.length} auctions`);

        // Clear error state on successful load
        setError("");
      } catch (error) {
        console.error("❌ Error loading auctions:", error);
        setError(`Failed to load auctions: ${error.message}`);
      } finally {
        setLoading(false);
        setIsLoadingAuctions(false);
      }
    },
    [isLoadingAuctions]
  );

  const loadWatchlist = useCallback(async () => {
    // Only load watchlist if user is logged in and has a token
    const token =
      localStorage.getItem("token") || localStorage.getItem("access_token");
    if (!user || !token) {
      return;
    }

    try {
      const response = await axiosInstance.get("watchlist/");
      setWatchlist(response.data.results || []);
    } catch (error) {
      // Silently handle 401 errors - user might not be logged in
      if (error.response?.status === 401) {
        console.log("Watchlist requires authentication");
        setWatchlist([]);
      } else {
        console.error("Error loading watchlist:", error);
      }
    }
  }, [user]);

  // Inject custom styles for NewAdvancedFilter in auctions page
  useEffect(() => {
    const styleElement = document.createElement("style");
    styleElement.textContent = auctionPageStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // Handle URL parameters and route parameters for category filtering
  useEffect(() => {
    const categoryParam = searchParams.get("category") || routeCategory;

    console.log("🔍 Category parameter detection:", {
      searchParams: searchParams.get("category"),
      routeCategory: routeCategory,
      finalCategoryParam: categoryParam,
      currentPath: location.pathname,
      currentSearch: location.search,
    });

    if (categoryParam) {
      console.log("✅ Setting category filter:", categoryParam);
      setSelectedCategory(categoryParam);
      setCategoryInfo({
        slug: categoryParam,
        name: categoryDisplayMapping[categoryParam] || categoryParam,
      });
    } else {
      console.log("❌ No category parameter found, clearing filter");
      setSelectedCategory("");
      setCategoryInfo(null);
    }
  }, [
    searchParams,
    routeCategory,
    categoryDisplayMapping,
    location.pathname,
    location.search,
  ]);

  useEffect(() => {
    let isMounted = true;

    const loadInitialData = async () => {
      if (isMounted) {
        await loadAuctions();
        if (user && isMounted) {
          await loadWatchlist();
        }
      }
    };

    loadInitialData();

    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // NO DEPENDENCIES - run only once

  // REMOVED: This useEffect was causing infinite re-renders

  // Handle navigation state (from CreateAuction) - SIMPLIFIED
  useEffect(() => {
    if (location.state?.refresh) {
      console.log("Refreshing auctions due to navigation state");

      if (location.state?.message) {
        setSuccessMessage(location.state.message);
        // Clear success message after 5 seconds
        setTimeout(() => setSuccessMessage(""), 5000);
      }

      // Clear the navigation state to prevent repeated refreshes
      window.history.replaceState({}, document.title);

      // Trigger a single refresh without causing loops
      loadAuctions(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Run only once on mount

  // WebSocket connection for real-time auction updates
  useEffect(() => {
    addDebugLog("🔄 WebSocket useEffect triggered", {
      loadAuctions: typeof loadAuctions,
      websocket: websocket ? "exists" : "null",
      timestamp: new Date().toISOString(),
    });

    // Check if WebSocket is available (ASGI server running)
    const isWebSocketEnabled =
      process.env.REACT_APP_WEBSOCKET_ENABLED === "true";
    const isPollingEnabled = process.env.REACT_APP_POLLING_ENABLED === "true";

    addDebugLog("🔧 WebSocket Configuration", {
      isWebSocketEnabled,
      isPollingEnabled,
      nodeEnv: process.env.NODE_ENV,
    });

    addDebugLog("🔍 Raw Environment Variables", {
      REACT_APP_WEBSOCKET_ENABLED: process.env.REACT_APP_WEBSOCKET_ENABLED,
      REACT_APP_POLLING_ENABLED: process.env.REACT_APP_POLLING_ENABLED,
      REACT_APP_DEBUG_ENV: process.env.REACT_APP_DEBUG_ENV,
    });

    if (!isWebSocketEnabled && !isPollingEnabled) {
      addDebugLog(
        "🔇 Both WebSocket and polling disabled - manual refresh only"
      );
      return;
    }

    if (!isWebSocketEnabled && isPollingEnabled) {
      addDebugLog("📡 WebSocket disabled - using polling for updates");
      // Use polling instead of WebSocket (less frequent to avoid refresh issues)
      const pollInterval = setInterval(() => {
        addDebugLog("🔄 Polling for auction updates...");
        // Only poll if component is still mounted and user is on auctions page
        if (location.pathname === "/auctions") {
          loadAuctions(false); // Don't force refresh, just update data
        }
      }, 300000); // Poll every 5 minutes (reduced frequency)

      return () => clearInterval(pollInterval);
    }

    // Only proceed with WebSocket if explicitly enabled
    if (!isWebSocketEnabled) {
      addDebugLog("🚫 WebSocket disabled - skipping connection");
      return;
    }

    // WebSocket enabled for both development and production
    addDebugLog("🔧 WebSocket enabled for " + process.env.NODE_ENV + " mode");

    // Check if WebSocket already exists to prevent multiple connections
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      addDebugLog("🔗 WebSocket already connected, skipping new connection");
      return;
    }

    const connectWebSocket = () => {
      try {
        // Close existing WebSocket if any
        if (websocket) {
          addDebugLog("🔌 Closing existing WebSocket connection");
          websocket.close();
          setWebsocket(null);
        }

        const token =
          localStorage.getItem("token") || localStorage.getItem("access_token");
        // Use correct WebSocket URL for development and production
        const wsBaseUrl = process.env.REACT_APP_WS_URL || "ws://127.0.0.1:8001";
        const wsUrl = token
          ? `${wsBaseUrl}/ws/auctions/?token=${token}`
          : `${wsBaseUrl}/ws/auctions/`;

        addDebugLog(
          "🔌 Attempting WebSocket connection to:",
          wsUrl.replace(token || "", "TOKEN_HIDDEN")
        );

        const ws = new WebSocket(wsUrl);

        ws.onopen = () => {
          addDebugLog("✅ Connected to auctions WebSocket successfully");
          setWebsocket(ws);
        };

        ws.onmessage = (event) => {
          const data = JSON.parse(event.data);
          addDebugLog("📨 Received WebSocket message", { type: data.type });

          if (data.type === "auction_created") {
            addDebugLog("🆕 New auction created via WebSocket");
            // Add the new auction to the existing list instead of reloading
            if (data.auction) {
              console.log("WebSocket auction data:", data.auction);
              console.log("WebSocket auction ID:", data.auction.id);

              // Check if auction already exists to prevent duplicates
              setAllAuctions((prev) => {
                const exists = prev.some(
                  (auction) => auction.id === data.auction.id
                );
                if (exists) {
                  console.log("⚠️ Auction already exists, skipping duplicate");
                  return prev;
                }
                console.log("✅ Adding new auction to list");
                const newList = [data.auction, ...prev];
                return removeDuplicateAuctions(newList);
              });

              setFilteredAuctions((prev) => {
                const exists = prev.some(
                  (auction) => auction.id === data.auction.id
                );
                if (exists) {
                  return prev;
                }
                const newList = [data.auction, ...prev];
                return removeDuplicateAuctions(newList);
              });

              setSuccessMessage(
                `New auction "${data.auction.title}" has been added!`
              );
              setTimeout(() => setSuccessMessage(""), 5000);
            }
          } else if (data.type === "auction_updated") {
            addDebugLog("🔄 Auction updated via WebSocket");
            // Update the specific auction instead of reloading all
            if (data.auction) {
              setAllAuctions((prev) =>
                prev.map((auction) =>
                  auction.id === data.auction.id ? data.auction : auction
                )
              );
              setFilteredAuctions((prev) =>
                prev.map((auction) =>
                  auction.id === data.auction.id ? data.auction : auction
                )
              );
            }
          }
        };

        ws.onclose = (event) => {
          addDebugLog("❌ Disconnected from auctions WebSocket", {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean,
          });
          setWebsocket(null);
          // Don't attempt to reconnect if WebSocket is disabled
          if (isWebSocketEnabled) {
            addDebugLog("🔄 Attempting to reconnect WebSocket in 5 seconds...");
            setTimeout(connectWebSocket, 5000);
          }
        };

        ws.onerror = (error) => {
          addDebugLog("❌ WebSocket error", {
            error: error.toString(),
            readyState: ws.readyState,
          });
          // Don't create additional polling intervals here
        };
      } catch (error) {
        console.error("Failed to connect to WebSocket:", error);
        // Don't create additional polling intervals here
      }
    };

    connectWebSocket();

    // Cleanup on unmount
    return () => {
      addDebugLog("🧹 Cleaning up WebSocket connection");
      if (websocket) {
        addDebugLog("🔌 Closing WebSocket in cleanup");
        websocket.close();
        setWebsocket(null);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // WebSocket useEffect dependencies - run only once

  // State to track if we're showing search results or category filtered results
  const [isShowingSearchResults, setIsShowingSearchResults] = useState(false);

  const handleSearchResults = useCallback(
    (searchResults) => {
      // If we have search results from the advanced search
      if (
        searchResults &&
        Array.isArray(searchResults) &&
        searchResults.length > 0
      ) {
        let filtered = [...searchResults];

        // Apply category filter from URL parameter if set (enhanced matching)
        if (selectedCategory) {
          filtered = filtered.filter((auction) =>
            matchesCategory(auction.category, selectedCategory)
          );
        }

        setFilteredAuctions(filtered);
        setIsShowingSearchResults(true);
        setCurrentPage(1);
      } else {
        // If no search results or empty search, fall back to category filtering
        setIsShowingSearchResults(false);
        // The useEffect below will handle the filtering
      }
    },
    [selectedCategory]
  );

  // Handle search results from Home page navigation
  useEffect(() => {
    if (fromSearch && navigationSearchResults) {
      setFilteredAuctions(navigationSearchResults);
      setIsShowingSearchResults(true);
      setCurrentPage(1);

      // Clear the navigation state to prevent re-triggering
      navigate(location.pathname, { replace: true, state: {} });
    }
  }, [fromSearch, navigationSearchResults, navigate, location.pathname]);

  // Initialize filtered auctions when allAuctions or selectedCategory changes
  // Only run this when not showing search results
  useEffect(() => {
    console.log("🔄 Filtering effect triggered:", {
      isShowingSearchResults,
      selectedCategory,
      allAuctionsLength: allAuctions.length,
      timestamp: new Date().toISOString(),
    });

    if (!isShowingSearchResults) {
      let filtered = [...allAuctions];

      if (selectedCategory) {
        console.log(`🔍 Filtering auctions for category: ${selectedCategory}`);
        console.log(
          `📦 Total auctions before filtering: ${allAuctions.length}`
        );

        // Show all available categories for debugging
        const availableCategories = [
          ...new Set(allAuctions.map((a) => a.category)),
        ];
        console.log(`📋 Available categories:`, availableCategories);

        // Show exact category matching
        console.log(`🎯 Looking for exact match: '${selectedCategory}'`);
        const exactMatches = allAuctions.filter(
          (a) => a.category === selectedCategory
        );
        console.log(`🎯 Exact matches found: ${exactMatches.length}`);

        // Show case-insensitive matching for debugging
        const caseInsensitiveMatches = allAuctions.filter(
          (a) =>
            a.category &&
            a.category.toLowerCase() === selectedCategory.toLowerCase()
        );
        console.log(
          `🎯 Case-insensitive matches: ${caseInsensitiveMatches.length}`
        );

        // Use enhanced category matching to handle special cases and case-insensitive filtering
        filtered = filtered.filter((auction) =>
          matchesCategory(auction.category, selectedCategory)
        );

        console.log(`✅ Final filtered auctions count: ${filtered.length}`);
        console.log(
          `📋 Filtered auctions:`,
          filtered.map((a) => ({
            id: a.id,
            title: a.title,
            category: a.category,
          }))
        );

        if (filtered.length === 0) {
          console.warn("⚠️ No auctions found for category:", selectedCategory);
          console.warn("⚠️ This might indicate a category mismatch issue");
        }
      } else {
        console.log("📋 No category selected, showing all auctions");
      }

      console.log(`🎯 Setting filtered auctions: ${filtered.length} items`);
      setFilteredAuctions(filtered);
      setCurrentPage(1);
    } else {
      console.log("🔍 Showing search results, skipping category filtering");
    }
  }, [allAuctions, selectedCategory, isShowingSearchResults]);

  const handleWatchToggle = async (auctionId) => {
    // Check if user is logged in first
    const token =
      localStorage.getItem("token") || localStorage.getItem("access_token");
    if (!user || !token) {
      alert("Please log in to manage your watchlist");
      return;
    }

    try {
      const isWatched = watchlist.some((item) => item.auction === auctionId);

      if (isWatched) {
        // Remove from watchlist
        const watchItem = watchlist.find((item) => item.auction === auctionId);
        await axiosInstance.delete(`watchlist/${watchItem.id}/`);
        setWatchlist((prev) =>
          prev.filter((item) => item.auction !== auctionId)
        );
      } else {
        // Add to watchlist
        const response = await axiosInstance.post("watchlist/", {
          auction: auctionId,
        });
        setWatchlist((prev) => [...prev, response.data]);
      }
    } catch (error) {
      console.error("Error toggling watchlist:", error);
      if (error.response?.status === 401) {
        alert("Please log in to manage your watchlist");
      } else {
        alert("Error updating watchlist. Please try again.");
      }
    }
  };

  // Navigation functions
  const handleBackToHome = () => {
    navigate("/home");
  };

  const handleViewAllAuctions = () => {
    navigate("/auctions");
  };

  // Ensure filteredAuctions is always an array before using slice
  const safeFilteredAuctions = Array.isArray(filteredAuctions)
    ? filteredAuctions
    : [];

  const auctionsToDisplay = safeFilteredAuctions.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const totalPages = Math.ceil(safeFilteredAuctions.length / ITEMS_PER_PAGE);

  if (loading) {
    return (
      <Container className="mt-5">
        <div className="text-center">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </Container>
    );
  }

  return (
    <Container className="mt-4">
      {/* Breadcrumb Navigation */}
      {(selectedCategory || categoryInfo) && (
        <Row className="mb-3">
          <Col>
            <Breadcrumb className="category-breadcrumb">
              <Breadcrumb.Item
                onClick={handleBackToHome}
                style={{ cursor: "pointer" }}
              >
                <FaHome className="me-1" />
                Home
              </Breadcrumb.Item>
              <Breadcrumb.Item
                onClick={handleViewAllAuctions}
                style={{ cursor: "pointer" }}
              >
                <FaGavel className="me-1" />
                All Auctions
              </Breadcrumb.Item>
              <Breadcrumb.Item active>
                <FaTags className="me-1" />
                {categoryInfo?.name || selectedCategory}
              </Breadcrumb.Item>
            </Breadcrumb>
          </Col>
        </Row>
      )}

      {/* Category Header */}
      <Row>
        <Col>
          {selectedCategory ? (
            <div className="category-header text-center">
              <h2 className="mb-3">
                <FaTags className="me-2 text-primary" />
                {categoryInfo?.name || selectedCategory} Auctions
              </h2>
              <div className="d-flex justify-content-center category-navigation-buttons">
                <Button
                  variant="outline-primary"
                  size="sm"
                  onClick={handleBackToHome}
                >
                  <FaHome className="me-1" />
                  Back to Home
                </Button>
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={handleViewAllAuctions}
                >
                  <FaGavel className="me-1" />
                  View All Auctions
                </Button>
              </div>
            </div>
          ) : (
            <h2 className="text-center mb-4">Live Auctions</h2>
          )}
          {error && <Alert variant="danger">{error}</Alert>}
          {successMessage && (
            <Alert
              variant="success"
              dismissible
              onClose={() => setSuccessMessage("")}
            >
              {successMessage}
            </Alert>
          )}
        </Col>
      </Row>

      {/* Debug Panel - Only for Admin Users */}
      {user && user.role === "admin" && showDebug && (
        <Row className="mb-3">
          <Col>
            <div className="card">
              <div className="card-header d-flex justify-content-between align-items-center">
                <h6 className="mb-0">🔍 WebSocket Debug Logs (Admin Only)</h6>
                <div>
                  <button
                    className="btn btn-sm btn-outline-secondary me-2"
                    onClick={() => setDebugLogs([])}
                  >
                    Clear
                  </button>
                  <button
                    className="btn btn-sm btn-outline-primary"
                    onClick={() => setShowDebug(false)}
                  >
                    Hide
                  </button>
                </div>
              </div>
              <div
                className="card-body"
                style={{ maxHeight: "300px", overflowY: "auto" }}
              >
                {debugLogs.length === 0 ? (
                  <p className="text-muted mb-0">No debug logs yet...</p>
                ) : (
                  debugLogs.map((log) => (
                    <div key={log.id} className="mb-2 p-2 border-bottom">
                      <div className="d-flex justify-content-between">
                        <strong className="text-primary">{log.message}</strong>
                        <small className="text-muted">{log.timestamp}</small>
                      </div>
                      {log.data && (
                        <pre
                          className="mt-1 mb-0"
                          style={{
                            fontSize: "0.8rem",
                            background: "#f8f9fa",
                            padding: "0.5rem",
                            borderRadius: "0.25rem",
                          }}
                        >
                          {log.data}
                        </pre>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </Col>
        </Row>
      )}

      {user && user.role === "admin" && !showDebug && (
        <Row className="mb-3">
          <Col>
            <button
              className="btn btn-sm btn-outline-info"
              onClick={() => setShowDebug(true)}
            >
              🔍 Show WebSocket Debug (Admin)
            </button>
          </Col>
        </Row>
      )}

      {/* Advanced Search */}
      <Row className="mb-4">
        <Col>
          <div className="new-advanced-filter-container">
            <NewAdvancedFilter
              onFilterResults={handleSearchResults}
              onClearResults={() => {
                setIsShowingSearchResults(false);
                setFilteredAuctions(allAuctions);
              }}
            />
          </div>
        </Col>
      </Row>

      {/* Results Summary and Refresh */}
      <Row className="mb-3">
        <Col>
          <div className="category-results-summary d-flex justify-content-between align-items-center">
            <p className="text-muted mb-0">
              {isShowingSearchResults ? (
                <>
                  <span className="badge bg-info me-2">Search Results</span>
                  Showing {auctionsToDisplay.length} of{" "}
                  {safeFilteredAuctions.length} search results
                  {selectedCategory && (
                    <>
                      {" "}
                      in{" "}
                      <strong>
                        {categoryInfo?.name || selectedCategory}
                      </strong>{" "}
                      category
                    </>
                  )}
                </>
              ) : selectedCategory ? (
                <>
                  Showing {auctionsToDisplay.length} of{" "}
                  {safeFilteredAuctions.length} auctions in{" "}
                  <strong>{categoryInfo?.name || selectedCategory}</strong>{" "}
                  category
                </>
              ) : (
                <>
                  Showing {auctionsToDisplay.length} of{" "}
                  {safeFilteredAuctions.length} auctions
                </>
              )}
            </p>
            <div className="d-flex gap-2">
              {isShowingSearchResults && (
                <button
                  className="btn btn-outline-secondary btn-sm"
                  onClick={() => {
                    setIsShowingSearchResults(false);
                    setCurrentPage(1);
                  }}
                >
                  ✕ Clear Search
                </button>
              )}
              <button
                className="btn btn-outline-primary btn-sm"
                onClick={() => loadAuctions(true)}
                disabled={loading || isLoadingAuctions}
              >
                {loading || isLoadingAuctions ? (
                  <>
                    <span
                      className="spinner-border spinner-border-sm me-2"
                      role="status"
                      aria-hidden="true"
                    ></span>
                    Refreshing...
                  </>
                ) : (
                  <>🔄 Refresh</>
                )}
              </button>
            </div>
          </div>
        </Col>
      </Row>

      {/* Auctions Grid */}
      <Row>
        {auctionsToDisplay.length === 0 ? (
          <Col>
            <div className="category-empty-state">
              <FaTags className="text-muted mb-3" size={48} />
              <h4>
                {selectedCategory
                  ? `No auctions found in ${
                      categoryInfo?.name || selectedCategory
                    } category`
                  : "No auctions found"}
              </h4>
              <p className="text-muted mb-4">
                {selectedCategory
                  ? "There are currently no active auctions in this category."
                  : "Try adjusting your filters or search terms."}
              </p>
              {selectedCategory && (
                <div className="d-flex justify-content-center category-navigation-buttons">
                  <Button variant="primary" onClick={handleViewAllAuctions}>
                    <FaGavel className="me-1" />
                    View All Auctions
                  </Button>
                  <Button variant="outline-primary" onClick={handleBackToHome}>
                    <FaHome className="me-1" />
                    Back to Home
                  </Button>
                </div>
              )}
            </div>
          </Col>
        ) : (
          auctionsToDisplay.map((auction, index) => (
            <Col
              md={4}
              lg={3}
              key={`auction-${auction.id}-${index}`}
              className="mb-4"
            >
              <AuctionCard
                auction={auction}
                onWatchToggle={handleWatchToggle}
                isWatched={watchlist.some(
                  (item) => item.auction === auction.id
                )}
              />
            </Col>
          ))
        )}
      </Row>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <Row>
          <Col>
            <div className="d-flex justify-content-center mt-4">
              <nav>
                <ul className="pagination">
                  <li
                    className={`page-item ${
                      currentPage === 1 ? "disabled" : ""
                    }`}
                  >
                    <button
                      className="page-link"
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </button>
                  </li>

                  {[...Array(totalPages)].map((_, index) => (
                    <li
                      key={index + 1}
                      className={`page-item ${
                        currentPage === index + 1 ? "active" : ""
                      }`}
                    >
                      <button
                        className="page-link"
                        onClick={() => setCurrentPage(index + 1)}
                      >
                        {index + 1}
                      </button>
                    </li>
                  ))}

                  <li
                    className={`page-item ${
                      currentPage === totalPages ? "disabled" : ""
                    }`}
                  >
                    <button
                      className="page-link"
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </Col>
        </Row>
      )}
    </Container>
  );
}

export default Auctions;
