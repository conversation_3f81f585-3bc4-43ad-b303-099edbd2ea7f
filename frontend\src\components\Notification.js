import React, { useEffect, useState } from "react";
import axios from "axios";
import { useAuth } from "../context/AuthContext";

function Notifications() {
  const [notifications, setNotifications] = useState([]);
  const { user } = useAuth();

  useEffect(() => {
    // Only fetch notifications if user is logged in
    if (!user) {
      return;
    }

    const token = localStorage.getItem("token"); // ✅ get JWT token

    // Only make the request if we have a token
    if (token) {
      axios
        .get("http://127.0.0.1:8000/api/notifications/", {
          headers: {
            Authorization: `Bearer ${token}`, // ✅ include it in header
          },
        })
        .then((res) => {
          // Handle paginated response from Django REST Framework
          const notificationsArray = res.data.results || res.data || [];
          setNotifications(
            Array.isArray(notificationsArray) ? notificationsArray : []
          );
        })
        .catch((err) => {
          console.error("Notification fetch failed:", err);
          // Clear notifications on error (e.g., token expired)
          setNotifications([]);
        });
    }
  }, [user]); // Re-run when user changes

  // Ensure notifications is always an array
  const safeNotifications = Array.isArray(notifications) ? notifications : [];

  // Only render if user is logged in and has notifications
  if (!user || safeNotifications.length === 0) {
    return null;
  }

  return (
    <div className="toast-container position-fixed top-0 end-0 p-3">
      {safeNotifications.map((note) => (
        <div key={note.id} className="toast show">
          <div className="toast-header">
            <strong className="me-auto">Notification</strong>
          </div>
          <div className="toast-body">{note.message}</div>
        </div>
      ))}
    </div>
  );
}

export default Notifications;
