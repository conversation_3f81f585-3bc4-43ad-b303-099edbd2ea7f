@echo off
setlocal enabledelayedexpansion

echo ========================================
echo   🚀 Online Auction System Launcher
echo ========================================
echo.
echo Starting all services with one command...
echo.

REM Get the directory where this batch file is located
set "PROJECT_DIR=%~dp0"
cd /d "%PROJECT_DIR%"

echo 📋 System Check:
echo ✅ Project Directory: %PROJECT_DIR%
echo.

REM Step 1: Check and Start Redis Server
echo 🔴 Step 1: Starting Redis Server...
tasklist /FI "IMAGENAME eq redis-server.exe" 2>NUL | find /I /N "redis-server.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Redis is already running
) else (
    echo 🔄 Starting Redis server...
    if exist "C:\Program Files\Redis\redis-server.exe" (
        start "Redis Server" "C:\Program Files\Redis\redis-server.exe"
        timeout /t 3 /nobreak >nul
        echo ✅ Redis server started
    ) else (
        echo ⚠️  Redis not found at default location, trying alternative...
        start "Redis Server" redis-server 2>nul
        timeout /t 2 /nobreak >nul
        echo ✅ Redis server started (alternative method)
    )
)
echo.

REM Step 2: Start Backend Django Server
echo 🟡 Step 2: Starting Backend (Django HTTP Server)...
cd /d "%PROJECT_DIR%backend"
if exist "venv\Scripts\activate.bat" (
    echo 🔄 Starting Django server on port 8000...
    start "Django Backend" cmd /k "title Django Backend & venv\Scripts\activate.bat & echo ✅ Django Backend Starting... & python manage.py runserver 127.0.0.1:8000"
    echo ✅ Django backend server initiated
) else (
    echo ❌ Virtual environment not found! Please run setup first.
    pause
    exit /b 1
)
timeout /t 3 /nobreak >nul
echo.

REM Step 3: Start WebSocket Server (Daphne)
echo 🟢 Step 3: Starting WebSocket Server (Daphne)...
echo 🔄 Starting Daphne server on port 8001...
start "WebSocket Server" cmd /k "title WebSocket Server & cd /d \"%PROJECT_DIR%backend\" & venv\Scripts\activate.bat & echo ✅ WebSocket Server Starting... & daphne -b 127.0.0.1 -p 8001 OnlineAuctionSystem.asgi:application"
echo ✅ WebSocket server initiated
timeout /t 3 /nobreak >nul
echo.

REM Step 4: Start Frontend React Server
echo 🔵 Step 4: Starting Frontend (React Development Server)...
cd /d "%PROJECT_DIR%frontend"
if exist "package.json" (
    echo 🔄 Starting React development server on port 3001...
    start "React Frontend" cmd /k "title React Frontend & echo ✅ React Frontend Starting... & npm start"
    echo ✅ React frontend server initiated
) else (
    echo ❌ Frontend package.json not found!
    pause
    exit /b 1
)
echo.

REM Step 5: Wait for servers to start
echo ⏳ Step 5: Waiting for all servers to initialize...
echo.
echo Please wait while all services start up...
timeout /t 8 /nobreak >nul

REM Step 6: Display status and URLs
echo.
echo ========================================
echo   🎉 All Services Started Successfully!
echo ========================================
echo.
echo 📊 Service Status:
echo ✅ Redis Cache:        Running
echo ✅ Django Backend:     http://127.0.0.1:8000
echo ✅ WebSocket Server:   ws://127.0.0.1:8001  
echo ✅ React Frontend:     http://localhost:3001
echo.
echo 🌐 Access Points:
echo 📱 Main Application:   http://localhost:3001
echo 🔧 Admin Panel:        http://127.0.0.1:8000/admin
echo 📡 API Endpoints:      http://127.0.0.1:8000/api/
echo.
echo 💡 Tips:
echo • Frontend will auto-open in browser (may take 30-60 seconds)
echo • If WebSocket issues occur, clear browser cache (Ctrl+Shift+Delete)
echo • Use Ctrl+C in any terminal window to stop that service
echo • Close all terminal windows to stop all services
echo.
echo 🔍 Troubleshooting:
echo • If Redis fails: Install Redis or check PATH environment
echo • If Backend fails: Check virtual environment setup
echo • If Frontend fails: Run 'npm install' in frontend folder
echo • If WebSocket fails: Check if port 8001 is available
echo.

REM Step 7: Open browser automatically
echo 🌐 Opening application in browser...
timeout /t 5 /nobreak >nul
start http://localhost:3001

echo.
echo ========================================
echo   System is now running!
echo   Press any key to exit this launcher.
echo ========================================
pause >nul
