"""
AI Chat Response Service for Auction System
Provides intelligent responses to user questions about auctions
"""

import logging
import re
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple

from django.utils import timezone
from django.db.models import Q, Count, Avg

from .models import Auction, Bid, User, Category
from .ai_services import PricePredictionService

logger = logging.getLogger(__name__)


class AIChatResponseService:
    """AI-powered chat response service for auction questions"""

    def __init__(self):
        self.price_service = PricePredictionService()
        self.response_patterns = self._initialize_patterns()

    def _initialize_patterns(self):
        """Initialize response patterns for different question types"""
        return {
            'greetings': [
                r'(?i)^(hi|hello|hey|good morning|good afternoon|good evening|greetings|howdy).*',
                r'(?i).*(hi there|hello there|hey there).*',
                r'(?i)^(what\'s up|whats up|sup|yo).*',
                r'(?i).*(nice to meet|pleasure to meet).*'
            ],
            'thanks': [
                r'(?i).*(thank you|thanks|thx|appreciate|grateful).*',
                r'(?i).*(you\'re helpful|very helpful|great help).*',
                r'(?i).*(awesome|excellent|perfect|wonderful).*'
            ],
            'price_inquiry': [
                r'(?i).*(?:price|cost|worth|value|estimate).*',
                r'(?i).*(?:how much|what.*cost|price range).*',
                r'(?i).*(?:final price|end price|selling price).*',
                r'(?i).*(?:current bid|highest bid|leading bid).*'
            ],
            'bidding_help': [
                r'(?i).*(?:how to bid|bidding process|place bid).*',
                r'(?i).*(?:minimum bid|bid increment|next bid).*',
                r'(?i).*(?:winning|win|highest bid).*',
                r'(?i).*(?:bid strategy|bidding tips|how do i bid).*'
            ],
            'auction_info': [
                r'(?i).*(?:when.*end|time left|duration|deadline).*',
                r'(?i).*(?:condition|description|details|specifications).*',
                r'(?i).*(?:seller|owner|who.*selling).*',
                r'(?i).*(?:category|type|what is this).*'
            ],
            'payment_shipping': [
                r'(?i).*(?:payment|pay|shipping|delivery|pickup).*',
                r'(?i).*(?:accepted.*payment|payment methods).*',
                r'(?i).*(?:ship|deliver|location|where).*'
            ],
            'general_help': [
                r'(?i).*(?:help|how|what|explain|guide).*',
                r'(?i).*(?:new|first time|beginner).*',
                r'(?i).*(?:rules|policy|terms).*'
            ]
        }

    async def generate_response(self, message: str, auction_id: int, user: User) -> Optional[str]:
        """
        Generate AI response based on user message and auction context
        """
        try:
            # Get auction context
            auction = await self._get_auction_context(auction_id)
            if not auction:
                return "I'm sorry, I couldn't find information about this auction."

            # Analyze message intent
            intent = self._analyze_intent(message)
            
            # Generate appropriate response
            response = await self._generate_contextual_response(
                message, intent, auction, user
            )
            
            return response

        except Exception as e:
            logger.error(f"Error generating AI response: {str(e)}")
            return "I'm sorry, I'm having trouble processing your question right now. Please try again later."

    def _analyze_intent(self, message: str) -> str:
        """Analyze user message to determine intent"""
        message_lower = message.lower()
        
        # Check for specific patterns
        for intent, patterns in self.response_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message):
                    return intent
        
        # Default to general help
        return 'general_help'

    async def _get_auction_context(self, auction_id: int) -> Optional[Auction]:
        """Get auction context for response generation"""
        try:
            from asgiref.sync import sync_to_async

            @sync_to_async
            def get_auction():
                return Auction.objects.select_related('owner').get(id=auction_id)

            return await get_auction()
        except Auction.DoesNotExist:
            return None

    async def _generate_contextual_response(
        self, message: str, intent: str, auction: Auction, user: User
    ) -> str:
        """Generate response based on intent and context"""

        if intent == 'greetings':
            return await self._handle_greetings(auction, user, message)

        elif intent == 'thanks':
            return await self._handle_thanks(auction, user, message)

        elif intent == 'price_inquiry':
            return await self._handle_price_inquiry(auction, message)

        elif intent == 'bidding_help':
            return await self._handle_bidding_help(auction, user, message)

        elif intent == 'auction_info':
            return await self._handle_auction_info(auction, message)

        elif intent == 'payment_shipping':
            return await self._handle_payment_shipping(auction, message)

        elif intent == 'help_requests':
            return await self._handle_help_requests(auction, user, message)

        elif intent == 'farewell':
            return await self._handle_farewell(auction, user, message)

        else:  # general_help
            return await self._handle_general_help(auction, message)

    async def _handle_greetings(self, auction: Auction, user: User, message: str) -> str:
        """Handle greeting messages with personalized responses"""
        message_lower = message.lower()

        # Time-based greetings
        current_hour = timezone.now().hour
        if current_hour < 12:
            time_greeting = "Good morning"
        elif current_hour < 17:
            time_greeting = "Good afternoon"
        else:
            time_greeting = "Good evening"

        # Personalized greeting based on message
        if 'good morning' in message_lower:
            greeting = "🌅 Good morning"
        elif 'good afternoon' in message_lower:
            greeting = "☀️ Good afternoon"
        elif 'good evening' in message_lower:
            greeting = "🌆 Good evening"
        elif any(casual in message_lower for casual in ['hey', 'yo', 'sup', 'what\'s up']):
            greeting = "👋 Hey there"
        else:
            greeting = f"👋 {time_greeting}"

        response = f"{greeting}, **{user.username}**! Welcome to the auction chat! 🎉\n\n"
        response += f"I'm your AI assistant for **{auction.title}**\n\n"

        # Add auction status
        if auction.is_active:
            response += f"🔥 **This auction is currently LIVE!**\n"
            response += f"💰 Current bid: ${auction.current_bid or auction.starting_bid:,.2f}\n"
            response += f"⏰ Time remaining: {self._get_time_remaining(auction)}\n\n"
        else:
            response += f"⏰ This auction has ended.\n\n"

        response += "I'm here to help you with:\n"
        response += "• 💰 Price predictions and analysis\n"
        response += "• 🎯 Bidding strategies and tips\n"
        response += "• ℹ️ Auction details and information\n"
        response += "• 💳 Payment and shipping questions\n\n"
        response += "Just ask me anything! 😊"

        return response

    async def _handle_thanks(self, auction: Auction, user: User, message: str) -> str:
        """Handle thank you messages"""
        message_lower = message.lower()

        # Varied thank you responses
        if 'very helpful' in message_lower or 'great help' in message_lower:
            response = "🌟 **I'm so glad I could help you!** That's what I'm here for!\n\n"
        elif 'awesome' in message_lower or 'excellent' in message_lower:
            response = "😊 **Thank you so much!** Your kind words mean a lot!\n\n"
        elif 'appreciate' in message_lower:
            response = "🙏 **You're very welcome!** I appreciate you too!\n\n"
        else:
            response = "😊 **You're absolutely welcome!** Happy to help!\n\n"

        response += "Feel free to ask me anything else about this auction. "
        response += "I'm always here to assist you! 💪\n\n"

        if auction.is_active:
            response += f"🔥 Don't forget - this auction is still live with {self._get_time_remaining(auction)} remaining!"

        return response

    async def _handle_help_requests(self, auction: Auction, user: User, message: str) -> str:
        """Handle general help requests"""
        response = f"🤖 **I'm here to help, {user.username}!**\n\n"

        response += f"For **{auction.title}**, I can assist you with:\n\n"
        response += "🔍 **Quick Commands:**\n"
        response += "• Type **'price'** - Get price analysis and predictions\n"
        response += "• Type **'bid help'** - Learn how to bid effectively\n"
        response += "• Type **'info'** - Get detailed auction information\n"
        response += "• Type **'payment'** - Payment and shipping details\n\n"

        response += "💬 **Or just ask naturally:**\n"
        response += "• \"What's the current price?\"\n"
        response += "• \"How do I place a bid?\"\n"
        response += "• \"When does this auction end?\"\n"
        response += "• \"What payment methods are accepted?\"\n\n"

        response += "I understand natural language, so feel free to ask me anything! 🧠✨"

        return response

    async def _handle_farewell(self, auction: Auction, user: User, message: str) -> str:
        """Handle farewell messages"""
        message_lower = message.lower()

        if 'good' in message_lower:
            response = "👋 **Have a wonderful day, {}!**\n\n".format(user.username)
        elif 'take care' in message_lower:
            response = "🤗 **You take care too, {}!**\n\n".format(user.username)
        else:
            response = "👋 **Goodbye, {}!** Thanks for chatting!\n\n".format(user.username)

        response += "Feel free to come back anytime if you have more questions about this auction. "
        response += "Good luck with your bidding! 🍀\n\n"

        if auction.is_active:
            response += f"⏰ **Reminder:** This auction ends in {self._get_time_remaining(auction)}!"

        return response

    async def _handle_price_inquiry(self, auction: Auction, message: str) -> str:
        """Handle price-related questions"""
        try:
            # Try to get AI price prediction
            try:
                predicted_price, confidence, features = self.price_service.predict_price(auction)

                current_bid = auction.current_bid or auction.starting_bid

                response = f"💰 **Price Analysis for {auction.title}**\n\n"
                response += f"• **Current Bid:** ${current_bid:,.2f}\n"
                response += f"• **AI Predicted Final Price:** ${predicted_price:,.2f}\n"
                response += f"• **Confidence Level:** {confidence*100:.1f}%\n\n"

                if predicted_price > current_bid:
                    increase = ((predicted_price - current_bid) / current_bid) * 100
                    response += f"📈 The AI predicts this auction could increase by approximately {increase:.1f}% from the current bid.\n\n"

                response += f"⏰ **Time Remaining:** {self._get_time_remaining(auction)}\n"
                response += f"📊 **Total Bids:** {auction.total_bids}\n\n"
                response += "💡 *This is an AI prediction based on historical data and auction characteristics. Actual results may vary.*"

                return response

            except Exception as pred_error:
                logger.warning(f"Price prediction failed: {str(pred_error)}")
                # Fallback to basic price information
                current_bid = auction.current_bid or auction.starting_bid

                response = f"💰 **Price Information for {auction.title}**\n\n"
                response += f"• **Current Bid:** ${current_bid:,.2f}\n"
                response += f"• **Starting Bid:** ${auction.starting_bid:,.2f}\n"
                response += f"• **Total Bids:** {auction.total_bids}\n"
                response += f"⏰ **Time Remaining:** {self._get_time_remaining(auction)}\n\n"

                # Add some basic price guidance
                if auction.total_bids > 5:
                    response += "📈 This auction has good bidding activity! The final price could be higher.\n"
                elif auction.total_bids == 0:
                    response += "🎯 No bids yet - great opportunity to get a good deal!\n"
                else:
                    response += "📊 Moderate bidding activity so far.\n"

                response += "\n💡 *For detailed AI price predictions, please check the auction details page.*"
                return response

        except Exception as e:
            logger.error(f"Error in price inquiry: {str(e)}")
            current_bid = auction.current_bid or auction.starting_bid
            return f"💰 The current bid for this auction is ${current_bid:,.2f}. You can find more detailed price information on the auction details page."

    async def _handle_bidding_help(self, auction: Auction, user: User, message: str) -> str:
        """Handle bidding-related questions"""
        current_bid = auction.current_bid or auction.starting_bid
        min_next_bid = current_bid + Decimal('10.00')  # Minimum increment
        
        response = f"🎯 **Bidding Information for {auction.title}**\n\n"
        
        if auction.is_active:
            response += f"• **Current Highest Bid:** ${current_bid:,.2f}\n"
            response += f"• **Minimum Next Bid:** ${min_next_bid:,.2f}\n"
            response += f"• **Time Remaining:** {self._get_time_remaining(auction)}\n\n"
            
            response += "📝 **How to Bid:**\n"
            response += "1. Enter your bid amount (must be higher than current bid)\n"
            response += "2. Click 'Place Bid' button\n"
            response += "3. You'll receive instant confirmation\n"
            response += "4. Watch for real-time updates from other bidders\n\n"
            
            response += "💡 **Bidding Tips:**\n"
            response += "• Bid strategically - don't reveal your maximum early\n"
            response += "• Watch the auction closely in final minutes\n"
            response += "• Consider the AI price prediction for guidance\n"
            response += "• Remember: highest bid when auction ends wins!"
            
        else:
            response += "⏰ This auction has ended.\n"
            if auction.winner:
                response += f"🏆 **Winner:** {auction.winner.username}\n"
                response += f"💰 **Final Price:** ${auction.current_bid:,.2f}"
            else:
                response += "No bids were placed on this auction."
        
        return response

    async def _handle_auction_info(self, auction: Auction, message: str) -> str:
        """Handle auction information questions"""
        response = f"ℹ️ **Auction Information: {auction.title}**\n\n"
        
        # Time information
        if 'time' in message.lower() or 'end' in message.lower():
            response += f"⏰ **Timing:**\n"
            response += f"• Started: {auction.start_time.strftime('%B %d, %Y at %I:%M %p')}\n"
            response += f"• Ends: {auction.end_time.strftime('%B %d, %Y at %I:%M %p')}\n"
            response += f"• Time Remaining: {self._get_time_remaining(auction)}\n\n"
        
        # Seller information
        if 'seller' in message.lower() or 'owner' in message.lower():
            response += f"👤 **Seller:** {auction.seller.username}\n"
            response += f"📅 **Member Since:** {auction.seller.date_joined.strftime('%B %Y')}\n\n"
        
        # Condition and details
        if 'condition' in message.lower() or 'detail' in message.lower():
            response += f"📋 **Details:**\n"
            response += f"• Category: {auction.category.name}\n"
            if hasattr(auction, 'condition'):
                response += f"• Condition: {auction.condition}\n"
            response += f"• Description: {auction.description[:200]}{'...' if len(auction.description) > 200 else ''}\n\n"
        
        response += f"📊 **Auction Stats:**\n"
        response += f"• Starting Bid: ${auction.starting_bid:,.2f}\n"
        response += f"• Current Bid: ${auction.current_bid or auction.starting_bid:,.2f}\n"
        response += f"• Total Bids: {auction.total_bids}\n"
        response += f"• Watchers: {getattr(auction, 'watchers_count', 'N/A')}"
        
        return response

    async def _handle_payment_shipping(self, auction: Auction, message: str) -> str:
        """Handle payment and shipping questions"""
        response = f"💳 **Payment & Shipping Information**\n\n"
        
        response += "💰 **Payment Process:**\n"
        response += "• Winner has 24 hours to complete payment\n"
        response += "• Secure payment via Stripe (Credit/Debit cards)\n"
        response += "• Payment confirmation sent via email\n"
        response += "• No payment = auction re-listed automatically\n\n"
        
        response += "📦 **Shipping & Delivery:**\n"
        response += "• Shipping details provided by seller\n"
        response += "• Pickup options may be available\n"
        response += "• Contact seller after winning for arrangements\n"
        response += "• Shipping costs typically paid by buyer\n\n"
        
        response += "🔒 **Security:**\n"
        response += "• All payments processed securely via Stripe\n"
        response += "• Buyer protection policies apply\n"
        response += "• Report any issues to admin immediately"
        
        return response

    async def _handle_general_help(self, auction: Auction, message: str) -> str:
        """Handle general help questions"""
        # Check for specific greetings or common phrases
        message_lower = message.lower()

        if any(greeting in message_lower for greeting in ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening']):
            response = f"👋 **Hello! Welcome to the auction chat!**\n\n"
            response += f"I'm your AI assistant for **{auction.title}**\n\n"
        elif any(thanks in message_lower for thanks in ['thank', 'thanks', 'appreciate']):
            response = f"😊 **You're welcome!**\n\n"
            response += "Happy to help with your auction questions!\n\n"
        else:
            response = f"🤖 **AuctionStore AI Assistant**\n\n"

        response += "I can help you with:\n\n"
        response += "💰 **Price Questions:** 'What's the predicted price?' or 'Price analysis'\n"
        response += "🎯 **Bidding Help:** 'How to bid?' or 'Bidding tips'\n"
        response += "ℹ️ **Auction Info:** 'When does it end?' or 'Seller info'\n"
        response += "💳 **Payment & Shipping:** 'Payment methods?' or 'Shipping info'\n\n"

        response += "**Quick Commands:**\n"
        response += "• Type 'price' for price analysis\n"
        response += "• Type 'help bid' for bidding assistance\n"
        response += "• Type 'info' for auction details\n"
        response += "• Type 'payment' for payment information\n\n"

        response += f"**Current Auction:** {auction.title}\n"
        response += f"**Current Bid:** ${auction.current_bid or auction.starting_bid:,.2f}\n"
        response += f"**Time Left:** {self._get_time_remaining(auction)}\n\n"

        if auction.is_active:
            response += "🔥 **This auction is live!** Place your bid now!\n\n"
        else:
            response += "⏰ **This auction has ended.**\n\n"

        response += "💡 *Just ask me anything about this auction!*"

        return response

    def _get_time_remaining(self, auction: Auction) -> str:
        """Get formatted time remaining for auction"""
        if not auction.is_active:
            return "Auction has ended"
        
        now = timezone.now()
        time_left = auction.end_time - now
        
        if time_left.days > 0:
            return f"{time_left.days} days, {time_left.seconds // 3600} hours"
        elif time_left.seconds > 3600:
            hours = time_left.seconds // 3600
            minutes = (time_left.seconds % 3600) // 60
            return f"{hours} hours, {minutes} minutes"
        elif time_left.seconds > 60:
            minutes = time_left.seconds // 60
            return f"{minutes} minutes"
        else:
            return f"{time_left.seconds} seconds"

    def should_respond(self, message: str) -> bool:
        """Determine if AI should respond to this message"""
        message_lower = message.lower().strip()

        # Check against all pattern categories
        for intent, patterns in self.response_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message):
                    return True

        # Additional checks for common interactions
        question_indicators = ['?', 'what', 'how', 'when', 'where', 'why', 'who', 'which']
        has_question = any(indicator in message_lower for indicator in question_indicators)

        # Quick commands
        quick_commands = ['price', 'bid help', 'info', 'payment', 'help']
        is_quick_command = message_lower in quick_commands

        # Single word responses that should get a reply
        single_word_responses = ['hi', 'hello', 'hey', 'thanks', 'thank', 'bye', 'goodbye', 'help']
        is_single_word = message_lower in single_word_responses

        return has_question or is_quick_command or is_single_word


# Global instance
ai_chat_service = AIChatResponseService()
