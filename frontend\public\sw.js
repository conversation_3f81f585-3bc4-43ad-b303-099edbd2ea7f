const CACHE_NAME = "auction-app-v1.0.2";
const STATIC_CACHE = "auction-static-v1.2";
const DYNAMIC_CACHE = "auction-dynamic-v1.2";

// Files to cache immediately
const STATIC_FILES = [
  "/",
  "/static/js/bundle.js",
  "/static/css/main.css",
  "/manifest.json",
  "/android-chrome-192x192.png",
  "/android-chrome-512x512.png",
  "/logo192.png",
  "/logo512.png",
  "/favicon.ico",
  "/placeholder-image.svg",
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /\/api\/auctions\//,
  /\/api\/categories\//,
  /\/api\/search\//,
];

// Install event - cache static files
self.addEventListener("install", (event) => {
  console.log("Service Worker: Installing...");
  event.waitUntil(
    caches
      .open(STATIC_CACHE)
      .then((cache) => {
        console.log("Service Worker: Caching static files");
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log("Service Worker: Static files cached");
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error("Service Worker: Cache failed", error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener("activate", (event) => {
  console.log("Service Worker: Activating...");
  event.waitUntil(
    caches
      .keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log("Service Worker: Deleting old cache", cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log("Service Worker: Activated");
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache or network
self.addEventListener("fetch", (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle API requests
  if (url.pathname.startsWith("/api/")) {
    event.respondWith(handleApiRequest(request));
    return;
  }

  // Handle static files
  if (request.destination === "document") {
    event.respondWith(handlePageRequest(request));
    return;
  }

  // Handle other resources
  event.respondWith(handleResourceRequest(request));
});

// Handle API requests with network-first strategy
async function handleApiRequest(request) {
  const cacheName = DYNAMIC_CACHE;

  try {
    // Try network first
    const networkResponse = await fetch(request);

    // Only cache GET requests (not POST, PUT, DELETE, etc.)
    if (networkResponse.ok && request.method === "GET") {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    // Only fallback to cache for GET requests
    if (request.method === "GET") {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
    }

    // Return offline response for API calls
    return new Response(
      JSON.stringify({
        error: "Offline",
        message: "You are currently offline. Please check your connection.",
      }),
      {
        status: 503,
        statusText: "Service Unavailable",
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

// Handle page requests with cache-first strategy
async function handlePageRequest(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);
    const cache = await caches.open(DYNAMIC_CACHE);
    cache.put(request, networkResponse.clone());

    return networkResponse;
  } catch (error) {
    // Return cached index.html for SPA routing
    return caches.match("/");
  }
}

// Handle resource requests
async function handleResourceRequest(request) {
  // Skip caching for chrome-extension and other unsupported schemes
  if (
    request.url.startsWith("chrome-extension://") ||
    request.url.startsWith("moz-extension://") ||
    request.url.startsWith("safari-extension://")
  ) {
    try {
      return await fetch(request);
    } catch (error) {
      return new Response("Extension resource unavailable", { status: 503 });
    }
  }

  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  try {
    const networkResponse = await fetch(request);
    // Only cache HTTP/HTTPS requests
    if (request.url.startsWith("http")) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    // Return placeholder for failed resources
    return new Response("Resource unavailable offline", { status: 503 });
  }
}

// Push notification event
self.addEventListener("push", (event) => {
  console.log("Service Worker: Push notification received");

  let notificationData = {
    title: "Auction Alert",
    body: "New auction update available!",
    icon: "/android-chrome-192x192.png",
    badge: "/android-chrome-192x192.png",
    tag: "auction-notification",
    requireInteraction: true,
  };

  if (event.data) {
    try {
      const data = event.data.json();
      notificationData = {
        ...notificationData,
        ...data,
        actions: [
          {
            action: "view",
            title: "View Auction",
            icon: "/android-chrome-192x192.png",
          },
          {
            action: "dismiss",
            title: "Dismiss",
            icon: "/android-chrome-192x192.png",
          },
        ],
      };
    } catch (error) {
      console.error("Error parsing push data:", error);
    }
  }

  event.waitUntil(
    self.registration.showNotification(notificationData.title, notificationData)
  );
});

// Notification click event
self.addEventListener("notificationclick", (event) => {
  console.log("Service Worker: Notification clicked");
  event.notification.close();

  if (event.action === "view") {
    event.waitUntil(
      clients.matchAll({ type: "window" }).then((clientList) => {
        // Check if app is already open
        for (const client of clientList) {
          if (client.url.includes(self.location.origin) && "focus" in client) {
            return client.focus();
          }
        }
        // Open new window if app is not open
        if (clients.openWindow) {
          return clients.openWindow("/");
        }
      })
    );
  }
});

// Background sync event
self.addEventListener("sync", (event) => {
  console.log("Service Worker: Background sync triggered");

  if (event.tag === "background-sync") {
    event.waitUntil(
      // Handle background sync tasks
      syncData()
    );
  }
});

async function syncData() {
  try {
    // Sync any pending data when connection is restored
    console.log("Service Worker: Syncing data...");
    // Add your sync logic here
  } catch (error) {
    console.error("Service Worker: Sync failed", error);
  }
}

// Message event for communication with main thread
self.addEventListener("message", (event) => {
  console.log("Service Worker: Message received", event.data);

  if (event.data && event.data.type === "SKIP_WAITING") {
    self.skipWaiting();
  }

  if (event.data && event.data.type === "GET_VERSION") {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});
