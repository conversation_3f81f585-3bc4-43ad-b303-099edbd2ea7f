{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Online_Auction_System\\\\frontend\\\\src\\\\components\\\\FraudAlertDetails.js\";\nimport React from 'react';\nimport { Card, Badge, Row, Col, Alert, ProgressBar } from 'react-bootstrap';\nimport { FaExclamationTriangle, FaRobot, FaCreditCard, FaUserSecret, FaEye, FaShieldAlt, FaClock, FaChartLine, FaUser, FaGavel } from 'react-icons/fa';\nconst FraudAlertDetails = ({\n  fraud\n}) => {\n  var _fraud$user, _fraud$details, _fraud$details2;\n  if (!fraud) return null;\n\n  // Get fraud type icon and color\n  const getFraudTypeInfo = type => {\n    const typeMap = {\n      'suspicious_bidding': {\n        icon: /*#__PURE__*/React.createElement(FaGavel, {\n          className: \"me-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 15\n          }\n        }),\n        color: 'warning',\n        title: 'Suspicious Bidding Pattern',\n        description: 'Unusual bidding behavior detected'\n      },\n      'bot_activity': {\n        icon: /*#__PURE__*/React.createElement(FaRobot, {\n          className: \"me-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 15\n          }\n        }),\n        color: 'danger',\n        title: 'Bot Activity Detected',\n        description: 'Automated behavior patterns identified'\n      },\n      'payment_fraud': {\n        icon: /*#__PURE__*/React.createElement(FaCreditCard, {\n          className: \"me-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }\n        }),\n        color: 'danger',\n        title: 'Payment Fraud',\n        description: 'Suspicious payment activity detected'\n      },\n      'account_takeover': {\n        icon: /*#__PURE__*/React.createElement(FaUserSecret, {\n          className: \"me-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }\n        }),\n        color: 'danger',\n        title: 'Account Takeover',\n        description: 'Unauthorized account access suspected'\n      },\n      'fake_listing': {\n        icon: /*#__PURE__*/React.createElement(FaEye, {\n          className: \"me-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }\n        }),\n        color: 'warning',\n        title: 'Fake Listing',\n        description: 'Potentially fraudulent auction listing'\n      },\n      'shill_bidding': {\n        icon: /*#__PURE__*/React.createElement(FaChartLine, {\n          className: \"me-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }\n        }),\n        color: 'warning',\n        title: 'Shill Bidding',\n        description: 'Coordinated bidding to inflate prices'\n      }\n    };\n    return typeMap[type] || {\n      icon: /*#__PURE__*/React.createElement(FaExclamationTriangle, {\n        className: \"me-2\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }\n      }),\n      color: 'secondary',\n      title: type.replace('_', ' ').toUpperCase(),\n      description: 'Security alert detected'\n    };\n  };\n\n  // Get risk level info\n  const getRiskLevel = score => {\n    if (score >= 90) return {\n      level: 'CRITICAL',\n      color: 'danger',\n      variant: 'danger'\n    };\n    if (score >= 80) return {\n      level: 'HIGH',\n      color: 'warning',\n      variant: 'warning'\n    };\n    if (score >= 60) return {\n      level: 'MEDIUM',\n      color: 'info',\n      variant: 'info'\n    };\n    return {\n      level: 'LOW',\n      color: 'success',\n      variant: 'success'\n    };\n  };\n\n  // Format detection details in a user-friendly way\n  const formatDetails = (details, fraudType) => {\n    if (!details || typeof details !== 'object') return null;\n    const formatters = {\n      'suspicious_bidding': d => [d.rapid_consecutive_bids && {\n        icon: '⚡',\n        text: 'Multiple rapid bids detected',\n        severity: 'high'\n      }, d.unusual_timing_pattern && {\n        icon: '⏰',\n        text: 'Unusual bidding timing pattern',\n        severity: 'medium'\n      }, d.bid_count_in_last_hour && {\n        icon: '📊',\n        text: `${d.bid_count_in_last_hour} bids in last hour`,\n        severity: 'high'\n      }, d.bid_increment_pattern === 'suspicious' && {\n        icon: '📈',\n        text: 'Suspicious bid increment pattern',\n        severity: 'medium'\n      }, d.time_between_bids_seconds && {\n        icon: '⏱️',\n        text: `Consistent timing: ${d.time_between_bids_seconds.join(', ')}s intervals`,\n        severity: 'high'\n      }],\n      'bot_activity': d => [d.automated_behavior && {\n        icon: '🤖',\n        text: 'Automated behavior detected',\n        severity: 'critical'\n      }, d.consistent_response_time && {\n        icon: '⚡',\n        text: 'Consistent response times (non-human)',\n        severity: 'high'\n      }, d.no_human_delays && {\n        icon: '🚫',\n        text: 'No natural human delays',\n        severity: 'high'\n      }, d.captcha_failures && {\n        icon: '🔒',\n        text: `${d.captcha_failures} CAPTCHA failures`,\n        severity: 'medium'\n      }, d.user_agent_suspicious && {\n        icon: '🌐',\n        text: 'Suspicious browser/device signature',\n        severity: 'medium'\n      }, d.bid_frequency_per_minute && {\n        icon: '📊',\n        text: `${d.bid_frequency_per_minute} actions per minute`,\n        severity: 'critical'\n      }],\n      'payment_fraud': d => [d.stolen_card_indicators && {\n        icon: '💳',\n        text: 'Stolen card indicators detected',\n        severity: 'critical'\n      }, d.billing_address_mismatch && {\n        icon: '📍',\n        text: 'Billing address mismatch',\n        severity: 'high'\n      }, d.multiple_failed_payments && {\n        icon: '❌',\n        text: 'Multiple failed payment attempts',\n        severity: 'high'\n      }, d.velocity_check_failed && {\n        icon: '🚨',\n        text: 'Velocity check failed',\n        severity: 'high'\n      }, d.cvv_failures && {\n        icon: '🔢',\n        text: `${d.cvv_failures} CVV verification failures`,\n        severity: 'medium'\n      }, d.high_risk_country && {\n        icon: '🌍',\n        text: 'Transaction from high-risk location',\n        severity: 'medium'\n      }],\n      'account_takeover': d => [d.login_from_new_location && {\n        icon: '📍',\n        text: 'Login from new/unusual location',\n        severity: 'high'\n      }, d.unusual_bidding_behavior && {\n        icon: '🎯',\n        text: 'Unusual bidding behavior for this user',\n        severity: 'medium'\n      }, d.password_recently_changed && {\n        icon: '🔑',\n        text: 'Password recently changed',\n        severity: 'medium'\n      }, d.different_payment_method && {\n        icon: '💳',\n        text: 'New payment method added',\n        severity: 'medium'\n      }, d.suspicious_ip && {\n        icon: '🌐',\n        text: `Suspicious IP: ${d.suspicious_ip}`,\n        severity: 'high'\n      }, d.login_time_unusual && {\n        icon: '⏰',\n        text: 'Login at unusual time',\n        severity: 'low'\n      }],\n      'fake_listing': d => [d.stock_photos_detected && {\n        icon: '📸',\n        text: 'Stock photos detected',\n        severity: 'high'\n      }, d.price_too_low_for_item && {\n        icon: '💰',\n        text: 'Price significantly below market value',\n        severity: 'high'\n      }, d.vague_description && {\n        icon: '📝',\n        text: 'Vague or generic description',\n        severity: 'medium'\n      }, d.new_seller_account && {\n        icon: '👤',\n        text: 'New seller account',\n        severity: 'medium'\n      }, d.no_seller_history && {\n        icon: '📊',\n        text: 'No previous selling history',\n        severity: 'medium'\n      }, d.similar_listings_found && {\n        icon: '🔍',\n        text: `${d.similar_listings_found} similar listings found elsewhere`,\n        severity: 'high'\n      }],\n      'shill_bidding': d => [d.shill_bidding_pattern && {\n        icon: '🎭',\n        text: 'Shill bidding pattern detected',\n        severity: 'high'\n      }, d.bidding_on_same_seller_items && {\n        icon: '🔄',\n        text: 'Repeatedly bidding on same seller items',\n        severity: 'high'\n      }, d.artificial_price_inflation && {\n        icon: '📈',\n        text: 'Artificial price inflation detected',\n        severity: 'high'\n      }, d.coordinated_bidding && {\n        icon: '🤝',\n        text: 'Coordinated bidding activity',\n        severity: 'critical'\n      }, d.seller_connection_suspected && {\n        icon: '🔗',\n        text: 'Connection to seller suspected',\n        severity: 'high'\n      }, d.bid_timing_coordination && {\n        icon: '⏰',\n        text: 'Coordinated bid timing',\n        severity: 'medium'\n      }]\n    };\n    const formatter = formatters[fraudType];\n    if (!formatter) return null;\n    return formatter(details).filter(Boolean);\n  };\n  const typeInfo = getFraudTypeInfo(fraud.fraud_type);\n  const riskInfo = getRiskLevel(fraud.risk_score);\n  const detailItems = formatDetails(fraud.details, fraud.fraud_type);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"fraud-alert-details\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(Card, {\n    className: `border-${typeInfo.color} mb-3`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Card.Header, {\n    className: `bg-${typeInfo.color} text-white`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    className: \"align-items-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h5\", {\n    className: \"mb-0\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 15\n    }\n  }, typeInfo.icon, typeInfo.title), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 15\n    }\n  }, typeInfo.description)), /*#__PURE__*/React.createElement(Col, {\n    xs: \"auto\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(Badge, {\n    bg: riskInfo.variant,\n    className: \"fs-6\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 15\n    }\n  }, riskInfo.level, \" RISK\")))), /*#__PURE__*/React.createElement(Card.Body, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    className: \"mb-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    md: 6,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex align-items-center mb-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaShieldAlt, {\n    className: \"me-2 text-danger\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 17\n    }\n  }, \"Risk Score: \", fraud.risk_score, \"/100\")), /*#__PURE__*/React.createElement(ProgressBar, {\n    variant: riskInfo.variant,\n    now: fraud.risk_score,\n    label: `${fraud.risk_score}%`,\n    className: \"mb-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(Col, {\n    md: 6,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex align-items-center mb-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaUser, {\n    className: \"me-2 text-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 17\n    }\n  }, \"User: \", ((_fraud$user = fraud.user) === null || _fraud$user === void 0 ? void 0 : _fraud$user.username) || fraud.user)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex align-items-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaClock, {\n    className: \"me-2 text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    className: \"text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 17\n    }\n  }, \"Detected: \", new Date(fraud.detected_at || fraud.created_at).toLocaleString())))), detailItems && detailItems.length > 0 && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"h6\", {\n    className: \"mb-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaExclamationTriangle, {\n    className: \"me-2 text-warning\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 17\n    }\n  }), \"Detection Details\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"detection-details\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 15\n    }\n  }, detailItems.map((item, index) => /*#__PURE__*/React.createElement(Alert, {\n    key: index,\n    variant: item.severity === 'critical' ? 'danger' : item.severity === 'high' ? 'warning' : item.severity === 'medium' ? 'info' : 'light',\n    className: \"py-2 mb-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 21\n    }\n  }, item.icon), item.text)))), ((_fraud$details = fraud.details) === null || _fraud$details === void 0 ? void 0 : _fraud$details.confidence_level) && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"mt-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"small\", {\n    className: \"text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 17\n    }\n  }, \"Detection Confidence:\"), \" \", fraud.details.confidence_level.replace('_', ' ').toUpperCase())), ((_fraud$details2 = fraud.details) === null || _fraud$details2 === void 0 ? void 0 : _fraud$details2.detection_reason) && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"mt-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"small\", {\n    className: \"text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 17\n    }\n  }, \"Reason:\"), \" \", fraud.details.detection_reason)))));\n};\nexport default FraudAlertDetails;", "map": {"version": 3, "names": ["React", "Card", "Badge", "Row", "Col", "<PERSON><PERSON>", "ProgressBar", "FaExclamationTriangle", "FaRobot", "FaCreditCard", "FaUserSecret", "FaEye", "FaShieldAlt", "FaClock", "FaChartLine", "FaUser", "FaGavel", "FraudAlertDetails", "fraud", "_fraud$user", "_fraud$details", "_fraud$details2", "getFraudTypeInfo", "type", "typeMap", "icon", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "title", "description", "replace", "toUpperCase", "getRiskLevel", "score", "level", "variant", "formatDetails", "details", "fraudType", "formatters", "d", "rapid_consecutive_bids", "text", "severity", "unusual_timing_pattern", "bid_count_in_last_hour", "bid_increment_pattern", "time_between_bids_seconds", "join", "automated_behavior", "consistent_response_time", "no_human_delays", "captcha_failures", "user_agent_suspicious", "bid_frequency_per_minute", "stolen_card_indicators", "billing_address_mismatch", "multiple_failed_payments", "velocity_check_failed", "cvv_failures", "high_risk_country", "login_from_new_location", "unusual_bidding_behavior", "password_recently_changed", "different_payment_method", "suspicious_ip", "login_time_unusual", "stock_photos_detected", "price_too_low_for_item", "vague_description", "new_seller_account", "no_seller_history", "similar_listings_found", "shill_bidding_pattern", "bidding_on_same_seller_items", "artificial_price_inflation", "coordinated_bidding", "seller_connection_suspected", "bid_timing_coordination", "formatter", "filter", "Boolean", "typeInfo", "fraud_type", "riskInfo", "risk_score", "detailItems", "Header", "xs", "bg", "Body", "md", "now", "label", "user", "username", "Date", "detected_at", "created_at", "toLocaleString", "length", "Fragment", "map", "item", "index", "key", "confidence_level", "detection_reason"], "sources": ["C:/Users/<USER>/Desktop/Online_Auction_System/frontend/src/components/FraudAlertDetails.js"], "sourcesContent": ["import React from 'react';\nimport { Card, Badge, Row, <PERSON>, <PERSON><PERSON>, ProgressBar } from 'react-bootstrap';\nimport { \n  FaExclamationTriangle, \n  FaRobot, \n  FaCreditCard, \n  FaUserSecret, \n  FaEye,\n  FaShieldAlt,\n  FaClock,\n  FaChartLine,\n  FaUser,\n  FaGavel\n} from 'react-icons/fa';\n\nconst FraudAlertDetails = ({ fraud }) => {\n  if (!fraud) return null;\n\n  // Get fraud type icon and color\n  const getFraudTypeInfo = (type) => {\n    const typeMap = {\n      'suspicious_bidding': {\n        icon: <FaGavel className=\"me-2\" />,\n        color: 'warning',\n        title: 'Suspicious Bidding Pattern',\n        description: 'Unusual bidding behavior detected'\n      },\n      'bot_activity': {\n        icon: <FaRobot className=\"me-2\" />,\n        color: 'danger',\n        title: 'Bot Activity Detected',\n        description: 'Automated behavior patterns identified'\n      },\n      'payment_fraud': {\n        icon: <FaCreditCard className=\"me-2\" />,\n        color: 'danger',\n        title: 'Payment Fraud',\n        description: 'Suspicious payment activity detected'\n      },\n      'account_takeover': {\n        icon: <FaUserSecret className=\"me-2\" />,\n        color: 'danger',\n        title: 'Account Takeover',\n        description: 'Unauthorized account access suspected'\n      },\n      'fake_listing': {\n        icon: <FaEye className=\"me-2\" />,\n        color: 'warning',\n        title: 'Fake Listing',\n        description: 'Potentially fraudulent auction listing'\n      },\n      'shill_bidding': {\n        icon: <FaChartLine className=\"me-2\" />,\n        color: 'warning',\n        title: 'Shill Bidding',\n        description: 'Coordinated bidding to inflate prices'\n      }\n    };\n    return typeMap[type] || {\n      icon: <FaExclamationTriangle className=\"me-2\" />,\n      color: 'secondary',\n      title: type.replace('_', ' ').toUpperCase(),\n      description: 'Security alert detected'\n    };\n  };\n\n  // Get risk level info\n  const getRiskLevel = (score) => {\n    if (score >= 90) return { level: 'CRITICAL', color: 'danger', variant: 'danger' };\n    if (score >= 80) return { level: 'HIGH', color: 'warning', variant: 'warning' };\n    if (score >= 60) return { level: 'MEDIUM', color: 'info', variant: 'info' };\n    return { level: 'LOW', color: 'success', variant: 'success' };\n  };\n\n  // Format detection details in a user-friendly way\n  const formatDetails = (details, fraudType) => {\n    if (!details || typeof details !== 'object') return null;\n\n    const formatters = {\n      'suspicious_bidding': (d) => [\n        d.rapid_consecutive_bids && { icon: '⚡', text: 'Multiple rapid bids detected', severity: 'high' },\n        d.unusual_timing_pattern && { icon: '⏰', text: 'Unusual bidding timing pattern', severity: 'medium' },\n        d.bid_count_in_last_hour && { icon: '📊', text: `${d.bid_count_in_last_hour} bids in last hour`, severity: 'high' },\n        d.bid_increment_pattern === 'suspicious' && { icon: '📈', text: 'Suspicious bid increment pattern', severity: 'medium' },\n        d.time_between_bids_seconds && { icon: '⏱️', text: `Consistent timing: ${d.time_between_bids_seconds.join(', ')}s intervals`, severity: 'high' }\n      ],\n      'bot_activity': (d) => [\n        d.automated_behavior && { icon: '🤖', text: 'Automated behavior detected', severity: 'critical' },\n        d.consistent_response_time && { icon: '⚡', text: 'Consistent response times (non-human)', severity: 'high' },\n        d.no_human_delays && { icon: '🚫', text: 'No natural human delays', severity: 'high' },\n        d.captcha_failures && { icon: '🔒', text: `${d.captcha_failures} CAPTCHA failures`, severity: 'medium' },\n        d.user_agent_suspicious && { icon: '🌐', text: 'Suspicious browser/device signature', severity: 'medium' },\n        d.bid_frequency_per_minute && { icon: '📊', text: `${d.bid_frequency_per_minute} actions per minute`, severity: 'critical' }\n      ],\n      'payment_fraud': (d) => [\n        d.stolen_card_indicators && { icon: '💳', text: 'Stolen card indicators detected', severity: 'critical' },\n        d.billing_address_mismatch && { icon: '📍', text: 'Billing address mismatch', severity: 'high' },\n        d.multiple_failed_payments && { icon: '❌', text: 'Multiple failed payment attempts', severity: 'high' },\n        d.velocity_check_failed && { icon: '🚨', text: 'Velocity check failed', severity: 'high' },\n        d.cvv_failures && { icon: '🔢', text: `${d.cvv_failures} CVV verification failures`, severity: 'medium' },\n        d.high_risk_country && { icon: '🌍', text: 'Transaction from high-risk location', severity: 'medium' }\n      ],\n      'account_takeover': (d) => [\n        d.login_from_new_location && { icon: '📍', text: 'Login from new/unusual location', severity: 'high' },\n        d.unusual_bidding_behavior && { icon: '🎯', text: 'Unusual bidding behavior for this user', severity: 'medium' },\n        d.password_recently_changed && { icon: '🔑', text: 'Password recently changed', severity: 'medium' },\n        d.different_payment_method && { icon: '💳', text: 'New payment method added', severity: 'medium' },\n        d.suspicious_ip && { icon: '🌐', text: `Suspicious IP: ${d.suspicious_ip}`, severity: 'high' },\n        d.login_time_unusual && { icon: '⏰', text: 'Login at unusual time', severity: 'low' }\n      ],\n      'fake_listing': (d) => [\n        d.stock_photos_detected && { icon: '📸', text: 'Stock photos detected', severity: 'high' },\n        d.price_too_low_for_item && { icon: '💰', text: 'Price significantly below market value', severity: 'high' },\n        d.vague_description && { icon: '📝', text: 'Vague or generic description', severity: 'medium' },\n        d.new_seller_account && { icon: '👤', text: 'New seller account', severity: 'medium' },\n        d.no_seller_history && { icon: '📊', text: 'No previous selling history', severity: 'medium' },\n        d.similar_listings_found && { icon: '🔍', text: `${d.similar_listings_found} similar listings found elsewhere`, severity: 'high' }\n      ],\n      'shill_bidding': (d) => [\n        d.shill_bidding_pattern && { icon: '🎭', text: 'Shill bidding pattern detected', severity: 'high' },\n        d.bidding_on_same_seller_items && { icon: '🔄', text: 'Repeatedly bidding on same seller items', severity: 'high' },\n        d.artificial_price_inflation && { icon: '📈', text: 'Artificial price inflation detected', severity: 'high' },\n        d.coordinated_bidding && { icon: '🤝', text: 'Coordinated bidding activity', severity: 'critical' },\n        d.seller_connection_suspected && { icon: '🔗', text: 'Connection to seller suspected', severity: 'high' },\n        d.bid_timing_coordination && { icon: '⏰', text: 'Coordinated bid timing', severity: 'medium' }\n      ]\n    };\n\n    const formatter = formatters[fraudType];\n    if (!formatter) return null;\n\n    return formatter(details).filter(Boolean);\n  };\n\n  const typeInfo = getFraudTypeInfo(fraud.fraud_type);\n  const riskInfo = getRiskLevel(fraud.risk_score);\n  const detailItems = formatDetails(fraud.details, fraud.fraud_type);\n\n  return (\n    <div className=\"fraud-alert-details\">\n      {/* Header */}\n      <Card className={`border-${typeInfo.color} mb-3`}>\n        <Card.Header className={`bg-${typeInfo.color} text-white`}>\n          <Row className=\"align-items-center\">\n            <Col>\n              <h5 className=\"mb-0\">\n                {typeInfo.icon}\n                {typeInfo.title}\n              </h5>\n              <small>{typeInfo.description}</small>\n            </Col>\n            <Col xs=\"auto\">\n              <Badge bg={riskInfo.variant} className=\"fs-6\">\n                {riskInfo.level} RISK\n              </Badge>\n            </Col>\n          </Row>\n        </Card.Header>\n        \n        <Card.Body>\n          {/* Risk Score */}\n          <Row className=\"mb-3\">\n            <Col md={6}>\n              <div className=\"d-flex align-items-center mb-2\">\n                <FaShieldAlt className=\"me-2 text-danger\" />\n                <strong>Risk Score: {fraud.risk_score}/100</strong>\n              </div>\n              <ProgressBar \n                variant={riskInfo.variant} \n                now={fraud.risk_score} \n                label={`${fraud.risk_score}%`}\n                className=\"mb-2\"\n              />\n            </Col>\n            <Col md={6}>\n              <div className=\"d-flex align-items-center mb-2\">\n                <FaUser className=\"me-2 text-primary\" />\n                <strong>User: {fraud.user?.username || fraud.user}</strong>\n              </div>\n              <div className=\"d-flex align-items-center\">\n                <FaClock className=\"me-2 text-muted\" />\n                <small className=\"text-muted\">\n                  Detected: {new Date(fraud.detected_at || fraud.created_at).toLocaleString()}\n                </small>\n              </div>\n            </Col>\n          </Row>\n\n          {/* Detection Details */}\n          {detailItems && detailItems.length > 0 && (\n            <>\n              <h6 className=\"mb-3\">\n                <FaExclamationTriangle className=\"me-2 text-warning\" />\n                Detection Details\n              </h6>\n              <div className=\"detection-details\">\n                {detailItems.map((item, index) => (\n                  <Alert \n                    key={index} \n                    variant={\n                      item.severity === 'critical' ? 'danger' :\n                      item.severity === 'high' ? 'warning' :\n                      item.severity === 'medium' ? 'info' : 'light'\n                    }\n                    className=\"py-2 mb-2\"\n                  >\n                    <span className=\"me-2\">{item.icon}</span>\n                    {item.text}\n                  </Alert>\n                ))}\n              </div>\n            </>\n          )}\n\n          {/* Confidence Level */}\n          {fraud.details?.confidence_level && (\n            <div className=\"mt-3\">\n              <small className=\"text-muted\">\n                <strong>Detection Confidence:</strong> {fraud.details.confidence_level.replace('_', ' ').toUpperCase()}\n              </small>\n            </div>\n          )}\n\n          {/* Detection Reason */}\n          {fraud.details?.detection_reason && (\n            <div className=\"mt-2\">\n              <small className=\"text-muted\">\n                <strong>Reason:</strong> {fraud.details.detection_reason}\n              </small>\n            </div>\n          )}\n        </Card.Body>\n      </Card>\n    </div>\n  );\n};\n\nexport default FraudAlertDetails;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,WAAW,QAAQ,iBAAiB;AAC3E,SACEC,qBAAqB,EACrBC,OAAO,EACPC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,WAAW,EACXC,OAAO,EACPC,WAAW,EACXC,MAAM,EACNC,OAAO,QACF,gBAAgB;AAEvB,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAA,IAAAC,WAAA,EAAAC,cAAA,EAAAC,eAAA;EACvC,IAAI,CAACH,KAAK,EAAE,OAAO,IAAI;;EAEvB;EACA,MAAMI,gBAAgB,GAAIC,IAAI,IAAK;IACjC,MAAMC,OAAO,GAAG;MACd,oBAAoB,EAAE;QACpBC,IAAI,eAAEzB,KAAA,CAAA0B,aAAA,CAACV,OAAO;UAACW,SAAS,EAAC,MAAM;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QAClCC,KAAK,EAAE,SAAS;QAChBC,KAAK,EAAE,4BAA4B;QACnCC,WAAW,EAAE;MACf,CAAC;MACD,cAAc,EAAE;QACdX,IAAI,eAAEzB,KAAA,CAAA0B,aAAA,CAAClB,OAAO;UAACmB,SAAS,EAAC,MAAM;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QAClCC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,uBAAuB;QAC9BC,WAAW,EAAE;MACf,CAAC;MACD,eAAe,EAAE;QACfX,IAAI,eAAEzB,KAAA,CAAA0B,aAAA,CAACjB,YAAY;UAACkB,SAAS,EAAC,MAAM;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACvCC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,eAAe;QACtBC,WAAW,EAAE;MACf,CAAC;MACD,kBAAkB,EAAE;QAClBX,IAAI,eAAEzB,KAAA,CAAA0B,aAAA,CAAChB,YAAY;UAACiB,SAAS,EAAC,MAAM;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACvCC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,kBAAkB;QACzBC,WAAW,EAAE;MACf,CAAC;MACD,cAAc,EAAE;QACdX,IAAI,eAAEzB,KAAA,CAAA0B,aAAA,CAACf,KAAK;UAACgB,SAAS,EAAC,MAAM;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QAChCC,KAAK,EAAE,SAAS;QAChBC,KAAK,EAAE,cAAc;QACrBC,WAAW,EAAE;MACf,CAAC;MACD,eAAe,EAAE;QACfX,IAAI,eAAEzB,KAAA,CAAA0B,aAAA,CAACZ,WAAW;UAACa,SAAS,EAAC,MAAM;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACtCC,KAAK,EAAE,SAAS;QAChBC,KAAK,EAAE,eAAe;QACtBC,WAAW,EAAE;MACf;IACF,CAAC;IACD,OAAOZ,OAAO,CAACD,IAAI,CAAC,IAAI;MACtBE,IAAI,eAAEzB,KAAA,CAAA0B,aAAA,CAACnB,qBAAqB;QAACoB,SAAS,EAAC,MAAM;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;MAChDC,KAAK,EAAE,WAAW;MAClBC,KAAK,EAAEZ,IAAI,CAACc,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC;MAC3CF,WAAW,EAAE;IACf,CAAC;EACH,CAAC;;EAED;EACA,MAAMG,YAAY,GAAIC,KAAK,IAAK;IAC9B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,UAAU;MAAEP,KAAK,EAAE,QAAQ;MAAEQ,OAAO,EAAE;IAAS,CAAC;IACjF,IAAIF,KAAK,IAAI,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,MAAM;MAAEP,KAAK,EAAE,SAAS;MAAEQ,OAAO,EAAE;IAAU,CAAC;IAC/E,IAAIF,KAAK,IAAI,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,QAAQ;MAAEP,KAAK,EAAE,MAAM;MAAEQ,OAAO,EAAE;IAAO,CAAC;IAC3E,OAAO;MAAED,KAAK,EAAE,KAAK;MAAEP,KAAK,EAAE,SAAS;MAAEQ,OAAO,EAAE;IAAU,CAAC;EAC/D,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACC,OAAO,EAAEC,SAAS,KAAK;IAC5C,IAAI,CAACD,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE,OAAO,IAAI;IAExD,MAAME,UAAU,GAAG;MACjB,oBAAoB,EAAGC,CAAC,IAAK,CAC3BA,CAAC,CAACC,sBAAsB,IAAI;QAAEvB,IAAI,EAAE,GAAG;QAAEwB,IAAI,EAAE,8BAA8B;QAAEC,QAAQ,EAAE;MAAO,CAAC,EACjGH,CAAC,CAACI,sBAAsB,IAAI;QAAE1B,IAAI,EAAE,GAAG;QAAEwB,IAAI,EAAE,gCAAgC;QAAEC,QAAQ,EAAE;MAAS,CAAC,EACrGH,CAAC,CAACK,sBAAsB,IAAI;QAAE3B,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,GAAGF,CAAC,CAACK,sBAAsB,oBAAoB;QAAEF,QAAQ,EAAE;MAAO,CAAC,EACnHH,CAAC,CAACM,qBAAqB,KAAK,YAAY,IAAI;QAAE5B,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,kCAAkC;QAAEC,QAAQ,EAAE;MAAS,CAAC,EACxHH,CAAC,CAACO,yBAAyB,IAAI;QAAE7B,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,sBAAsBF,CAAC,CAACO,yBAAyB,CAACC,IAAI,CAAC,IAAI,CAAC,aAAa;QAAEL,QAAQ,EAAE;MAAO,CAAC,CACjJ;MACD,cAAc,EAAGH,CAAC,IAAK,CACrBA,CAAC,CAACS,kBAAkB,IAAI;QAAE/B,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,6BAA6B;QAAEC,QAAQ,EAAE;MAAW,CAAC,EACjGH,CAAC,CAACU,wBAAwB,IAAI;QAAEhC,IAAI,EAAE,GAAG;QAAEwB,IAAI,EAAE,uCAAuC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EAC5GH,CAAC,CAACW,eAAe,IAAI;QAAEjC,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,yBAAyB;QAAEC,QAAQ,EAAE;MAAO,CAAC,EACtFH,CAAC,CAACY,gBAAgB,IAAI;QAAElC,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,GAAGF,CAAC,CAACY,gBAAgB,mBAAmB;QAAET,QAAQ,EAAE;MAAS,CAAC,EACxGH,CAAC,CAACa,qBAAqB,IAAI;QAAEnC,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,qCAAqC;QAAEC,QAAQ,EAAE;MAAS,CAAC,EAC1GH,CAAC,CAACc,wBAAwB,IAAI;QAAEpC,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,GAAGF,CAAC,CAACc,wBAAwB,qBAAqB;QAAEX,QAAQ,EAAE;MAAW,CAAC,CAC7H;MACD,eAAe,EAAGH,CAAC,IAAK,CACtBA,CAAC,CAACe,sBAAsB,IAAI;QAAErC,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,iCAAiC;QAAEC,QAAQ,EAAE;MAAW,CAAC,EACzGH,CAAC,CAACgB,wBAAwB,IAAI;QAAEtC,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,0BAA0B;QAAEC,QAAQ,EAAE;MAAO,CAAC,EAChGH,CAAC,CAACiB,wBAAwB,IAAI;QAAEvC,IAAI,EAAE,GAAG;QAAEwB,IAAI,EAAE,kCAAkC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EACvGH,CAAC,CAACkB,qBAAqB,IAAI;QAAExC,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,uBAAuB;QAAEC,QAAQ,EAAE;MAAO,CAAC,EAC1FH,CAAC,CAACmB,YAAY,IAAI;QAAEzC,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,GAAGF,CAAC,CAACmB,YAAY,4BAA4B;QAAEhB,QAAQ,EAAE;MAAS,CAAC,EACzGH,CAAC,CAACoB,iBAAiB,IAAI;QAAE1C,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,qCAAqC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CACvG;MACD,kBAAkB,EAAGH,CAAC,IAAK,CACzBA,CAAC,CAACqB,uBAAuB,IAAI;QAAE3C,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,iCAAiC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EACtGH,CAAC,CAACsB,wBAAwB,IAAI;QAAE5C,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,wCAAwC;QAAEC,QAAQ,EAAE;MAAS,CAAC,EAChHH,CAAC,CAACuB,yBAAyB,IAAI;QAAE7C,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,2BAA2B;QAAEC,QAAQ,EAAE;MAAS,CAAC,EACpGH,CAAC,CAACwB,wBAAwB,IAAI;QAAE9C,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,0BAA0B;QAAEC,QAAQ,EAAE;MAAS,CAAC,EAClGH,CAAC,CAACyB,aAAa,IAAI;QAAE/C,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,kBAAkBF,CAAC,CAACyB,aAAa,EAAE;QAAEtB,QAAQ,EAAE;MAAO,CAAC,EAC9FH,CAAC,CAAC0B,kBAAkB,IAAI;QAAEhD,IAAI,EAAE,GAAG;QAAEwB,IAAI,EAAE,uBAAuB;QAAEC,QAAQ,EAAE;MAAM,CAAC,CACtF;MACD,cAAc,EAAGH,CAAC,IAAK,CACrBA,CAAC,CAAC2B,qBAAqB,IAAI;QAAEjD,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,uBAAuB;QAAEC,QAAQ,EAAE;MAAO,CAAC,EAC1FH,CAAC,CAAC4B,sBAAsB,IAAI;QAAElD,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,wCAAwC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EAC5GH,CAAC,CAAC6B,iBAAiB,IAAI;QAAEnD,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,8BAA8B;QAAEC,QAAQ,EAAE;MAAS,CAAC,EAC/FH,CAAC,CAAC8B,kBAAkB,IAAI;QAAEpD,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,oBAAoB;QAAEC,QAAQ,EAAE;MAAS,CAAC,EACtFH,CAAC,CAAC+B,iBAAiB,IAAI;QAAErD,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,6BAA6B;QAAEC,QAAQ,EAAE;MAAS,CAAC,EAC9FH,CAAC,CAACgC,sBAAsB,IAAI;QAAEtD,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,GAAGF,CAAC,CAACgC,sBAAsB,mCAAmC;QAAE7B,QAAQ,EAAE;MAAO,CAAC,CACnI;MACD,eAAe,EAAGH,CAAC,IAAK,CACtBA,CAAC,CAACiC,qBAAqB,IAAI;QAAEvD,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,gCAAgC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EACnGH,CAAC,CAACkC,4BAA4B,IAAI;QAAExD,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,yCAAyC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EACnHH,CAAC,CAACmC,0BAA0B,IAAI;QAAEzD,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,qCAAqC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EAC7GH,CAAC,CAACoC,mBAAmB,IAAI;QAAE1D,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,8BAA8B;QAAEC,QAAQ,EAAE;MAAW,CAAC,EACnGH,CAAC,CAACqC,2BAA2B,IAAI;QAAE3D,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,gCAAgC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EACzGH,CAAC,CAACsC,uBAAuB,IAAI;QAAE5D,IAAI,EAAE,GAAG;QAAEwB,IAAI,EAAE,wBAAwB;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAElG,CAAC;IAED,MAAMoC,SAAS,GAAGxC,UAAU,CAACD,SAAS,CAAC;IACvC,IAAI,CAACyC,SAAS,EAAE,OAAO,IAAI;IAE3B,OAAOA,SAAS,CAAC1C,OAAO,CAAC,CAAC2C,MAAM,CAACC,OAAO,CAAC;EAC3C,CAAC;EAED,MAAMC,QAAQ,GAAGnE,gBAAgB,CAACJ,KAAK,CAACwE,UAAU,CAAC;EACnD,MAAMC,QAAQ,GAAGpD,YAAY,CAACrB,KAAK,CAAC0E,UAAU,CAAC;EAC/C,MAAMC,WAAW,GAAGlD,aAAa,CAACzB,KAAK,CAAC0B,OAAO,EAAE1B,KAAK,CAACwE,UAAU,CAAC;EAElE,oBACE1F,KAAA,CAAA0B,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElCjC,KAAA,CAAA0B,aAAA,CAACzB,IAAI;IAAC0B,SAAS,EAAE,UAAU8D,QAAQ,CAACvD,KAAK,OAAQ;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/CjC,KAAA,CAAA0B,aAAA,CAACzB,IAAI,CAAC6F,MAAM;IAACnE,SAAS,EAAE,MAAM8D,QAAQ,CAACvD,KAAK,aAAc;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxDjC,KAAA,CAAA0B,aAAA,CAACvB,GAAG;IAACwB,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjCjC,KAAA,CAAA0B,aAAA,CAACtB,GAAG;IAAAwB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACFjC,KAAA,CAAA0B,aAAA;IAAIC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjBwD,QAAQ,CAAChE,IAAI,EACbgE,QAAQ,CAACtD,KACR,CAAC,eACLnC,KAAA,CAAA0B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQwD,QAAQ,CAACrD,WAAmB,CACjC,CAAC,eACNpC,KAAA,CAAA0B,aAAA,CAACtB,GAAG;IAAC2F,EAAE,EAAC,MAAM;IAAAnE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACZjC,KAAA,CAAA0B,aAAA,CAACxB,KAAK;IAAC8F,EAAE,EAAEL,QAAQ,CAACjD,OAAQ;IAACf,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1C0D,QAAQ,CAAClD,KAAK,EAAC,OACX,CACJ,CACF,CACM,CAAC,eAEdzC,KAAA,CAAA0B,aAAA,CAACzB,IAAI,CAACgG,IAAI;IAAArE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAERjC,KAAA,CAAA0B,aAAA,CAACvB,GAAG;IAACwB,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBjC,KAAA,CAAA0B,aAAA,CAACtB,GAAG;IAAC8F,EAAE,EAAE,CAAE;IAAAtE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACTjC,KAAA,CAAA0B,aAAA;IAAKC,SAAS,EAAC,gCAAgC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7CjC,KAAA,CAAA0B,aAAA,CAACd,WAAW;IAACe,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC5CjC,KAAA,CAAA0B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQ,cAAY,EAACf,KAAK,CAAC0E,UAAU,EAAC,MAAY,CAC/C,CAAC,eACN5F,KAAA,CAAA0B,aAAA,CAACpB,WAAW;IACVoC,OAAO,EAAEiD,QAAQ,CAACjD,OAAQ;IAC1ByD,GAAG,EAAEjF,KAAK,CAAC0E,UAAW;IACtBQ,KAAK,EAAE,GAAGlF,KAAK,CAAC0E,UAAU,GAAI;IAC9BjE,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACjB,CACE,CAAC,eACNjC,KAAA,CAAA0B,aAAA,CAACtB,GAAG;IAAC8F,EAAE,EAAE,CAAE;IAAAtE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACTjC,KAAA,CAAA0B,aAAA;IAAKC,SAAS,EAAC,gCAAgC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7CjC,KAAA,CAAA0B,aAAA,CAACX,MAAM;IAACY,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACxCjC,KAAA,CAAA0B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQ,QAAM,EAAC,EAAAd,WAAA,GAAAD,KAAK,CAACmF,IAAI,cAAAlF,WAAA,uBAAVA,WAAA,CAAYmF,QAAQ,KAAIpF,KAAK,CAACmF,IAAa,CACvD,CAAC,eACNrG,KAAA,CAAA0B,aAAA;IAAKC,SAAS,EAAC,2BAA2B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxCjC,KAAA,CAAA0B,aAAA,CAACb,OAAO;IAACc,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACvCjC,KAAA,CAAA0B,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,YAClB,EAAC,IAAIsE,IAAI,CAACrF,KAAK,CAACsF,WAAW,IAAItF,KAAK,CAACuF,UAAU,CAAC,CAACC,cAAc,CAAC,CACrE,CACJ,CACF,CACF,CAAC,EAGLb,WAAW,IAAIA,WAAW,CAACc,MAAM,GAAG,CAAC,iBACpC3G,KAAA,CAAA0B,aAAA,CAAA1B,KAAA,CAAA4G,QAAA,qBACE5G,KAAA,CAAA0B,aAAA;IAAIC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClBjC,KAAA,CAAA0B,aAAA,CAACnB,qBAAqB;IAACoB,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,qBAErD,CAAC,eACLjC,KAAA,CAAA0B,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/B4D,WAAW,CAACgB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3B/G,KAAA,CAAA0B,aAAA,CAACrB,KAAK;IACJ2G,GAAG,EAAED,KAAM;IACXrE,OAAO,EACLoE,IAAI,CAAC5D,QAAQ,KAAK,UAAU,GAAG,QAAQ,GACvC4D,IAAI,CAAC5D,QAAQ,KAAK,MAAM,GAAG,SAAS,GACpC4D,IAAI,CAAC5D,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG,OACvC;IACDvB,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAErBjC,KAAA,CAAA0B,aAAA;IAAMC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE6E,IAAI,CAACrF,IAAW,CAAC,EACxCqF,IAAI,CAAC7D,IACD,CACR,CACE,CACL,CACH,EAGA,EAAA7B,cAAA,GAAAF,KAAK,CAAC0B,OAAO,cAAAxB,cAAA,uBAAbA,cAAA,CAAe6F,gBAAgB,kBAC9BjH,KAAA,CAAA0B,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBjC,KAAA,CAAA0B,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BjC,KAAA,CAAA0B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQ,uBAA6B,CAAC,KAAC,EAACf,KAAK,CAAC0B,OAAO,CAACqE,gBAAgB,CAAC5E,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAChG,CACJ,CACN,EAGA,EAAAjB,eAAA,GAAAH,KAAK,CAAC0B,OAAO,cAAAvB,eAAA,uBAAbA,eAAA,CAAe6F,gBAAgB,kBAC9BlH,KAAA,CAAA0B,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBjC,KAAA,CAAA0B,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BjC,KAAA,CAAA0B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQ,SAAe,CAAC,KAAC,EAACf,KAAK,CAAC0B,OAAO,CAACsE,gBACnC,CACJ,CAEE,CACP,CACH,CAAC;AAEV,CAAC;AAED,eAAejG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}