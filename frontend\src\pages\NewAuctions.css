.new-auctions-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
  font-weight: 400;
}

/* Results Summary */
.results-summary {
  margin: 20px 0;
  padding: 15px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3b82f6;
}

.results-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-count {
  font-weight: 600;
  color: #374151;
  font-size: 1rem;
}

/* Auctions Grid */
.auctions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.auction-card-wrapper {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-radius: 12px;
  overflow: hidden;
}

.auction-card-wrapper:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* No Results */
.no-results {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin: 30px 0;
}

.no-results-content h3 {
  font-size: 1.5rem;
  color: #374151;
  margin-bottom: 15px;
}

.no-results-content p {
  color: #6b7280;
  font-size: 1rem;
  margin-bottom: 20px;
  line-height: 1.6;
}

.clear-filters-btn {
  padding: 12px 24px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-filters-btn:hover {
  background: #dc2626;
}

/* Load More Section */
.load-more-section {
  text-align: center;
  margin: 40px 0;
}

.load-more-btn {
  padding: 15px 30px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.load-more-btn:hover {
  background: #2563eb;
  transform: translateY(-2px);
}

/* Quick Stats */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin: 40px 0;
  padding: 20px 0;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 5px;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
}

/* Loading State */
.loading-text {
  text-align: center;
  color: #6b7280;
  font-size: 1.1rem;
  margin-top: 20px;
}

/* Error Message */
.error-message {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #ef4444;
}

.error-message h3 {
  color: #ef4444;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.error-message p {
  color: #6b7280;
  margin-bottom: 20px;
  font-size: 1rem;
}

.retry-btn {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background: #2563eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .new-auctions-page {
    padding: 10px 0;
  }

  .container {
    padding: 0 15px;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }

  .auctions-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    margin: 20px 0;
  }

  .results-summary {
    padding: 12px 15px;
    margin: 15px 0;
  }

  .results-info {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .quick-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin: 30px 0;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 15px 0;
    margin-bottom: 20px;
  }

  .page-header h1 {
    font-size: 1.8rem;
  }

  .no-results {
    padding: 40px 15px;
  }

  .quick-stats {
    grid-template-columns: 1fr;
  }

  .stat-item {
    padding: 15px;
  }
}

/* Animation for filtering */
.auctions-grid.filtering {
  opacity: 0.6;
  pointer-events: none;
}

.auctions-grid.filtering .auction-card-wrapper {
  transform: scale(0.98);
}

/* Smooth transitions */
.auctions-grid,
.auction-card-wrapper,
.results-summary,
.quick-stats {
  transition: all 0.3s ease;
}

/* Focus states for accessibility */
.auction-card-wrapper:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.load-more-btn:focus,
.retry-btn:focus,
.clear-filters-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
