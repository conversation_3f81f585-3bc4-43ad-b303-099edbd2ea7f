@echo off
echo ====================================================
echo    SAFE COMMIT - NO DUPLICATES GUARANTEED
echo ====================================================
echo.

echo 🔍 STEP 1: Analyzing Repository State...
echo ====================================================

REM Check if we're in a git repository
git rev-parse --git-dir >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Error: Not a Git repository!
    echo 💡 Run 'git init' to initialize a repository.
    pause
    exit /b 1
)

REM Check for any changes at all
git diff-index --quiet HEAD -- 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Repository is clean - no changes to commit
    echo 🎯 Git automatically prevents duplicate commits!
    echo.
    echo 📊 Current status:
    git status --porcelain
    if %ERRORLEVEL% EQU 0 (
        echo    (No output = no changes)
    )
    echo.
    pause
    exit /b 0
)

echo 📊 STEP 2: What Git Will Actually Commit...
echo ====================================================

REM Show untracked files (new files)
echo 🆕 NEW FILES (will be added):
for /f "delims=" %%i in ('git ls-files --others --exclude-standard 2^>nul') do (
    echo    + %%i
    set "has_new=1"
)
if not defined has_new echo    (none)
echo.

REM Show modified files
echo 📝 MODIFIED FILES (only changed parts):
for /f "delims=" %%i in ('git diff --name-only 2^>nul') do (
    echo    ~ %%i
    set "has_modified=1"
)
if not defined has_modified echo    (none)
echo.

REM Show deleted files
echo 🗑️ DELETED FILES:
for /f "delims=" %%i in ('git diff --name-only --diff-filter=D 2^>nul') do (
    echo    - %%i
    set "has_deleted=1"
)
if not defined has_deleted echo    (none)
echo.

REM Show staged files
echo 📦 ALREADY STAGED FILES:
for /f "delims=" %%i in ('git diff --name-only --cached 2^>nul') do (
    echo    ✓ %%i
    set "has_staged=1"
)
if not defined has_staged echo    (none)
echo.

echo ====================================================
echo 🎯 SUMMARY: Git will only commit ACTUAL CHANGES
echo    - No duplicate files will be created
echo    - Only modified lines within files are stored
echo    - Unchanged files are completely ignored
echo ====================================================
echo.

REM Get commit message
set /p commit_msg="📝 Enter commit message: "
if "%commit_msg%"=="" (
    echo ❌ Commit message required!
    pause
    exit /b 1
)

echo.
echo 🔄 STEP 3: Staging Changes...
echo ====================================================

REM Add all changes (Git will ignore unchanged files)
git add .

echo ✅ All changes staged (unchanged files automatically ignored)
echo.

echo 📋 Final review - what will be committed:
git diff --name-only --cached
echo.

set /p confirm="✅ Commit these changes? (y/n): "
if /i not "%confirm%"=="y" (
    echo ❌ Commit cancelled.
    git reset HEAD . >nul 2>&1
    echo 🔄 Changes unstaged.
    pause
    exit /b 0
)

echo.
echo 💾 STEP 4: Committing Changes...
echo ====================================================

git commit -m "%commit_msg%"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Commit failed!
    pause
    exit /b 1
)

echo ✅ Commit successful!
echo.

set /p push_confirm="🚀 Push to GitHub? (y/n): "
if /i "%push_confirm%"=="y" (
    echo.
    echo 🚀 STEP 5: Pushing to GitHub...
    echo ====================================================
    
    git push origin main
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo 🎉 SUCCESS! Changes safely committed and pushed.
        echo 📊 No duplicates created - only actual changes committed.
    ) else (
        echo ❌ Push failed! Check internet connection and credentials.
        echo 💡 Changes are committed locally and safe.
    )
) else (
    echo 📝 Changes committed locally but not pushed.
    echo 💡 Run 'git push origin main' when ready.
)

echo.
echo ====================================================
echo ✅ SAFE COMMIT COMPLETED
echo 🎯 Only actual changes were committed (no duplicates)
echo ====================================================
pause
