"""
AI Chat Training Data and Response Templates
This file contains training data and templates for improving AI chat responses
"""

# Greeting patterns and responses
GREETING_PATTERNS = {
    'morning_greetings': [
        "good morning", "morning", "good morning!", "gm", "buenos dias"
    ],
    'afternoon_greetings': [
        "good afternoon", "afternoon", "good afternoon!", "buenas tardes"
    ],
    'evening_greetings': [
        "good evening", "evening", "good evening!", "buenas noches"
    ],
    'casual_greetings': [
        "hi", "hello", "hey", "yo", "sup", "what's up", "whats up", 
        "howdy", "hiya", "heya", "greetings"
    ],
    'formal_greetings': [
        "greetings", "salutations", "pleased to meet you", "nice to meet you"
    ]
}

# Thank you patterns and responses
THANKS_PATTERNS = {
    'basic_thanks': [
        "thank you", "thanks", "thx", "ty", "thank u"
    ],
    'enthusiastic_thanks': [
        "thank you so much", "thanks a lot", "really appreciate it",
        "you're awesome", "you're great", "fantastic help"
    ],
    'grateful_thanks': [
        "i appreciate", "grateful", "much appreciated", "very helpful"
    ]
}

# Help request patterns
HELP_PATTERNS = {
    'direct_help': [
        "help", "help me", "can you help", "i need help", "assist me"
    ],
    'question_help': [
        "how do i", "can you tell me", "could you explain", "what should i do"
    ],
    'guidance_help': [
        "guide me", "show me", "walk me through", "explain how"
    ]
}

# Farewell patterns
FAREWELL_PATTERNS = {
    'basic_farewell': [
        "bye", "goodbye", "see you", "later", "farewell"
    ],
    'polite_farewell': [
        "have a good day", "take care", "until next time", "see you later"
    ],
    'casual_farewell': [
        "catch you later", "peace", "cya", "ttyl", "gotta go"
    ]
}

# Response templates for different scenarios
RESPONSE_TEMPLATES = {
    'greeting_responses': {
        'morning': [
            "🌅 Good morning, {username}! Ready to find some great deals?",
            "☀️ Morning! Hope you're having a wonderful day!",
            "🌄 Good morning! Let's make this auction exciting!"
        ],
        'afternoon': [
            "☀️ Good afternoon, {username}! Perfect time for some bidding!",
            "🌞 Afternoon! How can I help you with this auction?",
            "🌤️ Good afternoon! Ready to explore this auction?"
        ],
        'evening': [
            "🌆 Good evening, {username}! Great time to check out auctions!",
            "🌙 Evening! Let's see what this auction has to offer!",
            "✨ Good evening! How can I assist you tonight?"
        ],
        'casual': [
            "👋 Hey there, {username}! What's up?",
            "🙋‍♂️ Hi! Ready to dive into this auction?",
            "👋 Hello! Great to see you here!"
        ]
    },
    
    'thanks_responses': {
        'basic': [
            "😊 You're welcome! Happy to help!",
            "🤗 No problem at all! That's what I'm here for!",
            "😄 Glad I could help! Feel free to ask anything else!"
        ],
        'enthusiastic': [
            "🌟 Aww, thank you so much! Your kind words mean a lot!",
            "🎉 You're absolutely amazing! Thanks for the feedback!",
            "💫 That's so sweet of you to say! I'm thrilled I could help!"
        ],
        'professional': [
            "🙏 You're very welcome! I appreciate your gratitude!",
            "✨ It's my pleasure to assist you! Thank you for the kind words!",
            "💼 Professional service is what we aim for! Glad you're satisfied!"
        ]
    },
    
    'help_responses': {
        'general': [
            "🤖 I'm here to help, {username}! What would you like to know?",
            "💪 Absolutely! I'm ready to assist you with anything!",
            "🎯 Of course! Let me know what you need help with!"
        ],
        'auction_specific': [
            "🏆 I can help you with everything about this auction!",
            "📊 Let me guide you through this auction process!",
            "🎪 I'm your auction expert! What do you need to know?"
        ]
    },
    
    'farewell_responses': {
        'basic': [
            "👋 Goodbye, {username}! Thanks for chatting!",
            "🌟 See you later! Good luck with your bidding!",
            "💫 Farewell! Hope to chat with you again soon!"
        ],
        'encouraging': [
            "🍀 Good luck with this auction, {username}!",
            "🎯 Happy bidding! May the best bid win!",
            "🏆 Go get 'em! I believe in your bidding skills!"
        ]
    }
}

# Contextual response enhancers
CONTEXT_ENHANCERS = {
    'auction_active': {
        'urgency_low': "🕐 You have plenty of time to decide!",
        'urgency_medium': "⏰ Time is ticking - consider your bid!",
        'urgency_high': "🚨 Hurry! This auction ends soon!"
    },
    
    'bid_activity': {
        'no_bids': "🎯 No bids yet - great opportunity!",
        'low_activity': "📊 Moderate bidding activity so far",
        'high_activity': "🔥 This auction is heating up!"
    },
    
    'price_context': {
        'good_deal': "💰 This looks like a great deal!",
        'fair_price': "⚖️ The price seems fair for this item",
        'premium_price': "💎 Premium pricing for quality item"
    }
}

# Personality traits for AI responses
AI_PERSONALITY = {
    'traits': [
        'helpful', 'friendly', 'knowledgeable', 'enthusiastic', 
        'professional', 'encouraging', 'patient', 'reliable'
    ],
    
    'tone_modifiers': {
        'excited': ['🎉', '🔥', '⚡', '🌟', '💫'],
        'helpful': ['💪', '🤝', '🎯', '✨', '🛠️'],
        'friendly': ['😊', '🤗', '👋', '😄', '🙂'],
        'professional': ['💼', '📊', '📈', '🎪', '🏆']
    }
}

# Common auction-related keywords and their contexts
AUCTION_KEYWORDS = {
    'price_related': [
        'price', 'cost', 'value', 'worth', 'expensive', 'cheap', 
        'deal', 'bargain', 'estimate', 'appraisal'
    ],
    
    'bidding_related': [
        'bid', 'bidding', 'offer', 'compete', 'win', 'winning',
        'strategy', 'increment', 'minimum', 'maximum'
    ],
    
    'time_related': [
        'time', 'deadline', 'end', 'finish', 'close', 'remaining',
        'left', 'duration', 'expires', 'countdown'
    ],
    
    'item_related': [
        'condition', 'quality', 'description', 'details', 'specs',
        'features', 'brand', 'model', 'authentic', 'genuine'
    ]
}

# Training scenarios for different user types
USER_SCENARIOS = {
    'new_user': {
        'needs': ['guidance', 'explanation', 'encouragement'],
        'concerns': ['how to bid', 'safety', 'payment process'],
        'response_style': 'detailed and patient'
    },
    
    'experienced_user': {
        'needs': ['quick info', 'specific details', 'advanced features'],
        'concerns': ['price analysis', 'competition', 'timing'],
        'response_style': 'concise and informative'
    },
    
    'casual_browser': {
        'needs': ['general info', 'entertainment', 'discovery'],
        'concerns': ['item details', 'pricing', 'availability'],
        'response_style': 'engaging and informative'
    }
}

def get_response_template(category, subcategory, **kwargs):
    """Get a response template with variable substitution"""
    try:
        templates = RESPONSE_TEMPLATES[category][subcategory]
        import random
        template = random.choice(templates)
        return template.format(**kwargs)
    except (KeyError, IndexError):
        return "I'm here to help! What would you like to know?"

def analyze_user_intent(message):
    """Analyze user message to determine intent and appropriate response style"""
    message_lower = message.lower()
    
    # Check for greeting patterns
    for category, patterns in GREETING_PATTERNS.items():
        if any(pattern in message_lower for pattern in patterns):
            return 'greeting', category
    
    # Check for thanks patterns
    for category, patterns in THANKS_PATTERNS.items():
        if any(pattern in message_lower for pattern in patterns):
            return 'thanks', category
    
    # Check for help patterns
    for category, patterns in HELP_PATTERNS.items():
        if any(pattern in message_lower for pattern in patterns):
            return 'help', category
    
    # Check for farewell patterns
    for category, patterns in FAREWELL_PATTERNS.items():
        if any(pattern in message_lower for pattern in patterns):
            return 'farewell', category
    
    return 'general', 'unknown'
