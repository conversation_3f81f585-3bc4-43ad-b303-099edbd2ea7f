.admin-dashboard {
  padding: var(--space-xl);
  background: var(--bg-secondary);
  min-height: 100vh;
  position: relative;
}

.admin-dashboard::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-elegant);
  opacity: 0.3;
  pointer-events: none;
  z-index: 0;
}

.admin-dashboard > * {
  position: relative;
  z-index: 1;
}

.admin-dashboard.loading,
.admin-dashboard.error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
}

.dashboard-header h1 {
  color: #333;
  font-size: 2.5em;
  margin-bottom: 10px;
}

.dashboard-header p {
  color: #666;
  font-size: 1.2em;
}

/* Navigation Tabs */
.dashboard-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  padding: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tab {
  background: transparent;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1em;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
}

.tab:hover {
  background: #f8f9fa;
  color: #333;
}

.tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Overview Tab */
.overview-tab {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.metric-card {
  background: var(--white);
  border-radius: var(--radius-auction);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-lg);
  border: 2px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--space-xl);
  transition: var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: var(--transition-normal);
}

.metric-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.metric-card:hover::before {
  transform: scaleX(1);
}

.metric-card.revenue {
  background: var(--gradient-success);
  color: white;
  border-color: var(--success-color);
}

.metric-card.growth {
  background: var(--gradient-primary);
  color: white;
  border-color: var(--primary-color);
}

.metric-card.users {
  background: var(--gradient-secondary);
  color: white;
  border-color: var(--secondary-color);
}

.metric-card.auctions {
  background: var(--gradient-accent);
  color: white;
  border-color: var(--accent-color);
}

.metric-icon {
  font-size: 3em;
  opacity: 0.9;
}

.metric-content h3 {
  margin: 0;
  font-size: 2.2em;
  font-weight: 700;
}

.metric-content p {
  margin: 5px 0;
  font-size: 1.2em;
  font-weight: 500;
}

.metric-content small {
  opacity: 0.8;
  font-size: 0.9em;
}

.metric-content .positive {
  color: #4CAF50;
}

.metric-content .negative {
  color: #f44336;
}

/* Revenue Breakdown */
.revenue-breakdown {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.revenue-breakdown h3 {
  margin-bottom: 20px;
  color: #333;
}

.revenue-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.revenue-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.revenue-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
}

.revenue-card h4 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 0.9em;
  text-transform: uppercase;
}

.revenue-card p {
  margin: 0;
  font-size: 1.5em;
  font-weight: 700;
  color: #4CAF50;
}

/* Recent Activity - Enhanced */
.recent-activity {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-top: 30px;
}

.recent-activity h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.4em;
  font-weight: 600;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
  margin-top: 20px;
}

.activity-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #007bff;
}

.activity-section h4 {
  color: #495057;
  margin-bottom: 15px;
  font-size: 1.1em;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.activity-item:hover {
  background-color: rgba(0, 123, 255, 0.05);
  border-radius: 6px;
  padding: 12px 8px;
  margin: 0 -8px;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-main {
  flex: 1;
}

.activity-main strong {
  color: #2c3e50;
  font-size: 0.95em;
  display: block;
  margin-bottom: 4px;
}

.activity-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.activity-amount {
  color: #28a745;
  font-weight: 600;
  font-size: 0.9em;
}

.activity-user {
  color: #6c757d;
  font-size: 0.85em;
}

.activity-auction {
  color: #6c757d;
  font-size: 0.85em;
}

.activity-email {
  color: #6c757d;
  font-size: 0.85em;
}

.activity-status {
  font-size: 0.8em;
  padding: 2px 6px;
  border-radius: 12px;
  font-weight: 500;
}

.activity-status.active {
  background-color: #d4edda;
  color: #155724;
}

.activity-status.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.activity-time {
  color: #adb5bd;
  font-size: 0.8em;
  white-space: nowrap;
  margin-left: 10px;
}

.bid-amount {
  color: #007bff !important;
  font-weight: 600 !important;
}

.no-activity {
  color: #6c757d;
  font-style: italic;
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 2px dashed #dee2e6;
}

/* Tables */
.users-tab, .auctions-tab, .analytics-tab {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Enhanced Analytics Tab Styling */
.analytics-tab {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e9ecef;
}

.analytics-tab .tab-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

.analytics-tab .tab-header h3 {
  margin: 0 0 10px 0;
  font-size: 1.8em;
  font-weight: 600;
}

.analytics-tab .tab-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1em;
}

.tab-header {
  margin-bottom: 25px;
  text-align: center;
}

.tab-header h3 {
  color: #333;
  margin-bottom: 5px;
}

.tab-header p {
  color: #666;
}

.users-table, .auctions-table {
  overflow-x: auto;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #eee;
  align-items: center;
  transition: background-color 0.3s ease;
}

.table-row:hover {
  background: #f8f9fa;
}

.user-info, .auction-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-info strong, .auction-info strong {
  color: #333;
}

.user-info small, .auction-info small {
  color: #666;
  font-size: 0.8em;
}

.badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 0.7em;
  font-weight: 600;
  text-transform: uppercase;
}

.badge.admin {
  background: #667eea;
  color: white;
}

.status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: 600;
  text-align: center;
}

.status.active, .status.approved {
  background: #d4edda;
  color: #155724;
}

.status.inactive, .status.pending {
  background: #f8d7da;
  color: #721c24;
}

.actions {
  display: flex;
  gap: 8px;
}

.delete-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8em;
  cursor: pointer;
  transition: all 0.3s ease;
}

.delete-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* AI Analytics - Improved Visibility */
.ai-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 25px;
  margin-top: 20px;
}

.ai-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #2c3e50;
  border: 2px solid #dee2e6;
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.ai-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.ai-card h4 {
  margin: 0 0 15px 0;
  font-size: 1.1em;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ai-card p {
  margin: 0 0 10px 0;
  font-size: 2.5em;
  font-weight: 700;
  color: #007bff;
  text-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.ai-card small {
  color: #6c757d;
  font-size: 0.85em;
  font-weight: 500;
  display: block;
  margin-top: 5px;
}

/* Special styling for different AI card types */
.ai-card:nth-child(1) {
  border-left: 4px solid #28a745;
}

.ai-card:nth-child(1) p {
  color: #28a745;
}

.ai-card:nth-child(2) {
  border-left: 4px solid #ffc107;
}

.ai-card:nth-child(2) p {
  color: #ffc107;
}

.ai-card:nth-child(3) {
  border-left: 4px solid #17a2b8;
}

.ai-card:nth-child(3) p {
  color: #17a2b8;
}

.ai-card:nth-child(4) {
  border-left: 4px solid #6f42c1;
}

.ai-card:nth-child(4) p {
  color: #6f42c1;
}

/* Dashboard Actions */
.dashboard-actions {
  text-align: center;
  margin-top: 30px;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 15px;
  }
  
  .dashboard-tabs {
    flex-direction: column;
    gap: 5px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .revenue-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .activity-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .activity-section {
    padding: 15px;
  }

  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .activity-time {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 10px;
    text-align: center;
  }
  
  .table-header {
    display: none;
  }
  
  .table-row {
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 10px;
    border: none;
  }
  
  .ai-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}
