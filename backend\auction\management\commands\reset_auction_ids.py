from django.core.management.base import BaseCommand
from django.db import transaction

from auction.models import (Auction, AutoBid, Bid, ChatMessage, ChatRoom,
                            Review, Watchlist)


class Command(BaseCommand):
    help = "Reset auction IDs to start from 1"

    def handle(self, *args, **options):
        self.stdout.write("🔄 Starting auction ID reset process...")

        with transaction.atomic():
            # Get all auctions ordered by creation date
            auctions = Auction.objects.all().order_by("created_at")
            total_auctions = auctions.count()

            if total_auctions == 0:
                self.stdout.write("❌ No auctions found to reset.")
                return

            self.stdout.write(f"📊 Found {total_auctions} auctions to renumber")

            # Store auction data temporarily
            auction_data = []
            for auction in auctions:
                auction_data.append(
                    {
                        "title": auction.title,
                        "description": auction.description,
                        "starting_bid": auction.starting_bid,
                        "current_bid": auction.current_bid,
                        "reserve_price": auction.reserve_price,
                        "buy_now_price": auction.buy_now_price,
                        "start_time": auction.start_time,
                        "end_time": auction.end_time,
                        "auction_type": auction.auction_type,
                        "category": auction.category,
                        "image": auction.image,
                        "additional_images": auction.additional_images,
                        "location": auction.location,
                        "condition": auction.condition,
                        "shipping_cost": auction.shipping_cost,
                        "owner": auction.owner,
                        "created_at": auction.created_at,
                        "updated_at": auction.updated_at,
                        "is_closed": auction.is_closed,
                        "approved": auction.approved,
                        "featured": auction.featured,
                        "views_count": auction.views_count,
                    }
                )

            self.stdout.write("🗑️  Deleting existing data...")

            # Delete all existing data
            ChatMessage.objects.all().delete()
            ChatRoom.objects.all().delete()
            Watchlist.objects.all().delete()
            AutoBid.objects.all().delete()
            Review.objects.all().delete()
            Bid.objects.all().delete()
            Auction.objects.all().delete()

            self.stdout.write("🔧 Resetting auto-increment sequence...")

            # Reset the auto-increment sequence (database-agnostic)
            from django.db import connection

            cursor = connection.cursor()

            # Check database type and reset sequence accordingly
            if "sqlite" in connection.vendor:
                cursor.execute(
                    "DELETE FROM sqlite_sequence WHERE name='auction_auction';"
                )
            elif "postgresql" in connection.vendor:
                cursor.execute("ALTER SEQUENCE auction_auction_id_seq RESTART WITH 1;")
            elif "mysql" in connection.vendor:
                cursor.execute("ALTER TABLE auction_auction AUTO_INCREMENT = 1;")
            else:
                self.stdout.write("⚠️  Unknown database type, skipping sequence reset")

            self.stdout.write("📝 Creating auctions with new IDs...")

            # Create auctions with new IDs
            for i, data in enumerate(auction_data, 1):
                auction = Auction.objects.create(
                    title=data["title"],
                    description=data["description"],
                    starting_bid=data["starting_bid"],
                    current_bid=data["current_bid"],
                    reserve_price=data["reserve_price"],
                    buy_now_price=data["buy_now_price"],
                    start_time=data["start_time"],
                    end_time=data["end_time"],
                    auction_type=data["auction_type"],
                    category=data["category"],
                    image=data["image"],
                    additional_images=data["additional_images"],
                    location=data["location"],
                    condition=data["condition"],
                    shipping_cost=data["shipping_cost"],
                    owner=data["owner"],
                    is_closed=data["is_closed"],
                    approved=data["approved"],
                    featured=data["featured"],
                    views_count=data["views_count"],
                )
                # Update timestamps manually
                Auction.objects.filter(id=auction.id).update(
                    created_at=data["created_at"], updated_at=data["updated_at"]
                )
                self.stdout.write(f"✅ Created auction {auction.id}: '{data['title']}'")

            self.stdout.write("🎉 Successfully reset auction IDs!")
            self.stdout.write(f"📊 Final count: {Auction.objects.count()} auctions")
            self.stdout.write(f"🔢 ID range: 1 to {Auction.objects.count()}")

            # Display the new auction list
            self.stdout.write("\n📋 Updated Auction List:")
            self.stdout.write("-" * 60)
            for auction in Auction.objects.all().order_by("id"):
                self.stdout.write(f"ID {auction.id:2d}: {auction.title}")
