#!/usr/bin/env python3
"""
Test script for role-based user system
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from django.contrib.auth.models import User
from auction.models import UserProfile

def test_role_system():
    """Test the role-based system functionality"""
    print("🧪 Testing Role-Based User System")
    print("=" * 50)
    
    # Test 1: Create users with different roles
    print("\n1. Creating test users with different roles...")
    
    # Create bidder user
    try:
        bidder_user = User.objects.create_user(
            username='test_bidder',
            email='<EMAIL>',
            password='testpass123'
        )
        bidder_profile, created = UserProfile.objects.get_or_create(user=bidder_user)
        bidder_profile.user_role = 'bidder'
        bidder_profile.save()
        print(f"✅ Created bidder user: {bidder_user.username}")
        print(f"   - Can create auctions: {bidder_profile.can_create_auctions()}")
        print(f"   - Can bid on auctions: {bidder_profile.can_bid_on_auctions()}")
    except Exception as e:
        print(f"❌ Error creating bidder user: {e}")
    
    # Create seller user
    try:
        seller_user = User.objects.create_user(
            username='test_seller',
            email='<EMAIL>',
            password='testpass123'
        )
        seller_profile, created = UserProfile.objects.get_or_create(user=seller_user)
        seller_profile.user_role = 'seller'
        seller_profile.save()
        print(f"✅ Created seller user: {seller_user.username}")
        print(f"   - Can create auctions: {seller_profile.can_create_auctions()}")
        print(f"   - Can bid on auctions: {seller_profile.can_bid_on_auctions()}")
    except Exception as e:
        print(f"❌ Error creating seller user: {e}")
    
    # Create both user
    try:
        both_user = User.objects.create_user(
            username='test_both',
            email='<EMAIL>',
            password='testpass123'
        )
        both_profile, created = UserProfile.objects.get_or_create(user=both_user)
        both_profile.user_role = 'both'
        both_profile.save()
        print(f"✅ Created both user: {both_user.username}")
        print(f"   - Can create auctions: {both_profile.can_create_auctions()}")
        print(f"   - Can bid on auctions: {both_profile.can_bid_on_auctions()}")
    except Exception as e:
        print(f"❌ Error creating both user: {e}")
    
    # Test 2: Test JWT token generation
    print("\n2. Testing JWT token generation...")
    try:
        from auction.serializers import MyTokenObtainPairSerializer
        from rest_framework_simplejwt.tokens import RefreshToken
        
        # Test token for bidder
        token = MyTokenObtainPairSerializer.get_token(bidder_user)
        print(f"✅ Generated token for bidder user")
        print(f"   - User role: {token.get('user_role')}")
        print(f"   - Can create auctions: {token.get('can_create_auctions')}")
        print(f"   - Can bid on auctions: {token.get('can_bid_on_auctions')}")
        
        # Test token for seller
        token = MyTokenObtainPairSerializer.get_token(seller_user)
        print(f"✅ Generated token for seller user")
        print(f"   - User role: {token.get('user_role')}")
        print(f"   - Can create auctions: {token.get('can_create_auctions')}")
        print(f"   - Can bid on auctions: {token.get('can_bid_on_auctions')}")
        
    except Exception as e:
        print(f"❌ Error testing JWT tokens: {e}")
    
    # Test 3: Clean up test users
    print("\n3. Cleaning up test users...")
    try:
        User.objects.filter(username__startswith='test_').delete()
        print("✅ Cleaned up test users")
    except Exception as e:
        print(f"❌ Error cleaning up: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Role-based system test completed!")

if __name__ == "__main__":
    test_role_system()
