{"ast": null, "code": "var hashClear = require('./_hashClear'),\n  hashDelete = require('./_hashDelete'),\n  hashGet = require('./_hashGet'),\n  hashHas = require('./_hashHas'),\n  hashSet = require('./_hashSet');\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n    length = entries == null ? 0 : entries.length;\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\nmodule.exports = Hash;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}