import React, { useState, useEffect } from "react";
import { extendedAuctionService } from "../services/extendedAuctionService";
import "./EnhancedAdminDashboard.css";

const EnhancedAdminDashboard = () => {
  const [dashboardStats, setDashboardStats] = useState(null);
  const [revenueReport, setRevenueReport] = useState(null);
  const [userActivity, setUserActivity] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [wsConnection, setWsConnection] = useState(null);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  });

  useEffect(() => {
    fetchAllData();
    setupWebSocketConnection();

    // DISABLED: Auto-refresh functionality to prevent excessive API calls
    // Use WebSocket for real-time updates instead
    // const interval = setInterval(() => {
    //   console.log("Auto-refreshing dashboard data...");
    //   fetchAllData();
    // }, 60000); // Refresh every minute

    return () => {
      if (wsConnection) {
        wsConnection.close();
      }
      // clearInterval(interval);
    };
  }, []);

  const fetchAllData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [statsResponse, revenueResponse, activityResponse] =
        await Promise.all([
          extendedAuctionService.getAdminDashboardStats(),
          extendedAuctionService.getRevenueReport(
            dateRange.startDate,
            dateRange.endDate
          ),
          extendedAuctionService.getUserActivityReport(),
        ]);

      if (statsResponse.success) {
        setDashboardStats(statsResponse.data);
      }
      if (revenueResponse.success) {
        setRevenueReport(revenueResponse.data);
      }
      if (activityResponse.success) {
        setUserActivity(activityResponse.data);
      }
    } catch (err) {
      setError("Failed to load dashboard data");
      console.error("Dashboard error:", err);
    } finally {
      setLoading(false);
    }
  };

  const setupWebSocketConnection = () => {
    try {
      const socket = extendedAuctionService.subscribeToAdminDashboard(
        (data) => {
          console.log("Received dashboard update:", data);

          // Update dashboard stats in real-time
          setDashboardStats((prev) => {
            if (!prev) return null;

            return {
              ...prev,
              overview: {
                ...prev.overview,
                ...data.basic_metrics,
              },
              revenue_data: {
                ...prev.revenue_data,
                ...data.revenue_data,
              },
            };
          });

          // Show notification of update
          console.log("Dashboard updated in real-time");
        }
      );
      setWsConnection(socket);
    } catch (err) {
      console.error("WebSocket connection error:", err);
    }
  };

  const handleDateRangeChange = async () => {
    try {
      const response = await extendedAuctionService.getRevenueReport(
        dateRange.startDate,
        dateRange.endDate
      );
      if (response.success) {
        setRevenueReport(response.data);
      }
    } catch (err) {
      console.error("Error updating revenue report:", err);
    }
  };

  if (loading) {
    return (
      <div className="enhanced-admin-loading">
        <div className="loading-spinner"></div>
        <p>Loading enhanced dashboard...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="enhanced-admin-error">
        <p>❌ {error}</p>
        <button onClick={fetchAllData} className="retry-btn">
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="enhanced-admin-dashboard">
      <div className="dashboard-header">
        <h1>📊 Enhanced Admin Dashboard</h1>
        <p>Real-time analytics and comprehensive reporting</p>
      </div>

      {/* Overview Stats */}
      {dashboardStats?.overview && (
        <div className="stats-overview">
          <div className="stat-card">
            <div className="stat-icon">👥</div>
            <div className="stat-content">
              <h3>{dashboardStats.overview.total_users.toLocaleString()}</h3>
              <p>Total Users</p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">🏷️</div>
            <div className="stat-content">
              <h3>{dashboardStats.overview.total_auctions.toLocaleString()}</h3>
              <p>Total Auctions</p>
            </div>
          </div>

          <div className="stat-card active">
            <div className="stat-icon">🔥</div>
            <div className="stat-content">
              <h3>
                {dashboardStats.overview.active_auctions.toLocaleString()}
              </h3>
              <p>Active Auctions</p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">💰</div>
            <div className="stat-content">
              <h3>${dashboardStats.overview.total_revenue.toLocaleString()}</h3>
              <p>Total Revenue</p>
            </div>
          </div>
        </div>
      )}

      {/* Revenue Report */}
      {revenueReport && (
        <div className="revenue-section">
          <div className="section-header">
            <h2>💰 Revenue Report</h2>
            <div className="date-range-controls">
              <input
                type="date"
                value={dateRange.startDate}
                onChange={(e) =>
                  setDateRange((prev) => ({
                    ...prev,
                    startDate: e.target.value,
                  }))
                }
              />
              <input
                type="date"
                value={dateRange.endDate}
                onChange={(e) =>
                  setDateRange((prev) => ({ ...prev, endDate: e.target.value }))
                }
              />
              <button onClick={handleDateRangeChange} className="update-btn">
                Update
              </button>
            </div>
          </div>

          <div className="revenue-stats">
            <div className="revenue-card">
              <h4>Total Revenue</h4>
              <p className="revenue-amount">
                ${revenueReport.summary.total_revenue.toLocaleString()}
              </p>
            </div>
            <div className="revenue-card">
              <h4>Average Sale Price</h4>
              <p className="revenue-amount">
                ${revenueReport.summary.average_sale_price.toLocaleString()}
              </p>
            </div>
            <div className="revenue-card">
              <h4>Total Sales</h4>
              <p className="revenue-amount">
                {revenueReport.summary.total_sales.toLocaleString()}
              </p>
            </div>
          </div>

          {/* Category Breakdown */}
          {revenueReport.category_breakdown && (
            <div className="category-breakdown">
              <h3>Revenue by Category</h3>
              <div className="category-list">
                {revenueReport.category_breakdown.map((category, index) => (
                  <div key={index} className="category-item">
                    <span className="category-name">
                      {category.category__name}
                    </span>
                    <span className="category-revenue">
                      ₹{category.revenue.toLocaleString()}
                    </span>
                    <span className="category-sales">
                      ({category.sales_count} sales)
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* User Activity */}
      {userActivity && (
        <div className="user-activity-section">
          <h2>👥 User Activity</h2>
          <div className="activity-stats">
            <div className="activity-card">
              <h4>Active Users (30d)</h4>
              <p>{userActivity.active_users_30d.toLocaleString()}</p>
            </div>
            <div className="activity-card">
              <h4>Total Users</h4>
              <p>{userActivity.total_users.toLocaleString()}</p>
            </div>
          </div>

          {/* Top Bidders */}
          {userActivity.top_bidders && (
            <div className="top-bidders">
              <h3>🏆 Top Bidders</h3>
              <div className="bidders-list">
                {userActivity.top_bidders.map((bidder, index) => (
                  <div key={index} className="bidder-item">
                    <span className="bidder-rank">#{index + 1}</span>
                    <span className="bidder-name">{bidder.username}</span>
                    <span className="bidder-bids">{bidder.bid_count} bids</span>
                    <span className="bidder-amount">
                      ₹{bidder.total_bid_amount.toLocaleString()}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Top Sellers */}
      {dashboardStats?.top_sellers && (
        <div className="top-sellers-section">
          <h2>🌟 Top Sellers</h2>
          <div className="sellers-list">
            {dashboardStats.top_sellers.map((seller, index) => (
              <div key={index} className="seller-item">
                <span className="seller-rank">#{index + 1}</span>
                <span className="seller-name">{seller.username}</span>
                <span className="seller-auctions">
                  {seller.auction_count} auctions
                </span>
                <span className="seller-sales">
                  ${seller.total_sales.toLocaleString()}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="dashboard-footer">
        <button onClick={fetchAllData} className="refresh-btn">
          🔄 Refresh All Data
        </button>
        <p className="last-updated">
          Last updated: {new Date().toLocaleTimeString()}
        </p>
      </div>
    </div>
  );
};

export default EnhancedAdminDashboard;
