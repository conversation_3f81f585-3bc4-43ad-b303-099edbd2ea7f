"""
Custom CSRF Exemption Middleware
Exempts API endpoints from CSRF while keeping it enabled for admin panel
"""

from django.conf import settings
from django.middleware.csrf import CsrfViewMiddleware
from django.utils.deprecation import MiddlewareMixin


class APICSRFExemptMiddleware(MiddlewareMixin):
    """
    Middleware that exempts API endpoints from CSRF protection
    while keeping CSRF enabled for admin panel and other views
    """

    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)

    def process_view(self, request, view_func, view_args, view_kwargs):
        # Check if this is an API request
        if request.path.startswith("/api/"):
            # Exempt API requests from CSRF
            setattr(request, "_dont_enforce_csrf_checks", True)

        # For all other requests (including admin), CSRF will be enforced
        return None


class SelectiveCSRFMiddleware(CsrfViewMiddleware):
    """
    Custom CSRF middleware that only applies to non-API requests
    """

    def process_view(self, request, callback, callback_args, callback_kwargs):
        # Skip CSRF for API endpoints
        if request.path.startswith("/api/"):
            return None

        # Apply CSRF for all other requests (admin, etc.)
        return super().process_view(request, callback, callback_args, callback_kwargs)
