/* ===== DARK MODE THEME SYSTEM ===== */

/* CSS Variables for Light Theme (Default) */
:root {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #868e96;
  --text-inverse: #ffffff;
  --border-color: #dee2e6;
  --border-light: #f1f3f4;
  --border-dark: #adb5bd;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.25);
  --primary-color: #3498db;
  --secondary-color: #2c3e50;
  --success-color: #27ae60;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --info-color: #17a2b8;
}

/* CSS Variables for Dark Theme */
[data-theme="dark"], .dark-mode-bg {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-inverse: #212529;
  --border-color: #334155;
  --border-light: #475569;
  --border-dark: #64748b;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.6);
  --primary-color: #3498db;
  --secondary-color: #34495e;
  --success-color: #2ecc71;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --info-color: #3498db;
}

/* ===== GLOBAL DARK MODE STYLES ===== */

/* Body and Background */
.dark-mode-bg {
  background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
  color: var(--text-primary);
  min-height: 100vh;
}

/* Text Colors */
.dark-mode-bg,
.dark-mode-bg * {
  color: var(--text-primary) !important;
}

.dark-mode-bg .text-muted {
  color: var(--text-muted) !important;
}

.dark-mode-bg .text-secondary {
  color: var(--text-secondary) !important;
}

.dark-mode-bg .text-dark {
  color: var(--text-primary) !important;
}

.dark-mode-bg .text-light {
  color: var(--text-primary) !important;
}

/* ===== COMPONENT SPECIFIC STYLES ===== */

/* Cards */
.dark-mode-bg .card,
.dark-mode-card {
  background: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
  box-shadow: var(--shadow-md);
}

.dark-mode-bg .card:hover {
  background: var(--bg-tertiary) !important;
  border-color: var(--primary-color) !important;
}

.dark-mode-bg .card-header {
  background: var(--bg-tertiary) !important;
  border-bottom-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.dark-mode-bg .card-footer {
  background: var(--bg-tertiary) !important;
  border-top-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* Buttons */
.dark-mode-bg .btn-outline-primary {
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.dark-mode-bg .btn-outline-primary:hover {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.dark-mode-bg .btn-outline-secondary {
  color: var(--text-secondary) !important;
  border-color: var(--border-color) !important;
}

.dark-mode-bg .btn-outline-secondary:hover {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* Forms */
.dark-mode-bg .form-control {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.dark-mode-bg .form-control:focus {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--primary-color) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25) !important;
}

.dark-mode-bg .form-control::placeholder {
  color: var(--text-muted) !important;
}

.dark-mode-bg .form-select {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.dark-mode-bg .form-label {
  color: var(--text-primary) !important;
}

/* Tables */
.dark-mode-bg .table {
  color: var(--text-primary) !important;
}

.dark-mode-bg .table-striped > tbody > tr:nth-of-type(odd) > td {
  background-color: var(--bg-tertiary) !important;
}

.dark-mode-bg .table-hover > tbody > tr:hover > td {
  background-color: var(--bg-tertiary) !important;
}

/* Modals */
.dark-mode-bg .modal-content {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.dark-mode-bg .modal-header {
  border-bottom-color: var(--border-color) !important;
}

.dark-mode-bg .modal-footer {
  border-top-color: var(--border-color) !important;
}

/* Dropdowns */
.dark-mode-bg .dropdown-menu {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
}

.dark-mode-bg .dropdown-item {
  color: var(--text-primary) !important;
}

.dark-mode-bg .dropdown-item:hover {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* Alerts */
.dark-mode-bg .alert-primary {
  background-color: rgba(52, 152, 219, 0.2) !important;
  border-color: var(--primary-color) !important;
  color: var(--text-primary) !important;
}

.dark-mode-bg .alert-success {
  background-color: rgba(46, 204, 113, 0.2) !important;
  border-color: var(--success-color) !important;
  color: var(--text-primary) !important;
}

.dark-mode-bg .alert-danger {
  background-color: rgba(231, 76, 60, 0.2) !important;
  border-color: var(--danger-color) !important;
  color: var(--text-primary) !important;
}

.dark-mode-bg .alert-warning {
  background-color: rgba(243, 156, 18, 0.2) !important;
  border-color: var(--warning-color) !important;
  color: var(--text-primary) !important;
}

/* Breadcrumbs */
.dark-mode-bg .breadcrumb {
  background-color: var(--bg-secondary) !important;
}

.dark-mode-bg .breadcrumb-item a {
  color: var(--primary-color) !important;
}

.dark-mode-bg .breadcrumb-item.active {
  color: var(--text-secondary) !important;
}

/* Pagination */
.dark-mode-bg .page-link {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.dark-mode-bg .page-link:hover {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--primary-color) !important;
  color: var(--text-primary) !important;
}

.dark-mode-bg .page-item.active .page-link {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

/* Footer */
.dark-mode-bg footer {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

.dark-mode-bg footer .bg-light {
  background-color: var(--bg-secondary) !important;
}

.dark-mode-bg footer .bg-dark {
  background-color: var(--bg-primary) !important;
}

.dark-mode-bg footer a {
  color: var(--text-secondary) !important;
}

.dark-mode-bg footer a:hover {
  color: var(--primary-color) !important;
}

/* Containers */
.dark-mode-container {
  background-color: transparent !important;
}

/* Borders */
.dark-mode-bg .border {
  border-color: var(--border-color) !important;
}

.dark-mode-bg .border-top {
  border-top-color: var(--border-color) !important;
}

.dark-mode-bg .border-bottom {
  border-bottom-color: var(--border-color) !important;
}

.dark-mode-bg .border-left {
  border-left-color: var(--border-color) !important;
}

.dark-mode-bg .border-right {
  border-right-color: var(--border-color) !important;
}

/* Shadows */
.dark-mode-bg .shadow-sm {
  box-shadow: var(--shadow-sm) !important;
}

.dark-mode-bg .shadow {
  box-shadow: var(--shadow-md) !important;
}

.dark-mode-bg .shadow-lg {
  box-shadow: var(--shadow-lg) !important;
}

/* Specific Background Colors */
.dark-mode-bg .bg-white {
  background-color: var(--bg-secondary) !important;
}

.dark-mode-bg .bg-light {
  background-color: var(--bg-secondary) !important;
}

.dark-mode-bg .bg-secondary {
  background-color: var(--bg-tertiary) !important;
}

/* Text Selection */
.dark-mode-bg ::selection {
  background-color: var(--primary-color);
  color: white;
}

.dark-mode-bg ::-moz-selection {
  background-color: var(--primary-color);
  color: white;
}
