# Fraud Detection System - Test Documentation

## Overview
Comprehensive test suite for the Online Auction System's fraud detection functionality. This test suite validates the fraud detection models, API endpoints, business logic, and integration with other system components.

## Test Structure

### 📁 Test Files
- `test_fraud_detection.py` - Main test suite with all test classes
- `run_fraud_detection_tests.py` - Quick test runner script
- `FRAUD_DETECTION_TESTS.md` - This documentation file

### 🧪 Test Classes

#### 1. FraudDetectionModelTest
Tests the FraudDetection model functionality:
- ✅ Model creation and validation
- ✅ String representation
- ✅ Fraud type choices validation
- ✅ Status choices validation  
- ✅ Risk score validation (0-100)
- ✅ JSON details field functionality
- ✅ Resolution workflow

#### 2. FraudDetectionAPITest
Tests the REST API endpoints:
- ✅ GET fraud detection list
- ✅ GET specific fraud detection record
- ✅ POST create new fraud detection
- ✅ PATCH update fraud detection status
- ✅ DELETE fraud detection record
- ✅ Filtering by fraud_type, status, user
- ✅ Ordering by risk_score, created_at

#### 3. FraudDetectionBusinessLogicTest
Tests fraud detection scenarios:
- ✅ Suspicious bidding pattern detection
- ✅ Fake listing detection
- ✅ Payment fraud detection
- ✅ Account takeover detection
- ✅ Bot activity detection
- ✅ Fraud resolution workflow
- ✅ False positive handling
- ✅ Confirmed fraud handling

#### 4. FraudDetectionIntegrationTest
Tests integration with other components:
- ✅ Notification system integration
- ✅ User profile impact
- ✅ High-risk fraud workflow
- ✅ Reporting and metrics

#### 5. FraudDetectionLiveAPITest
Tests live API endpoints:
- ✅ Real-time API testing
- ✅ Dashboard integration testing
- ✅ Live system validation

## Fraud Detection Features Tested

### 🔍 Fraud Types
1. **Suspicious Bidding** - Rapid or unusual bidding patterns
2. **Fake Listing** - Fraudulent auction listings
3. **Payment Fraud** - Payment-related fraud attempts
4. **Account Takeover** - Compromised user accounts
5. **Bot Activity** - Automated/bot behavior detection

### 📊 Risk Scoring
- Risk scores from 0-100
- Validation of score ranges
- High-risk threshold testing (95+)
- Risk-based workflow testing

### 🔄 Status Workflow
- **Pending** - Initial fraud detection state
- **Resolved** - Fraud case resolved by admin
- **Confirmed** - Fraud confirmed after investigation
- **False Positive** - Incorrectly flagged as fraud

### 📋 Details Field
- JSON field for storing fraud-specific data
- Complex nested data structures
- Fraud pattern information
- Investigation notes and actions

## Running the Tests

### Quick Test Runner
```bash
cd testing_suite
python run_fraud_detection_tests.py
```

### Full Test Suite
```bash
cd testing_suite
python test_fraud_detection.py
```

### Django Test Runner
```bash
python manage.py test testing_suite.test_fraud_detection
```

### Specific Test Class
```bash
python manage.py test testing_suite.test_fraud_detection.FraudDetectionModelTest
```

## Prerequisites

### 1. Django Server Running
```bash
python manage.py runserver
```

### 2. Database Setup
```bash
python manage.py makemigrations
python manage.py migrate
```

### 3. Required Dependencies
- Django REST Framework
- requests library
- PostgreSQL (or configured database)

## Test Data

### Sample Fraud Detection Records
The tests create various fraud detection scenarios:

```python
# Suspicious Bidding Example
{
    "fraud_type": "suspicious_bidding",
    "risk_score": 85,
    "details": {
        "rapid_bids": True,
        "bid_count": 10,
        "time_span_minutes": 5,
        "bid_increments": "small_consistent"
    },
    "status": "pending"
}

# Bot Activity Example
{
    "fraud_type": "bot_activity", 
    "risk_score": 93,
    "details": {
        "automated_bidding": True,
        "consistent_timing": True,
        "bid_frequency_per_minute": 12,
        "captcha_failures": 5
    },
    "status": "confirmed"
}
```

## API Endpoints Tested

### Base URL: `http://127.0.0.1:8000/api/fraud-detection/`

#### GET Endpoints
- `GET /fraud-detection/` - List all fraud detections
- `GET /fraud-detection/{id}/` - Get specific fraud detection
- `GET /fraud-detection/?status=pending` - Filter by status
- `GET /fraud-detection/?fraud_type=suspicious_bidding` - Filter by type
- `GET /fraud-detection/?ordering=-risk_score` - Order by risk score

#### POST/PATCH/DELETE Endpoints
- `POST /fraud-detection/` - Create new fraud detection
- `PATCH /fraud-detection/{id}/` - Update fraud detection
- `DELETE /fraud-detection/{id}/` - Delete fraud detection

## Expected Test Results

### ✅ Successful Test Output
```
🔍 FRAUD DETECTION SYSTEM - COMPREHENSIVE TEST SUITE
==================================================================

📋 Running Django Unit Tests...
✅ All Django unit tests passed!

🌐 Running Live API Tests...
✅ GET fraud-detection list: 5 records
✅ Filter by status=pending: 2 records  
✅ Filter by fraud_type: 1 records
✅ Order by risk_score: Highest risk = 95

📊 Testing Dashboard Integration
✅ Dashboard pending alerts: 2 alerts

🎯 FRAUD DETECTION TEST SUMMARY
✅ Model Tests: FraudDetection model validation and functionality
✅ API Tests: REST API endpoints for fraud detection
✅ Business Logic Tests: Fraud detection scenarios and workflows
✅ Integration Tests: Integration with notifications and dashboard
✅ Live API Tests: Real-time API endpoint testing

🚀 Ready for production fraud detection!
```

## Troubleshooting

### Common Issues

#### 1. Django Server Not Running
```
❌ API connection failed: Connection refused
💡 Make sure the Django server is running on http://127.0.0.1:8000
```
**Solution:** Start Django server with `python manage.py runserver`

#### 2. Database Migration Issues
```
❌ Django tests failed to run: no such table: auction_frauddetection
```
**Solution:** Run migrations with `python manage.py migrate`

#### 3. Import Errors
```
❌ Model test import failed: No module named 'auction.models'
```
**Solution:** Ensure you're in the correct directory and Django is properly configured

## Integration with Dashboard

The fraud detection system integrates with the admin dashboard:

### Dashboard Features Tested
- ✅ Fraud alerts display
- ✅ Pending fraud cases count
- ✅ Fraud resolution actions
- ✅ Risk score visualization
- ✅ Fraud type categorization

### Dashboard API Integration
```javascript
// Frontend code tested
const fraudResponse = await fetch(
  "http://127.0.0.1:8000/api/fraud-detection/?status=pending",
  {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("access_token")}`,
    },
  }
);
```

## Performance Metrics

### Test Coverage
- **Model Tests:** 100% of FraudDetection model functionality
- **API Tests:** All CRUD operations and filtering
- **Business Logic:** All 5 fraud types and workflows
- **Integration:** Dashboard and notification integration

### Test Execution Time
- Model Tests: ~2-3 seconds
- API Tests: ~5-8 seconds  
- Business Logic Tests: ~3-5 seconds
- Integration Tests: ~2-3 seconds
- **Total:** ~12-19 seconds

## Next Steps

After running these tests successfully:

1. **Manual Testing:** Test fraud detection in the browser dashboard
2. **User Scenarios:** Create test scenarios with real user behavior
3. **Performance Testing:** Test with large datasets
4. **Security Testing:** Validate fraud detection security measures
5. **Production Deployment:** Deploy fraud detection to production environment

## Support

For issues with fraud detection tests:
1. Check Django server is running
2. Verify database migrations are applied
3. Ensure all dependencies are installed
4. Review test output for specific error messages
5. Check API endpoints are accessible
