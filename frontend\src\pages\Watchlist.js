import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Spin<PERSON> } from "react-bootstrap";
import { <PERSON>a<PERSON><PERSON>t, FaRegHeart, FaEye, FaClock, FaGavel, FaTrash } from "react-icons/fa";
import { Link } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import axiosInstance from "../api/axiosInstance";
import { formatAuctionPrice } from "../utils/currency";
import "./Watchlist.css";

const Watchlist = () => {
  const { user } = useAuth();
  const [watchlist, setWatchlist] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (user) {
      loadWatchlist();
    } else {
      setLoading(false);
    }
  }, [user]);

  const loadWatchlist = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get("watchlist/");
      const watchlistData = response.data.results || response.data || [];
      setWatchlist(watchlistData);
      setError("");
    } catch (error) {
      console.error("Error loading watchlist:", error);
      if (error.response?.status === 401) {
        setError("Please log in to view your watchlist");
      } else {
        setError("Failed to load watchlist. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  const removeFromWatchlist = async (watchlistId, auctionTitle) => {
    if (!window.confirm(`Remove "${auctionTitle}" from your watchlist?`)) {
      return;
    }

    try {
      await axiosInstance.delete(`watchlist/${watchlistId}/`);
      setWatchlist((prev) => prev.filter((item) => item.id !== watchlistId));
      alert("Removed from watchlist successfully!");
    } catch (error) {
      console.error("Error removing from watchlist:", error);
      alert("Failed to remove from watchlist. Please try again.");
    }
  };

  const clearAllWatchlist = async () => {
    if (!window.confirm("Are you sure you want to clear your entire watchlist? This action cannot be undone.")) {
      return;
    }

    try {
      // Remove all items one by one
      const deletePromises = watchlist.map((item) =>
        axiosInstance.delete(`watchlist/${item.id}/`)
      );
      await Promise.all(deletePromises);
      setWatchlist([]);
      alert("Watchlist cleared successfully!");
    } catch (error) {
      console.error("Error clearing watchlist:", error);
      alert("Failed to clear watchlist. Please try again.");
    }
  };

  const formatTimeRemaining = (endTime) => {
    const now = new Date();
    const end = new Date(endTime);
    const diff = end - now;

    if (diff <= 0) return "Ended";

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const getStatusBadge = (endTime) => {
    const now = new Date();
    const end = new Date(endTime);
    const isEnded = end <= now;

    return (
      <Badge bg={isEnded ? "secondary" : "success"}>
        {isEnded ? "ENDED" : "ACTIVE"}
      </Badge>
    );
  };

  if (!user) {
    return (
      <Container className="mt-5">
        <Row className="justify-content-center">
          <Col md={6}>
            <Alert variant="warning" className="text-center">
              <h4>Login Required</h4>
              <p>Please log in to view and manage your watchlist.</p>
              <Link to="/login" className="btn btn-primary">
                Login
              </Link>
            </Alert>
          </Col>
        </Row>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container className="mt-5">
        <Row className="justify-content-center">
          <Col md={6} className="text-center">
            <Spinner animation="border" role="status">
              <span className="visually-hidden">Loading...</span>
            </Spinner>
            <p className="mt-3">Loading your watchlist...</p>
          </Col>
        </Row>
      </Container>
    );
  }

  return (
    <Container className="mt-4">
      <Row>
        <Col>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h2 className="mb-1">
                <FaHeart className="me-2 text-danger" />
                My Watchlist
              </h2>
              <p className="text-muted mb-0">
                {watchlist.length} {watchlist.length === 1 ? "auction" : "auctions"} saved
              </p>
            </div>
            {watchlist.length > 0 && (
              <Button
                variant="outline-danger"
                size="sm"
                onClick={clearAllWatchlist}
                className="d-flex align-items-center"
              >
                <FaTrash className="me-1" />
                Clear All
              </Button>
            )}
          </div>

          {error && (
            <Alert variant="danger" className="mb-4">
              {error}
            </Alert>
          )}

          {watchlist.length === 0 ? (
            <Card className="text-center py-5">
              <Card.Body>
                <FaRegHeart size={48} className="text-muted mb-3" />
                <h4>Your watchlist is empty</h4>
                <p className="text-muted mb-4">
                  Start adding auctions to your watchlist to keep track of items you're interested in.
                </p>
                <Link to="/auctions" className="btn btn-primary">
                  Browse Auctions
                </Link>
              </Card.Body>
            </Card>
          ) : (
            <Row>
              {watchlist.map((item) => (
                <Col lg={6} xl={4} key={item.id} className="mb-4">
                  <Card className="h-100 watchlist-card">
                    <div className="position-relative">
                      <Card.Img
                        variant="top"
                        src={item.auction_image || "/placeholder-image.svg"}
                        alt={item.auction_title}
                        style={{ height: "200px", objectFit: "contain" }}
                        onError={(e) => {
                          e.target.src = "/placeholder-image.svg";
                        }}
                      />
                      {getStatusBadge(item.auction_end_time)}
                      <Button
                        variant="danger"
                        size="sm"
                        className="position-absolute top-0 end-0 m-2 rounded-circle"
                        onClick={() => removeFromWatchlist(item.id, item.auction_title)}
                        title="Remove from watchlist"
                      >
                        <FaTrash />
                      </Button>
                    </div>

                    <Card.Body className="d-flex flex-column">
                      <Card.Title className="text-truncate" title={item.auction_title}>
                        {item.auction_title}
                      </Card.Title>

                      <div className="mb-3">
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <span className="text-muted small">Current Bid:</span>
                          <span className="fw-bold text-success">
                            {formatAuctionPrice(item.auction_current_bid)}
                          </span>
                        </div>

                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <span className="text-muted small">
                            <FaClock className="me-1" />
                            Time Left:
                          </span>
                          <span className={`fw-bold ${
                            new Date(item.auction_end_time) <= new Date() ? "text-muted" : "text-danger"
                          }`}>
                            {formatTimeRemaining(item.auction_end_time)}
                          </span>
                        </div>

                        <div className="d-flex justify-content-between align-items-center">
                          <span className="text-muted small">Added:</span>
                          <span className="small">
                            {new Date(item.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>

                      <div className="mt-auto">
                        <div className="d-grid gap-2">
                          <Link
                            to={`/auction/${item.auction}`}
                            className="btn btn-primary btn-sm"
                          >
                            View Auction
                          </Link>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => removeFromWatchlist(item.id, item.auction_title)}
                          >
                            <FaHeart className="me-1" />
                            Remove from Watchlist
                          </Button>
                        </div>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>
          )}
        </Col>
      </Row>
    </Container>
  );
};

export default Watchlist;
