.trending-auctions {
  padding: 20px;
  background: var(--white);
  border-radius: var(--radius-auction);
  box-shadow: var(--shadow-lg);
  margin: 20px 0;
}

.trending-header {
  text-align: center;
  margin-bottom: 30px;
}

.trending-header h2 {
  color: var(--primary-color);
  font-size: 2rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.trending-header p {
  color: var(--text-muted);
  font-size: 1.1rem;
}

.trending-auctions-loading,
.trending-auctions-error {
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.trending-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.trending-item {
  position: relative;
  border-radius: var(--radius-auction);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.trending-item:hover {
  transform: translateY(-5px);
}

.trending-stats {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.bid-count,
.activity-indicator {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.activity-indicator {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.no-trending {
  text-align: center;
  padding: 40px;
  color: var(--text-muted);
  font-size: 1.1rem;
}

.trending-footer {
  text-align: center;
  border-top: 1px solid var(--border-light);
  padding-top: 20px;
}

.refresh-btn,
.retry-btn {
  background: var(--gradient-primary);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: var(--radius-button);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover,
.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .trending-auctions {
    padding: 15px;
    margin: 15px 0;
  }

  .trending-header h2 {
    font-size: 1.5rem;
  }

  .trending-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .trending-stats {
    position: static;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 10px;
    padding: 0 10px;
  }
}

/* Dark mode support */
.dark-mode-bg .trending-auctions {
  background: var(--dark-card);
  color: var(--dark-text);
}

.dark-mode-bg .trending-header h2 {
  color: var(--dark-primary);
}

.dark-mode-bg .trending-footer {
  border-top-color: var(--dark-border);
}
