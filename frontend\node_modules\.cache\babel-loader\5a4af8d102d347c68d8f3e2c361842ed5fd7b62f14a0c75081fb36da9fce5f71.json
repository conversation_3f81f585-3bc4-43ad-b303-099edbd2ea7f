{"ast": null, "code": "import { Path } from \"d3-path\";\nexport function withPath(shape) {\n  let digits = 3;\n  shape.digits = function (_) {\n    if (!arguments.length) return digits;\n    if (_ == null) {\n      digits = null;\n    } else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    return shape;\n  };\n  return () => new Path(digits);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}