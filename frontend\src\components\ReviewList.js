import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>ton, <PERSON>, <PERSON>, Pa<PERSON> } from "react-bootstrap";
import { FaStar, FaThumbsUp, FaFlag, FaCheckCircle, FaUser } from "react-icons/fa";
import axiosInstance from "../api/axiosInstance";
import { useAuth } from "../context/AuthContext";
import "./ReviewList.css";

const ReviewList = ({ auctionId, userId, reviewerId, showStats = true }) => {
  const { user } = useAuth();
  const [reviews, setReviews] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    loadReviews();
    if (showStats) {
      loadStats();
    }
  }, [auctionId, userId, reviewerId, currentPage]);

  const loadReviews = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (auctionId) params.append('auction', auctionId);
      if (userId) params.append('user', userId);
      if (reviewerId) params.append('reviewer', reviewerId);
      params.append('page', currentPage);
      params.append('page_size', '10');

      const response = await axiosInstance.get(`/reviews/?${params.toString()}`);
      
      if (response.data.results) {
        setReviews(response.data.results);
        setTotalPages(Math.ceil(response.data.count / 10));
      } else {
        setReviews(response.data);
      }
      
      setError("");
    } catch (err) {
      console.error("Error loading reviews:", err);
      setError("Failed to load reviews");
      setReviews([]);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const params = new URLSearchParams();
      if (auctionId) params.append('auction', auctionId);
      if (userId) params.append('user', userId);

      const response = await axiosInstance.get(`/reviews/statistics/?${params.toString()}`);
      setStats(response.data);
    } catch (err) {
      console.error("Error loading review stats:", err);
    }
  };

  const handleHelpful = async (reviewId) => {
    if (!user) {
      alert("Please log in to mark reviews as helpful");
      return;
    }

    try {
      await axiosInstance.post(`/reviews/${reviewId}/mark_helpful/`);
      loadReviews(); // Reload to get updated helpful count
    } catch (err) {
      console.error("Error marking review as helpful:", err);
      alert("Failed to mark review as helpful");
    }
  };

  const handleReport = async (reviewId) => {
    if (!user) {
      alert("Please log in to report reviews");
      return;
    }

    if (window.confirm("Are you sure you want to report this review for inappropriate content?")) {
      try {
        await axiosInstance.post(`/reviews/${reviewId}/report/`);
        alert("Review reported successfully. Our team will review it.");
      } catch (err) {
        console.error("Error reporting review:", err);
        alert("Failed to report review");
      }
    }
  };

  const renderStars = (rating) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <FaStar
          key={i}
          className={i <= rating ? "star-filled" : "star-empty"}
        />
      );
    }
    return stars;
  };

  const renderStats = () => {
    if (!stats || !showStats) return null;

    return (
      <Card className="mb-4 stats-card">
        <Card.Body>
          <Row>
            <Col md={3} className="text-center">
              <h3 className="mb-1">{stats.average_rating?.toFixed(1) || "0.0"}</h3>
              <div className="mb-2">{renderStars(Math.round(stats.average_rating || 0))}</div>
              <small className="text-muted">{stats.total_reviews} reviews</small>
            </Col>
            <Col md={6}>
              <div className="rating-breakdown">
                {[5, 4, 3, 2, 1].map(rating => (
                  <div key={rating} className="rating-row">
                    <span className="rating-label">{rating} star</span>
                    <div className="rating-bar">
                      <div 
                        className="rating-fill"
                        style={{
                          width: `${stats.total_reviews > 0 
                            ? (stats.rating_distribution[`${rating}_star`] / stats.total_reviews) * 100 
                            : 0}%`
                        }}
                      ></div>
                    </div>
                    <span className="rating-count">
                      {stats.rating_distribution[`${rating}_star`] || 0}
                    </span>
                  </div>
                ))}
              </div>
            </Col>
            <Col md={3} className="text-center">
              <div className="stat-item">
                <FaCheckCircle className="text-success me-1" />
                <strong>{stats.verified_reviews}</strong>
                <br />
                <small className="text-muted">Verified</small>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="text-center py-4">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading reviews...</span>
        </Spinner>
        <p className="mt-2">Loading reviews...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="danger">
        {error}
      </Alert>
    );
  }

  return (
    <div className="review-list">
      {renderStats()}

      {reviews.length === 0 ? (
        <Card className="text-center py-4">
          <Card.Body>
            <FaUser size={48} className="text-muted mb-3" />
            <h5>No Reviews Yet</h5>
            <p className="text-muted">
              Be the first to share your experience with this {auctionId ? 'auction' : 'user'}!
            </p>
          </Card.Body>
        </Card>
      ) : (
        <>
          {reviews.map((review) => (
            <Card key={review.id} className="mb-3 review-card">
              <Card.Body>
                <Row>
                  <Col md={8}>
                    <div className="d-flex align-items-center mb-2">
                      <div className="stars me-2">
                        {renderStars(review.rating)}
                      </div>
                      <Badge 
                        bg={review.review_type === 'seller' ? 'primary' : 
                            review.review_type === 'buyer' ? 'success' : 'secondary'}
                        className="me-2"
                      >
                        {review.review_type === 'seller' ? 'Seller Review' :
                         review.review_type === 'buyer' ? 'Buyer Review' : 'General'}
                      </Badge>
                      {review.is_verified && (
                        <Badge bg="success" className="me-2">
                          <FaCheckCircle className="me-1" />
                          Verified
                        </Badge>
                      )}
                    </div>
                    
                    <p className="review-comment mb-2">{review.comment}</p>
                    
                    <div className="review-meta">
                      <small className="text-muted">
                        By <strong>{review.reviewer_full_name}</strong> • {review.time_ago}
                        {review.auction_title && (
                          <> • <em>{review.auction_title}</em></>
                        )}
                      </small>
                    </div>
                  </Col>
                  
                  <Col md={4} className="text-end">
                    <div className="review-actions">
                      {user && (
                        <>
                          <Button
                            variant="outline-primary"
                            size="sm"
                            className="me-2"
                            onClick={() => handleHelpful(review.id)}
                          >
                            <FaThumbsUp className="me-1" />
                            Helpful ({review.helpful_count})
                          </Button>
                          
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => handleReport(review.id)}
                          >
                            <FaFlag className="me-1" />
                            Report
                          </Button>
                        </>
                      )}
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          ))}

          {totalPages > 1 && (
            <div className="d-flex justify-content-center mt-4">
              <Pagination>
                <Pagination.Prev 
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(currentPage - 1)}
                />
                
                {[...Array(totalPages)].map((_, index) => (
                  <Pagination.Item
                    key={index + 1}
                    active={index + 1 === currentPage}
                    onClick={() => setCurrentPage(index + 1)}
                  >
                    {index + 1}
                  </Pagination.Item>
                ))}
                
                <Pagination.Next 
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(currentPage + 1)}
                />
              </Pagination>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ReviewList;
