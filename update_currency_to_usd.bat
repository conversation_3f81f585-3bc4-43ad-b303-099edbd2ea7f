@echo off
echo ====================================================
echo    CURRENCY CONVERSION: INR to USD
echo ====================================================
echo.
echo This script will convert your auction system from INR to USD
echo.
echo What this script does:
echo - Converts all auction amounts from INR to USD
echo - Updates payment records to USD
echo - Updates bid amounts to USD
echo - Uses exchange rate: 1 INR = 0.012 USD
echo.

set /p confirm="Do you want to proceed? (y/n): "
if /i "%confirm%" neq "y" (
    echo Operation cancelled.
    pause
    exit /b
)

echo.
echo ====================================================
echo    STEP 1: DRY RUN - Preview Changes
echo ====================================================
echo.
echo Running dry run to show what will be converted...
cd backend
python manage.py convert_currency_to_usd --dry-run
echo.

set /p proceed="Do you want to apply these changes? (y/n): "
if /i "%proceed%" neq "y" (
    echo Operation cancelled.
    cd ..
    pause
    exit /b
)

echo.
echo ====================================================
echo    STEP 2: APPLYING CURRENCY CONVERSION
echo ====================================================
echo.
echo Converting all amounts from INR to USD...
python manage.py convert_currency_to_usd

echo.
echo ====================================================
echo    STEP 3: UPDATING ENVIRONMENT VARIABLES
echo ====================================================
echo.
echo Please update your .env files with the following:
echo.
echo Backend (.env):
echo DEFAULT_CURRENCY=USD
echo CURRENCY_SYMBOL=$
echo STRIPE_CURRENCY=usd
echo.
echo Frontend (.env):
echo REACT_APP_DEFAULT_CURRENCY=USD
echo REACT_APP_CURRENCY_SYMBOL=$
echo.

echo ====================================================
echo    CURRENCY CONVERSION COMPLETED!
echo ====================================================
echo.
echo ✅ All auction amounts converted to USD
echo ✅ Payment records updated
echo ✅ Bid amounts converted
echo ✅ AI chat responses updated
echo ✅ Frontend components updated
echo.
echo NEXT STEPS:
echo 1. Update your .env files as shown above
echo 2. Restart your application
echo 3. Test the payment system with Stripe
echo 4. Verify all amounts display correctly
echo.
echo Your auction system is now configured for USD!
echo.
cd ..
pause
