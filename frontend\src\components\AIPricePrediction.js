import React, { useState } from "react";
import axiosInstance from "../api/axiosInstance";
import "./AIPricePrediction.css";

const AIPricePrediction = ({ auctionId, auctionTitle }) => {
  const [prediction, setPrediction] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  const generatePrediction = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await axiosInstance.post(
        `ai/predict-price/${auctionId}/`
      );
      setPrediction(response.data);
    } catch (err) {
      setError(
        err.response?.data?.error ||
          err.message ||
          "Failed to generate prediction"
      );
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 80) return "#4CAF50"; // Green
    if (confidence >= 60) return "#FF9800"; // Orange
    return "#f44336"; // Red
  };

  const getConfidenceLabel = (confidence) => {
    if (confidence >= 80) return "High Confidence";
    if (confidence >= 60) return "Medium Confidence";
    return "Low Confidence";
  };

  return (
    <div className="ai-price-prediction">
      <div className="prediction-header">
        <h3>🤖 AI Price Prediction</h3>
        <p>Get an AI-powered estimate of the final auction price</p>
      </div>

      {!prediction && !loading && (
        <button
          className="generate-prediction-btn"
          onClick={generatePrediction}
          disabled={loading}
        >
          Generate Price Prediction
        </button>
      )}

      {loading && (
        <div className="prediction-loading">
          <div className="loading-spinner"></div>
          <p>Analyzing auction data...</p>
        </div>
      )}

      {error && (
        <div className="prediction-error">
          <p>❌ Error: {error}</p>
          <button onClick={generatePrediction}>Try Again</button>
        </div>
      )}

      {prediction && (
        <div className="prediction-results">
          <div className="prediction-main">
            <div className="predicted-price">
              <span className="price-label">Predicted Final Price:</span>
              <span className="price-value">${prediction.predicted_price}</span>
            </div>

            <div className="confidence-indicator">
              <div className="confidence-bar">
                <div
                  className="confidence-fill"
                  style={{
                    width: `${prediction.confidence_percentage}%`,
                    backgroundColor: getConfidenceColor(
                      prediction.confidence_percentage
                    ),
                  }}
                ></div>
              </div>
              <span
                className="confidence-label"
                style={{
                  color: getConfidenceColor(prediction.confidence_percentage),
                }}
              >
                {getConfidenceLabel(prediction.confidence_percentage)} (
                {prediction.confidence_percentage}%)
              </span>
            </div>
          </div>

          <div className="prediction-meta">
            <p>
              <strong>Model Version:</strong> {prediction.model_version}
            </p>
            <p>
              <strong>Generated:</strong>{" "}
              {new Date(prediction.generated_at).toLocaleString()}
            </p>
          </div>

          <button
            className="toggle-details-btn"
            onClick={() => setShowDetails(!showDetails)}
          >
            {showDetails ? "Hide Details" : "Show Analysis Details"}
          </button>

          {showDetails && (
            <div className="prediction-details">
              <h4>📊 Analysis Breakdown</h4>
              <div className="features-grid">
                {Object.entries(prediction.features_analyzed).map(
                  ([key, value]) => (
                    <div key={key} className="feature-item">
                      <span className="feature-name">
                        {key
                          .replace(/_/g, " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                        :
                      </span>
                      <span className="feature-value">
                        {typeof value === "number"
                          ? value.toFixed(2)
                          : value.toString()}
                      </span>
                    </div>
                  )
                )}
              </div>
            </div>
          )}

          <div className="prediction-actions">
            <button
              className="refresh-prediction-btn"
              onClick={generatePrediction}
              disabled={loading}
            >
              🔄 Refresh Prediction
            </button>
          </div>
        </div>
      )}

      <div className="prediction-disclaimer">
        <p>
          <small>
            ⚠️ This is an AI-generated estimate based on historical data and
            auction characteristics. Actual results may vary significantly.
          </small>
        </p>
      </div>
    </div>
  );
};

export default AIPricePrediction;
