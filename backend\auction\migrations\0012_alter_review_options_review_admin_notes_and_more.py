# Generated by Django 5.1.5 on 2025-06-05 16:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("auction", "0011_add_user_role"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="review",
            options={"ordering": ["-created_at"]},
        ),
        migrations.AddField(
            model_name="review",
            name="admin_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="review",
            name="helpful_count",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="review",
            name="is_approved",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="review",
            name="is_verified",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="review",
            name="reported_count",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="review",
            name="review_type",
            field=models.CharField(
                choices=[
                    ("seller", "Seller Review"),
                    ("buyer", "Buyer Review"),
                    ("general", "General Review"),
                ],
                default="general",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="review",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name="review",
            name="auction",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="reviews",
                to="auction.auction",
            ),
        ),
        migrations.AlterField(
            model_name="review",
            name="comment",
            field=models.TextField(max_length=1000),
        ),
        migrations.AlterField(
            model_name="review",
            name="rating",
            field=models.IntegerField(
                choices=[
                    (1, "1 Star - Poor"),
                    (2, "2 Stars - Fair"),
                    (3, "3 Stars - Good"),
                    (4, "4 Stars - Very Good"),
                    (5, "5 Stars - Excellent"),
                ]
            ),
        ),
        migrations.AlterUniqueTogether(
            name="review",
            unique_together={("reviewer", "reviewee", "auction")},
        ),
    ]
