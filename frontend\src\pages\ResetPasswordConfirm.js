import React, { useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import Swal from "sweetalert2";

function ResetPasswordConfirm() {
  const { uidb64, token } = useParams();
  const navigate = useNavigate();

  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!password) {
      Swal.fire("Error", "Please enter a new password", "error");
      return;
    }
    if (password.length < 8) {
      Swal.fire("Error", "Password must be at least 8 characters.", "error");
      return;
    }

    setLoading(true);

    try {
      const res = await fetch(
        `/api/auth/password-reset-confirm/${uidb64}/${token}/`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ password }),
        }
      );

      const data = await res.json();

      if (!res.ok) {
        Swal.fire("Error", data.error || "Failed to reset password", "error");
        setLoading(false);
        return;
      }

      Swal.fire("Success", data.message, "success");
      setPassword("");
      setTimeout(() => navigate("/login"), 2000);
    } catch (error) {
      Swal.fire("Error", "Something went wrong. Please try again.", "error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mt-5" style={{ maxWidth: "400px" }}>
      <h2 className="mb-4 text-center">Reset Your Password</h2>
      <form onSubmit={handleSubmit}>
        <div className="mb-3 position-relative">
          <label>New Password</label>
          <input
            type={showPassword ? "text" : "password"}
            className="form-control"
            required
            minLength={8}
            placeholder="Enter new password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            autoFocus
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="btn btn-sm btn-outline-secondary position-absolute"
            style={{
              top: "50%",
              right: "10px",
              transform: "translateY(-50%)",
              zIndex: 2,
            }}
          >
            {showPassword ? "Hide" : "Show"}
          </button>
        </div>

        <button
          className="btn btn-primary w-100"
          type="submit"
          disabled={loading}
        >
          {loading ? "Resetting..." : "Reset Password"}
        </button>
      </form>
    </div>
  );
}

export default ResetPasswordConfirm;
