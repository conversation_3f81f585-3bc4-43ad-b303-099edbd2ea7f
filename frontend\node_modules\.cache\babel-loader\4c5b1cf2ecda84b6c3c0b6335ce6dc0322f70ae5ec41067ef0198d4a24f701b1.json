{"ast": null, "code": "import axios from \"axios\";\n\n// Use different base URLs for development and production\nconst getBaseURL = () => {\n  if (process.env.NODE_ENV === \"development\") {\n    // Check if we're running on a non-standard port (like 3001)\n    const currentPort = window.location.port;\n    if (currentPort && currentPort !== \"3000\") {\n      // If running on non-standard port, directly connect to backend\n      return \"http://127.0.0.1:8000/api/\";\n    }\n    // Standard development setup with proxy\n    return \"/api/\";\n  } else {\n    // In production, use relative URL (proxy will handle it)\n    return \"/api/\";\n  }\n};\nconst axiosInstance = axios.create({\n  baseURL: getBaseURL(),\n  headers: {\n    \"Content-Type\": \"application/json\"\n  },\n  timeout: 10000\n});\n\n// Request interceptor to add authentication token\naxiosInstance.interceptors.request.use(config => {\n  var _config$url, _config$url2, _config$url3;\n  // Reduced logging - only log important requests\n  if (!((_config$url = config.url) !== null && _config$url !== void 0 && _config$url.includes(\"notifications/\")) && !((_config$url2 = config.url) !== null && _config$url2 !== void 0 && _config$url2.includes(\"admin/users/\")) && !((_config$url3 = config.url) !== null && _config$url3 !== void 0 && _config$url3.includes(\"health/\"))) {\n    console.log(`📤 Making request to: ${config.url}`);\n  }\n\n  // List of endpoints that don't require authentication\n  const publicEndpoints = [\"analytics/\", \"analytics/dashboard/\", \"analytics-dashboard/\", \"analytics-test/\", \"categories/\", \"search/filters/\", \"featured-auctions/\", \"auctions/featured/\", \"auth/register/\", \"auth/login/\", \"auth/password-reset/\", \"contact-messages/\", \"register/\", \"login/\", \"landing/data/\", \"landing/stats/\", \"categories-with-counts/\", \"new-search/filters/\"];\n\n  // Check if this is a public endpoint\n  const isPublicEndpoint = publicEndpoints.some(endpoint => config.url && config.url.includes(endpoint));\n\n  // Special handling for auctions endpoint - GET is public, POST/PUT/PATCH/DELETE require auth\n  const isAuctionsEndpoint = config.url && config.url.includes(\"auctions/\");\n  const isReadOnlyRequest = config.method && config.method.toLowerCase() === \"get\";\n\n  // Special handling for chat endpoints - always require auth for POST/PUT/PATCH/DELETE\n  const isChatEndpoint = config.url && (config.url.includes(\"chat-messages/\") || config.url.includes(\"chat-rooms/\"));\n  const isWriteOperation = config.method && ![\"get\", \"head\", \"options\"].includes(config.method.toLowerCase());\n\n  // Only add auth token for non-public endpoints OR for write operations on auctions OR for chat operations\n  if (!isPublicEndpoint || isAuctionsEndpoint && !isReadOnlyRequest || isChatEndpoint && isWriteOperation) {\n    const token = localStorage.getItem(\"token\") || localStorage.getItem(\"access_token\");\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n  }\n  return config;\n}, error => {\n  console.error(\"❌ Request error:\", error);\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle responses and errors\naxiosInstance.interceptors.response.use(response => {\n  var _response$config$url, _response$config$url2;\n  // Only log non-routine responses to reduce console spam\n  if (!((_response$config$url = response.config.url) !== null && _response$config$url !== void 0 && _response$config$url.includes(\"notifications/\")) && !((_response$config$url2 = response.config.url) !== null && _response$config$url2 !== void 0 && _response$config$url2.includes(\"admin/users/\"))) {\n    var _response$config$url3;\n    // Special logging for landing data to help debug excessive calls\n    if ((_response$config$url3 = response.config.url) !== null && _response$config$url3 !== void 0 && _response$config$url3.includes(\"landing/data/\")) {\n      console.log(`🚨 LANDING DATA RESPONSE: ${response.status} ${response.config.url} at ${new Date().toISOString()}`);\n    } else {\n      console.log(`✅ Response success: ${response.status} ${response.config.url}`);\n    }\n  }\n  return response;\n}, error => {\n  var _error$response, _error$config, _error$response2, _error$response3;\n  console.error(`❌ Response error: ${(_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status} ${(_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url}`, (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data);\n  if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 401) {\n    // Check if this is a protected endpoint that actually requires auth\n    const protectedEndpoints = [\"notifications/\", \"admin/\", \"profile/\", \"autobids/\", \"chat-messages/\", \"chat-rooms/\", \"bids/\", \"payments/\"];\n    const isProtectedEndpoint = protectedEndpoints.some(endpoint => {\n      var _error$config2, _error$config2$url;\n      return (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : (_error$config2$url = _error$config2.url) === null || _error$config2$url === void 0 ? void 0 : _error$config2$url.includes(endpoint);\n    });\n\n    // Only handle auth errors for protected endpoints\n    if (isProtectedEndpoint) {\n      console.log(\"🔐 Authentication required for protected endpoint\");\n      localStorage.removeItem(\"token\");\n      localStorage.removeItem(\"access_token\");\n      localStorage.removeItem(\"auction_loggedin_user\");\n\n      // Only redirect if not already on login page and not in the middle of navigation\n      if (!window.location.pathname.includes(\"/login\") && !window.location.pathname.includes(\"/register\")) {\n        // Use a timeout to prevent immediate redirect loops\n        setTimeout(() => {\n          window.location.href = \"/login\";\n        }, 100);\n      }\n    } else {\n      var _error$config3;\n      // For public endpoints, just log the error but don't redirect\n      console.log(\"ℹ️ 401 on public endpoint, ignoring:\", (_error$config3 = error.config) === null || _error$config3 === void 0 ? void 0 : _error$config3.url);\n    }\n  }\n  return Promise.reject(error);\n});\nexport default axiosInstance;", "map": {"version": 3, "names": ["axios", "getBaseURL", "process", "env", "NODE_ENV", "currentPort", "window", "location", "port", "axiosInstance", "create", "baseURL", "headers", "timeout", "interceptors", "request", "use", "config", "_config$url", "_config$url2", "_config$url3", "url", "includes", "console", "log", "publicEndpoints", "isPublicEndpoint", "some", "endpoint", "isAuctionsEndpoint", "isReadOnlyRequest", "method", "toLowerCase", "isChatEndpoint", "isWriteOperation", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_response$config$url", "_response$config$url2", "_response$config$url3", "status", "Date", "toISOString", "_error$response", "_error$config", "_error$response2", "_error$response3", "data", "protectedEndpoints", "isProtectedEndpoint", "_error$config2", "_error$config2$url", "removeItem", "pathname", "setTimeout", "href", "_error$config3"], "sources": ["C:/Users/<USER>/Desktop/Online_Auction_System/frontend/src/api/axiosInstance.js"], "sourcesContent": ["import axios from \"axios\";\r\n\r\n// Use different base URLs for development and production\r\nconst getBaseURL = () => {\r\n  if (process.env.NODE_ENV === \"development\") {\r\n    // Check if we're running on a non-standard port (like 3001)\r\n    const currentPort = window.location.port;\r\n    if (currentPort && currentPort !== \"3000\") {\r\n      // If running on non-standard port, directly connect to backend\r\n      return \"http://127.0.0.1:8000/api/\";\r\n    }\r\n    // Standard development setup with proxy\r\n    return \"/api/\";\r\n  } else {\r\n    // In production, use relative URL (proxy will handle it)\r\n    return \"/api/\";\r\n  }\r\n};\r\n\r\nconst axiosInstance = axios.create({\r\n  baseURL: getBaseURL(),\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n  timeout: 10000,\r\n});\r\n\r\n// Request interceptor to add authentication token\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    // Reduced logging - only log important requests\r\n    if (\r\n      !config.url?.includes(\"notifications/\") &&\r\n      !config.url?.includes(\"admin/users/\") &&\r\n      !config.url?.includes(\"health/\")\r\n    ) {\r\n      console.log(`📤 Making request to: ${config.url}`);\r\n    }\r\n\r\n    // List of endpoints that don't require authentication\r\n    const publicEndpoints = [\r\n      \"analytics/\",\r\n      \"analytics/dashboard/\",\r\n      \"analytics-dashboard/\",\r\n      \"analytics-test/\",\r\n      \"categories/\",\r\n      \"search/filters/\",\r\n      \"featured-auctions/\",\r\n      \"auctions/featured/\",\r\n      \"auth/register/\",\r\n      \"auth/login/\",\r\n      \"auth/password-reset/\",\r\n      \"contact-messages/\",\r\n      \"register/\",\r\n      \"login/\",\r\n      \"landing/data/\",\r\n      \"landing/stats/\",\r\n      \"categories-with-counts/\",\r\n      \"new-search/filters/\",\r\n    ];\r\n\r\n    // Check if this is a public endpoint\r\n    const isPublicEndpoint = publicEndpoints.some(\r\n      (endpoint) => config.url && config.url.includes(endpoint)\r\n    );\r\n\r\n    // Special handling for auctions endpoint - GET is public, POST/PUT/PATCH/DELETE require auth\r\n    const isAuctionsEndpoint = config.url && config.url.includes(\"auctions/\");\r\n    const isReadOnlyRequest =\r\n      config.method && config.method.toLowerCase() === \"get\";\r\n\r\n    // Special handling for chat endpoints - always require auth for POST/PUT/PATCH/DELETE\r\n    const isChatEndpoint =\r\n      config.url &&\r\n      (config.url.includes(\"chat-messages/\") ||\r\n        config.url.includes(\"chat-rooms/\"));\r\n    const isWriteOperation =\r\n      config.method &&\r\n      ![\"get\", \"head\", \"options\"].includes(config.method.toLowerCase());\r\n\r\n    // Only add auth token for non-public endpoints OR for write operations on auctions OR for chat operations\r\n    if (\r\n      !isPublicEndpoint ||\r\n      (isAuctionsEndpoint && !isReadOnlyRequest) ||\r\n      (isChatEndpoint && isWriteOperation)\r\n    ) {\r\n      const token =\r\n        localStorage.getItem(\"token\") || localStorage.getItem(\"access_token\");\r\n      if (token) {\r\n        config.headers.Authorization = `Bearer ${token}`;\r\n      }\r\n    }\r\n\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error(\"❌ Request error:\", error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor to handle responses and errors\r\naxiosInstance.interceptors.response.use(\r\n  (response) => {\r\n    // Only log non-routine responses to reduce console spam\r\n    if (\r\n      !response.config.url?.includes(\"notifications/\") &&\r\n      !response.config.url?.includes(\"admin/users/\")\r\n    ) {\r\n      // Special logging for landing data to help debug excessive calls\r\n      if (response.config.url?.includes(\"landing/data/\")) {\r\n        console.log(\r\n          `🚨 LANDING DATA RESPONSE: ${response.status} ${\r\n            response.config.url\r\n          } at ${new Date().toISOString()}`\r\n        );\r\n      } else {\r\n        console.log(\r\n          `✅ Response success: ${response.status} ${response.config.url}`\r\n        );\r\n      }\r\n    }\r\n    return response;\r\n  },\r\n  (error) => {\r\n    console.error(\r\n      `❌ Response error: ${error.response?.status} ${error.config?.url}`,\r\n      error.response?.data\r\n    );\r\n\r\n    if (error.response?.status === 401) {\r\n      // Check if this is a protected endpoint that actually requires auth\r\n      const protectedEndpoints = [\r\n        \"notifications/\",\r\n        \"admin/\",\r\n        \"profile/\",\r\n        \"autobids/\",\r\n        \"chat-messages/\",\r\n        \"chat-rooms/\",\r\n        \"bids/\",\r\n        \"payments/\",\r\n      ];\r\n\r\n      const isProtectedEndpoint = protectedEndpoints.some((endpoint) =>\r\n        error.config?.url?.includes(endpoint)\r\n      );\r\n\r\n      // Only handle auth errors for protected endpoints\r\n      if (isProtectedEndpoint) {\r\n        console.log(\"🔐 Authentication required for protected endpoint\");\r\n        localStorage.removeItem(\"token\");\r\n        localStorage.removeItem(\"access_token\");\r\n        localStorage.removeItem(\"auction_loggedin_user\");\r\n\r\n        // Only redirect if not already on login page and not in the middle of navigation\r\n        if (\r\n          !window.location.pathname.includes(\"/login\") &&\r\n          !window.location.pathname.includes(\"/register\")\r\n        ) {\r\n          // Use a timeout to prevent immediate redirect loops\r\n          setTimeout(() => {\r\n            window.location.href = \"/login\";\r\n          }, 100);\r\n        }\r\n      } else {\r\n        // For public endpoints, just log the error but don't redirect\r\n        console.log(\"ℹ️ 401 on public endpoint, ignoring:\", error.config?.url);\r\n      }\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default axiosInstance;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1C;IACA,MAAMC,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI;IACxC,IAAIH,WAAW,IAAIA,WAAW,KAAK,MAAM,EAAE;MACzC;MACA,OAAO,4BAA4B;IACrC;IACA;IACA,OAAO,OAAO;EAChB,CAAC,MAAM;IACL;IACA,OAAO,OAAO;EAChB;AACF,CAAC;AAED,MAAMI,aAAa,GAAGT,KAAK,CAACU,MAAM,CAAC;EACjCC,OAAO,EAAEV,UAAU,CAAC,CAAC;EACrBW,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAJ,aAAa,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EAAA,IAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA;EACV;EACA,IACE,GAAAF,WAAA,GAACD,MAAM,CAACI,GAAG,cAAAH,WAAA,eAAVA,WAAA,CAAYI,QAAQ,CAAC,gBAAgB,CAAC,KACvC,GAAAH,YAAA,GAACF,MAAM,CAACI,GAAG,cAAAF,YAAA,eAAVA,YAAA,CAAYG,QAAQ,CAAC,cAAc,CAAC,KACrC,GAAAF,YAAA,GAACH,MAAM,CAACI,GAAG,cAAAD,YAAA,eAAVA,YAAA,CAAYE,QAAQ,CAAC,SAAS,CAAC,GAChC;IACAC,OAAO,CAACC,GAAG,CAAC,yBAAyBP,MAAM,CAACI,GAAG,EAAE,CAAC;EACpD;;EAEA;EACA,MAAMI,eAAe,GAAG,CACtB,YAAY,EACZ,sBAAsB,EACtB,sBAAsB,EACtB,iBAAiB,EACjB,aAAa,EACb,iBAAiB,EACjB,oBAAoB,EACpB,oBAAoB,EACpB,gBAAgB,EAChB,aAAa,EACb,sBAAsB,EACtB,mBAAmB,EACnB,WAAW,EACX,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,yBAAyB,EACzB,qBAAqB,CACtB;;EAED;EACA,MAAMC,gBAAgB,GAAGD,eAAe,CAACE,IAAI,CAC1CC,QAAQ,IAAKX,MAAM,CAACI,GAAG,IAAIJ,MAAM,CAACI,GAAG,CAACC,QAAQ,CAACM,QAAQ,CAC1D,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGZ,MAAM,CAACI,GAAG,IAAIJ,MAAM,CAACI,GAAG,CAACC,QAAQ,CAAC,WAAW,CAAC;EACzE,MAAMQ,iBAAiB,GACrBb,MAAM,CAACc,MAAM,IAAId,MAAM,CAACc,MAAM,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK;;EAExD;EACA,MAAMC,cAAc,GAClBhB,MAAM,CAACI,GAAG,KACTJ,MAAM,CAACI,GAAG,CAACC,QAAQ,CAAC,gBAAgB,CAAC,IACpCL,MAAM,CAACI,GAAG,CAACC,QAAQ,CAAC,aAAa,CAAC,CAAC;EACvC,MAAMY,gBAAgB,GACpBjB,MAAM,CAACc,MAAM,IACb,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,CAACT,QAAQ,CAACL,MAAM,CAACc,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC;;EAEnE;EACA,IACE,CAACN,gBAAgB,IAChBG,kBAAkB,IAAI,CAACC,iBAAkB,IACzCG,cAAc,IAAIC,gBAAiB,EACpC;IACA,MAAMC,KAAK,GACTC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACvE,IAAIF,KAAK,EAAE;MACTlB,MAAM,CAACL,OAAO,CAAC0B,aAAa,GAAG,UAAUH,KAAK,EAAE;IAClD;EACF;EAEA,OAAOlB,MAAM;AACf,CAAC,EACAsB,KAAK,IAAK;EACThB,OAAO,CAACgB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;EACxC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA9B,aAAa,CAACK,YAAY,CAAC4B,QAAQ,CAAC1B,GAAG,CACpC0B,QAAQ,IAAK;EAAA,IAAAC,oBAAA,EAAAC,qBAAA;EACZ;EACA,IACE,GAAAD,oBAAA,GAACD,QAAQ,CAACzB,MAAM,CAACI,GAAG,cAAAsB,oBAAA,eAAnBA,oBAAA,CAAqBrB,QAAQ,CAAC,gBAAgB,CAAC,KAChD,GAAAsB,qBAAA,GAACF,QAAQ,CAACzB,MAAM,CAACI,GAAG,cAAAuB,qBAAA,eAAnBA,qBAAA,CAAqBtB,QAAQ,CAAC,cAAc,CAAC,GAC9C;IAAA,IAAAuB,qBAAA;IACA;IACA,KAAAA,qBAAA,GAAIH,QAAQ,CAACzB,MAAM,CAACI,GAAG,cAAAwB,qBAAA,eAAnBA,qBAAA,CAAqBvB,QAAQ,CAAC,eAAe,CAAC,EAAE;MAClDC,OAAO,CAACC,GAAG,CACT,6BAA6BkB,QAAQ,CAACI,MAAM,IAC1CJ,QAAQ,CAACzB,MAAM,CAACI,GAAG,OACd,IAAI0B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EACjC,CAAC;IACH,CAAC,MAAM;MACLzB,OAAO,CAACC,GAAG,CACT,uBAAuBkB,QAAQ,CAACI,MAAM,IAAIJ,QAAQ,CAACzB,MAAM,CAACI,GAAG,EAC/D,CAAC;IACH;EACF;EACA,OAAOqB,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAU,eAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,gBAAA;EACT7B,OAAO,CAACgB,KAAK,CACX,sBAAAU,eAAA,GAAqBV,KAAK,CAACG,QAAQ,cAAAO,eAAA,uBAAdA,eAAA,CAAgBH,MAAM,KAAAI,aAAA,GAAIX,KAAK,CAACtB,MAAM,cAAAiC,aAAA,uBAAZA,aAAA,CAAc7B,GAAG,EAAE,GAAA8B,gBAAA,GAClEZ,KAAK,CAACG,QAAQ,cAAAS,gBAAA,uBAAdA,gBAAA,CAAgBE,IAClB,CAAC;EAED,IAAI,EAAAD,gBAAA,GAAAb,KAAK,CAACG,QAAQ,cAAAU,gBAAA,uBAAdA,gBAAA,CAAgBN,MAAM,MAAK,GAAG,EAAE;IAClC;IACA,MAAMQ,kBAAkB,GAAG,CACzB,gBAAgB,EAChB,QAAQ,EACR,UAAU,EACV,WAAW,EACX,gBAAgB,EAChB,aAAa,EACb,OAAO,EACP,WAAW,CACZ;IAED,MAAMC,mBAAmB,GAAGD,kBAAkB,CAAC3B,IAAI,CAAEC,QAAQ;MAAA,IAAA4B,cAAA,EAAAC,kBAAA;MAAA,QAAAD,cAAA,GAC3DjB,KAAK,CAACtB,MAAM,cAAAuC,cAAA,wBAAAC,kBAAA,GAAZD,cAAA,CAAcnC,GAAG,cAAAoC,kBAAA,uBAAjBA,kBAAA,CAAmBnC,QAAQ,CAACM,QAAQ,CAAC;IAAA,CACvC,CAAC;;IAED;IACA,IAAI2B,mBAAmB,EAAE;MACvBhC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChEY,YAAY,CAACsB,UAAU,CAAC,OAAO,CAAC;MAChCtB,YAAY,CAACsB,UAAU,CAAC,cAAc,CAAC;MACvCtB,YAAY,CAACsB,UAAU,CAAC,uBAAuB,CAAC;;MAEhD;MACA,IACE,CAACpD,MAAM,CAACC,QAAQ,CAACoD,QAAQ,CAACrC,QAAQ,CAAC,QAAQ,CAAC,IAC5C,CAAChB,MAAM,CAACC,QAAQ,CAACoD,QAAQ,CAACrC,QAAQ,CAAC,WAAW,CAAC,EAC/C;QACA;QACAsC,UAAU,CAAC,MAAM;UACftD,MAAM,CAACC,QAAQ,CAACsD,IAAI,GAAG,QAAQ;QACjC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,MAAM;MAAA,IAAAC,cAAA;MACL;MACAvC,OAAO,CAACC,GAAG,CAAC,sCAAsC,GAAAsC,cAAA,GAAEvB,KAAK,CAACtB,MAAM,cAAA6C,cAAA,uBAAZA,cAAA,CAAczC,GAAG,CAAC;IACxE;EACF;EACA,OAAOmB,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAe9B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}