/* Professional Dark & Light Auction Theme - Corporate Excellence */

:root {
  /* Primary Colors - Professional Navy Blue */
  --primary-color: #1e3a8a;
  --primary-light: #3b82f6;
  --primary-dark: #1e40af;
  --primary-ultra-light: #eff6ff;

  /* Secondary Colors - Elegant Gold */
  --secondary-color: #d97706;
  --secondary-light: #f59e0b;
  --secondary-dark: #b45309;
  --secondary-ultra-light: #fffbeb;

  /* Accent Colors - Professional Teal */
  --accent-color: #0891b2;
  --accent-light: #06b6d4;
  --accent-dark: #0e7490;
  --accent-ultra-light: #ecfeff;

  /* Supporting Colors - Success Green */
  --supporting-color: #059669;
  --supporting-light: #10b981;
  --supporting-dark: #047857;
  --supporting-ultra-light: #ecfdf5;

  /* Status Colors - Professional & Clear */
  --success-color: #059669;
  --success-light: #10b981;
  --success-dark: #047857;

  --error-color: #dc2626;
  --error-light: #ef4444;
  --error-dark: #b91c1c;

  --warning-color: #d97706;
  --warning-light: #f59e0b;
  --warning-dark: #b45309;

  --info-color: #0891b2;
  --info-light: #06b6d4;
  --info-dark: #0e7490;

  /* Neutral Colors - Professional Grays */
  --white: #ffffff;
  --light-gray: #f8fafc;
  --gray: #64748b;
  --dark-gray: #475569;
  --black: #0f172a;
  --charcoal: #1e293b;

  /* Background Colors - Professional Hierarchy */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-dark: var(--charcoal);
  --bg-light: var(--primary-ultra-light);
  --bg-accent: var(--accent-ultra-light);
  --bg-supporting: var(--supporting-ultra-light);
  --bg-secondary-light: var(--secondary-ultra-light);

  /* Text Colors - High Contrast & Professional */
  --text-primary: var(--black);
  --text-secondary: var(--dark-gray);
  --text-light: #ffffff;
  --text-muted: var(--gray);
  --text-accent: var(--accent-color);
  --text-supporting: var(--supporting-color);

  /* Navigation Colors */
  --nav-bg: var(--charcoal);
  --nav-text: #ffffff;
  --nav-hover: var(--primary-light);
  --nav-active: var(--secondary-color);

  /* Border Colors - Professional & Clean */
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --border-dark: #cbd5e0;
  --border-primary: var(--primary-color);
  --border-accent: var(--accent-color);

  /* Enhanced Shadows - Professional Depth */
  --shadow-sm: 0 1px 3px rgba(15, 23, 42, 0.1);
  --shadow-md: 0 4px 12px rgba(15, 23, 42, 0.15);
  --shadow-lg: 0 10px 25px rgba(15, 23, 42, 0.2);
  --shadow-xl: 0 20px 40px rgba(15, 23, 42, 0.25);
  --shadow-auction: 0 8px 25px rgba(217, 119, 6, 0.2);
  --shadow-accent: 0 8px 25px rgba(8, 145, 178, 0.2);

  /* Modern Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%);
  --gradient-supporting: linear-gradient(135deg, var(--supporting-color) 0%, var(--supporting-light) 100%);

  /* Complex Gradients - Professional & Dynamic */
  --gradient-hero: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 50%, var(--accent-color) 100%);
  --gradient-auction: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
  --gradient-premium: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 50%, var(--secondary-color) 100%);
  --gradient-success: linear-gradient(135deg, var(--supporting-color) 0%, var(--supporting-light) 100%);
  --gradient-elegant: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-nav: linear-gradient(135deg, var(--charcoal) 0%, var(--black) 100%);

  /* Glass Morphism Effects - Professional */
  --glass-bg: rgba(255, 255, 255, 0.9);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(15, 23, 42, 0.1);
  
  /* Enhanced Transitions */
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-smooth: 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* Modern Border Radius */
  --radius-xs: 2px;
  --radius-sm: 6px;
  --radius-md: 10px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-2xl: 32px;
  --radius-full: 50%;
  --radius-auction: 20px;

  /* Spacing System */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  --space-3xl: 64px;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

/* Links */
a {
  color: var(--accent-color);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--accent-dark);
  text-decoration: none;
}

/* Enhanced Button System */
.btn {
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-lg);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition-bounce);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: var(--gradient-hero);
  box-shadow: var(--shadow-lg);
  transform: translateY(-3px) scale(1.02);
  color: var(--white);
}

.btn-secondary {
  background: var(--gradient-auction);
  color: var(--white);
  box-shadow: var(--shadow-auction);
}

.btn-secondary:hover {
  background: var(--gradient-secondary);
  box-shadow: var(--shadow-lg);
  transform: translateY(-3px) scale(1.02);
  color: var(--white);
}

.btn-accent {
  background: var(--gradient-accent);
  color: var(--white);
  box-shadow: var(--shadow-accent);
}

.btn-accent:hover {
  background: var(--accent-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-3px) scale(1.02);
  color: var(--white);
}

.btn-supporting {
  background: var(--gradient-supporting);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-supporting:hover {
  background: var(--supporting-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-3px) scale(1.02);
  color: var(--white);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  backdrop-filter: blur(10px);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.btn-success {
  background: var(--gradient-success);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-success:hover {
  background: var(--success-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-3px) scale(1.02);
  color: var(--white);
}

.btn-danger {
  background: linear-gradient(135deg, var(--error-color) 0%, var(--error-dark) 100%);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-danger:hover {
  background: var(--error-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-3px) scale(1.02);
  color: var(--white);
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-warning:hover {
  background: var(--warning-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-3px) scale(1.02);
  color: var(--white);
}

.btn-info {
  background: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-info:hover {
  background: var(--info-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-3px) scale(1.02);
  color: var(--white);
}

/* Enhanced Card System */
.card {
  background: var(--white);
  border-radius: var(--radius-auction);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: var(--transition-smooth);
  overflow: hidden;
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-premium);
  opacity: 0;
  transition: var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-8px) scale(1.02);
  border-color: var(--border-primary);
}

.card:hover::before {
  opacity: 1;
}

.card-auction {
  background: linear-gradient(145deg, var(--white) 0%, var(--bg-secondary-light) 100%);
  border: 2px solid var(--border-light);
}

.card-auction:hover {
  border-color: var(--secondary-color);
  box-shadow: var(--shadow-auction);
}

.card-premium {
  background: var(--gradient-premium);
  color: var(--white);
  border: none;
}

.card-premium .card-body {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.card-header {
  background: var(--bg-light);
  border-bottom: 1px solid var(--border-light);
  padding: var(--space-lg);
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: var(--space-lg);
  right: var(--space-lg);
  height: 2px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

.card-body {
  padding: var(--space-lg);
}

.card-footer {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
  padding: var(--space-md) var(--space-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Forms */
.form-control {
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 12px 16px;
  font-size: 1rem;
  transition: var(--transition-fast);
  background: var(--white);
  color: var(--text-primary);
}

.form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  outline: none;
}

.form-label {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: block;
}

/* Alerts */
.alert {
  border-radius: var(--radius-md);
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  border: none;
  font-weight: 500;
}

.alert-success {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-dark);
  border-left: 4px solid var(--success-color);
}

.alert-danger {
  background: rgba(231, 76, 60, 0.1);
  color: var(--error-dark);
  border-left: 4px solid var(--error-color);
}

.alert-warning {
  background: rgba(243, 156, 18, 0.1);
  color: var(--warning-dark);
  border-left: 4px solid var(--warning-color);
}

.alert-info {
  background: rgba(52, 152, 219, 0.1);
  color: var(--accent-dark);
  border-left: 4px solid var(--accent-color);
}

/* Badges */
.badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background: var(--primary-color);
  color: var(--white);
}

.badge-secondary {
  background: var(--secondary-color);
  color: var(--white);
}

.badge-success {
  background: var(--success-color);
  color: var(--white);
}

.badge-danger {
  background: var(--error-color);
  color: var(--white);
}

.badge-warning {
  background: var(--warning-color);
  color: var(--white);
}

/* Utilities */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--error-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-muted { color: var(--text-muted) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-light { background-color: var(--bg-light) !important; }
.bg-white { background-color: var(--white) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

.rounded { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }

/* Dark Mode Support */
.dark-mode-bg {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: #334155;
  --border-light: #475569;
  --border-dark: #64748b;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.6);
}

.dark-mode-bg .card {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.dark-mode-bg .card:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary-light);
}

.dark-mode-card {
  background: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }

  .btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }

  .card-body {
    padding: 1rem;
  }

  .hero-section {
    padding: 80px 0;
    min-height: 60vh;
  }
}
