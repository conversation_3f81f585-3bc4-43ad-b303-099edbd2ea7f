"""
WebSocket service integration for extended auction functionality
"""

from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import json
from django.utils import timezone

from auction.models import Auction, Bid


class AuctionWebSocketService:
    """Service to handle WebSocket communications for extended auction features"""
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    def send_trending_update(self, auction_id):
        """Send trending auction update to all connected clients"""
        try:
            auction = Auction.objects.get(id=auction_id)
            
            # Send to general auction updates group
            async_to_sync(self.channel_layer.group_send)(
                "auction_updates",
                {
                    "type": "trending_update",
                    "auction_id": auction_id,
                    "title": auction.title,
                    "current_bid": float(auction.current_bid),
                    "bid_count": auction.bids.count(),
                    "time_remaining": self._get_time_remaining(auction),
                }
            )
        except Auction.DoesNotExist:
            pass
        except Exception as e:
            print(f"Error sending trending update: {e}")
    
    def send_ending_soon_alert(self, auction_id):
        """Send ending soon alert for auctions ending within 1 hour"""
        try:
            auction = Auction.objects.get(id=auction_id)
            time_remaining = self._get_time_remaining(auction)
            
            if time_remaining <= 3600:  # 1 hour in seconds
                async_to_sync(self.channel_layer.group_send)(
                    "auction_updates",
                    {
                        "type": "ending_soon_alert",
                        "auction_id": auction_id,
                        "title": auction.title,
                        "time_remaining": time_remaining,
                        "current_bid": float(auction.current_bid),
                    }
                )
        except Auction.DoesNotExist:
            pass
        except Exception as e:
            print(f"Error sending ending soon alert: {e}")
    
    def send_auction_analytics_update(self, auction_id):
        """Send real-time analytics update for specific auction"""
        try:
            auction = Auction.objects.get(id=auction_id)
            bids = auction.bids.all()
            
            analytics_data = {
                "auction_id": auction_id,
                "total_bids": bids.count(),
                "unique_bidders": bids.values('bidder').distinct().count(),
                "current_bid": float(auction.current_bid),
                "bid_increase": float(auction.current_bid - auction.starting_bid),
                "time_remaining": self._get_time_remaining(auction),
                "last_bid_time": bids.last().timestamp.isoformat() if bids.exists() else None,
            }
            
            # Send to auction-specific group
            async_to_sync(self.channel_layer.group_send)(
                f"auction_{auction_id}_analytics",
                {
                    "type": "analytics_update",
                    "data": analytics_data,
                }
            )
        except Auction.DoesNotExist:
            pass
        except Exception as e:
            print(f"Error sending analytics update: {e}")
    
    def send_admin_dashboard_update(self):
        """Send dashboard statistics update to admin users"""
        try:
            from auction.analytics_services import advanced_analytics_service

            # Get fresh analytics data
            dashboard_data = advanced_analytics_service.get_comprehensive_dashboard_data(use_cache=False)

            # Send to admin group
            async_to_sync(self.channel_layer.group_send)(
                "admin_dashboard",
                {
                    "type": "dashboard_update",
                    "data": {
                        "basic_metrics": dashboard_data.get("basic_metrics", {}),
                        "revenue_data": dashboard_data.get("revenue_data", {}),
                        "timestamp": timezone.now().isoformat(),
                    }
                }
            )

            print("Admin dashboard update sent via WebSocket")

        except Exception as e:
            print(f"Error sending admin dashboard update: {e}")
    
    def send_user_stats_update(self, user_id):
        """Send user statistics update"""
        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            user = User.objects.get(id=user_id)
            user_auctions = Auction.objects.filter(seller=user)
            user_bids = Bid.objects.filter(bidder=user)
            
            stats_data = {
                "user_id": user_id,
                "auctions_created": user_auctions.count(),
                "active_auctions": user_auctions.filter(is_active=True).count(),
                "bids_placed": user_bids.count(),
                "timestamp": timezone.now().isoformat(),
            }
            
            # Send to user-specific group
            async_to_sync(self.channel_layer.group_send)(
                f"user_{user_id}_stats",
                {
                    "type": "user_stats_update",
                    "data": stats_data,
                }
            )
        except Exception as e:
            print(f"Error sending user stats update: {e}")
    
    def _get_time_remaining(self, auction):
        """Calculate time remaining in seconds"""
        if not auction.is_active:
            return 0
        
        now = timezone.now()
        if auction.end_time <= now:
            return 0
        
        return int((auction.end_time - now).total_seconds())


# Global instance
auction_websocket_service = AuctionWebSocketService()
