import React from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext"; // Assuming you have an AuthContext for managing login status
import { useTheme } from "../context/ThemeContext"; // Theme context for dark mode

function Footer() {
  const { user } = useAuth(); // Get user from AuthContext to check if logged in
  const { darkMode, theme } = useTheme(); // Get theme information
  const navigate = useNavigate(); // To navigate programmatically

  const handleCreateAuctionClick = (event) => {
    if (!user) {
      // If the user is not logged in, prevent the default link behavior and redirect to login
      event.preventDefault();
      navigate("/login"); // Navigate to login page
    } else if (!user.can_create_auctions && !user.is_staff) {
      // If user doesn't have permission to create auctions
      event.preventDefault();
      alert(
        "Your account type doesn't allow creating auctions. Please contact support to change your role."
      );
    }
    // Otherwise, the Link component will take care of navigating to the correct page.
  };

  const footerStyle = {
    backgroundColor: darkMode ? theme.surface : "#f8f9fa",
    color: darkMode ? theme.text : "#212529",
    borderTop: `1px solid ${darkMode ? theme.border : "#dee2e6"}`,
  };

  const linkStyle = {
    color: darkMode ? theme.textSecondary : "#6c757d",
    textDecoration: "none",
  };

  const linkHoverStyle = {
    color: darkMode ? theme.primary : "#3498db",
  };

  const copyrightStyle = {
    backgroundColor: darkMode ? theme.background : "#343a40",
    color: darkMode ? theme.text : "#ffffff",
  };

  return (
    <footer className="text-center text-lg-start mt-5" style={footerStyle}>
      <div className="container p-4">
        <div className="row">
          {/* Company Info */}
          <div className="col-lg-6 col-md-12 mb-4 mb-md-0 text-start">
            <h5
              className="text-uppercase"
              style={{ color: darkMode ? theme.text : "#212529" }}
            >
              AuctionStore
            </h5>
            <p style={{ color: darkMode ? theme.textSecondary : "#6c757d" }}>
              The best place to buy, sell, and bid for rare collectibles and
              amazing treasures!
            </p>
          </div>

          {/* Navigation Links */}
          <div className="col-lg-3 col-md-6 mb-4 mb-md-0 text-start">
            <h5
              className="text-uppercase"
              style={{ color: darkMode ? theme.text : "#212529" }}
            >
              Quick Links
            </h5>
            <ul className="list-unstyled mb-0">
              <li>
                <Link
                  to="/home"
                  style={linkStyle}
                  onMouseEnter={(e) =>
                    (e.target.style.color = linkHoverStyle.color)
                  }
                  onMouseLeave={(e) => (e.target.style.color = linkStyle.color)}
                >
                  Home
                </Link>
              </li>
              <li>
                <Link
                  to="/auctions"
                  style={linkStyle}
                  onMouseEnter={(e) =>
                    (e.target.style.color = linkHoverStyle.color)
                  }
                  onMouseLeave={(e) => (e.target.style.color = linkStyle.color)}
                >
                  Auctions
                </Link>
              </li>
              {/* Create Auction Link */}
              <li>
                <Link
                  to="/create-auction"
                  style={linkStyle}
                  onClick={handleCreateAuctionClick}
                  onMouseEnter={(e) =>
                    (e.target.style.color = linkHoverStyle.color)
                  }
                  onMouseLeave={(e) => (e.target.style.color = linkStyle.color)}
                >
                  Create Auction
                </Link>
              </li>
              <li>
                <Link
                  to="/profile"
                  style={linkStyle}
                  onMouseEnter={(e) =>
                    (e.target.style.color = linkHoverStyle.color)
                  }
                  onMouseLeave={(e) => (e.target.style.color = linkStyle.color)}
                >
                  My Profile
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Copyright */}
      <div className="text-center p-3" style={copyrightStyle}>
        © {new Date().getFullYear()} AuctionStore. All rights reserved.
      </div>
    </footer>
  );
}

export default Footer;
