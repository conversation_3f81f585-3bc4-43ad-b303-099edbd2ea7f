{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Online_Auction_System\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\";\nimport React, { useState, useEffect } from \"react\";\nimport { Container, Row, Col, Card, Table, Badge, Button, Modal, Form, Alert } from \"react-bootstrap\";\nimport { FaUsers, FaGavel, FaDollarSign, FaEye, FaChartLine, FaExclamationTriangle } from \"react-icons/fa\";\nimport { useAuth } from \"../context/AuthContext\";\nimport { formatAuctionPrice } from \"../utils/currency\";\nimport FraudAlertDetails from \"../components/FraudAlertDetails\";\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from \"recharts\";\nconst Dashboard = () => {\n  const {\n    user\n  } = useAuth();\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeAuctions: 0,\n    totalRevenue: 0,\n    totalViews: 0\n  });\n  const [recentAuctions, setRecentAuctions] = useState([]);\n  const [fraudAlerts, setFraudAlerts] = useState([]);\n  const [analyticsData, setAnalyticsData] = useState([]);\n  const [showFraudModal, setShowFraudModal] = useState(false);\n  const [selectedFraud, setSelectedFraud] = useState(null);\n  const [showImageEditModal, setShowImageEditModal] = useState(false);\n  const [selectedAuction, setSelectedAuction] = useState(null);\n  const [newImageUrl, setNewImageUrl] = useState(\"\");\n  const [imageEditLoading, setImageEditLoading] = useState(false);\n  const [imageEditMessage, setImageEditMessage] = useState(\"\");\n  useEffect(() => {\n    if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\") {\n      fetchDashboardData();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [user]);\n  const fetchDashboardData = async () => {\n    try {\n      // Fetch dashboard statistics\n      const statsResponse = await fetch(\"http://127.0.0.1:8000/api/analytics/\", {\n        headers: {\n          \"Content-Type\": \"application/json\"\n        }\n      });\n      if (statsResponse.ok) {\n        const analyticsData = await statsResponse.json();\n        // Process analytics data to get stats\n        processAnalyticsData(analyticsData.results || []);\n      }\n\n      // Fetch recent auctions\n      const auctionsResponse = await fetch(\"http://127.0.0.1:8000/api/auctions/?ordering=-created_at&page_size=10\", {\n        headers: {\n          Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`\n        }\n      });\n      if (auctionsResponse.ok) {\n        const auctionsData = await auctionsResponse.json();\n        setRecentAuctions(auctionsData.results || []);\n      }\n\n      // Fetch fraud alerts\n      const fraudResponse = await fetch(\"http://127.0.0.1:8000/api/fraud-detection/?status=pending\", {\n        headers: {\n          Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`\n        }\n      });\n      if (fraudResponse.ok) {\n        const fraudData = await fraudResponse.json();\n        setFraudAlerts(fraudData.results || []);\n      }\n    } catch (error) {\n      console.error(\"Error fetching dashboard data:\", error);\n    }\n  };\n  const processAnalyticsData = data => {\n    var _userMetrics$find, _auctionMetrics$find, _revenueMetrics$find, _auctionMetrics$find2;\n    // Process analytics data to extract key metrics\n    const userMetrics = data.filter(item => item.metric_type === \"user_engagement\");\n    const revenueMetrics = data.filter(item => item.metric_type === \"revenue\");\n    const auctionMetrics = data.filter(item => item.metric_type === \"auction_performance\");\n    setStats({\n      totalUsers: ((_userMetrics$find = userMetrics.find(m => m.metric_name === \"total_users\")) === null || _userMetrics$find === void 0 ? void 0 : _userMetrics$find.metric_value) || 0,\n      activeAuctions: ((_auctionMetrics$find = auctionMetrics.find(m => m.metric_name === \"active_auctions\")) === null || _auctionMetrics$find === void 0 ? void 0 : _auctionMetrics$find.metric_value) || 0,\n      totalRevenue: ((_revenueMetrics$find = revenueMetrics.find(m => m.metric_name === \"total_revenue\")) === null || _revenueMetrics$find === void 0 ? void 0 : _revenueMetrics$find.metric_value) || 0,\n      totalViews: ((_auctionMetrics$find2 = auctionMetrics.find(m => m.metric_name === \"total_views\")) === null || _auctionMetrics$find2 === void 0 ? void 0 : _auctionMetrics$find2.metric_value) || 0\n    });\n\n    // Prepare chart data\n    const chartData = data.slice(0, 7).map(item => ({\n      date: new Date(item.date).toLocaleDateString(),\n      value: parseFloat(item.metric_value),\n      type: item.metric_type\n    }));\n    setAnalyticsData(chartData);\n  };\n  const handleFraudAction = async (fraudId, action) => {\n    try {\n      const response = await fetch(`http://127.0.0.1:8000/api/fraud-detection/${fraudId}/`, {\n        method: \"PATCH\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`\n        },\n        body: JSON.stringify({\n          status: action,\n          resolved_at: new Date().toISOString()\n        })\n      });\n      if (response.ok) {\n        setFraudAlerts(prev => prev.filter(alert => alert.id !== fraudId));\n        setShowFraudModal(false);\n      }\n    } catch (error) {\n      console.error(\"Error updating fraud alert:\", error);\n    }\n  };\n  const handleImageEdit = auction => {\n    setSelectedAuction(auction);\n    setNewImageUrl(auction.image || \"\");\n    setImageEditMessage(\"\");\n    setShowImageEditModal(true);\n  };\n  const handleImageUpdate = async () => {\n    if (!selectedAuction) return;\n    setImageEditLoading(true);\n    setImageEditMessage(\"\");\n    try {\n      const response = await fetch(`http://127.0.0.1:8000/api/admin/edit-auction-image/${selectedAuction.id}/`, {\n        method: \"PATCH\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`\n        },\n        body: JSON.stringify({\n          image_url: newImageUrl\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setImageEditMessage(\"Image URL updated successfully!\");\n        // Update the auction in the list\n        setRecentAuctions(prev => prev.map(auction => auction.id === selectedAuction.id ? {\n          ...auction,\n          image: newImageUrl\n        } : auction));\n        setTimeout(() => {\n          setShowImageEditModal(false);\n        }, 2000);\n      } else {\n        setImageEditMessage(data.error || \"Failed to update image URL\");\n      }\n    } catch (error) {\n      console.error(\"Error updating image:\", error);\n      setImageEditMessage(\"Error updating image URL\");\n    } finally {\n      setImageEditLoading(false);\n    }\n  };\n  const StatCard = ({\n    title,\n    value,\n    icon,\n    color = \"primary\"\n  }) => /*#__PURE__*/React.createElement(Card, {\n    className: \"h-100\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(Card.Body, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex justify-content-between align-items-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h6\", {\n    className: \"text-muted mb-1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 13\n    }\n  }, title), /*#__PURE__*/React.createElement(\"h3\", {\n    className: `text-${color} mb-0`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 13\n    }\n  }, value)), /*#__PURE__*/React.createElement(\"div\", {\n    className: `text-${color}`,\n    style: {\n      fontSize: \"2rem\"\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 11\n    }\n  }, icon))));\n  if ((user === null || user === void 0 ? void 0 : user.role) !== \"admin\") {\n    return /*#__PURE__*/React.createElement(Container, {\n      className: \"mt-5\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"text-center\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }\n    }, /*#__PURE__*/React.createElement(\"h3\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }\n    }, \"Access Denied\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }\n    }, \"You don't have permission to view this page.\")));\n  }\n  return /*#__PURE__*/React.createElement(Container, {\n    fluid: true,\n    className: \"mt-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    className: \"mb-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 11\n    }\n  }, \"Admin Dashboard\"), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 11\n    }\n  }, \"Monitor your auction platform performance\"))), /*#__PURE__*/React.createElement(Row, {\n    className: \"mb-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    md: 3,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(StatCard, {\n    title: \"Total Users\",\n    value: stats.totalUsers.toLocaleString(),\n    icon: /*#__PURE__*/React.createElement(FaUsers, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 19\n      }\n    }),\n    color: \"primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 11\n    }\n  })), /*#__PURE__*/React.createElement(Col, {\n    md: 3,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(StatCard, {\n    title: \"Active Auctions\",\n    value: stats.activeAuctions.toLocaleString(),\n    icon: /*#__PURE__*/React.createElement(FaGavel, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 19\n      }\n    }),\n    color: \"success\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 11\n    }\n  })), /*#__PURE__*/React.createElement(Col, {\n    md: 3,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(StatCard, {\n    title: \"Total Revenue\",\n    value: formatAuctionPrice(stats.totalRevenue),\n    icon: /*#__PURE__*/React.createElement(FaDollarSign, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 19\n      }\n    }),\n    color: \"warning\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 11\n    }\n  })), /*#__PURE__*/React.createElement(Col, {\n    md: 3,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(StatCard, {\n    title: \"Total Views\",\n    value: stats.totalViews.toLocaleString(),\n    icon: /*#__PURE__*/React.createElement(FaEye, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 19\n      }\n    }),\n    color: \"info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 11\n    }\n  }))), /*#__PURE__*/React.createElement(Row, {\n    className: \"mb-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    md: 8,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Card, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Card.Header, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h5\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaChartLine, {\n    className: \"me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 17\n    }\n  }), \"Analytics Overview\")), /*#__PURE__*/React.createElement(Card.Body, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(ResponsiveContainer, {\n    width: \"100%\",\n    height: 300,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(LineChart, {\n    data: analyticsData,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(CartesianGrid, {\n    strokeDasharray: \"3 3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(XAxis, {\n    dataKey: \"date\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(YAxis, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(Tooltip, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(Line, {\n    type: \"monotone\",\n    dataKey: \"value\",\n    stroke: \"#8884d8\",\n    strokeWidth: 2,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 19\n    }\n  })))))), /*#__PURE__*/React.createElement(Col, {\n    md: 4,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Card, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Card.Header, {\n    className: \"d-flex justify-content-between align-items-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h5\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaExclamationTriangle, {\n    className: \"me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 17\n    }\n  }), \"Fraud Alerts\"), /*#__PURE__*/React.createElement(Badge, {\n    bg: \"danger\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 15\n    }\n  }, fraudAlerts.length)), /*#__PURE__*/React.createElement(Card.Body, {\n    style: {\n      maxHeight: \"300px\",\n      overflowY: \"auto\"\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 13\n    }\n  }, fraudAlerts.length === 0 ? /*#__PURE__*/React.createElement(\"p\", {\n    className: \"text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 17\n    }\n  }, \"No pending fraud alerts\") : fraudAlerts.map(alert => /*#__PURE__*/React.createElement(\"div\", {\n    key: alert.id,\n    className: \"border-bottom pb-2 mb-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex justify-content-between align-items-start\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 23\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 25\n    }\n  }, alert.fraud_type.replace(\"_\", \" \")), /*#__PURE__*/React.createElement(\"br\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 25\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    className: \"text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 25\n    }\n  }, \"Risk Score: \", alert.risk_score, \"/100\")), /*#__PURE__*/React.createElement(Button, {\n    variant: \"outline-primary\",\n    size: \"sm\",\n    onClick: () => {\n      setSelectedFraud(alert);\n      setShowFraudModal(true);\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 23\n    }\n  }, \"Review\")))))))), /*#__PURE__*/React.createElement(Row, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Card, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Card.Header, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h5\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 15\n    }\n  }, \"Recent Auctions\")), /*#__PURE__*/React.createElement(Card.Body, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(Table, {\n    responsive: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 21\n    }\n  }, \"Title\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 21\n    }\n  }, \"Owner\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 21\n    }\n  }, \"Current Bid\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 21\n    }\n  }, \"Bids\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 21\n    }\n  }, \"Status\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 21\n    }\n  }, \"Created\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 21\n    }\n  }, \"Actions\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 17\n    }\n  }, recentAuctions.map(auction => /*#__PURE__*/React.createElement(\"tr\", {\n    key: auction.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 23\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex align-items-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: auction.image || \"/placeholder-image.svg\",\n    alt: auction.title,\n    style: {\n      width: \"40px\",\n      height: \"40px\",\n      objectFit: \"contain\",\n      marginRight: \"10px\",\n      borderRadius: \"4px\"\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 27\n    }\n  }), auction.title)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 23\n    }\n  }, auction.owner), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 23\n    }\n  }, formatAuctionPrice(auction.current_bid)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 23\n    }\n  }, auction.total_bids || 0), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 23\n    }\n  }, /*#__PURE__*/React.createElement(Badge, {\n    bg: auction.is_active ? \"success\" : \"secondary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 25\n    }\n  }, auction.is_active ? \"Active\" : \"Ended\")), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 23\n    }\n  }, new Date(auction.created_at).toLocaleDateString()), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 23\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    variant: \"outline-primary\",\n    size: \"sm\",\n    onClick: () => handleImageEdit(auction),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 25\n    }\n  }, \"Edit Image\")))))))))), /*#__PURE__*/React.createElement(Modal, {\n    show: showFraudModal,\n    onHide: () => setShowFraudModal(false),\n    size: \"lg\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Modal.Header, {\n    closeButton: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Modal.Title, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaExclamationTriangle, {\n    className: \"me-2 text-warning\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 13\n    }\n  }), \"Security Alert Analysis\")), /*#__PURE__*/React.createElement(Modal.Body, {\n    className: \"p-0\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 9\n    }\n  }, selectedFraud && /*#__PURE__*/React.createElement(FraudAlertDetails, {\n    fraud: selectedFraud,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 29\n    }\n  })), /*#__PURE__*/React.createElement(Modal.Footer, {\n    className: \"bg-light\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex justify-content-between w-100\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    variant: \"secondary\",\n    onClick: () => setShowFraudModal(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 13\n    }\n  }, \"Close\"), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    variant: \"success\",\n    className: \"me-2\",\n    onClick: () => handleFraudAction(selectedFraud === null || selectedFraud === void 0 ? void 0 : selectedFraud.id, \"false_positive\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 15\n    }\n  }, \"Mark as False Positive\"), /*#__PURE__*/React.createElement(Button, {\n    variant: \"danger\",\n    onClick: () => handleFraudAction(selectedFraud === null || selectedFraud === void 0 ? void 0 : selectedFraud.id, \"confirmed\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 15\n    }\n  }, \"Confirm Fraud\"))))), /*#__PURE__*/React.createElement(Modal, {\n    show: showImageEditModal,\n    onHide: () => setShowImageEditModal(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Modal.Header, {\n    closeButton: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Modal.Title, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 11\n    }\n  }, \"Edit Auction Image\")), /*#__PURE__*/React.createElement(Modal.Body, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 9\n    }\n  }, selectedAuction && /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h6\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 15\n    }\n  }, \"Auction: \", selectedAuction.title), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 15\n    }\n  }, \"Owner: \", selectedAuction.owner), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"mb-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 17\n    }\n  }, \"Current Image:\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: selectedAuction.image && selectedAuction.image.trim() !== \"\" ? selectedAuction.image : \"/placeholder-image.svg\",\n    alt: selectedAuction.title,\n    style: {\n      maxWidth: \"200px\",\n      maxHeight: \"150px\",\n      objectFit: \"contain\",\n      border: \"1px solid #ddd\",\n      borderRadius: \"4px\"\n    },\n    onError: e => {\n      e.target.src = \"/placeholder-image.svg\";\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 19\n    }\n  })), selectedAuction.image && /*#__PURE__*/React.createElement(\"small\", {\n    className: \"text-muted d-block mt-1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 19\n    }\n  }, \"Current URL: \", selectedAuction.image)), /*#__PURE__*/React.createElement(Form.Group, {\n    className: \"mb-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Form.Label, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 17\n    }\n  }, \"New Image URL:\"), /*#__PURE__*/React.createElement(Form.Control, {\n    type: \"url\",\n    value: newImageUrl,\n    onChange: e => setNewImageUrl(e.target.value),\n    placeholder: \"https://example.com/image.jpg\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 537,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(Form.Text, {\n    className: \"text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 17\n    }\n  }, \"Enter a valid image URL (must start with http:// or https://)\")), newImageUrl && newImageUrl !== selectedAuction.image && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"mb-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 19\n    }\n  }, \"Preview New Image:\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: newImageUrl,\n    alt: \"Preview\",\n    style: {\n      maxWidth: \"200px\",\n      maxHeight: \"150px\",\n      objectFit: \"contain\",\n      border: \"1px solid #ddd\",\n      borderRadius: \"4px\"\n    },\n    onError: e => {\n      e.target.style.display = \"none\";\n    },\n    onLoad: e => {\n      e.target.style.display = \"block\";\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 21\n    }\n  }))), imageEditMessage && /*#__PURE__*/React.createElement(Alert, {\n    variant: imageEditMessage.includes(\"success\") ? \"success\" : \"danger\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 17\n    }\n  }, imageEditMessage))), /*#__PURE__*/React.createElement(Modal.Footer, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    variant: \"secondary\",\n    onClick: () => setShowImageEditModal(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 588,\n      columnNumber: 11\n    }\n  }, \"Cancel\"), /*#__PURE__*/React.createElement(Button, {\n    variant: \"primary\",\n    onClick: handleImageUpdate,\n    disabled: imageEditLoading || !newImageUrl,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 594,\n      columnNumber: 11\n    }\n  }, imageEditLoading ? \"Updating...\" : \"Update Image\"))));\n};\nexport default Dashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "<PERSON><PERSON>", "Modal", "Form", "<PERSON><PERSON>", "FaUsers", "FaGavel", "FaDollarSign", "FaEye", "FaChartLine", "FaExclamationTriangle", "useAuth", "formatAuctionPrice", "FraudAlertDetails", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "Dashboard", "user", "stats", "setStats", "totalUsers", "activeAuctions", "totalRevenue", "totalViews", "recentAuctions", "setRecentAuctions", "fraudAlerts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "analyticsData", "setAnalyticsData", "showFraudModal", "setShowFraudModal", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showImageEditModal", "setShowImageEditModal", "selectedAuction", "setSelectedAuction", "newImageUrl", "setNewImageUrl", "imageEditLoading", "setImageEditLoading", "imageEditMessage", "setImageEditMessage", "role", "fetchDashboardData", "statsResponse", "fetch", "headers", "ok", "json", "processAnalyticsData", "results", "auctionsResponse", "Authorization", "localStorage", "getItem", "auctionsData", "fraudResponse", "fraudData", "error", "console", "data", "_userMetrics$find", "_auctionMetrics$find", "_revenueMetrics$find", "_auctionMetrics$find2", "userMetrics", "filter", "item", "metric_type", "revenueMetrics", "auctionMetrics", "find", "m", "metric_name", "metric_value", "chartData", "slice", "map", "date", "Date", "toLocaleDateString", "value", "parseFloat", "type", "handleFraudAction", "fraudId", "action", "response", "method", "body", "JSON", "stringify", "status", "resolved_at", "toISOString", "prev", "alert", "id", "handleImageEdit", "auction", "image", "handleImageUpdate", "image_url", "setTimeout", "StatCard", "title", "icon", "color", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "style", "fontSize", "fluid", "md", "toLocaleString", "Header", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "stroke", "strokeWidth", "bg", "length", "maxHeight", "overflowY", "key", "fraud_type", "replace", "risk_score", "variant", "size", "onClick", "responsive", "src", "alt", "objectFit", "marginRight", "borderRadius", "owner", "current_bid", "total_bids", "is_active", "created_at", "show", "onHide", "closeButton", "Title", "fraud", "Footer", "trim", "max<PERSON><PERSON><PERSON>", "border", "onError", "e", "target", "Group", "Label", "Control", "onChange", "placeholder", "Text", "display", "onLoad", "includes", "disabled"], "sources": ["C:/Users/<USER>/Desktop/Online_Auction_System/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Container,\r\n  Row,\r\n  Col,\r\n  Card,\r\n  Table,\r\n  Badge,\r\n  Button,\r\n  Modal,\r\n  Form,\r\n  Alert,\r\n} from \"react-bootstrap\";\r\nimport {\r\n  FaUsers,\r\n  FaGavel,\r\n  FaDollarSign,\r\n  FaEye,\r\n  FaChartLine,\r\n  FaExclamationTriangle,\r\n} from \"react-icons/fa\";\r\nimport { useAuth } from \"../context/AuthContext\";\r\nimport { formatAuctionPrice } from \"../utils/currency\";\r\nimport FraudAlertDetails from \"../components/FraudAlertDetails\";\r\nimport {\r\n  LineChart,\r\n  Line,\r\n  XAxis,\r\n  YAxis,\r\n  CartesianGrid,\r\n  Tooltip,\r\n  ResponsiveContainer,\r\n} from \"recharts\";\r\n\r\nconst Dashboard = () => {\r\n  const { user } = useAuth();\r\n  const [stats, setStats] = useState({\r\n    totalUsers: 0,\r\n    activeAuctions: 0,\r\n    totalRevenue: 0,\r\n    totalViews: 0,\r\n  });\r\n  const [recentAuctions, setRecentAuctions] = useState([]);\r\n  const [fraudAlerts, setFraudAlerts] = useState([]);\r\n  const [analyticsData, setAnalyticsData] = useState([]);\r\n  const [showFraudModal, setShowFraudModal] = useState(false);\r\n  const [selectedFraud, setSelectedFraud] = useState(null);\r\n  const [showImageEditModal, setShowImageEditModal] = useState(false);\r\n  const [selectedAuction, setSelectedAuction] = useState(null);\r\n  const [newImageUrl, setNewImageUrl] = useState(\"\");\r\n  const [imageEditLoading, setImageEditLoading] = useState(false);\r\n  const [imageEditMessage, setImageEditMessage] = useState(\"\");\r\n\r\n  useEffect(() => {\r\n    if (user?.role === \"admin\") {\r\n      fetchDashboardData();\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [user]);\r\n\r\n  const fetchDashboardData = async () => {\r\n    try {\r\n      // Fetch dashboard statistics\r\n      const statsResponse = await fetch(\r\n        \"http://127.0.0.1:8000/api/analytics/\",\r\n        {\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n        }\r\n      );\r\n\r\n      if (statsResponse.ok) {\r\n        const analyticsData = await statsResponse.json();\r\n        // Process analytics data to get stats\r\n        processAnalyticsData(analyticsData.results || []);\r\n      }\r\n\r\n      // Fetch recent auctions\r\n      const auctionsResponse = await fetch(\r\n        \"http://127.0.0.1:8000/api/auctions/?ordering=-created_at&page_size=10\",\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (auctionsResponse.ok) {\r\n        const auctionsData = await auctionsResponse.json();\r\n        setRecentAuctions(auctionsData.results || []);\r\n      }\r\n\r\n      // Fetch fraud alerts\r\n      const fraudResponse = await fetch(\r\n        \"http://127.0.0.1:8000/api/fraud-detection/?status=pending\",\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (fraudResponse.ok) {\r\n        const fraudData = await fraudResponse.json();\r\n        setFraudAlerts(fraudData.results || []);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching dashboard data:\", error);\r\n    }\r\n  };\r\n\r\n  const processAnalyticsData = (data) => {\r\n    // Process analytics data to extract key metrics\r\n    const userMetrics = data.filter(\r\n      (item) => item.metric_type === \"user_engagement\"\r\n    );\r\n    const revenueMetrics = data.filter(\r\n      (item) => item.metric_type === \"revenue\"\r\n    );\r\n    const auctionMetrics = data.filter(\r\n      (item) => item.metric_type === \"auction_performance\"\r\n    );\r\n\r\n    setStats({\r\n      totalUsers:\r\n        userMetrics.find((m) => m.metric_name === \"total_users\")\r\n          ?.metric_value || 0,\r\n      activeAuctions:\r\n        auctionMetrics.find((m) => m.metric_name === \"active_auctions\")\r\n          ?.metric_value || 0,\r\n      totalRevenue:\r\n        revenueMetrics.find((m) => m.metric_name === \"total_revenue\")\r\n          ?.metric_value || 0,\r\n      totalViews:\r\n        auctionMetrics.find((m) => m.metric_name === \"total_views\")\r\n          ?.metric_value || 0,\r\n    });\r\n\r\n    // Prepare chart data\r\n    const chartData = data.slice(0, 7).map((item) => ({\r\n      date: new Date(item.date).toLocaleDateString(),\r\n      value: parseFloat(item.metric_value),\r\n      type: item.metric_type,\r\n    }));\r\n    setAnalyticsData(chartData);\r\n  };\r\n\r\n  const handleFraudAction = async (fraudId, action) => {\r\n    try {\r\n      const response = await fetch(\r\n        `http://127.0.0.1:8000/api/fraud-detection/${fraudId}/`,\r\n        {\r\n          method: \"PATCH\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\r\n          },\r\n          body: JSON.stringify({\r\n            status: action,\r\n            resolved_at: new Date().toISOString(),\r\n          }),\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        setFraudAlerts((prev) => prev.filter((alert) => alert.id !== fraudId));\r\n        setShowFraudModal(false);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating fraud alert:\", error);\r\n    }\r\n  };\r\n\r\n  const handleImageEdit = (auction) => {\r\n    setSelectedAuction(auction);\r\n    setNewImageUrl(auction.image || \"\");\r\n    setImageEditMessage(\"\");\r\n    setShowImageEditModal(true);\r\n  };\r\n\r\n  const handleImageUpdate = async () => {\r\n    if (!selectedAuction) return;\r\n\r\n    setImageEditLoading(true);\r\n    setImageEditMessage(\"\");\r\n\r\n    try {\r\n      const response = await fetch(\r\n        `http://127.0.0.1:8000/api/admin/edit-auction-image/${selectedAuction.id}/`,\r\n        {\r\n          method: \"PATCH\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\r\n          },\r\n          body: JSON.stringify({\r\n            image_url: newImageUrl,\r\n          }),\r\n        }\r\n      );\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        setImageEditMessage(\"Image URL updated successfully!\");\r\n        // Update the auction in the list\r\n        setRecentAuctions((prev) =>\r\n          prev.map((auction) =>\r\n            auction.id === selectedAuction.id\r\n              ? { ...auction, image: newImageUrl }\r\n              : auction\r\n          )\r\n        );\r\n        setTimeout(() => {\r\n          setShowImageEditModal(false);\r\n        }, 2000);\r\n      } else {\r\n        setImageEditMessage(data.error || \"Failed to update image URL\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating image:\", error);\r\n      setImageEditMessage(\"Error updating image URL\");\r\n    } finally {\r\n      setImageEditLoading(false);\r\n    }\r\n  };\r\n\r\n  const StatCard = ({ title, value, icon, color = \"primary\" }) => (\r\n    <Card className=\"h-100\">\r\n      <Card.Body>\r\n        <div className=\"d-flex justify-content-between align-items-center\">\r\n          <div>\r\n            <h6 className=\"text-muted mb-1\">{title}</h6>\r\n            <h3 className={`text-${color} mb-0`}>{value}</h3>\r\n          </div>\r\n          <div className={`text-${color}`} style={{ fontSize: \"2rem\" }}>\r\n            {icon}\r\n          </div>\r\n        </div>\r\n      </Card.Body>\r\n    </Card>\r\n  );\r\n\r\n  if (user?.role !== \"admin\") {\r\n    return (\r\n      <Container className=\"mt-5\">\r\n        <div className=\"text-center\">\r\n          <h3>Access Denied</h3>\r\n          <p>You don't have permission to view this page.</p>\r\n        </div>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Container fluid className=\"mt-4\">\r\n      <Row className=\"mb-4\">\r\n        <Col>\r\n          <h2>Admin Dashboard</h2>\r\n          <p className=\"text-muted\">\r\n            Monitor your auction platform performance\r\n          </p>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Statistics Cards */}\r\n      <Row className=\"mb-4\">\r\n        <Col md={3}>\r\n          <StatCard\r\n            title=\"Total Users\"\r\n            value={stats.totalUsers.toLocaleString()}\r\n            icon={<FaUsers />}\r\n            color=\"primary\"\r\n          />\r\n        </Col>\r\n        <Col md={3}>\r\n          <StatCard\r\n            title=\"Active Auctions\"\r\n            value={stats.activeAuctions.toLocaleString()}\r\n            icon={<FaGavel />}\r\n            color=\"success\"\r\n          />\r\n        </Col>\r\n        <Col md={3}>\r\n          <StatCard\r\n            title=\"Total Revenue\"\r\n            value={formatAuctionPrice(stats.totalRevenue)}\r\n            icon={<FaDollarSign />}\r\n            color=\"warning\"\r\n          />\r\n        </Col>\r\n        <Col md={3}>\r\n          <StatCard\r\n            title=\"Total Views\"\r\n            value={stats.totalViews.toLocaleString()}\r\n            icon={<FaEye />}\r\n            color=\"info\"\r\n          />\r\n        </Col>\r\n      </Row>\r\n\r\n      <Row className=\"mb-4\">\r\n        {/* Analytics Chart */}\r\n        <Col md={8}>\r\n          <Card>\r\n            <Card.Header>\r\n              <h5>\r\n                <FaChartLine className=\"me-2\" />\r\n                Analytics Overview\r\n              </h5>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              <ResponsiveContainer width=\"100%\" height={300}>\r\n                <LineChart data={analyticsData}>\r\n                  <CartesianGrid strokeDasharray=\"3 3\" />\r\n                  <XAxis dataKey=\"date\" />\r\n                  <YAxis />\r\n                  <Tooltip />\r\n                  <Line\r\n                    type=\"monotone\"\r\n                    dataKey=\"value\"\r\n                    stroke=\"#8884d8\"\r\n                    strokeWidth={2}\r\n                  />\r\n                </LineChart>\r\n              </ResponsiveContainer>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n\r\n        {/* Fraud Alerts */}\r\n        <Col md={4}>\r\n          <Card>\r\n            <Card.Header className=\"d-flex justify-content-between align-items-center\">\r\n              <h5>\r\n                <FaExclamationTriangle className=\"me-2\" />\r\n                Fraud Alerts\r\n              </h5>\r\n              <Badge bg=\"danger\">{fraudAlerts.length}</Badge>\r\n            </Card.Header>\r\n            <Card.Body style={{ maxHeight: \"300px\", overflowY: \"auto\" }}>\r\n              {fraudAlerts.length === 0 ? (\r\n                <p className=\"text-muted\">No pending fraud alerts</p>\r\n              ) : (\r\n                fraudAlerts.map((alert) => (\r\n                  <div key={alert.id} className=\"border-bottom pb-2 mb-2\">\r\n                    <div className=\"d-flex justify-content-between align-items-start\">\r\n                      <div>\r\n                        <strong>{alert.fraud_type.replace(\"_\", \" \")}</strong>\r\n                        <br />\r\n                        <small className=\"text-muted\">\r\n                          Risk Score: {alert.risk_score}/100\r\n                        </small>\r\n                      </div>\r\n                      <Button\r\n                        variant=\"outline-primary\"\r\n                        size=\"sm\"\r\n                        onClick={() => {\r\n                          setSelectedFraud(alert);\r\n                          setShowFraudModal(true);\r\n                        }}\r\n                      >\r\n                        Review\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n                ))\r\n              )}\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Recent Auctions */}\r\n      <Row>\r\n        <Col>\r\n          <Card>\r\n            <Card.Header>\r\n              <h5>Recent Auctions</h5>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              <Table responsive>\r\n                <thead>\r\n                  <tr>\r\n                    <th>Title</th>\r\n                    <th>Owner</th>\r\n                    <th>Current Bid</th>\r\n                    <th>Bids</th>\r\n                    <th>Status</th>\r\n                    <th>Created</th>\r\n                    <th>Actions</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {recentAuctions.map((auction) => (\r\n                    <tr key={auction.id}>\r\n                      <td>\r\n                        <div className=\"d-flex align-items-center\">\r\n                          <img\r\n                            src={auction.image || \"/placeholder-image.svg\"}\r\n                            alt={auction.title}\r\n                            style={{\r\n                              width: \"40px\",\r\n                              height: \"40px\",\r\n                              objectFit: \"contain\",\r\n                              marginRight: \"10px\",\r\n                              borderRadius: \"4px\",\r\n                            }}\r\n                          />\r\n                          {auction.title}\r\n                        </div>\r\n                      </td>\r\n                      <td>{auction.owner}</td>\r\n                      <td>{formatAuctionPrice(auction.current_bid)}</td>\r\n                      <td>{auction.total_bids || 0}</td>\r\n                      <td>\r\n                        <Badge bg={auction.is_active ? \"success\" : \"secondary\"}>\r\n                          {auction.is_active ? \"Active\" : \"Ended\"}\r\n                        </Badge>\r\n                      </td>\r\n                      <td>\r\n                        {new Date(auction.created_at).toLocaleDateString()}\r\n                      </td>\r\n                      <td>\r\n                        <Button\r\n                          variant=\"outline-primary\"\r\n                          size=\"sm\"\r\n                          onClick={() => handleImageEdit(auction)}\r\n                        >\r\n                          Edit Image\r\n                        </Button>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </Table>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Fraud Alert Modal */}\r\n      <Modal\r\n        show={showFraudModal}\r\n        onHide={() => setShowFraudModal(false)}\r\n        size=\"lg\"\r\n      >\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            <FaExclamationTriangle className=\"me-2 text-warning\" />\r\n            Security Alert Analysis\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body className=\"p-0\">\r\n          {selectedFraud && <FraudAlertDetails fraud={selectedFraud} />}\r\n        </Modal.Body>\r\n        <Modal.Footer className=\"bg-light\">\r\n          <div className=\"d-flex justify-content-between w-100\">\r\n            <Button\r\n              variant=\"secondary\"\r\n              onClick={() => setShowFraudModal(false)}\r\n            >\r\n              Close\r\n            </Button>\r\n            <div>\r\n              <Button\r\n                variant=\"success\"\r\n                className=\"me-2\"\r\n                onClick={() =>\r\n                  handleFraudAction(selectedFraud?.id, \"false_positive\")\r\n                }\r\n              >\r\n                Mark as False Positive\r\n              </Button>\r\n              <Button\r\n                variant=\"danger\"\r\n                onClick={() =>\r\n                  handleFraudAction(selectedFraud?.id, \"confirmed\")\r\n                }\r\n              >\r\n                Confirm Fraud\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* Image Edit Modal */}\r\n      <Modal\r\n        show={showImageEditModal}\r\n        onHide={() => setShowImageEditModal(false)}\r\n      >\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Edit Auction Image</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {selectedAuction && (\r\n            <div>\r\n              <h6>Auction: {selectedAuction.title}</h6>\r\n              <p className=\"text-muted\">Owner: {selectedAuction.owner}</p>\r\n\r\n              {/* Current Image Preview */}\r\n              <div className=\"mb-3\">\r\n                <label className=\"form-label\">Current Image:</label>\r\n                <div className=\"text-center\">\r\n                  <img\r\n                    src={\r\n                      selectedAuction.image &&\r\n                      selectedAuction.image.trim() !== \"\"\r\n                        ? selectedAuction.image\r\n                        : \"/placeholder-image.svg\"\r\n                    }\r\n                    alt={selectedAuction.title}\r\n                    style={{\r\n                      maxWidth: \"200px\",\r\n                      maxHeight: \"150px\",\r\n                      objectFit: \"contain\",\r\n                      border: \"1px solid #ddd\",\r\n                      borderRadius: \"4px\",\r\n                    }}\r\n                    onError={(e) => {\r\n                      e.target.src = \"/placeholder-image.svg\";\r\n                    }}\r\n                  />\r\n                </div>\r\n                {selectedAuction.image && (\r\n                  <small className=\"text-muted d-block mt-1\">\r\n                    Current URL: {selectedAuction.image}\r\n                  </small>\r\n                )}\r\n              </div>\r\n\r\n              {/* New Image URL Input */}\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>New Image URL:</Form.Label>\r\n                <Form.Control\r\n                  type=\"url\"\r\n                  value={newImageUrl}\r\n                  onChange={(e) => setNewImageUrl(e.target.value)}\r\n                  placeholder=\"https://example.com/image.jpg\"\r\n                />\r\n                <Form.Text className=\"text-muted\">\r\n                  Enter a valid image URL (must start with http:// or https://)\r\n                </Form.Text>\r\n              </Form.Group>\r\n\r\n              {/* New Image Preview */}\r\n              {newImageUrl && newImageUrl !== selectedAuction.image && (\r\n                <div className=\"mb-3\">\r\n                  <label className=\"form-label\">Preview New Image:</label>\r\n                  <div className=\"text-center\">\r\n                    <img\r\n                      src={newImageUrl}\r\n                      alt=\"Preview\"\r\n                      style={{\r\n                        maxWidth: \"200px\",\r\n                        maxHeight: \"150px\",\r\n                        objectFit: \"contain\",\r\n                        border: \"1px solid #ddd\",\r\n                        borderRadius: \"4px\",\r\n                      }}\r\n                      onError={(e) => {\r\n                        e.target.style.display = \"none\";\r\n                      }}\r\n                      onLoad={(e) => {\r\n                        e.target.style.display = \"block\";\r\n                      }}\r\n                    />\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Status Message */}\r\n              {imageEditMessage && (\r\n                <Alert\r\n                  variant={\r\n                    imageEditMessage.includes(\"success\") ? \"success\" : \"danger\"\r\n                  }\r\n                >\r\n                  {imageEditMessage}\r\n                </Alert>\r\n              )}\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button\r\n            variant=\"secondary\"\r\n            onClick={() => setShowImageEditModal(false)}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={handleImageUpdate}\r\n            disabled={imageEditLoading || !newImageUrl}\r\n          >\r\n            {imageEditLoading ? \"Updating...\" : \"Update Image\"}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default Dashboard;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,QACA,iBAAiB;AACxB,SACEC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,KAAK,EACLC,WAAW,EACXC,qBAAqB,QAChB,gBAAgB;AACvB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,mBAAmB,QACd,UAAU;AAEjB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC;IACjCgC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC8C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAE5DC,SAAS,CAAC,MAAM;IACd,IAAI,CAAA4B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,IAAI,MAAK,OAAO,EAAE;MAC1BC,kBAAkB,CAAC,CAAC;IACtB;IACA;EACF,CAAC,EAAE,CAAC5B,IAAI,CAAC,CAAC;EAEV,MAAM4B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF;MACA,MAAMC,aAAa,GAAG,MAAMC,KAAK,CAC/B,sCAAsC,EACtC;QACEC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIF,aAAa,CAACG,EAAE,EAAE;QACpB,MAAMrB,aAAa,GAAG,MAAMkB,aAAa,CAACI,IAAI,CAAC,CAAC;QAChD;QACAC,oBAAoB,CAACvB,aAAa,CAACwB,OAAO,IAAI,EAAE,CAAC;MACnD;;MAEA;MACA,MAAMC,gBAAgB,GAAG,MAAMN,KAAK,CAClC,uEAAuE,EACvE;QACEC,OAAO,EAAE;UACPM,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QAC/D;MACF,CACF,CAAC;MAED,IAAIH,gBAAgB,CAACJ,EAAE,EAAE;QACvB,MAAMQ,YAAY,GAAG,MAAMJ,gBAAgB,CAACH,IAAI,CAAC,CAAC;QAClDzB,iBAAiB,CAACgC,YAAY,CAACL,OAAO,IAAI,EAAE,CAAC;MAC/C;;MAEA;MACA,MAAMM,aAAa,GAAG,MAAMX,KAAK,CAC/B,2DAA2D,EAC3D;QACEC,OAAO,EAAE;UACPM,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QAC/D;MACF,CACF,CAAC;MAED,IAAIE,aAAa,CAACT,EAAE,EAAE;QACpB,MAAMU,SAAS,GAAG,MAAMD,aAAa,CAACR,IAAI,CAAC,CAAC;QAC5CvB,cAAc,CAACgC,SAAS,CAACP,OAAO,IAAI,EAAE,CAAC;MACzC;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,MAAMT,oBAAoB,GAAIW,IAAI,IAAK;IAAA,IAAAC,iBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA;IACrC;IACA,MAAMC,WAAW,GAAGL,IAAI,CAACM,MAAM,CAC5BC,IAAI,IAAKA,IAAI,CAACC,WAAW,KAAK,iBACjC,CAAC;IACD,MAAMC,cAAc,GAAGT,IAAI,CAACM,MAAM,CAC/BC,IAAI,IAAKA,IAAI,CAACC,WAAW,KAAK,SACjC,CAAC;IACD,MAAME,cAAc,GAAGV,IAAI,CAACM,MAAM,CAC/BC,IAAI,IAAKA,IAAI,CAACC,WAAW,KAAK,qBACjC,CAAC;IAEDnD,QAAQ,CAAC;MACPC,UAAU,EACR,EAAA2C,iBAAA,GAAAI,WAAW,CAACM,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,KAAK,aAAa,CAAC,cAAAZ,iBAAA,uBAAxDA,iBAAA,CACIa,YAAY,KAAI,CAAC;MACvBvD,cAAc,EACZ,EAAA2C,oBAAA,GAAAQ,cAAc,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,KAAK,iBAAiB,CAAC,cAAAX,oBAAA,uBAA/DA,oBAAA,CACIY,YAAY,KAAI,CAAC;MACvBtD,YAAY,EACV,EAAA2C,oBAAA,GAAAM,cAAc,CAACE,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,KAAK,eAAe,CAAC,cAAAV,oBAAA,uBAA7DA,oBAAA,CACIW,YAAY,KAAI,CAAC;MACvBrD,UAAU,EACR,EAAA2C,qBAAA,GAAAM,cAAc,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,KAAK,aAAa,CAAC,cAAAT,qBAAA,uBAA3DA,qBAAA,CACIU,YAAY,KAAI;IACxB,CAAC,CAAC;;IAEF;IACA,MAAMC,SAAS,GAAGf,IAAI,CAACgB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEV,IAAI,KAAM;MAChDW,IAAI,EAAE,IAAIC,IAAI,CAACZ,IAAI,CAACW,IAAI,CAAC,CAACE,kBAAkB,CAAC,CAAC;MAC9CC,KAAK,EAAEC,UAAU,CAACf,IAAI,CAACO,YAAY,CAAC;MACpCS,IAAI,EAAEhB,IAAI,CAACC;IACb,CAAC,CAAC,CAAC;IACHzC,gBAAgB,CAACgD,SAAS,CAAC;EAC7B,CAAC;EAED,MAAMS,iBAAiB,GAAG,MAAAA,CAAOC,OAAO,EAAEC,MAAM,KAAK;IACnD,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM1C,KAAK,CAC1B,6CAA6CwC,OAAO,GAAG,EACvD;QACEG,MAAM,EAAE,OAAO;QACf1C,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCM,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QAC/D,CAAC;QACDmC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,MAAM,EAAEN,MAAM;UACdO,WAAW,EAAE,IAAId,IAAI,CAAC,CAAC,CAACe,WAAW,CAAC;QACtC,CAAC;MACH,CACF,CAAC;MAED,IAAIP,QAAQ,CAACxC,EAAE,EAAE;QACftB,cAAc,CAAEsE,IAAI,IAAKA,IAAI,CAAC7B,MAAM,CAAE8B,KAAK,IAAKA,KAAK,CAACC,EAAE,KAAKZ,OAAO,CAAC,CAAC;QACtExD,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMwC,eAAe,GAAIC,OAAO,IAAK;IACnChE,kBAAkB,CAACgE,OAAO,CAAC;IAC3B9D,cAAc,CAAC8D,OAAO,CAACC,KAAK,IAAI,EAAE,CAAC;IACnC3D,mBAAmB,CAAC,EAAE,CAAC;IACvBR,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMoE,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACnE,eAAe,EAAE;IAEtBK,mBAAmB,CAAC,IAAI,CAAC;IACzBE,mBAAmB,CAAC,EAAE,CAAC;IAEvB,IAAI;MACF,MAAM8C,QAAQ,GAAG,MAAM1C,KAAK,CAC1B,sDAAsDX,eAAe,CAAC+D,EAAE,GAAG,EAC3E;QACET,MAAM,EAAE,OAAO;QACf1C,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCM,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QAC/D,CAAC;QACDmC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBW,SAAS,EAAElE;QACb,CAAC;MACH,CACF,CAAC;MAED,MAAMwB,IAAI,GAAG,MAAM2B,QAAQ,CAACvC,IAAI,CAAC,CAAC;MAElC,IAAIuC,QAAQ,CAACxC,EAAE,EAAE;QACfN,mBAAmB,CAAC,iCAAiC,CAAC;QACtD;QACAlB,iBAAiB,CAAEwE,IAAI,IACrBA,IAAI,CAAClB,GAAG,CAAEsB,OAAO,IACfA,OAAO,CAACF,EAAE,KAAK/D,eAAe,CAAC+D,EAAE,GAC7B;UAAE,GAAGE,OAAO;UAAEC,KAAK,EAAEhE;QAAY,CAAC,GAClC+D,OACN,CACF,CAAC;QACDI,UAAU,CAAC,MAAM;UACftE,qBAAqB,CAAC,KAAK,CAAC;QAC9B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLQ,mBAAmB,CAACmB,IAAI,CAACF,KAAK,IAAI,4BAA4B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CjB,mBAAmB,CAAC,0BAA0B,CAAC;IACjD,CAAC,SAAS;MACRF,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMiE,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAExB,KAAK;IAAEyB,IAAI;IAAEC,KAAK,GAAG;EAAU,CAAC,kBACzD1H,KAAA,CAAA2H,aAAA,CAACrH,IAAI;IAACsH,SAAS,EAAC,OAAO;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrBlI,KAAA,CAAA2H,aAAA,CAACrH,IAAI,CAAC6H,IAAI;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRlI,KAAA,CAAA2H,aAAA;IAAKC,SAAS,EAAC,mDAAmD;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChElI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACElI,KAAA,CAAA2H,aAAA;IAAIC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEV,KAAU,CAAC,eAC5CxH,KAAA,CAAA2H,aAAA;IAAIC,SAAS,EAAE,QAAQF,KAAK,OAAQ;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAElC,KAAU,CAC7C,CAAC,eACNhG,KAAA,CAAA2H,aAAA;IAAKC,SAAS,EAAE,QAAQF,KAAK,EAAG;IAACU,KAAK,EAAE;MAAEC,QAAQ,EAAE;IAAO,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1DT,IACE,CACF,CACI,CACP,CACP;EAED,IAAI,CAAA3F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,IAAI,MAAK,OAAO,EAAE;IAC1B,oBACEzD,KAAA,CAAA2H,aAAA,CAACxH,SAAS;MAACyH,SAAS,EAAC,MAAM;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzBlI,KAAA,CAAA2H,aAAA;MAAKC,SAAS,EAAC,aAAa;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC1BlI,KAAA,CAAA2H,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,eAAiB,CAAC,eACtBlI,KAAA,CAAA2H,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,8CAA+C,CAC/C,CACI,CAAC;EAEhB;EAEA,oBACElI,KAAA,CAAA2H,aAAA,CAACxH,SAAS;IAACmI,KAAK;IAACV,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BlI,KAAA,CAAA2H,aAAA,CAACvH,GAAG;IAACwH,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBlI,KAAA,CAAA2H,aAAA,CAACtH,GAAG;IAAAwH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACFlI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iBAAmB,CAAC,eACxBlI,KAAA,CAAA2H,aAAA;IAAGC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2CAEvB,CACA,CACF,CAAC,eAGNlI,KAAA,CAAA2H,aAAA,CAACvH,GAAG;IAACwH,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBlI,KAAA,CAAA2H,aAAA,CAACtH,GAAG;IAACkI,EAAE,EAAE,CAAE;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACTlI,KAAA,CAAA2H,aAAA,CAACJ,QAAQ;IACPC,KAAK,EAAC,aAAa;IACnBxB,KAAK,EAAEjE,KAAK,CAACE,UAAU,CAACuG,cAAc,CAAC,CAAE;IACzCf,IAAI,eAAEzH,KAAA,CAAA2H,aAAA,CAAC9G,OAAO;MAAAgH,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAClBR,KAAK,EAAC,SAAS;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAChB,CACE,CAAC,eACNlI,KAAA,CAAA2H,aAAA,CAACtH,GAAG;IAACkI,EAAE,EAAE,CAAE;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACTlI,KAAA,CAAA2H,aAAA,CAACJ,QAAQ;IACPC,KAAK,EAAC,iBAAiB;IACvBxB,KAAK,EAAEjE,KAAK,CAACG,cAAc,CAACsG,cAAc,CAAC,CAAE;IAC7Cf,IAAI,eAAEzH,KAAA,CAAA2H,aAAA,CAAC7G,OAAO;MAAA+G,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAClBR,KAAK,EAAC,SAAS;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAChB,CACE,CAAC,eACNlI,KAAA,CAAA2H,aAAA,CAACtH,GAAG;IAACkI,EAAE,EAAE,CAAE;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACTlI,KAAA,CAAA2H,aAAA,CAACJ,QAAQ;IACPC,KAAK,EAAC,eAAe;IACrBxB,KAAK,EAAE5E,kBAAkB,CAACW,KAAK,CAACI,YAAY,CAAE;IAC9CsF,IAAI,eAAEzH,KAAA,CAAA2H,aAAA,CAAC5G,YAAY;MAAA8G,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IACvBR,KAAK,EAAC,SAAS;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAChB,CACE,CAAC,eACNlI,KAAA,CAAA2H,aAAA,CAACtH,GAAG;IAACkI,EAAE,EAAE,CAAE;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACTlI,KAAA,CAAA2H,aAAA,CAACJ,QAAQ;IACPC,KAAK,EAAC,aAAa;IACnBxB,KAAK,EAAEjE,KAAK,CAACK,UAAU,CAACoG,cAAc,CAAC,CAAE;IACzCf,IAAI,eAAEzH,KAAA,CAAA2H,aAAA,CAAC3G,KAAK;MAAA6G,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAChBR,KAAK,EAAC,MAAM;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACb,CACE,CACF,CAAC,eAENlI,KAAA,CAAA2H,aAAA,CAACvH,GAAG;IAACwH,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEnBlI,KAAA,CAAA2H,aAAA,CAACtH,GAAG;IAACkI,EAAE,EAAE,CAAE;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACTlI,KAAA,CAAA2H,aAAA,CAACrH,IAAI;IAAAuH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACHlI,KAAA,CAAA2H,aAAA,CAACrH,IAAI,CAACmI,MAAM;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACVlI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACElI,KAAA,CAAA2H,aAAA,CAAC1G,WAAW;IAAC2G,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,sBAE9B,CACO,CAAC,eACdlI,KAAA,CAAA2H,aAAA,CAACrH,IAAI,CAAC6H,IAAI;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRlI,KAAA,CAAA2H,aAAA,CAAC/F,mBAAmB;IAAC8G,KAAK,EAAC,MAAM;IAACC,MAAM,EAAE,GAAI;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5ClI,KAAA,CAAA2H,aAAA,CAACrG,SAAS;IAACqD,IAAI,EAAElC,aAAc;IAAAoF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BlI,KAAA,CAAA2H,aAAA,CAACjG,aAAa;IAACkH,eAAe,EAAC,KAAK;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACvClI,KAAA,CAAA2H,aAAA,CAACnG,KAAK;IAACqH,OAAO,EAAC,MAAM;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACxBlI,KAAA,CAAA2H,aAAA,CAAClG,KAAK;IAAAoG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACTlI,KAAA,CAAA2H,aAAA,CAAChG,OAAO;IAAAkG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACXlI,KAAA,CAAA2H,aAAA,CAACpG,IAAI;IACH2E,IAAI,EAAC,UAAU;IACf2C,OAAO,EAAC,OAAO;IACfC,MAAM,EAAC,SAAS;IAChBC,WAAW,EAAE,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAChB,CACQ,CACQ,CACZ,CACP,CACH,CAAC,eAGNlI,KAAA,CAAA2H,aAAA,CAACtH,GAAG;IAACkI,EAAE,EAAE,CAAE;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACTlI,KAAA,CAAA2H,aAAA,CAACrH,IAAI;IAAAuH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACHlI,KAAA,CAAA2H,aAAA,CAACrH,IAAI,CAACmI,MAAM;IAACb,SAAS,EAAC,mDAAmD;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxElI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACElI,KAAA,CAAA2H,aAAA,CAACzG,qBAAqB;IAAC0G,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,gBAExC,CAAC,eACLlI,KAAA,CAAA2H,aAAA,CAACnH,KAAK;IAACwI,EAAE,EAAC,QAAQ;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE3F,WAAW,CAAC0G,MAAc,CACnC,CAAC,eACdjJ,KAAA,CAAA2H,aAAA,CAACrH,IAAI,CAAC6H,IAAI;IAACC,KAAK,EAAE;MAAEc,SAAS,EAAE,OAAO;MAAEC,SAAS,EAAE;IAAO,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzD3F,WAAW,CAAC0G,MAAM,KAAK,CAAC,gBACvBjJ,KAAA,CAAA2H,aAAA;IAAGC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yBAA0B,CAAC,GAErD3F,WAAW,CAACqD,GAAG,CAAEmB,KAAK,iBACpB/G,KAAA,CAAA2H,aAAA;IAAKyB,GAAG,EAAErC,KAAK,CAACC,EAAG;IAACY,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrDlI,KAAA,CAAA2H,aAAA;IAAKC,SAAS,EAAC,kDAAkD;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/DlI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACElI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAASnB,KAAK,CAACsC,UAAU,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAU,CAAC,eACrDtJ,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,eACNlI,KAAA,CAAA2H,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAChB,EAACnB,KAAK,CAACwC,UAAU,EAAC,MACzB,CACJ,CAAC,eACNvJ,KAAA,CAAA2H,aAAA,CAAClH,MAAM;IACL+I,OAAO,EAAC,iBAAiB;IACzBC,IAAI,EAAC,IAAI;IACTC,OAAO,EAAEA,CAAA,KAAM;MACb5G,gBAAgB,CAACiE,KAAK,CAAC;MACvBnE,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAE;IAAAiF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACH,QAEO,CACL,CACF,CACN,CAEM,CACP,CACH,CACF,CAAC,eAGNlI,KAAA,CAAA2H,aAAA,CAACvH,GAAG;IAAAyH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACFlI,KAAA,CAAA2H,aAAA,CAACtH,GAAG;IAAAwH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACFlI,KAAA,CAAA2H,aAAA,CAACrH,IAAI;IAAAuH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACHlI,KAAA,CAAA2H,aAAA,CAACrH,IAAI,CAACmI,MAAM;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACVlI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iBAAmB,CACZ,CAAC,eACdlI,KAAA,CAAA2H,aAAA,CAACrH,IAAI,CAAC6H,IAAI;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRlI,KAAA,CAAA2H,aAAA,CAACpH,KAAK;IAACoJ,UAAU;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACflI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACElI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACElI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,OAAS,CAAC,eACdlI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,OAAS,CAAC,eACdlI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,aAAe,CAAC,eACpBlI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,MAAQ,CAAC,eACblI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,QAAU,CAAC,eACflI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,SAAW,CAAC,eAChBlI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,SAAW,CACb,CACC,CAAC,eACRlI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG7F,cAAc,CAACuD,GAAG,CAAEsB,OAAO,iBAC1BlH,KAAA,CAAA2H,aAAA;IAAIyB,GAAG,EAAElC,OAAO,CAACF,EAAG;IAAAa,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClBlI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACElI,KAAA,CAAA2H,aAAA;IAAKC,SAAS,EAAC,2BAA2B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxClI,KAAA,CAAA2H,aAAA;IACEiC,GAAG,EAAE1C,OAAO,CAACC,KAAK,IAAI,wBAAyB;IAC/C0C,GAAG,EAAE3C,OAAO,CAACM,KAAM;IACnBY,KAAK,EAAE;MACLM,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdmB,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,MAAM;MACnBC,YAAY,EAAE;IAChB,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACH,CAAC,EACDhB,OAAO,CAACM,KACN,CACH,CAAC,eACLxH,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKhB,OAAO,CAAC+C,KAAU,CAAC,eACxBjK,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK9G,kBAAkB,CAAC8F,OAAO,CAACgD,WAAW,CAAM,CAAC,eAClDlK,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKhB,OAAO,CAACiD,UAAU,IAAI,CAAM,CAAC,eAClCnK,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACElI,KAAA,CAAA2H,aAAA,CAACnH,KAAK;IAACwI,EAAE,EAAE9B,OAAO,CAACkD,SAAS,GAAG,SAAS,GAAG,WAAY;IAAAvC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpDhB,OAAO,CAACkD,SAAS,GAAG,QAAQ,GAAG,OAC3B,CACL,CAAC,eACLpK,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG,IAAIpC,IAAI,CAACoB,OAAO,CAACmD,UAAU,CAAC,CAACtE,kBAAkB,CAAC,CAC/C,CAAC,eACL/F,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACElI,KAAA,CAAA2H,aAAA,CAAClH,MAAM;IACL+I,OAAO,EAAC,iBAAiB;IACzBC,IAAI,EAAC,IAAI;IACTC,OAAO,EAAEA,CAAA,KAAMzC,eAAe,CAACC,OAAO,CAAE;IAAAW,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzC,YAEO,CACN,CACF,CACL,CACI,CACF,CACE,CACP,CACH,CACF,CAAC,eAGNlI,KAAA,CAAA2H,aAAA,CAACjH,KAAK;IACJ4J,IAAI,EAAE3H,cAAe;IACrB4H,MAAM,EAAEA,CAAA,KAAM3H,iBAAiB,CAAC,KAAK,CAAE;IACvC6G,IAAI,EAAC,IAAI;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAETlI,KAAA,CAAA2H,aAAA,CAACjH,KAAK,CAAC+H,MAAM;IAAC+B,WAAW;IAAA3C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBlI,KAAA,CAAA2H,aAAA,CAACjH,KAAK,CAAC+J,KAAK;IAAA5C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACVlI,KAAA,CAAA2H,aAAA,CAACzG,qBAAqB;IAAC0G,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,2BAE5C,CACD,CAAC,eACflI,KAAA,CAAA2H,aAAA,CAACjH,KAAK,CAACyH,IAAI;IAACP,SAAS,EAAC,KAAK;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxBrF,aAAa,iBAAI7C,KAAA,CAAA2H,aAAA,CAACtG,iBAAiB;IAACqJ,KAAK,EAAE7H,aAAc;IAAAgF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAClD,CAAC,eACblI,KAAA,CAAA2H,aAAA,CAACjH,KAAK,CAACiK,MAAM;IAAC/C,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChClI,KAAA,CAAA2H,aAAA;IAAKC,SAAS,EAAC,sCAAsC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnDlI,KAAA,CAAA2H,aAAA,CAAClH,MAAM;IACL+I,OAAO,EAAC,WAAW;IACnBE,OAAO,EAAEA,CAAA,KAAM9G,iBAAiB,CAAC,KAAK,CAAE;IAAAiF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzC,OAEO,CAAC,eACTlI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACElI,KAAA,CAAA2H,aAAA,CAAClH,MAAM;IACL+I,OAAO,EAAC,SAAS;IACjB5B,SAAS,EAAC,MAAM;IAChB8B,OAAO,EAAEA,CAAA,KACPvD,iBAAiB,CAACtD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEmE,EAAE,EAAE,gBAAgB,CACtD;IAAAa,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACF,wBAEO,CAAC,eACTlI,KAAA,CAAA2H,aAAA,CAAClH,MAAM;IACL+I,OAAO,EAAC,QAAQ;IAChBE,OAAO,EAAEA,CAAA,KACPvD,iBAAiB,CAACtD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEmE,EAAE,EAAE,WAAW,CACjD;IAAAa,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACF,eAEO,CACL,CACF,CACO,CACT,CAAC,eAGRlI,KAAA,CAAA2H,aAAA,CAACjH,KAAK;IACJ4J,IAAI,EAAEvH,kBAAmB;IACzBwH,MAAM,EAAEA,CAAA,KAAMvH,qBAAqB,CAAC,KAAK,CAAE;IAAA6E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3ClI,KAAA,CAAA2H,aAAA,CAACjH,KAAK,CAAC+H,MAAM;IAAC+B,WAAW;IAAA3C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBlI,KAAA,CAAA2H,aAAA,CAACjH,KAAK,CAAC+J,KAAK;IAAA5C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oBAA+B,CAChC,CAAC,eACflI,KAAA,CAAA2H,aAAA,CAACjH,KAAK,CAACyH,IAAI;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACRjF,eAAe,iBACdjD,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACElI,KAAA,CAAA2H,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,WAAS,EAACjF,eAAe,CAACuE,KAAU,CAAC,eACzCxH,KAAA,CAAA2H,aAAA;IAAGC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAO,EAACjF,eAAe,CAACgH,KAAS,CAAC,eAG5DjK,KAAA,CAAA2H,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBlI,KAAA,CAAA2H,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gBAAqB,CAAC,eACpDlI,KAAA,CAAA2H,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BlI,KAAA,CAAA2H,aAAA;IACEiC,GAAG,EACD3G,eAAe,CAACkE,KAAK,IACrBlE,eAAe,CAACkE,KAAK,CAACyD,IAAI,CAAC,CAAC,KAAK,EAAE,GAC/B3H,eAAe,CAACkE,KAAK,GACrB,wBACL;IACD0C,GAAG,EAAE5G,eAAe,CAACuE,KAAM;IAC3BY,KAAK,EAAE;MACLyC,QAAQ,EAAE,OAAO;MACjB3B,SAAS,EAAE,OAAO;MAClBY,SAAS,EAAE,SAAS;MACpBgB,MAAM,EAAE,gBAAgB;MACxBd,YAAY,EAAE;IAChB,CAAE;IACFe,OAAO,EAAGC,CAAC,IAAK;MACdA,CAAC,CAACC,MAAM,CAACrB,GAAG,GAAG,wBAAwB;IACzC,CAAE;IAAA/B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACH,CACE,CAAC,EACLjF,eAAe,CAACkE,KAAK,iBACpBnH,KAAA,CAAA2H,aAAA;IAAOC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAC5B,EAACjF,eAAe,CAACkE,KACzB,CAEN,CAAC,eAGNnH,KAAA,CAAA2H,aAAA,CAAChH,IAAI,CAACuK,KAAK;IAACtD,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BlI,KAAA,CAAA2H,aAAA,CAAChH,IAAI,CAACwK,KAAK;IAAAtD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gBAA0B,CAAC,eACvClI,KAAA,CAAA2H,aAAA,CAAChH,IAAI,CAACyK,OAAO;IACXlF,IAAI,EAAC,KAAK;IACVF,KAAK,EAAE7C,WAAY;IACnBkI,QAAQ,EAAGL,CAAC,IAAK5H,cAAc,CAAC4H,CAAC,CAACC,MAAM,CAACjF,KAAK,CAAE;IAChDsF,WAAW,EAAC,+BAA+B;IAAAzD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC5C,CAAC,eACFlI,KAAA,CAAA2H,aAAA,CAAChH,IAAI,CAAC4K,IAAI;IAAC3D,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+DAEvB,CACD,CAAC,EAGZ/E,WAAW,IAAIA,WAAW,KAAKF,eAAe,CAACkE,KAAK,iBACnDnH,KAAA,CAAA2H,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBlI,KAAA,CAAA2H,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oBAAyB,CAAC,eACxDlI,KAAA,CAAA2H,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BlI,KAAA,CAAA2H,aAAA;IACEiC,GAAG,EAAEzG,WAAY;IACjB0G,GAAG,EAAC,SAAS;IACbzB,KAAK,EAAE;MACLyC,QAAQ,EAAE,OAAO;MACjB3B,SAAS,EAAE,OAAO;MAClBY,SAAS,EAAE,SAAS;MACpBgB,MAAM,EAAE,gBAAgB;MACxBd,YAAY,EAAE;IAChB,CAAE;IACFe,OAAO,EAAGC,CAAC,IAAK;MACdA,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAACoD,OAAO,GAAG,MAAM;IACjC,CAAE;IACFC,MAAM,EAAGT,CAAC,IAAK;MACbA,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAACoD,OAAO,GAAG,OAAO;IAClC,CAAE;IAAA3D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACH,CACE,CACF,CACN,EAGA3E,gBAAgB,iBACfvD,KAAA,CAAA2H,aAAA,CAAC/G,KAAK;IACJ4I,OAAO,EACLjG,gBAAgB,CAACmI,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,QACpD;IAAA7D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEA3E,gBACI,CAEN,CAEG,CAAC,eACbvD,KAAA,CAAA2H,aAAA,CAACjH,KAAK,CAACiK,MAAM;IAAA9C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACXlI,KAAA,CAAA2H,aAAA,CAAClH,MAAM;IACL+I,OAAO,EAAC,WAAW;IACnBE,OAAO,EAAEA,CAAA,KAAM1G,qBAAqB,CAAC,KAAK,CAAE;IAAA6E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7C,QAEO,CAAC,eACTlI,KAAA,CAAA2H,aAAA,CAAClH,MAAM;IACL+I,OAAO,EAAC,SAAS;IACjBE,OAAO,EAAEtC,iBAAkB;IAC3BuE,QAAQ,EAAEtI,gBAAgB,IAAI,CAACF,WAAY;IAAA0E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAE1C7E,gBAAgB,GAAG,aAAa,GAAG,cAC9B,CACI,CACT,CACE,CAAC;AAEhB,CAAC;AAED,eAAexB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}