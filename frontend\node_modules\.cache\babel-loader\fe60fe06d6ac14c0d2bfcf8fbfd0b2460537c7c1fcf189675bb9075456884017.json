{"ast": null, "code": "import { timeInterval } from \"./interval.js\";\nimport { durationSecond } from \"./duration.js\";\nexport const second = timeInterval(date => {\n  date.setTime(date - date.getMilliseconds());\n}, (date, step) => {\n  date.setTime(+date + step * durationSecond);\n}, (start, end) => {\n  return (end - start) / durationSecond;\n}, date => {\n  return date.getUTCSeconds();\n});\nexport const seconds = second.range;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}