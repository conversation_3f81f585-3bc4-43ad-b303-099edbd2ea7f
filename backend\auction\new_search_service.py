from django.db.models import Q, Count
from django.utils import timezone
from .models import Auction
import logging

logger = logging.getLogger(__name__)

class NewAdvancedSearchService:
    """
    New Advanced Search Service for precise filtering
    """
    
    def __init__(self):
        self.search_weights = {
            "title": 3.0,
            "description": 2.0,
            "category": 1.5,
            "condition": 1.0,
        }

    def search_auctions(self, filters):
        """
        Perform advanced search with precise filtering
        
        Args:
            filters (dict): Dictionary containing search filters
            
        Returns:
            QuerySet: Filtered auction queryset
        """
        try:
            # Start with base queryset - only approved auctions
            queryset = Auction.objects.filter(approved=True)
            
            # Apply text search if provided
            search_query = filters.get('q', '').strip()
            if search_query:
                queryset = self._apply_text_search(queryset, search_query)
            
            # Apply category filter (exact match)
            category = filters.get('category', '').strip()
            if category:
                queryset = queryset.filter(category__iexact=category)
                logger.info(f"Applied category filter: {category}, results: {queryset.count()}")
            
            # Apply price range filters
            min_price = filters.get('min_price', '').strip()
            if min_price:
                try:
                    min_price_value = float(min_price)
                    queryset = queryset.filter(current_bid__gte=min_price_value)
                    logger.info(f"Applied min price filter: {min_price_value}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid min_price value: {min_price}")
            
            max_price = filters.get('max_price', '').strip()
            if max_price:
                try:
                    max_price_value = float(max_price)
                    queryset = queryset.filter(current_bid__lte=max_price_value)
                    logger.info(f"Applied max price filter: {max_price_value}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid max_price value: {max_price}")
            
            # Apply condition filter (exact match)
            condition = filters.get('condition', '').strip()
            if condition:
                queryset = queryset.filter(condition__iexact=condition)
                logger.info(f"Applied condition filter: {condition}")
            
            # Apply status filter
            status = filters.get('status', '').strip()
            if status:
                queryset = self._apply_status_filter(queryset, status)
                logger.info(f"Applied status filter: {status}")
            
            # Apply sorting
            sort_by = filters.get('sort_by', 'created_at')
            sort_order = filters.get('sort_order', 'desc')
            queryset = self._apply_sorting(queryset, sort_by, sort_order)
            
            logger.info(f"Final search results: {queryset.count()} auctions")
            return queryset
            
        except Exception as e:
            logger.error(f"Error in search_auctions: {str(e)}")
            # Return empty queryset on error
            return Auction.objects.none()

    def _apply_text_search(self, queryset, search_query):
        """Apply text search across multiple fields"""
        search_terms = search_query.split()
        search_q = Q()
        
        for term in search_terms:
            term_q = (
                Q(title__icontains=term) |
                Q(description__icontains=term) |
                Q(category__icontains=term) |
                Q(condition__icontains=term)
            )
            search_q &= term_q
        
        return queryset.filter(search_q)

    def _apply_status_filter(self, queryset, status):
        """Apply status-based filtering"""
        now = timezone.now()
        
        if status == 'active':
            return queryset.filter(end_time__gt=now, is_active=True)
        elif status == 'ending_soon':
            # Ending within 24 hours
            ending_soon_time = now + timezone.timedelta(hours=24)
            return queryset.filter(
                end_time__gt=now,
                end_time__lte=ending_soon_time,
                is_active=True
            )
        elif status == 'ended':
            return queryset.filter(end_time__lte=now)
        
        return queryset

    def _apply_sorting(self, queryset, sort_by, sort_order):
        """Apply sorting to queryset"""
        # Map sort fields to actual model fields
        sort_field_map = {
            'created_at': 'created_at',
            'end_time': 'end_time',
            'current_bid': 'current_bid',
            'starting_bid': 'starting_bid',
            'title': 'title',
            'views_count': 'views_count',
        }
        
        # Get the actual field name
        field = sort_field_map.get(sort_by, 'created_at')
        
        # Apply sort order
        if sort_order == 'asc':
            order_field = field
        else:
            order_field = f'-{field}'
        
        return queryset.order_by(order_field)

    def get_filter_options(self):
        """Get available filter options"""
        try:
            # Get unique categories
            categories = list(
                Auction.objects.filter(approved=True)
                .values_list('category', flat=True)
                .distinct()
                .order_by('category')
            )
            
            # Get unique conditions
            conditions = list(
                Auction.objects.filter(approved=True)
                .values_list('condition', flat=True)
                .distinct()
                .order_by('condition')
            )
            
            # Define sort options
            sort_options = [
                {'value': 'created_at', 'label': 'Date Created'},
                {'value': 'end_time', 'label': 'Ending Time'},
                {'value': 'current_bid', 'label': 'Current Bid'},
                {'value': 'starting_bid', 'label': 'Starting Bid'},
                {'value': 'title', 'label': 'Title'},
                {'value': 'views_count', 'label': 'Popularity'},
            ]
            
            return {
                'categories': [cat for cat in categories if cat],  # Remove empty categories
                'conditions': [cond for cond in conditions if cond],  # Remove empty conditions
                'sort_options': sort_options,
            }
            
        except Exception as e:
            logger.error(f"Error getting filter options: {str(e)}")
            return {
                'categories': [],
                'conditions': [],
                'sort_options': [
                    {'value': 'created_at', 'label': 'Date Created'},
                    {'value': 'current_bid', 'label': 'Current Bid'},
                ],
            }

    def get_search_suggestions(self, query, limit=5):
        """Get search suggestions based on query"""
        if not query or len(query) < 2:
            return []
        
        try:
            # Get suggestions from auction titles
            title_suggestions = list(
                Auction.objects.filter(
                    approved=True,
                    title__icontains=query
                )
                .values_list('title', flat=True)
                .distinct()[:limit]
            )
            
            # Get category suggestions
            category_suggestions = list(
                Auction.objects.filter(
                    approved=True,
                    category__icontains=query
                )
                .values_list('category', flat=True)
                .distinct()[:3]
            )
            
            # Combine and limit suggestions
            all_suggestions = title_suggestions + category_suggestions
            return list(set(all_suggestions))[:limit]
            
        except Exception as e:
            logger.error(f"Error getting search suggestions: {str(e)}")
            return []

    def get_filter_counts(self, base_filters):
        """Get counts for each filter option based on current filters"""
        try:
            base_queryset = self.search_auctions(base_filters)
            
            # Get category counts
            category_counts = dict(
                base_queryset.values('category')
                .annotate(count=Count('id'))
                .values_list('category', 'count')
            )
            
            # Get condition counts
            condition_counts = dict(
                base_queryset.values('condition')
                .annotate(count=Count('id'))
                .values_list('condition', 'count')
            )
            
            return {
                'categories': category_counts,
                'conditions': condition_counts,
            }
            
        except Exception as e:
            logger.error(f"Error getting filter counts: {str(e)}")
            return {
                'categories': {},
                'conditions': {},
            }
