@echo off
echo ====================================================
echo           QUICK COMMIT TO GITHUB
echo ====================================================
echo.

REM Check if a commit message was provided
if "%~1"=="" (
    echo Usage: quick_commit.bat "Your commit message"
    echo.
    echo Example: quick_commit.bat "fix: update currency to USD"
    echo.
    pause
    exit /b 1
)

echo 📝 Commit Message: %~1
echo.

echo 🔍 Checking Git status...
git status --porcelain
echo.

echo 📦 Adding all changes...
git add .

echo 💾 Committing changes...
git commit -m "%~1"

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Commit failed! Please check for errors.
    pause
    exit /b 1
)

echo 🚀 Pushing to GitHub...
git push origin main

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Push failed! Please check your internet connection and GitHub credentials.
    pause
    exit /b 1
)

echo.
echo ✅ Successfully committed and pushed to GitHub!
echo 🎉 All changes are now live on your repository.
echo.
pause
