from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.contrib.admin.views.decorators import staff_member_required
from django.http import JsonResponse
from django.urls import include, path
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt


def api_root(request):
    return JsonResponse(
        {
            "message": "Online Auction System API",
            "version": "1.0",
            "endpoints": {
                "auctions": "/api/auctions/",
                "bids": "/api/bids/",
                "users": "/api/register/",
                "login": "/api/login/",
                "admin": "/admin/",
                "extended_features": {
                    "trending_auctions": "/api/extended/auctions/trending/",
                    "ending_soon": "/api/extended/auctions/ending-soon/",
                    "popular_categories": "/api/extended/auctions/popular-categories/",
                    "user_stats": "/api/extended/auctions/user-stats/",
                    "admin_dashboard": "/api/extended/admin/dashboard-stats/",
                    "revenue_report": "/api/extended/admin/revenue-report/",
                    "user_export": "/api/extended/user/export/"
                }
            },
        }
    )


urlpatterns = [
    path("", api_root, name="api_root"),
    path("admin/", admin.site.urls),
    path("api/", include("auction.urls")),  # Main auction functionality
    path("api/extended/", include("auctions.urls")),  # Extended auction functionality
    # WebSocket URLs are handled by ASGI, not Django URLs
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
