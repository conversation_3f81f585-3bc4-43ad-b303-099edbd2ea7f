.ai-price-prediction {
  background: var(--gradient-premium);
  border-radius: var(--radius-auction);
  padding: var(--space-2xl);
  margin: var(--space-xl) 0;
  color: var(--text-light);
  box-shadow: var(--shadow-xl);
  border: 2px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.ai-price-prediction::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 70%, rgba(217, 119, 6, 0.2) 0%, transparent 60%),
    radial-gradient(circle at 70% 30%, rgba(8, 145, 178, 0.2) 0%, transparent 60%);
  pointer-events: none;
}

.prediction-header h3 {
  margin: 0 0 8px 0;
  font-size: 1.4em;
  font-weight: 600;
}

.prediction-header p {
  margin: 0 0 20px 0;
  opacity: 0.9;
  font-size: 0.95em;
}

.generate-prediction-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: var(--text-light);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-lg);
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-bounce);
  backdrop-filter: blur(15px);
  position: relative;
  z-index: 1;
  letter-spacing: 0.5px;
}

.generate-prediction-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.generate-prediction-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.prediction-loading {
  text-align: center;
  padding: 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.prediction-error {
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}

.prediction-error button {
  background: #f44336;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  margin-top: 10px;
  cursor: pointer;
}

.prediction-results {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.prediction-main {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
  backdrop-filter: blur(10px);
}

.predicted-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.price-label {
  font-size: 1.1em;
  font-weight: 500;
}

.price-value {
  font-size: 2em;
  font-weight: 700;
  color: #FFD700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.confidence-indicator {
  margin-top: 15px;
}

.confidence-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.confidence-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.8s ease;
}

.confidence-label {
  font-size: 0.9em;
  font-weight: 500;
}

.prediction-meta {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 15px;
  font-size: 0.9em;
}

.prediction-meta p {
  margin: 4px 0;
}

.toggle-details-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9em;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.toggle-details-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.prediction-details {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from { opacity: 0; max-height: 0; }
  to { opacity: 1; max-height: 500px; }
}

.prediction-details h4 {
  margin: 0 0 15px 0;
  font-size: 1.1em;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.feature-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  font-size: 0.85em;
}

.feature-name {
  font-weight: 500;
  opacity: 0.9;
}

.feature-value {
  font-weight: 600;
  color: #FFD700;
}

.prediction-actions {
  text-align: center;
}

.refresh-prediction-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.3s ease;
}

.refresh-prediction-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.refresh-prediction-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.prediction-disclaimer {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.prediction-disclaimer small {
  opacity: 0.8;
  font-size: 0.8em;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-price-prediction {
    padding: 15px;
    margin: 15px 0;
  }
  
  .predicted-price {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .price-value {
    font-size: 1.8em;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-item {
    flex-direction: column;
    gap: 4px;
  }
}
