import React, { createContext, useContext, useEffect, useState } from "react";
import axiosInstance from "../api/axiosInstance";

const NotificationContext = createContext();
export const useNotifications = () => useContext(NotificationContext);

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    const token =
      localStorage.getItem("access_token") || localStorage.getItem("token");
    if (!token) {
      console.log(
        "No authentication token found, skipping notifications fetch"
      );
      return;
    }

    axiosInstance
      .get("notifications/")
      .then((response) => {
        setNotifications(response.data.results || response.data || []);
        console.log("✅ Notifications loaded successfully");
      })
      .catch((error) => {
        if (error.response?.status === 401) {
          console.log(
            "🔐 Notifications require authentication - user not logged in"
          );
        } else {
          console.error("❌ Notification fetch failed:", error);
        }
      });
  }, []);

  return (
    <NotificationContext.Provider value={{ notifications }}>
      {children}
    </NotificationContext.Provider>
  );
};
