<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Cache - Online Auction System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        button {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
        }
        .success { background: rgba(40, 167, 69, 0.3); }
        .error { background: rgba(220, 53, 69, 0.3); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Clear Browser Cache</h1>
        <p>This will clear all cached data and force a fresh reload of the application.</p>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <button onclick="clearAllCache()">🗑️ Clear All Cache</button>
        <button onclick="clearServiceWorker()">⚙️ Clear Service Worker</button>
        <button onclick="clearLocalStorage()">💾 Clear Local Storage</button>
        <button onclick="reloadApp()">🔄 Reload App</button>
        
        <div style="margin-top: 30px;">
            <h3>Manual Steps:</h3>
            <ol style="text-align: left;">
                <li>Press <strong>Ctrl + Shift + Delete</strong> (Windows) or <strong>Cmd + Shift + Delete</strong> (Mac)</li>
                <li>Select "All time" for time range</li>
                <li>Check "Cached images and files"</li>
                <li>Click "Clear data"</li>
                <li>Refresh the page with <strong>Ctrl + F5</strong> or <strong>Cmd + Shift + R</strong></li>
            </ol>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        async function clearAllCache() {
            try {
                // Clear all caches
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                    showStatus('✅ All caches cleared successfully!');
                } else {
                    showStatus('⚠️ Cache API not supported', 'error');
                }
            } catch (error) {
                showStatus('❌ Error clearing cache: ' + error.message, 'error');
            }
        }

        async function clearServiceWorker() {
            try {
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    await Promise.all(registrations.map(reg => reg.unregister()));
                    showStatus('✅ Service worker cleared successfully!');
                } else {
                    showStatus('⚠️ Service Worker not supported', 'error');
                }
            } catch (error) {
                showStatus('❌ Error clearing service worker: ' + error.message, 'error');
            }
        }

        function clearLocalStorage() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                showStatus('✅ Local storage cleared successfully!');
            } catch (error) {
                showStatus('❌ Error clearing local storage: ' + error.message, 'error');
            }
        }

        function reloadApp() {
            showStatus('🔄 Reloading application...');
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        }

        // Auto-clear on page load
        window.addEventListener('load', () => {
            console.log('🧹 Cache clearing page loaded');
        });
    </script>
</body>
</html>
