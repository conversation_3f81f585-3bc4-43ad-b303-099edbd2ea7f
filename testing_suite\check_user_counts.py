#!/usr/bin/env python3
"""
Script to check user counts across different parts of the system
"""

import os
import sys
import django
import requests
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from django.contrib.auth.models import User
from auction.models import Auction, Bid
from django.utils import timezone

def check_database_counts():
    """Check actual counts in the database"""
    print("🔍 Checking Database Counts")
    print("=" * 50)
    
    # User counts
    total_users = User.objects.count()
    admin_users = User.objects.filter(is_staff=True).count()
    regular_users = User.objects.filter(is_staff=False).count()
    
    # Recent users (last 30 days)
    month_ago = timezone.now() - timedelta(days=30)
    recent_users = User.objects.filter(date_joined__gte=month_ago).count()
    
    # Active users (users who bid in last 30 days)
    active_users = User.objects.filter(bid__created_at__gte=month_ago).distinct().count()
    
    print(f"👥 Total Users: {total_users}")
    print(f"👑 Admin Users: {admin_users}")
    print(f"👤 Regular Users: {regular_users}")
    print(f"🆕 Recent Users (30d): {recent_users}")
    print(f"🔥 Active Users (30d): {active_users}")
    
    # Auction counts
    total_auctions = Auction.objects.count()
    active_auctions = Auction.objects.filter(
        end_time__gt=timezone.now(), 
        approved=True
    ).count()
    
    print(f"\n🏷️ Total Auctions: {total_auctions}")
    print(f"🔥 Active Auctions: {active_auctions}")
    
    # Bid counts
    total_bids = Bid.objects.count()
    recent_bids = Bid.objects.filter(created_at__gte=month_ago).count()
    
    print(f"\n💰 Total Bids: {total_bids}")
    print(f"🆕 Recent Bids (30d): {recent_bids}")
    
    return {
        'total_users': total_users,
        'admin_users': admin_users,
        'regular_users': regular_users,
        'recent_users': recent_users,
        'active_users': active_users,
        'total_auctions': total_auctions,
        'active_auctions': active_auctions,
        'total_bids': total_bids,
        'recent_bids': recent_bids,
    }

def check_api_endpoints():
    """Check what the API endpoints return"""
    print("\n🌐 Checking API Endpoints")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000/api"
    
    # Check platform stats endpoint
    try:
        response = requests.get(f"{base_url}/platform-stats/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            stats = data.get('stats', {})
            print(f"📊 Platform Stats API:")
            print(f"   Total Users: {stats.get('total_users', 'N/A')}")
            print(f"   Total Auctions: {stats.get('total_auctions', 'N/A')}")
            print(f"   Active Auctions: {stats.get('active_auctions', 'N/A')}")
            print(f"   Total Bids: {stats.get('total_bids', 'N/A')}")
        else:
            print(f"❌ Platform Stats API error: {response.status_code}")
    except Exception as e:
        print(f"❌ Platform Stats API error: {e}")
    
    # Check landing page data endpoint
    try:
        response = requests.get(f"{base_url}/landing-page-data/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            stats = data.get('stats', {})
            print(f"\n🏠 Landing Page API:")
            print(f"   Total Users: {stats.get('total_users', 'N/A')}")
            print(f"   Total Auctions: {stats.get('total_auctions', 'N/A')}")
            print(f"   Active Auctions: {stats.get('active_auctions', 'N/A')}")
            print(f"   Total Bids: {stats.get('total_bids', 'N/A')}")
        else:
            print(f"❌ Landing Page API error: {response.status_code}")
    except Exception as e:
        print(f"❌ Landing Page API error: {e}")

def check_analytics_service():
    """Check analytics service data"""
    print("\n📈 Checking Analytics Service")
    print("=" * 50)
    
    try:
        from auction.analytics_services import advanced_analytics_service
        
        # Get dashboard data
        dashboard_data = advanced_analytics_service.get_comprehensive_dashboard_data()
        
        if dashboard_data and 'basic_metrics' in dashboard_data:
            metrics = dashboard_data['basic_metrics']
            print(f"📊 Analytics Service:")
            print(f"   Total Users: {metrics.get('total_users', 'N/A')}")
            print(f"   Total Auctions: {metrics.get('total_auctions', 'N/A')}")
            print(f"   Active Auctions: {metrics.get('active_auctions', 'N/A')}")
            print(f"   Total Bids: {metrics.get('total_bids', 'N/A')}")
        else:
            print("❌ Analytics service returned no data")
            
    except Exception as e:
        print(f"❌ Analytics service error: {e}")

def generate_recommendations():
    """Generate recommendations for fixing the discrepancy"""
    print("\n💡 Recommendations")
    print("=" * 50)
    
    db_counts = check_database_counts()
    
    print("\n🎯 Issues Found:")
    print("1. Home page uses hardcoded user count (85)")
    print("2. Admin dashboard uses real database counts")
    print("3. Analytics service uses real database counts")
    print("4. This creates inconsistency across the platform")
    
    print("\n✅ Recommended Solutions:")
    print("1. Update home page to use real user counts from API")
    print("2. Create a unified stats service for consistency")
    print("3. Add caching to prevent excessive database queries")
    print("4. Consider privacy settings for public vs admin views")
    
    print(f"\n📊 Correct Counts to Use:")
    print(f"   Total Users: {db_counts['total_users']}")
    print(f"   Total Auctions: {db_counts['total_auctions']}")
    print(f"   Active Auctions: {db_counts['active_auctions']}")
    print(f"   Total Bids: {db_counts['total_bids']}")

if __name__ == "__main__":
    print("🔍 User Count Discrepancy Investigation")
    print("=" * 60)
    
    # Check database
    db_counts = check_database_counts()
    
    # Check API endpoints
    check_api_endpoints()
    
    # Check analytics service
    check_analytics_service()
    
    # Generate recommendations
    generate_recommendations()
    
    print("\n" + "=" * 60)
    print("🎉 Investigation completed!")
