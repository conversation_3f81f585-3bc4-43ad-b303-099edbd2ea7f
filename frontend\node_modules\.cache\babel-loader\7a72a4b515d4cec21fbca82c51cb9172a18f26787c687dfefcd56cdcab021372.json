{"ast": null, "code": "import value from \"./value.js\";\nimport numberArray, { isNumberArray } from \"./numberArray.js\";\nexport default function (a, b) {\n  return (isNumberArray(b) ? numberArray : genericArray)(a, b);\n}\nexport function genericArray(a, b) {\n  var nb = b ? b.length : 0,\n    na = a ? Math.min(nb, a.length) : 0,\n    x = new Array(na),\n    c = new Array(nb),\n    i;\n  for (i = 0; i < na; ++i) x[i] = value(a[i], b[i]);\n  for (; i < nb; ++i) c[i] = b[i];\n  return function (t) {\n    for (i = 0; i < na; ++i) c[i] = x[i](t);\n    return c;\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}