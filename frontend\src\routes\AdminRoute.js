import { useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import React, { useEffect, useState } from "react";

function AdminRoute({ children }) {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      // If no user is logged in, redirect to login page
      navigate("/login");
    } else if (user.role !== "admin") {
      // If user is not an admin, redirect to home page
      navigate("/");
    } else {
      setLoading(false); // Set loading to false once user is verified as admin
    }
  }, [user, navigate]);

  // While checking the user and role, don't render the children (the admin page) until the check is complete.
  if (loading) {
    return <div>Loading...</div>; // You can replace this with a spinner or loading indicator if preferred.
  }

  return <>{children}</>; // Render admin content if the user is logged in and is an admin
}

export default AdminRoute;
