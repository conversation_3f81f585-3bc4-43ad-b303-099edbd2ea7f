version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: auction_db
      POSTGRES_USER: auction_user
      POSTGRES_PASSWORD: auction_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  backend:
    build: ./backend
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - ./backend:/app
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=1
      - DATABASE_URL=**************************************************/auction_db
      - REDIS_URL=redis://redis:6379/0

  celery:
    build: ./backend
    command: celery -A OnlineAuctionSystem worker --loglevel=info
    volumes:
      - ./backend:/app
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=1
      - DATABASE_URL=**************************************************/auction_db
      - REDIS_URL=redis://redis:6379/0

  celery-beat:
    build: ./backend
    command: celery -A OnlineAuctionSystem beat --loglevel=info
    volumes:
      - ./backend:/app
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=1
      - DATABASE_URL=**************************************************/auction_db
      - REDIS_URL=redis://redis:6379/0

  frontend:
    build: ./frontend
    ports:
      - "3001:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8000

volumes:
  postgres_data:
  redis_data:
