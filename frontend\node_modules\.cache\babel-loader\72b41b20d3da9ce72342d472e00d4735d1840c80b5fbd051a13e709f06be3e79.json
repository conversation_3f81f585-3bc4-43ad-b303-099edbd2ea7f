{"ast": null, "code": "import curveLinear from \"./linear.js\";\nexport var curveRadialLinear = curveRadial(curveLinear);\nfunction Radial(curve) {\n  this._curve = curve;\n}\nRadial.prototype = {\n  areaStart: function () {\n    this._curve.areaStart();\n  },\n  areaEnd: function () {\n    this._curve.areaEnd();\n  },\n  lineStart: function () {\n    this._curve.lineStart();\n  },\n  lineEnd: function () {\n    this._curve.lineEnd();\n  },\n  point: function (a, r) {\n    this._curve.point(r * Math.sin(a), r * -Math.cos(a));\n  }\n};\nexport default function curveRadial(curve) {\n  function radial(context) {\n    return new Radial(curve(context));\n  }\n  radial._curve = curve;\n  return radial;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}