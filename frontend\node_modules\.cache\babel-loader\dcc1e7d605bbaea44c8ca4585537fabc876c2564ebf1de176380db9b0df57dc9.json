{"ast": null, "code": "// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\nexport default function (s) {\n  out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {\n    switch (s[i]) {\n      case \".\":\n        i0 = i1 = i;\n        break;\n      case \"0\":\n        if (i0 === 0) i0 = i;\n        i1 = i;\n        break;\n      default:\n        if (!+s[i]) break out;\n        if (i0 > 0) i0 = 0;\n        break;\n    }\n  }\n  return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}