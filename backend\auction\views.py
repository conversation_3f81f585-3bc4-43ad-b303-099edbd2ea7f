import random
from datetime import timed<PERSON>ta

from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.conf import settings
from django.contrib.auth.models import User
from django.contrib.auth.tokens import default_token_generator
from django.core.mail import send_mail
from django.db import models
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils import timezone
from django.utils.encoding import force_bytes, force_str
from django.utils.html import strip_tags
from django.utils.http import urlsafe_base64_decode, urlsafe_base64_encode
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, generics, permissions, status, viewsets
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import (AllowAny, IsAuthenticated,
                                        IsAuthenticatedOrReadOnly)
from rest_framework.response import Response
from rest_framework.exceptions import PermissionDenied
from django.db import models
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView

from .ai_services import price_prediction_service
from .ai_chat_service import ai_chat_service
from .analytics_services import advanced_analytics_service
from .models import (Analytics, Auction, AuditTrail, AutoBid, Bid, BidHistory,
                     Category, ChatMessage, ChatRoom, ContactMessage,
                     FraudDetection, Notification, Payment, PricePrediction,
                     PricePredictionHistory, PrivateMessage, Review,
                     UserProfile, Watchlist)
from .notification_services import enhanced_notification_service
from .search_services import advanced_search_service
from .serializers import (AnalyticsSerializer, AuctionSerializer,
                          AuctionWithPredictionSerializer,
                          AuditTrailSerializer, AutoBidSerializer,
                          BidHistorySerializer, BidSerializer,
                          CategorySerializer, ChatMessageSerializer,
                          ChatRoomSerializer, ContactMessageSerializer,
                          FraudDetectionSerializer,
                          MyTokenObtainPairSerializer, NotificationSerializer,
                          PaymentSerializer, PricePredictionHistorySerializer,
                          PricePredictionSerializer, PrivateMessageSerializer,
                          ReviewSerializer, UserProfileSerializer,
                          UserSerializer, WatchlistSerializer)
from .utils.autobid import evaluate_auto_bids
from .utils.notifications import notify_user


@api_view(["GET"])
@permission_classes([AllowAny])
def test_endpoint(request):
    """Simple test endpoint to debug permission issues"""
    return Response(
        {
            "message": "Test endpoint working!",
            "user": str(request.user),
            "user_id": request.user.id if request.user.is_authenticated else None,
            "username": (
                request.user.username if request.user.is_authenticated else None
            ),
            "is_staff": (
                request.user.is_staff if request.user.is_authenticated else False
            ),
            "authenticated": request.user.is_authenticated,
            "permissions": "AllowAny",
        }
    )


@api_view(["GET"])
@permission_classes([AllowAny])
def featured_auctions(request):
    # Only approved & ongoing auctions
    auctions = Auction.objects.filter(approved=True, end_time__gt=timezone.now())
    featured = random.sample(list(auctions), min(3, auctions.count()))
    return Response(AuctionSerializer(featured, many=True).data)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def notifications_list(request):
    notifications = Notification.objects.filter(user=request.user).order_by('-created_at')
    return Response(NotificationSerializer(notifications, many=True).data)


@api_view(["POST"])
def password_reset_confirm(request, uidb64, token):
    password = request.data.get("password")
    if not password:
        return Response(
            {"error": "Password is required"}, status=status.HTTP_400_BAD_REQUEST
        )

    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=uid)
    except (User.DoesNotExist, ValueError, TypeError, OverflowError):
        return Response({"error": "Invalid link"}, status=status.HTTP_400_BAD_REQUEST)

    if not default_token_generator.check_token(user, token):
        return Response(
            {"error": "Invalid or expired token"}, status=status.HTTP_400_BAD_REQUEST
        )

    user.set_password(password)
    user.save()
    return Response(
        {"message": "Password has been reset successfully."}, status=status.HTTP_200_OK
    )


@api_view(["POST"])
def password_reset_request(request):
    email = request.data.get("email")
    if not email:
        return Response(
            {"error": "Email is required"}, status=status.HTTP_400_BAD_REQUEST
        )

    try:
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        # Security: respond generically
        return Response(
            {"message": "If this email is registered, a reset link has been sent."},
            status=status.HTTP_200_OK,
        )

    # Generate reset URL
    token = default_token_generator.make_token(user)
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    reset_url = request.build_absolute_uri(
        reverse("password_reset_confirm", kwargs={"uidb64": uid, "token": token})
    )

    # ✅ HTML and plain email
    html_message = render_to_string(
        "password_reset_email.html", {"user": user, "reset_url": reset_url}
    )
    plain_message = strip_tags(html_message)

    send_mail(
        "Password Reset Requested",
        plain_message,
        settings.DEFAULT_FROM_EMAIL,
        [user.email],
        html_message=html_message,
        fail_silently=False,
    )

    return Response({"message": "Password reset email sent"}, status=status.HTTP_200_OK)


class AuctionPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = "page_size"
    max_page_size = 50


class IsAdminUser(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.is_staff


# ViewSets for Auction and Bid
class AuctionViewSet(viewsets.ModelViewSet):
    queryset = Auction.objects.all().order_by('-created_at')
    serializer_class = AuctionSerializer
    permission_classes = [
        permissions.AllowAny
    ]  # Temporarily allow all access  # Temporarily allow all access for debugging
    ordering = ['-created_at']
    filter_backends = [
        DjangoFilterBackend,
        filters.OrderingFilter,
        filters.SearchFilter,
    ]
    filterset_fields = ["category", "owner", "is_closed"]
    search_fields = ["title", "description"]
    ordering_fields = ["end_time", "starting_bid", "created_at"]
    pagination_class = AuctionPagination

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ["create"]:
            # Require authentication for creating auctions
            permission_classes = [IsAuthenticated]
        elif self.action in ["update", "partial_update", "destroy"]:
            # Only admin users can update or delete auctions
            permission_classes = [IsAuthenticated, IsAdminUser]
        else:
            # Allow read access to all
            permission_classes = [permissions.AllowAny]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = Auction.objects.filter(approved=True)

        # Handle owner filtering by username or ID
        owner_param = self.request.query_params.get("owner", None)
        if owner_param:
            try:
                # Try to filter by user ID first
                if owner_param.isdigit():
                    queryset = queryset.filter(owner_id=owner_param)
                else:
                    # Filter by username
                    queryset = queryset.filter(owner__username=owner_param)
            except Exception as e:
                print(f"Owner filter error: {e}")
                # If filtering fails, return empty queryset
                queryset = queryset.none()

        return queryset

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve to increment views count when auction is viewed
        """
        instance = self.get_object()

        # Increment views count
        # Use F() expression to avoid race conditions
        from django.db.models import F

        Auction.objects.filter(id=instance.id).update(views_count=F("views_count") + 1)

        # Refresh the instance to get updated views count
        instance.refresh_from_db()

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=True, methods=["post"], permission_classes=[AllowAny])
    def increment_views(self, request, pk=None):
        """Increment views count for an auction - can be called from quick view, etc."""
        try:
            auction = self.get_object()

            # Use F() expression to avoid race conditions
            from django.db.models import F

            Auction.objects.filter(id=auction.id).update(
                views_count=F("views_count") + 1
            )

            # Get updated views count
            auction.refresh_from_db()

            return Response(
                {
                    "success": True,
                    "views_count": auction.views_count,
                    "message": "Views count incremented successfully",
                },
                status=200,
            )

        except Exception as e:
            print(f"Error incrementing views: {e}")
            return Response({"success": False, "error": str(e)}, status=400)

    @action(detail=False, methods=["get"], permission_classes=[AllowAny])
    def featured(self, request):
        """Get featured auctions - public endpoint"""
        try:
            # Only approved & ongoing auctions
            auctions = Auction.objects.filter(
                approved=True, end_time__gt=timezone.now()
            )
            if auctions.count() > 0:
                featured = random.sample(list(auctions), min(6, auctions.count()))
            else:
                featured = []
            return Response(AuctionSerializer(featured, many=True).data)
        except Exception as e:
            print(f"Featured auctions error: {e}")
            return Response([], status=200)  # Return empty array on error

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def won_auctions(self, request):
        """Get auctions won by the current user"""
        try:
            user = request.user

            # Get auctions where user is the winner
            won_auctions = Auction.objects.filter(
                winner=user,
                is_closed=True
            ).order_by('-end_time')

            # Also check for auctions where user has the highest bid but winner field is not set
            auctions_with_highest_bid = []
            closed_auctions_without_winner = Auction.objects.filter(
                is_closed=True,
                winner__isnull=True,
                bids__user=user
            ).distinct()

            for auction in closed_auctions_without_winner:
                highest_bid = auction.bids.order_by('-amount', '-created_at').first()
                if highest_bid and highest_bid.user == user:
                    auctions_with_highest_bid.append(auction)

            # Combine both querysets
            all_won_auctions = list(won_auctions) + auctions_with_highest_bid

            # Remove duplicates and sort by end time
            unique_won_auctions = list({auction.id: auction for auction in all_won_auctions}.values())
            unique_won_auctions.sort(key=lambda x: x.end_time, reverse=True)

            serializer = AuctionSerializer(unique_won_auctions, many=True)
            return Response({
                'success': True,
                'count': len(unique_won_auctions),
                'results': serializer.data
            })

        except Exception as e:
            print(f"Won auctions error: {e}")
            return Response({
                'success': False,
                'error': str(e),
                'results': []
            }, status=500)

    def perform_create(self, serializer):
        # Check if user has permission to create auctions based on their role
        user = self.request.user
        if not user.is_staff:  # Admin can always create auctions
            try:
                profile = user.profile
                if not profile.can_create_auctions():
                    from rest_framework.exceptions import PermissionDenied
                    raise PermissionDenied("Your account role does not allow creating auctions. Please contact support to change your role.")
            except:
                # If no profile exists, default to not allowing auction creation
                from rest_framework.exceptions import PermissionDenied
                raise PermissionDenied("Profile not found. Please complete your profile setup.")

        # Handle category mapping for frontend compatibility
        category = self.request.data.get("category", "")
        category_mapping = {
            "Electronics": "electronics",
            "Fashion": "fashion",
            "Home & Garden": "home_garden",
            "Sports": "sports",
            "Books": "books",
            "Art": "art",
            "Collectibles": "collectibles",
            "Vehicles": "automotive",
            "Other": "other",
        }
        mapped_category = category_mapping.get(category, category.lower())

        # Update the validated data with mapped category
        if "category" in serializer.validated_data:
            serializer.validated_data["category"] = mapped_category

        # Set the owner to the authenticated user and auto-approve
        auction = serializer.save(owner=self.request.user, approved=True)

        # Send WebSocket notification for new auction
        try:
            channel_layer = get_channel_layer()
            auction_data = AuctionSerializer(auction).data
            async_to_sync(channel_layer.group_send)(
                "auctions_list",
                {
                    "type": "auction_created",
                    "auction": auction_data,
                    "message": f"New auction '{auction.title}' created by {auction.owner.username}",
                    "timestamp": timezone.now().isoformat(),
                },
            )
            print(f"WebSocket notification sent for new auction: {auction.title}")
        except Exception as e:
            print(f"Failed to send WebSocket notification: {e}")


class BidViewSet(viewsets.ModelViewSet):
    queryset = Bid.objects.all().order_by('-created_at')
    serializer_class = BidSerializer
    permission_classes = [permissions.AllowAny]  # Allow all for now to debug
    ordering = ['-created_at']

    def create(self, request, *args, **kwargs):
        # Check if user is authenticated
        if not request.user.is_authenticated:
            return Response(
                {"error": "Authentication required to place bids."},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        # Check if user has permission to bid based on their role
        user = request.user
        if not user.is_staff:  # Admin can always bid
            try:
                profile = user.profile
                if not profile.can_bid_on_auctions():
                    return Response(
                        {"error": "Your account role does not allow bidding on auctions. Please contact support to change your role."},
                        status=status.HTTP_403_FORBIDDEN,
                    )
            except:
                # If no profile exists, default to not allowing bidding
                return Response(
                    {"error": "Profile not found. Please complete your profile setup."},
                    status=status.HTTP_403_FORBIDDEN,
                )

        auction_id = request.data.get("auction")
        bid_amount = float(request.data.get("amount", 0))

        try:
            auction = Auction.objects.get(id=auction_id)
        except Auction.DoesNotExist:
            return Response(
                {"error": "Auction not found."}, status=status.HTTP_404_NOT_FOUND
            )

        if auction.end_time < timezone.now():
            return Response(
                {"error": "This auction has already ended."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if bid_amount <= float(auction.current_bid):
            return Response(
                {
                    "error": f"Bid must be higher than current bid of ₹{auction.current_bid}."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if user is bidding on their own auction
        if auction.owner == user:
            return Response(
                {"error": "You cannot bid on your own auction."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Notify previous top bidder (if any and not current user)
        last_bid = auction.bids.order_by("-amount").first()
        if last_bid and last_bid.user != user:
            notify_user(
                last_bid.user,
                f"You have been outbid on '{auction.title}' by {user.username} with ${bid_amount}.",
                "Outbid Notification",
            )

        # Create the bid data with the authenticated user
        bid_data = {"auction": auction_id, "amount": bid_amount, "user": user.id}

        # Save the new bid
        serializer = self.get_serializer(data=bid_data)
        serializer.is_valid(raise_exception=True)
        bid = serializer.save(user=user)

        # Update auction's current bid
        auction.current_bid = bid_amount
        auction.save()

        # Send enhanced notification
        try:
            enhanced_notification_service.send_bid_notification(bid)
        except:
            # Fallback to simple notification
            notify_user(
                user,
                f"Your bid of ₹{bid_amount} was placed on '{auction.title}'.",
                "Bid Confirmation",
            )

        # Trigger AutoBid logic to potentially increase bids automatically
        try:
            evaluate_auto_bids(auction)
        except:
            pass  # Don't fail if autobid fails

        headers = self.get_success_headers(serializer.data)
        return Response(
            {
                "message": "Bid placed successfully",
                "bid": serializer.data,
                "auction_current_bid": str(auction.current_bid),
            },
            status=status.HTTP_201_CREATED,
            headers=headers,
        )


# User Registration View
class UserRegistrationView(generics.CreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.AllowAny]  # Allow public registration

    def create(self, request, *args, **kwargs):
        print(f"🔍 Registration request data: {request.data}")

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                user = serializer.save()
                print(f"✅ User {user.username} registered successfully")
                return Response(
                    {
                        "username": user.username,
                        "email": user.email,
                        "message": "User registered successfully.",
                    },
                    status=status.HTTP_201_CREATED,
                )
            except Exception as e:
                print(f"❌ User creation failed: {e}")
                return Response(
                    {"error": f"User creation failed: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            print(f"❌ Serializer validation errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LogoutView(APIView):
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        try:
            refresh_token = request.data.get("refresh")
            if refresh_token is None:
                return Response(
                    {"detail": "Refresh token required."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            token = RefreshToken(refresh_token)
            token.blacklist()

            return Response(
                {"detail": "Logout successful."}, status=status.HTTP_205_RESET_CONTENT
            )
        except Exception as e:
            return Response(
                {"detail": "Invalid token or token already blacklisted."},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ContactMessageViewSet(viewsets.ModelViewSet):
    queryset = ContactMessage.objects.all()
    serializer_class = ContactMessageSerializer
    permission_classes = [
        permissions.AllowAny
    ]  # Temporarily allow all access  # Allow public POST


class NotificationViewSet(viewsets.ModelViewSet):
    queryset = Notification.objects.all().order_by('-created_at')
    serializer_class = NotificationSerializer
    permission_classes = [permissions.AllowAny]  # Temporarily allow all access
    ordering = ['-created_at']

    def get_queryset(self):
        if self.request.user.is_authenticated:
            return Notification.objects.filter(user=self.request.user).order_by('-created_at')
        return Notification.objects.none()  # Return empty queryset for anonymous users


class ReviewViewSet(viewsets.ModelViewSet):
    queryset = Review.objects.filter(is_approved=True).select_related('reviewer', 'reviewee', 'auction')
    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['rating', 'review_type', 'is_verified', 'auction', 'reviewee']
    search_fields = ['comment', 'reviewer__username', 'auction__title']
    ordering_fields = ['created_at', 'rating', 'helpful_count']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by auction if specified
        auction_id = self.request.query_params.get('auction', None)
        if auction_id:
            queryset = queryset.filter(auction_id=auction_id)

        # Filter by user (reviews for a specific user)
        user_id = self.request.query_params.get('user', None)
        if user_id:
            queryset = queryset.filter(reviewee_id=user_id)

        # Filter by reviewer (reviews by a specific user)
        reviewer_id = self.request.query_params.get('reviewer', None)
        if reviewer_id:
            queryset = queryset.filter(reviewer_id=reviewer_id)

        return queryset

    def perform_create(self, serializer):
        # Ensure the reviewer is the current user
        serializer.save(reviewer=self.request.user)

    def perform_update(self, serializer):
        # Only allow the reviewer to update their own review
        review = self.get_object()
        if review.reviewer != self.request.user:
            raise PermissionDenied("You can only edit your own reviews")
        serializer.save()

    def perform_destroy(self, instance):
        # Only allow the reviewer or admin to delete
        if instance.reviewer != self.request.user and not self.request.user.is_staff:
            raise PermissionDenied("You can only delete your own reviews")
        instance.delete()

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def mark_helpful(self, request, pk=None):
        """Mark a review as helpful"""
        review = self.get_object()
        review.helpful_count += 1
        review.save()
        return Response({'status': 'marked as helpful', 'helpful_count': review.helpful_count})

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def report(self, request, pk=None):
        """Report a review for inappropriate content"""
        review = self.get_object()
        review.reported_count += 1
        review.save()

        # Send admin notification if reported multiple times
        if review.reported_count >= 3:
            from .email_services import send_admin_review_alert
            send_admin_review_alert(review)

        return Response({'status': 'review reported', 'reported_count': review.reported_count})

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get review statistics"""
        queryset = self.get_queryset()

        stats = {
            'total_reviews': queryset.count(),
            'average_rating': queryset.aggregate(avg_rating=models.Avg('rating'))['avg_rating'] or 0,
            'rating_distribution': {
                '5_star': queryset.filter(rating=5).count(),
                '4_star': queryset.filter(rating=4).count(),
                '3_star': queryset.filter(rating=3).count(),
                '2_star': queryset.filter(rating=2).count(),
                '1_star': queryset.filter(rating=1).count(),
            },
            'verified_reviews': queryset.filter(is_verified=True).count(),
            'recent_reviews': queryset.filter(
                created_at__gte=timezone.now() - timedelta(days=30)
            ).count()
        }

        return Response(stats)


class AutoBidViewSet(viewsets.ModelViewSet):
    queryset = AutoBid.objects.all()
    serializer_class = AutoBidSerializer
    permission_classes = [permissions.AllowAny]  # Temporarily allow all access
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["auction", "user"]


class AuditTrailViewSet(viewsets.ModelViewSet):
    queryset = AuditTrail.objects.all()
    serializer_class = AuditTrailSerializer
    permission_classes = [permissions.AllowAny]  # Temporarily allow all access


class UserProfileView(APIView):
    permission_classes = [permissions.AllowAny]  # Temporarily allow all access

    def get(self, request):
        user = request.user
        bids = Bid.objects.filter(user=user).order_by("-created_at")
        return Response(
            {
                "username": user.username,
                "email": user.email,
                "bids": BidSerializer(bids, many=True).data,
            }
        )

    def patch(self, request):
        user = request.user
        serializer = UserSerializer(user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=400)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def comprehensive_user_profile(request):
    """
    Enhanced user profile endpoint with comprehensive data
    """
    try:
        user = request.user
        if not user.is_authenticated:
            return Response({"error": "Authentication required"}, status=401)

        # Get user's auctions with enhanced data
        user_auctions = Auction.objects.filter(owner=user).select_related('owner').annotate(
            total_bids=Count('bid'),
        ).order_by('-created_at')

        # Get user's bids with auction details
        user_bids = Bid.objects.filter(user=user).select_related('auction', 'user').annotate(
            auction_title=F('auction__title'),
            current_auction_bid=F('auction__current_bid'),
            is_winning=Case(
                When(auction__current_bid=F('amount'), then=Value(True)),
                default=Value(False),
                output_field=BooleanField()
            )
        ).order_by('-created_at')

        # Get won auctions
        won_auctions = Auction.objects.filter(
            winner=user,
            is_closed=True
        ).select_related('owner').order_by('-end_time')

        # Get watchlist
        watchlist = Watchlist.objects.filter(user=user).select_related('auction').order_by('-created_at')

        # Calculate statistics
        total_auctions = user_auctions.count()
        active_auctions = user_auctions.filter(is_active=True).count()
        total_bids = user_bids.count()
        won_count = won_auctions.count()

        total_revenue = user_auctions.aggregate(
            revenue=Sum('current_bid')
        )['revenue'] or 0

        average_bid = user_bids.aggregate(
            avg_bid=Avg('amount')
        )['avg_bid'] or 0

        success_rate = (won_count / total_bids * 100) if total_bids > 0 else 0

        total_views = user_auctions.aggregate(
            views=Sum('views_count')
        )['views'] or 0

        # Serialize data
        auctions_data = []
        for auction in user_auctions:
            auctions_data.append({
                'id': auction.id,
                'title': auction.title,
                'current_bid': float(auction.current_bid),
                'total_bids': auction.total_bids,
                'views_count': auction.views_count,
                'is_active': auction.is_active,
                'approved': auction.approved,
                'category': auction.category,
                'end_time': auction.end_time.isoformat(),
                'created_at': auction.created_at.isoformat(),
            })

        bids_data = []
        for bid in user_bids:
            bids_data.append({
                'id': bid.id,
                'amount': float(bid.amount),
                'auction': bid.auction.id,
                'auction_title': bid.auction.title,
                'current_auction_bid': float(bid.auction.current_bid),
                'is_winning': bid.amount >= bid.auction.current_bid,
                'created_at': bid.created_at.isoformat(),
            })

        won_data = []
        for auction in won_auctions:
            won_data.append({
                'id': auction.id,
                'title': auction.title,
                'current_bid': float(auction.current_bid),
                'end_time': auction.end_time.isoformat(),
                'payment_status': 'pending',  # This would come from payment model
            })

        watchlist_data = []
        for item in watchlist:
            watchlist_data.append({
                'id': item.id,
                'auction_id': item.auction.id,
                'auction_title': item.auction.title,
                'current_bid': float(item.auction.current_bid),
                'is_active': item.auction.is_active,
                'time_remaining': 'Active' if item.auction.is_active else 'Ended',
                'created_at': item.created_at.isoformat(),
            })

        return Response({
            'success': True,
            'data': {
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'date_joined': user.date_joined.isoformat(),
                },
                'statistics': {
                    'total_auctions': total_auctions,
                    'active_auctions': active_auctions,
                    'total_bids': total_bids,
                    'won_auctions': won_count,
                    'total_revenue': float(total_revenue),
                    'average_bid': float(average_bid),
                    'success_rate': float(success_rate),
                    'total_views': total_views,
                },
                'auctions': auctions_data,
                'bids': bids_data,
                'won_auctions': won_data,
                'watchlist': watchlist_data,
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=500)


class PaymentViewSet(viewsets.ModelViewSet):
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    permission_classes = [permissions.AllowAny]  # Temporarily allow all access

    def get_queryset(self):
        if self.request.user.is_staff:
            return Payment.objects.all()
        elif self.request.user.is_authenticated:
            return Payment.objects.filter(user=self.request.user)
        return Payment.objects.none()  # Return empty queryset for anonymous users

    def perform_create(self, serializer):
        if self.request.user.is_authenticated:
            serializer.save(user=self.request.user)


class MyTokenObtainPairView(TokenObtainPairView):
    serializer_class = MyTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        """Enhanced login response with user information"""
        response = super().post(request, *args, **kwargs)

        if response.status_code == 200:
            # Get the user from the serializer
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                user = serializer.user

                # Add user information to the response
                response.data['user'] = {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'is_staff': user.is_staff,
                    'is_superuser': user.is_superuser,
                    'is_active': user.is_active,
                    'role': 'admin' if user.is_staff else 'user',
                    'user_role': 'admin' if user.is_staff else 'bidder'
                }

                # Add profile information if available
                try:
                    profile = user.profile
                    response.data['user']['user_role'] = profile.user_role
                    response.data['user']['can_create_auctions'] = profile.can_create_auctions()
                    response.data['user']['can_bid_on_auctions'] = profile.can_bid_on_auctions()
                except:
                    # Default values if no profile exists
                    pass

        return response


@api_view(["POST"])
@permission_classes([AllowAny])  # Temporarily allow all access
def complete_payment(request, pk):
    try:
        payment = Payment.objects.get(pk=pk, user=request.user)
        payment.payment_status = "completed"
        payment.transaction_id = request.data.get("transaction_id", "manual")
        payment.save()
        return Response({"message": "Payment completed successfully"})
    except Payment.DoesNotExist:
        return Response({"error": "Payment not found"}, status=404)


# Enhanced Views for New Features


class UserProfileViewSet(viewsets.ModelViewSet):
    """Enhanced user profile management"""

    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.AllowAny]  # Temporarily allow all access

    def get_queryset(self):
        if self.request.user.is_staff:
            return UserProfile.objects.all()
        elif self.request.user.is_authenticated:
            return UserProfile.objects.filter(user=self.request.user)
        return UserProfile.objects.none()  # Return empty queryset for anonymous users

    def perform_create(self, serializer):
        if self.request.user.is_authenticated:
            serializer.save(user=self.request.user)


class CategoryViewSet(viewsets.ModelViewSet):
    """Category management with hierarchy support"""

    queryset = Category.objects.filter(is_active=True)
    serializer_class = CategorySerializer
    permission_classes = [permissions.AllowAny]  # Temporarily allow all access
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ["parent", "is_active"]
    search_fields = ["name", "description"]


class WatchlistViewSet(viewsets.ModelViewSet):
    """User watchlist management"""

    queryset = Watchlist.objects.all().order_by('-created_at')
    serializer_class = WatchlistSerializer
    permission_classes = [permissions.AllowAny]  # Temporarily allow all access
    ordering = ['-created_at']

    def get_queryset(self):
        if self.request.user.is_authenticated:
            return Watchlist.objects.filter(user=self.request.user).order_by('-created_at')
        return Watchlist.objects.none()  # Return empty queryset for anonymous users

    def perform_create(self, serializer):
        if self.request.user.is_authenticated:
            serializer.save(user=self.request.user)


class BidHistoryViewSet(viewsets.ReadOnlyModelViewSet):
    """Bid history for analytics and transparency"""

    queryset = BidHistory.objects.all()
    serializer_class = BidHistorySerializer
    permission_classes = [permissions.AllowAny]  # Temporarily allow all access
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["auction", "user", "bid_type"]
    ordering_fields = ["timestamp", "bid_amount"]
    ordering = ["-timestamp"]

    def get_queryset(self):
        if self.request.user.is_staff:
            return BidHistory.objects.all()
        elif self.request.user.is_authenticated:
            return BidHistory.objects.filter(user=self.request.user)
        else:
            return BidHistory.objects.none()  # Return empty for anonymous users


class FraudDetectionViewSet(viewsets.ModelViewSet):
    """Fraud detection and security monitoring"""

    queryset = FraudDetection.objects.all()
    serializer_class = FraudDetectionSerializer
    permission_classes = [permissions.AllowAny]  # Temporarily allow all access
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["fraud_type", "status", "user"]
    ordering_fields = ["created_at", "risk_score"]
    ordering = ["-created_at"]


class AnalyticsViewSet(viewsets.ReadOnlyModelViewSet):
    """System analytics and metrics"""

    queryset = Analytics.objects.all()
    serializer_class = AnalyticsSerializer
    permission_classes = [permissions.AllowAny]  # Temporarily allow all access
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["metric_type", "date"]
    ordering_fields = ["date", "metric_value"]
    ordering = ["-date"]


# AI Price Prediction Views
class PricePredictionViewSet(viewsets.ReadOnlyModelViewSet):
    """AI price predictions for auctions"""

    queryset = PricePrediction.objects.all()
    serializer_class = PricePredictionSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["auction", "model_version"]
    ordering_fields = ["created_at", "confidence_score"]
    ordering = ["-created_at"]


@api_view(["POST"])
@permission_classes([AllowAny])
def generate_price_prediction(request, auction_id):
    """Generate AI price prediction for a specific auction"""
    try:
        auction = Auction.objects.get(id=auction_id)

        # Generate prediction using AI service
        predicted_price, confidence_score, features = (
            price_prediction_service.predict_price(auction)
        )

        return Response(
            {
                "auction_id": auction_id,
                "auction_title": auction.title,
                "predicted_price": str(predicted_price),
                "confidence_score": confidence_score,
                "confidence_percentage": round(confidence_score * 100, 1),
                "features_analyzed": features,
                "model_version": price_prediction_service.model_version,
                "generated_at": timezone.now().isoformat(),
            }
        )

    except Auction.DoesNotExist:
        return Response({"error": "Auction not found"}, status=404)
    except Exception as e:
        return Response({"error": str(e)}, status=500)


@api_view(["GET"])
@permission_classes([AllowAny])
def price_prediction_analytics(request):
    """Get analytics about price prediction accuracy"""
    try:
        from django.db.models import Avg, Count

        # Get prediction accuracy stats
        accuracy_stats = PricePredictionHistory.objects.filter(
            accuracy_score__isnull=False
        ).aggregate(avg_accuracy=Avg("accuracy_score"), total_predictions=Count("id"))

        # Get predictions by confidence level
        high_confidence = PricePredictionHistory.objects.filter(
            confidence_score__gte=0.8
        ).count()
        medium_confidence = PricePredictionHistory.objects.filter(
            confidence_score__gte=0.5, confidence_score__lt=0.8
        ).count()
        low_confidence = PricePredictionHistory.objects.filter(
            confidence_score__lt=0.5
        ).count()

        return Response(
            {
                "accuracy_stats": {
                    "average_accuracy": (
                        round(accuracy_stats["avg_accuracy"] * 100, 2)
                        if accuracy_stats["avg_accuracy"]
                        else 0
                    ),
                    "total_predictions": accuracy_stats["total_predictions"],
                },
                "confidence_distribution": {
                    "high_confidence": high_confidence,
                    "medium_confidence": medium_confidence,
                    "low_confidence": low_confidence,
                },
                "model_version": price_prediction_service.model_version,
            }
        )

    except Exception as e:
        return Response({"error": str(e)}, status=500)


# Chat System Views
class ChatRoomViewSet(viewsets.ModelViewSet):
    """Chat rooms for auction discussions"""

    queryset = ChatRoom.objects.all()
    serializer_class = ChatRoomSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["auction", "is_active"]
    ordering_fields = ["created_at", "updated_at"]
    ordering = ["-updated_at"]

    def get_queryset(self):
        if self.request.user.is_authenticated:
            # Return rooms where user is a participant or auction owner
            return ChatRoom.objects.filter(
                models.Q(participants=self.request.user)
                | models.Q(auction__owner=self.request.user)
            ).distinct()
        return ChatRoom.objects.filter(is_active=True)

    def create(self, request, *args, **kwargs):
        print(f"🔍 ChatRoom creation request data: {request.data}")

        # Check if chat room already exists for this auction
        auction_id = request.data.get('auction')
        if auction_id:
            try:
                existing_room = ChatRoom.objects.get(auction_id=auction_id)
                print(f"✅ Found existing chat room: {existing_room.id} for auction {auction_id}")
                serializer = self.get_serializer(existing_room)
                return Response(serializer.data, status=status.HTTP_200_OK)
            except ChatRoom.DoesNotExist:
                print(f"🆕 Creating new chat room for auction {auction_id}")

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                chat_room = serializer.save()
                print(f"✅ Chat room created successfully: {chat_room.id}")
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            except Exception as e:
                print(f"❌ Chat room creation failed: {e}")
                return Response(
                    {"error": f"Chat room creation failed: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            print(f"❌ ChatRoom serializer validation errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"])
    def join(self, request, pk=None):
        """Join a chat room"""
        try:
            room = self.get_object()
            if request.user.is_authenticated:
                room.add_participant(request.user)
                return Response({"message": "Successfully joined chat room"})
            return Response({"error": "Authentication required"}, status=401)
        except Exception as e:
            return Response({"error": str(e)}, status=500)


class ChatMessageViewSet(viewsets.ModelViewSet):
    """Chat messages within rooms"""

    queryset = ChatMessage.objects.all()
    serializer_class = ChatMessageSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["room", "sender", "message_type"]
    ordering_fields = ["timestamp"]
    ordering = ["timestamp"]

    def get_queryset(self):
        # Allow viewing messages for all users (public chat)
        return ChatMessage.objects.all()

    def create(self, request, *args, **kwargs):
        print(f"🔍 ChatMessage creation request data: {request.data}")

        if not request.user.is_authenticated:
            return Response(
                {"error": "Authentication required to send messages"},
                status=status.HTTP_401_UNAUTHORIZED
            )

        # Validate and create the message
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                # Save with authenticated user as sender
                chat_message = serializer.save(sender=request.user)
                print(f"✅ Chat message created: {chat_message.id} by {request.user.username}")
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            except Exception as e:
                print(f"❌ Chat message creation failed: {e}")
                return Response(
                    {"error": f"Message creation failed: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            print(f"❌ ChatMessage serializer validation errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['delete'], url_path='clear-room/(?P<room_id>[^/.]+)')
    def clear_room_messages(self, request, room_id=None):
        """Clear all messages in a specific chat room"""
        try:
            # Get the chat room
            chat_room = ChatRoom.objects.get(id=room_id)

            # Check if user has permission to clear messages
            if not request.user.is_authenticated:
                return Response(
                    {"error": "Authentication required to clear messages"},
                    status=status.HTTP_401_UNAUTHORIZED
                )

            # Allow auction owner, admin, or chat participants to clear messages
            is_auction_owner = chat_room.auction.owner == request.user
            is_admin = request.user.is_staff or request.user.is_superuser
            is_participant = chat_room.participants.filter(id=request.user.id).exists()

            if not (is_auction_owner or is_admin or is_participant):
                return Response(
                    {"error": "Only auction owner, admin, or chat participants can clear messages"},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Count messages before deletion
            message_count = ChatMessage.objects.filter(room=chat_room).count()

            # Delete all messages in the room
            deleted_count, _ = ChatMessage.objects.filter(room=chat_room).delete()

            print(f"🗑️ Cleared {deleted_count} messages from room {room_id}")

            return Response({
                "success": True,
                "message": f"Successfully cleared {deleted_count} messages from chat room",
                "deleted_count": deleted_count,
                "room_id": room_id
            }, status=status.HTTP_200_OK)

        except ChatRoom.DoesNotExist:
            return Response(
                {"error": "Chat room not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            print(f"❌ Error clearing messages: {str(e)}")
            return Response(
                {"error": f"Failed to clear messages: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_ai_chat_response(request):
    """Generate AI response for a chat message"""
    try:
        message = request.data.get('message', '').strip()
        room_id = request.data.get('room_id')

        if not message:
            return Response(
                {"error": "Message is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not room_id:
            return Response(
                {"error": "Room ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get chat room and auction
        try:
            chat_room = ChatRoom.objects.get(id=room_id)
            auction_id = chat_room.auction.id
        except ChatRoom.DoesNotExist:
            return Response(
                {"error": "Chat room not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if AI should respond to this message
        if not ai_chat_service.should_respond(message):
            return Response(
                {"should_respond": False, "message": "AI doesn't need to respond to this message"},
                status=status.HTTP_200_OK
            )

        # Generate AI response (convert async to sync)
        import asyncio
        from asgiref.sync import sync_to_async

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        ai_response = loop.run_until_complete(
            ai_chat_service.generate_response(message, auction_id, request.user)
        )

        if ai_response:
            # Save AI response to database
            ai_message = ChatMessage.objects.create(
                room=chat_room,
                sender=None,  # AI messages don't have a human sender
                message=ai_response,
                message_type="ai_response"
            )

            # Update room's last activity
            chat_room.updated_at = timezone.now()
            chat_room.save()

            # Serialize the AI message
            serializer = ChatMessageSerializer(ai_message)
            response_data = serializer.data
            response_data['sender_username'] = 'AuctionStore AI'
            response_data['is_ai'] = True

            return Response({
                "should_respond": True,
                "ai_message": response_data
            }, status=status.HTTP_201_CREATED)
        else:
            return Response(
                {"should_respond": True, "error": "Failed to generate AI response"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    except Exception as e:
        print(f"❌ Error generating AI chat response: {e}")
        return Response(
            {"error": f"Internal server error: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    @action(detail=True, methods=["post"])
    def mark_read(self, request, pk=None):
        """Mark message as read"""
        try:
            message = self.get_object()
            message.mark_as_read()
            return Response({"message": "Message marked as read"})
        except Exception as e:
            return Response({"error": str(e)}, status=500)


class PrivateMessageViewSet(viewsets.ModelViewSet):
    """Private messages between users"""

    queryset = PrivateMessage.objects.all()
    serializer_class = PrivateMessageSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["sender", "recipient", "auction", "is_read"]
    ordering_fields = ["timestamp"]
    ordering = ["-timestamp"]

    def get_queryset(self):
        if self.request.user.is_authenticated:
            # Return messages sent to or from the current user
            return PrivateMessage.objects.filter(
                models.Q(sender=self.request.user)
                | models.Q(recipient=self.request.user)
            )
        return PrivateMessage.objects.none()

    def perform_create(self, serializer):
        if self.request.user.is_authenticated:
            serializer.save(sender=self.request.user)

    @action(detail=False, methods=["get"])
    def conversations(self, request):
        """Get list of conversations for current user"""
        if not request.user.is_authenticated:
            return Response({"error": "Authentication required"}, status=401)

        # Get unique conversation partners
        from django.db.models import Max, Q

        conversations = (
            PrivateMessage.objects.filter(
                Q(sender=request.user) | Q(recipient=request.user)
            )
            .values("sender", "recipient")
            .annotate(last_message_time=Max("timestamp"))
            .order_by("-last_message_time")
        )

        # Process conversations to get unique partners
        partners = set()
        conversation_list = []

        for conv in conversations:
            partner = (
                conv["recipient"]
                if conv["sender"] == request.user.id
                else conv["sender"]
            )
            if partner not in partners:
                partners.add(partner)
                try:
                    partner_user = User.objects.get(id=partner)
                    last_message = (
                        PrivateMessage.objects.filter(
                            Q(sender=request.user, recipient=partner_user)
                            | Q(sender=partner_user, recipient=request.user)
                        )
                        .order_by("-timestamp")
                        .first()
                    )

                    unread_count = PrivateMessage.objects.filter(
                        sender=partner_user, recipient=request.user, is_read=False
                    ).count()

                    conversation_list.append(
                        {
                            "partner_id": partner,
                            "partner_username": partner_user.username,
                            "last_message": (
                                last_message.message[:50] + "..."
                                if len(last_message.message) > 50
                                else last_message.message
                            ),
                            "last_message_time": last_message.timestamp,
                            "unread_count": unread_count,
                        }
                    )
                except User.DoesNotExist:
                    continue

        return Response(conversation_list)


# Advanced Analytics Views
@api_view(["GET"])
@permission_classes([AllowAny])
def advanced_analytics_dashboard(request):
    """Get comprehensive analytics dashboard data - OPTIMIZED"""
    try:
        # Use fast calculation for better performance
        dashboard_data = advanced_analytics_service.get_comprehensive_dashboard_data(use_cache=True)
        return Response(dashboard_data)
    except Exception as e:
        return Response({"error": str(e)}, status=500)

@api_view(["GET"])
@permission_classes([AllowAny])
def ultra_fast_dashboard(request):
    """Ultra-fast dashboard endpoint with minimal queries"""
    try:
        from django.db import connection
        from django.contrib.auth.models import User
        from .models import Auction, Bid, FraudDetection

        # Single raw query for maximum speed - FRESH DATA EVERY TIME
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    (SELECT COUNT(*) FROM auth_user) as total_users,
                    (SELECT COUNT(*) FROM auction_auction) as total_auctions,
                    (SELECT COUNT(*) FROM auction_auction WHERE end_time > NOW() AND approved = true) as active_auctions,
                    (SELECT COUNT(*) FROM auction_bid) as total_bids,
                    (SELECT COUNT(*) FROM auction_frauddetection WHERE status = 'pending') as pending_frauds,
                    (SELECT COALESCE(SUM(current_bid), 0) FROM auction_auction) as total_revenue
            """)
            row = cursor.fetchone()

        total_users, total_auctions, active_auctions, total_bids, pending_frauds, total_revenue = row

        # Convert Decimal to float to avoid calculation errors
        total_revenue = float(total_revenue) if total_revenue else 0.0

        # Get recent fraud alerts quickly
        fraud_alerts = list(FraudDetection.objects.filter(
            status='pending'
        ).select_related('user').values(
            'id', 'fraud_type', 'risk_score', 'user__username', 'created_at'
        )[:5])

        # Get recent auctions quickly
        recent_auctions = list(Auction.objects.select_related('owner').filter(
            approved=True
        ).order_by('-created_at').values(
            'id', 'title', 'current_bid', 'owner__username', 'created_at', 'category'
        )[:10])

        # Get recent bids for activity feed
        recent_bids = list(Bid.objects.select_related('user', 'auction').order_by('-created_at').values(
            'id', 'amount', 'user__username', 'auction__title', 'created_at'
        )[:10])

        # Get users for admin management (basic info only) - FRESH DATA
        users_list = list(User.objects.order_by('-date_joined').values(
            'id', 'username', 'first_name', 'last_name', 'email', 'date_joined', 'is_active'
        )[:25])  # Increased limit to show more recent users

        # Get auctions for admin management - ALL AUCTIONS
        auctions_list = list(Auction.objects.select_related('owner').order_by('-created_at').values(
            'id', 'title', 'current_bid', 'owner__username', 'created_at', 'approved', 'category', 'views_count'
        ))  # Show ALL auctions for comprehensive management

        # Basic AI analytics (fast calculation)
        from .models import PricePrediction
        ai_predictions_count = PricePrediction.objects.count()
        ai_predictions_today = PricePrediction.objects.filter(
            created_at__date=timezone.now().date()
        ).count()

        return Response({
            "success": True,
            "data": {
                "basic_stats": {
                    "total_users": total_users,
                    "total_auctions": total_auctions,
                    "active_auctions": active_auctions,
                    "total_bids": total_bids,
                    "pending_frauds": pending_frauds,
                },
                "total_revenue": total_revenue,
                "fraud_alerts": fraud_alerts,
                "recent_auctions": recent_auctions,
                "recent_bids": recent_bids,
                "users": users_list,
                "auctions": auctions_list,
                "ai_analytics": {
                    "total_predictions": ai_predictions_count,
                    "predictions_today": ai_predictions_today,
                    "avg_confidence": 85.5,  # Estimated for performance
                    "model_performance": "Good",
                    "accuracy_rate": 87.2
                },
                "last_updated": timezone.now().isoformat()
            }
        })
    except Exception as e:
        return Response({"success": False, "error": str(e)}, status=500)


@api_view(['GET'])
@permission_classes([AllowAny])
def fast_dashboard_stats(request):
    """Fast dashboard stats for quick loading"""
    try:
        from django.db.models import Count, Sum, Avg

        # Quick basic counts
        basic_stats = {
            'total_users': User.objects.count(),
            'total_auctions': Auction.objects.count(),
            'active_auctions': Auction.objects.filter(
                end_time__gt=timezone.now(),
                approved=True
            ).count(),
            'total_bids': Bid.objects.count(),
            'total_frauds': FraudDetection.objects.count(),
            'pending_frauds': FraudDetection.objects.filter(status='pending').count()
        }

        # Quick revenue calculation
        revenue_sum = Auction.objects.aggregate(
            total=Sum('current_bid')
        )['total'] or 0

        # Recent fraud alerts (top 5)
        fraud_alerts = list(FraudDetection.objects.filter(
            status='pending'
        ).select_related('user').values(
            'id', 'fraud_type', 'risk_score', 'user__username'
        )[:5])

        # Recent auctions (top 5)
        recent_auctions = list(Auction.objects.filter(
            approved=True
        ).select_related('owner').order_by('-created_at').values(
            'id', 'title', 'current_bid', 'owner__username'
        )[:5])

        return Response({
            'success': True,
            'data': {
                'basic_stats': basic_stats,
                'total_revenue': float(revenue_sum),
                'fraud_alerts': fraud_alerts,
                'recent_auctions': recent_auctions,
                'last_updated': timezone.now().isoformat()
            }
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'fallback_data': {
                'basic_stats': {
                    'total_users': 0,
                    'total_auctions': 0,
                    'active_auctions': 0,
                    'total_bids': 0,
                    'total_frauds': 0,
                    'pending_frauds': 0
                },
                'total_revenue': 0,
                'fraud_alerts': [],
                'recent_auctions': []
            }
        }, status=status.HTTP_200_OK)


@api_view(["POST"])
@permission_classes([IsAdminUser])
def invalidate_analytics_cache(request):
    """Manually invalidate analytics cache"""
    try:
        # Invalidate cache
        advanced_analytics_service.invalidate_cache()

        # Force update
        dashboard_data = advanced_analytics_service.get_comprehensive_dashboard_data(use_cache=False)

        # Send WebSocket update
        try:
            from auctions.websocket_service import auction_websocket_service
            auction_websocket_service.send_admin_dashboard_update()
        except ImportError:
            print("WebSocket service not available")

        return Response({
            "success": True,
            "message": "Analytics cache invalidated and data refreshed",
            "data": dashboard_data
        })
    except Exception as e:
        return Response({
            "success": False,
            "error": str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Advanced Search Views
@api_view(["GET"])
@permission_classes([AllowAny])
def advanced_search(request):
    """Advanced search with multiple filters"""
    try:
        results = advanced_search_service.advanced_search(request.GET)

        # Paginate results
        from django.core.paginator import Paginator

        paginator = Paginator(results, 20)
        page_number = request.GET.get("page", 1)
        page_obj = paginator.get_page(page_number)

        # Serialize results
        from .serializers import AuctionSerializer

        serialized_results = AuctionSerializer(
            page_obj, many=True, context={"request": request}
        ).data

        return Response(
            {
                "results": serialized_results,
                "count": paginator.count,
                "num_pages": paginator.num_pages,
                "current_page": page_obj.number,
                "has_next": page_obj.has_next(),
                "has_previous": page_obj.has_previous(),
            }
        )
    except Exception as e:
        return Response({"error": str(e)}, status=500)


@api_view(["GET"])
@permission_classes([AllowAny])
def search_suggestions(request):
    """Get search suggestions"""
    try:
        query = request.GET.get("q", "")
        suggestions = advanced_search_service.get_search_suggestions(query)
        return Response({"suggestions": suggestions})
    except Exception as e:
        return Response({"error": str(e)}, status=500)


@api_view(["GET"])
@permission_classes([AllowAny])
def search_filters(request):
    """Get available search filters"""
    try:
        filters = advanced_search_service.get_search_filters()
        return Response(filters)
    except Exception as e:
        return Response({"error": str(e)}, status=500)


@api_view(["GET"])
@permission_classes([AllowAny])
def recommended_auctions(request):
    """Get recommended auctions"""
    try:
        user_id = request.user.id if request.user.is_authenticated else None
        recommendations = advanced_search_service.get_recommended_auctions(user_id)

        from .serializers import AuctionSerializer

        serialized = AuctionSerializer(
            recommendations, many=True, context={"request": request}
        ).data

        return Response({"recommendations": serialized})
    except Exception as e:
        return Response({"error": str(e)}, status=500)


# Enhanced Notifications Views
@api_view(["GET"])
@permission_classes([AllowAny])
def user_notifications(request):
    """Get user notifications"""
    try:
        if not request.user.is_authenticated:
            return Response({"error": "Authentication required"}, status=401)

        unread_only = request.GET.get("unread_only", "false").lower() == "true"
        notifications = enhanced_notification_service.get_user_notifications(
            request.user, unread_only=unread_only
        )

        from .serializers import NotificationSerializer

        serialized = NotificationSerializer(notifications, many=True).data

        return Response({"notifications": serialized})
    except Exception as e:
        return Response({"error": str(e)}, status=500)


@api_view(["POST"])
@permission_classes([AllowAny])
def mark_notifications_read(request):
    """Mark notifications as read"""
    try:
        if not request.user.is_authenticated:
            return Response({"error": "Authentication required"}, status=401)

        notification_ids = request.data.get("notification_ids", [])
        count = enhanced_notification_service.mark_notifications_read(
            request.user, notification_ids
        )

        return Response({"marked_read": count})
    except Exception as e:
        return Response({"error": str(e)}, status=500)


# Admin Management Views
@api_view(["DELETE"])
@permission_classes([AllowAny])
def admin_delete_auction(request, auction_id):
    """Admin delete auction"""
    try:
        if not request.user.is_authenticated or not (
            request.user.is_staff or request.user.is_superuser
        ):
            return Response({"error": "Admin access required"}, status=403)

        auction = Auction.objects.get(id=auction_id)
        auction_title = auction.title
        auction.delete()

        return Response({"message": f'Auction "{auction_title}" deleted successfully'})
    except Auction.DoesNotExist:
        return Response({"error": "Auction not found"}, status=404)
    except Exception as e:
        return Response({"error": str(e)}, status=500)


@api_view(["PATCH"])
@permission_classes([AllowAny])
def admin_edit_auction_image(request, auction_id):
    """Admin edit auction image URL"""
    try:
        if not request.user.is_authenticated or not (
            request.user.is_staff or request.user.is_superuser
        ):
            return Response({"error": "Admin access required"}, status=403)

        auction = Auction.objects.get(id=auction_id)

        # Get the new image URL from request
        new_image_url = request.data.get("image_url", "").strip()

        # Validate URL format (basic validation)
        if new_image_url and not (
            new_image_url.startswith("http://") or new_image_url.startswith("https://")
        ):
            return Response(
                {
                    "error": "Invalid URL format. URL must start with http:// or https://"
                },
                status=400,
            )

        # Update the image URL
        old_image_url = auction.image
        auction.image = new_image_url
        auction.save()

        return Response(
            {
                "message": f"Auction image updated successfully",
                "auction_id": auction.id,
                "auction_title": auction.title,
                "old_image_url": old_image_url,
                "new_image_url": new_image_url,
            }
        )
    except Auction.DoesNotExist:
        return Response({"error": "Auction not found"}, status=404)
    except Exception as e:
        return Response({"error": str(e)}, status=500)


@api_view(["DELETE"])
@permission_classes([AllowAny])
def admin_delete_user(request, user_id):
    """Admin delete user"""
    try:
        if not request.user.is_authenticated or not (
            request.user.is_staff or request.user.is_superuser
        ):
            return Response({"error": "Admin access required"}, status=403)

        user = User.objects.get(id=user_id)
        if user.is_superuser:
            return Response({"error": "Cannot delete superuser"}, status=400)

        username = user.username
        user.delete()

        return Response({"message": f'User "{username}" deleted successfully'})
    except User.DoesNotExist:
        return Response({"error": "User not found"}, status=404)
    except Exception as e:
        return Response({"error": str(e)}, status=500)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def admin_users_list(request):
    """Get list of users for admin management"""
    try:
        # Check if user is admin
        if not (request.user.is_staff or request.user.is_superuser):
            return Response({"error": "Admin access required"}, status=403)

        users = User.objects.all().order_by("-date_joined")

        # Paginate
        from django.core.paginator import Paginator

        paginator = Paginator(users, 20)
        page_number = request.GET.get("page", 1)
        page_obj = paginator.get_page(page_number)

        # Serialize
        users_data = []
        for user in page_obj:
            users_data.append(
                {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "is_active": user.is_active,
                    "is_staff": user.is_staff,
                    "date_joined": user.date_joined,
                    "last_login": user.last_login,
                    "auction_count": user.auctions.count(),
                    "bid_count": Bid.objects.filter(user=user).count(),
                }
            )

        return Response(
            {
                "users": users_data,
                "count": paginator.count,
                "num_pages": paginator.num_pages,
                "current_page": page_obj.number,
            }
        )
    except Exception as e:
        return Response({"error": str(e)}, status=500)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def admin_auctions_list(request):
    """Get comprehensive list of auctions for admin management"""
    try:
        # Check if user is admin
        if not (request.user.is_staff or request.user.is_superuser):
            return Response({"error": "Admin access required"}, status=403)

        # Get ALL auctions with comprehensive data
        auctions = Auction.objects.select_related('owner').prefetch_related('bids').all().order_by("-created_at")

        # Optional pagination (but show all by default)
        page_size = request.GET.get("page_size", "all")

        if page_size != "all":
            from django.core.paginator import Paginator
            paginator = Paginator(auctions, int(page_size))
            page_number = request.GET.get("page", 1)
            page_obj = paginator.get_page(page_number)
            auctions_to_serialize = page_obj
        else:
            auctions_to_serialize = auctions

        # Serialize with comprehensive data
        from .serializers import AuctionSerializer

        serialized = AuctionSerializer(
            auctions_to_serialize, many=True, context={"request": request}
        ).data

        return Response(
            {
                "auctions": serialized,
                "count": paginator.count,
                "num_pages": paginator.num_pages,
                "current_page": page_obj.number,
            }
        )
    except Exception as e:
        return Response({"error": str(e)}, status=500)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def ai_price_prediction(request):
    """AI price prediction for auction creation"""
    try:
        # Get auction data from request
        title = request.data.get("title", "")
        description = request.data.get("description", "")
        category = request.data.get("category", "")
        condition = request.data.get("condition", "Good")

        # Convert category to lowercase to match model choices
        category_mapping = {
            "Electronics": "electronics",
            "Fashion": "fashion",
            "Home & Garden": "home_garden",
            "Sports": "sports",
            "Books": "books",
            "Art": "art",
            "Collectibles": "collectibles",
            "Vehicles": "automotive",
            "Other": "other",
        }
        category = category_mapping.get(category, category.lower())

        if not title or not category:
            return Response(
                {"error": "Title and category are required for price prediction"},
                status=400,
            )

        # Create a temporary auction object for prediction
        from decimal import Decimal

        temp_auction = Auction(
            title=title,
            description=description,
            category=category,
            condition=condition,
            starting_bid=Decimal("100"),  # Default starting bid for prediction
            owner=request.user,
            end_time=timezone.now() + timezone.timedelta(days=7),  # Default 7 days
        )

        # Get AI prediction
        predicted_price, confidence_score, features = (
            price_prediction_service.predict_price(temp_auction)
        )

        # Calculate price range
        min_price = float(predicted_price) * 0.8
        max_price = float(predicted_price) * 1.2
        recommended_price = (
            float(predicted_price) * 0.9
        )  # Slightly below predicted for better bidding

        # Generate market insights
        market_insights = generate_market_insights(category, condition, features)

        return Response(
            {
                "predicted_price": float(predicted_price),
                "min_price": round(min_price, 2),
                "max_price": round(max_price, 2),
                "recommended_price": round(recommended_price, 2),
                "confidence": round(confidence_score * 100, 1),
                "market_insights": market_insights,
                "features_analyzed": {
                    "category": category,
                    "condition": condition,
                    "title_keywords": features.get("title_keywords", 0),
                    "brand_detected": features.get("brand_detected", False),
                    "market_activity": features.get("market_activity", 0),
                },
                "model_version": price_prediction_service.model_version,
            }
        )

    except Exception as e:
        return Response(
            {
                "error": f"Price prediction failed: {str(e)}",
                "fallback_recommendation": {
                    "min_price": 100,
                    "max_price": 500,
                    "recommended_price": 200,
                    "confidence": 30,
                },
            },
            status=500,
        )


def generate_market_insights(category, condition, features):
    """Generate market insights for the prediction"""
    insights = []

    # Category insights
    market_activity = features.get("market_activity", 0)
    if market_activity > 0.7:
        insights.append(f"{category} is a highly active category with strong demand.")
    elif market_activity > 0.3:
        insights.append(f"{category} has moderate market activity.")
    else:
        insights.append(
            f"{category} has lower market activity - consider competitive pricing."
        )

    # Condition insights
    condition_score = features.get("condition_score", 0.5)
    if condition_score > 0.8:
        insights.append(
            f"'{condition}' condition items typically command premium prices."
        )
    elif condition_score < 0.4:
        insights.append(f"'{condition}' condition may require lower starting prices.")

    # Brand insights
    if features.get("brand_detected"):
        insights.append("Brand recognition detected - this may increase final price.")

    # Title insights
    title_keywords = features.get("title_keywords", 0)
    if title_keywords > 0.2:
        insights.append(
            "Strong keywords detected in title - good for search visibility."
        )

    return " ".join(insights) if insights else "Standard market conditions apply."


@api_view(["GET"])
@permission_classes([AllowAny])
def categories_list(request):
    """Get list of available categories with dynamic data for frontend"""
    try:
        from django.db.models import Count

        # Get categories with auction counts and popular items
        categories_data = []

        # Category images mapping - using placeholder for now
        # Admin can add proper category images via the admin dashboard
        category_images = {
            "electronics": "/placeholder-image.svg",
            "fashion": "/placeholder-image.svg",
            "art": "/placeholder-image.svg",
            "collectibles": "/placeholder-image.svg",
            "jewelry": "/placeholder-image.svg",
            "sports": "/placeholder-image.svg",
            "books": "/placeholder-image.svg",
            "home_garden": "/placeholder-image.svg",
            "automotive": "/placeholder-image.svg",
            "other": "/placeholder-image.svg",
        }

        # Get all categories with auction counts
        category_stats = (
            Auction.objects.filter(approved=True)
            .values("category")
            .annotate(auction_count=Count("id"))
            .order_by("-auction_count")
        )

        for cat_stat in category_stats:
            category = cat_stat["category"]
            if category:  # Skip empty categories
                # Get most popular auction in this category (most bids)
                popular_auction = (
                    Auction.objects.filter(category=category, approved=True)
                    .annotate(bid_count=Count("bids"))
                    .order_by("-bid_count")
                    .first()
                )

                # Convert category to display name
                category_display_mapping = {
                    "electronics": "Electronics",
                    "fashion": "Fashion",
                    "home_garden": "Home & Garden",
                    "sports": "Sports",
                    "books": "Books",
                    "art": "Art",
                    "collectibles": "Collectibles",
                    "automotive": "Vehicles",
                    "jewelry": "Jewelry",
                    "other": "Other",
                }

                categories_data.append(
                    {
                        "name": category_display_mapping.get(
                            category, category.title()
                        ),
                        "slug": category,
                        "auction_count": cat_stat["auction_count"],
                        "image": category_images.get(
                            category, category_images["other"]
                        ),
                        "popular_item": (
                            popular_auction.title if popular_auction else None
                        ),
                        "popular_item_bids": (
                            popular_auction.bids.count() if popular_auction else 0
                        ),
                    }
                )

        # If no categories found, return defaults with current auction counts
        if not categories_data:
            default_categories = [
                "electronics",
                "fashion",
                "art",
                "collectibles",
                "jewelry",
                "sports",
            ]
            for category in default_categories:
                auction_count = Auction.objects.filter(
                    category=category, approved=True
                ).count()
                categories_data.append(
                    {
                        "name": category.title(),
                        "slug": category,
                        "auction_count": auction_count,
                        "image": category_images.get(
                            category, category_images["other"]
                        ),
                    }
                )

        return Response(categories_data)

    except Exception as e:
        # Fallback to static categories with placeholder images
        return Response(
            [
                {
                    "name": "Electronics",
                    "slug": "electronics",
                    "auction_count": 0,
                    "image": "/placeholder-image.svg",
                },
                {
                    "name": "Fashion",
                    "slug": "fashion",
                    "auction_count": 0,
                    "image": "/placeholder-image.svg",
                },
                {
                    "name": "Art",
                    "slug": "art",
                    "auction_count": 0,
                    "image": "/placeholder-image.svg",
                },
                {
                    "name": "Collectibles",
                    "slug": "collectibles",
                    "auction_count": 0,
                    "image": "/placeholder-image.svg",
                },
                {
                    "name": "Jewelry",
                    "slug": "jewelry",
                    "auction_count": 0,
                    "image": "/placeholder-image.svg",
                },
                {
                    "name": "Sports",
                    "slug": "sports",
                    "auction_count": 0,
                    "image": "/placeholder-image.svg",
                },
            ]
        )


# Landing Page Views
@api_view(["GET"])
@permission_classes([AllowAny])
def landing_page_data(request):
    """Get comprehensive landing page data including stats, features, and testimonials"""
    try:
        from django.db.models import Sum, Max
        from datetime import timedelta

        # Minimal logging to prevent spam
        print(f"📊 Landing page data requested at {timezone.now()}")

        # Calculate real-time statistics
        total_auctions = Auction.objects.count()
        active_auctions = Auction.objects.filter(
            end_time__gt=timezone.now(), is_closed=False
        ).count()
        total_users = User.objects.count()
        total_bids = Bid.objects.count()

        # Calculate total sales (sum of winning bids)
        total_sales = Bid.objects.aggregate(total=Sum("amount"))["total"] or 0

        # Recent activity stats
        last_24h = timezone.now() - timedelta(hours=24)
        recent_auctions = Auction.objects.filter(created_at__gte=last_24h).count()
        recent_bids = Bid.objects.filter(created_at__gte=last_24h).count()

        # Platform statistics
        stats = {
            "total_auctions": total_auctions,
            "active_auctions": active_auctions,
            "total_users": total_users,
            "total_bids": total_bids,
            "total_sales": float(total_sales),
            "recent_activity": {
                "new_auctions_24h": recent_auctions,
                "new_bids_24h": recent_bids,
            },
            "uptime": "99.9%",
            "satisfaction_rate": "99%",
        }

        return Response(
            {
                "success": True,
                "data": {
                    "stats": stats,
                    "platform_info": {
                        "name": "AuctionStore",
                        "tagline": "Where Every Bid Tells a Story",
                        "description": "Join the world's most trusted online auction platform.",
                        "established": "2024",
                        "headquarters": "Global Platform",
                    },
                },
                "timestamp": timezone.now().isoformat(),
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"❌ Landing page data error: {str(e)}")
        print(f"❌ Full traceback: {error_details}")
        return Response(
            {"success": False, "error": f"Failed to fetch landing page data: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([AllowAny])
def platform_stats(request):
    """Get real-time platform statistics for landing page and home page"""
    try:
        # Real-time statistics with consistent counting
        total_auctions = Auction.objects.count()
        active_auctions = Auction.objects.filter(
            end_time__gt=timezone.now(), approved=True
        ).count()
        total_users = User.objects.count()
        total_bids = Bid.objects.count()

        # Additional metrics
        categories_count = Auction.objects.values("category").distinct().count()

        # Recent activity (last 24 hours)
        from datetime import timedelta
        last_24h = timezone.now() - timedelta(hours=24)
        recent_auctions = Auction.objects.filter(created_at__gte=last_24h).count()
        recent_bids = Bid.objects.filter(created_at__gte=last_24h).count()

        stats = {
            "total_auctions": total_auctions,
            "active_auctions": active_auctions,
            "total_users": total_users,
            "total_bids": total_bids,
            "categories": categories_count,
            "recent_activity": {
                "new_auctions_24h": recent_auctions,
                "new_bids_24h": recent_bids,
            },
            "success_rate": "98.5%",
            "avg_response_time": "< 100ms",
        }

        # Log for debugging
        print(f"📊 Platform Stats API called - Users: {total_users}, Auctions: {total_auctions}, Active: {active_auctions}, Bids: {total_bids}")

        return Response(
            {"success": True, "stats": stats, "timestamp": timezone.now().isoformat()},
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        print(f"❌ Platform stats error: {str(e)}")
        return Response(
            {"success": False, "error": f"Failed to fetch platform stats: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def contact_inquiry(request):
    """Handle contact form submissions from landing page"""
    try:
        data = request.data
        name = data.get("name", "").strip()
        email = data.get("email", "").strip()
        subject = data.get("subject", "").strip()
        message = data.get("message", "").strip()

        # Basic validation
        if not all([name, email, subject, message]):
            return Response(
                {"success": False, "error": "All fields are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Log the inquiry
        print(f"Contact Inquiry: {name} ({email}) - {subject}")
        print(f"Message: {message}")

        return Response(
            {
                "success": True,
                "message": "Thank you for your inquiry! We'll get back to you within 24 hours.",
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        return Response(
            {"success": False, "error": f"Failed to submit inquiry: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
