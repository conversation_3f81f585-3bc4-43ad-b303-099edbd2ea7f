import React, { useState } from "react";
import { useAuth } from "../context/AuthContext";
import { useNavigate } from "react-router-dom";
import "./QuickAdminLogin.css";

const QuickAdminLogin = () => {
  const { login, user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleQuickAdminLogin = async () => {
    setLoading(true);
    setError("");

    try {
      await login("aisha_admin", "aisha2024!");
      navigate("/admin-dashboard");
    } catch (err) {
      setError("Admin login failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Don't show if user is already logged in as admin
  if (user && (user.role === "admin" || user.is_staff)) {
    return (
      <div className="quick-admin-login admin-logged-in">
        <div className="admin-status">
          <h4>👑 Admin Access</h4>
          <p>Welcome, {user.username}!</p>
          <div className="admin-links">
            <button
              className="btn btn-primary"
              onClick={() => navigate("/admin-dashboard")}
            >
              🛡️ Admin Panel
            </button>
            <button
              className="btn btn-secondary"
              onClick={() => navigate("/dashboard")}
            >
              📊 Analytics
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="quick-admin-login">
      <div className="admin-login-card">
        <h4>🛡️ Quick Admin Access</h4>
        <p>Access the admin panel with one click</p>

        {error && <div className="alert alert-danger">{error}</div>}

        <button
          className="btn btn-admin"
          onClick={handleQuickAdminLogin}
          disabled={loading}
        >
          {loading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2"></span>
              Logging in...
            </>
          ) : (
            <>🔑 Login as Admin</>
          )}
        </button>

        <div className="admin-info">
          <small>
            <strong>Admin Features:</strong>
            <br />
            • AI Analytics Dashboard
            <br />
            • User Management
            <br />
            • Auction Management
            <br />
            • Chat System Monitoring
            <br />• Price Prediction Analytics
          </small>
        </div>
      </div>
    </div>
  );
};

export default QuickAdminLogin;
