from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer

from .models import (Analytics, Auction, AuditTrail, AutoBid, Bid, BidHistory,
                     Category, ChatMessage, ChatRoom, ContactMessage,
                     FraudDetection, Notification, Payment, PricePrediction,
                     PricePredictionHistory, PrivateMessage, Review,
                     UserProfile, Watchlist)


class CategoryField(serializers.CharField):
    """Custom category field that handles frontend-to-backend mapping"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def to_internal_value(self, data):
        """Convert frontend category to backend format"""
        category_mapping = {
            "Electronics": "electronics",
            "Fashion": "fashion",
            "Home & Garden": "home_garden",
            "Sports": "sports",
            "Books": "books",
            "Art": "art",
            "Collectibles": "collectibles",
            "Vehicles": "automotive",
            "Jewelry": "jewelry",
            "Other": "other",
        }

        # If the value is in the mapping, convert it
        if data in category_mapping:
            return category_mapping[data]

        # If it's already in the correct format, return as is
        valid_choices = [choice[0] for choice in Auction.CATEGORIES]
        if data in valid_choices:
            return data

        # If neither, raise validation error
        raise serializers.ValidationError(f"'{data}' is not a valid category choice.")

    def to_representation(self, value):
        """Convert backend category to frontend format"""
        category_display_mapping = {
            "electronics": "Electronics",
            "fashion": "Fashion",
            "home_garden": "Home & Garden",
            "sports": "Sports",
            "books": "Books",
            "art": "Art",
            "collectibles": "Collectibles",
            "automotive": "Vehicles",
            "jewelry": "Jewelry",
            "other": "Other",
        }

        return category_display_mapping.get(value, value)


class UserSerializer(serializers.ModelSerializer):
    user_role = serializers.CharField(write_only=True, required=False, default='bidder')
    username = serializers.CharField(max_length=150)  # Override to bypass Django's default validation

    class Meta:
        model = User
        fields = ["username", "password", "email", "user_role"]
        extra_kwargs = {"password": {"write_only": True}}

    def create(self, validated_data):
        try:
            user_role = validated_data.pop('user_role', 'bidder')

            # Create user
            user = User(**validated_data)
            user.set_password(validated_data["password"])  # Hash the password
            user.save()

            # Create or update user profile with role
            from .models import UserProfile
            try:
                profile, created = UserProfile.objects.get_or_create(user=user)
                profile.user_role = user_role
                profile.save()
                print(f"✅ User profile created/updated for {user.username} with role {user_role}")
            except Exception as profile_error:
                print(f"❌ Profile creation error: {profile_error}")
                # Don't fail user creation if profile fails
                pass

            return user
        except Exception as e:
            print(f"❌ User creation error: {e}")
            raise serializers.ValidationError(f"User creation failed: {str(e)}")

    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("This email is already in use.")
        return value

    def validate_username(self, value):
        # Clean the username - replace spaces with underscores
        cleaned_username = value.replace(' ', '_').replace('-', '_')

        # Check if cleaned username already exists
        if User.objects.filter(username=cleaned_username).exists():
            raise serializers.ValidationError("This username is already taken.")

        # Return the cleaned username
        return cleaned_username

    def validate_user_role(self, value):
        valid_roles = ['bidder', 'seller', 'both']
        if value not in valid_roles:
            raise serializers.ValidationError(f"Invalid role. Must be one of: {', '.join(valid_roles)}")
        return value


class AuctionSerializer(serializers.ModelSerializer):
    owner = serializers.ReadOnlyField(source="owner.username")
    owner_rating = serializers.SerializerMethodField()
    winner = serializers.ReadOnlyField(source="winner.username")
    winner_id = serializers.ReadOnlyField(source="winner.id")
    time_remaining = serializers.SerializerMethodField()
    total_bids = serializers.ReadOnlyField()
    is_active = serializers.ReadOnlyField()
    reserve_met = serializers.ReadOnlyField()
    watchers_count = serializers.SerializerMethodField()
    category = CategoryField()

    class Meta:
        model = Auction
        fields = [
            "id",
            "title",
            "description",
            "starting_bid",
            "current_bid",
            "reserve_price",
            "buy_now_price",
            "start_time",
            "end_time",
            "auction_type",
            "category",
            "image",
            "additional_images",
            "location",
            "condition",
            "shipping_cost",
            "owner",
            "owner_rating",
            "winner",
            "winner_id",
            "created_at",
            "updated_at",
            "is_closed",
            "approved",
            "featured",
            "views_count",
            "time_remaining",
            "total_bids",
            "is_active",
            "reserve_met",
            "watchers_count",
        ]
        read_only_fields = ["current_bid", "views_count", "created_at", "updated_at"]

    def get_current_bid(self, obj):
        return obj.current_bid

    def get_owner_rating(self, obj):
        if hasattr(obj.owner, "profile"):
            return float(obj.owner.profile.rating)
        return 0.0

    def get_time_remaining(self, obj):
        time_remaining = obj.time_remaining
        if time_remaining:
            return {
                "days": time_remaining.days,
                "hours": time_remaining.seconds // 3600,
                "minutes": (time_remaining.seconds % 3600) // 60,
                "seconds": time_remaining.seconds % 60,
            }
        return None

    def get_watchers_count(self, obj):
        return obj.watchers.count()


class BidSerializer(serializers.ModelSerializer):
    class Meta:
        model = Bid
        fields = "__all__"

    def validate(self, data):
        auction = data["auction"]
        if data["amount"] <= auction.current_bid:
            raise serializers.ValidationError(
                f"Bid amount must be greater than the current bid of {auction.current_bid}."
            )
        return data


class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = "__all__"


class ReviewSerializer(serializers.ModelSerializer):
    reviewer_name = serializers.CharField(source='reviewer.username', read_only=True)
    reviewer_full_name = serializers.SerializerMethodField()
    reviewee_name = serializers.CharField(source='reviewee.username', read_only=True)
    reviewee_full_name = serializers.SerializerMethodField()
    auction_title = serializers.CharField(source='auction.title', read_only=True)
    rating_display = serializers.SerializerMethodField()
    time_ago = serializers.SerializerMethodField()
    can_edit = serializers.SerializerMethodField()

    class Meta:
        model = Review
        fields = [
            'id', 'reviewer', 'reviewee', 'auction', 'rating', 'comment',
            'review_type', 'is_verified', 'helpful_count', 'reported_count',
            'is_approved', 'created_at', 'updated_at',
            'reviewer_name', 'reviewer_full_name', 'reviewee_name', 'reviewee_full_name',
            'auction_title', 'rating_display', 'time_ago', 'can_edit'
        ]
        read_only_fields = ['reviewer', 'is_verified', 'helpful_count', 'reported_count', 'is_approved']

    def get_reviewer_full_name(self, obj):
        return obj.reviewer.get_full_name() or obj.reviewer.username

    def get_reviewee_full_name(self, obj):
        return obj.reviewee.get_full_name() or obj.reviewee.username

    def get_rating_display(self, obj):
        return {
            'stars': '⭐' * obj.rating,
            'text': f"{obj.rating}/5",
            'percentage': (obj.rating / 5) * 100
        }

    def get_time_ago(self, obj):
        from django.utils import timezone
        from datetime import timedelta

        now = timezone.now()
        diff = now - obj.created_at

        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours > 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
        else:
            return "Just now"

    def get_can_edit(self, obj):
        request = self.context.get('request')
        if request and request.user:
            return obj.reviewer == request.user
        return False

    def validate_rating(self, value):
        if value < 1 or value > 5:
            raise serializers.ValidationError("Rating must be between 1 and 5")
        return value

    def validate_comment(self, value):
        if len(value.strip()) < 10:
            raise serializers.ValidationError("Review comment must be at least 10 characters long")
        return value.strip()

    def create(self, validated_data):
        # Set the reviewer to the current user
        request = self.context.get('request')
        if request and request.user:
            validated_data['reviewer'] = request.user
        return super().create(validated_data)


class AutoBidSerializer(serializers.ModelSerializer):
    class Meta:
        model = AutoBid
        fields = "__all__"


class AuditTrailSerializer(serializers.ModelSerializer):
    class Meta:
        model = AuditTrail
        fields = "__all__"


class ContactMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContactMessage
        fields = "__all__"


class PaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payment
        fields = "__all__"


class MyTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        token["user_id"] = user.id  # Add user ID
        token["username"] = user.username
        token["email"] = user.email

        # Set role based on staff status and user profile
        if user.is_staff:
            token["role"] = "admin"
            token["user_role"] = "admin"
        else:
            token["role"] = "user"
            # Get user role from profile
            try:
                profile = user.profile
                token["user_role"] = profile.user_role
                token["can_create_auctions"] = profile.can_create_auctions()
                token["can_bid_on_auctions"] = profile.can_bid_on_auctions()
            except:
                # Default to bidder if no profile exists
                token["user_role"] = "bidder"
                token["can_create_auctions"] = False
                token["can_bid_on_auctions"] = True

        return token


# Enhanced Serializers for New Models


class UserProfileSerializer(serializers.ModelSerializer):
    user = serializers.StringRelatedField(read_only=True)

    class Meta:
        model = UserProfile
        fields = "__all__"
        read_only_fields = [
            "user",
            "rating",
            "total_sales",
            "total_purchases",
            "created_at",
            "updated_at",
        ]


class CategorySerializer(serializers.ModelSerializer):
    subcategories = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = "__all__"
        read_only_fields = ["created_at"]

    def get_subcategories(self, obj):
        if obj.subcategories.exists():
            return CategorySerializer(obj.subcategories.all(), many=True).data
        return []


class WatchlistSerializer(serializers.ModelSerializer):
    auction_title = serializers.ReadOnlyField(source="auction.title")
    auction_image = serializers.ReadOnlyField(source="auction.image")
    auction_current_bid = serializers.ReadOnlyField(source="auction.current_bid")
    auction_end_time = serializers.ReadOnlyField(source="auction.end_time")

    class Meta:
        model = Watchlist
        fields = [
            "id",
            "auction",
            "auction_title",
            "auction_image",
            "auction_current_bid",
            "auction_end_time",
            "created_at",
        ]
        read_only_fields = ["created_at"]


class BidHistorySerializer(serializers.ModelSerializer):
    user = serializers.StringRelatedField(read_only=True)
    auction_title = serializers.ReadOnlyField(source="auction.title")

    class Meta:
        model = BidHistory
        fields = "__all__"
        read_only_fields = ["timestamp"]


class FraudDetectionSerializer(serializers.ModelSerializer):
    user = serializers.StringRelatedField(read_only=True)
    auction_title = serializers.ReadOnlyField(source="auction.title")
    resolved_by_username = serializers.ReadOnlyField(source="resolved_by.username")

    class Meta:
        model = FraudDetection
        fields = "__all__"
        read_only_fields = ["created_at"]


class AnalyticsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Analytics
        fields = "__all__"
        read_only_fields = ["created_at"]


# AI Price Prediction Serializers
class PricePredictionSerializer(serializers.ModelSerializer):
    auction_title = serializers.ReadOnlyField(source="auction.title")
    auction_category = serializers.ReadOnlyField(source="auction.category")

    class Meta:
        model = PricePrediction
        fields = [
            "id",
            "auction",
            "auction_title",
            "auction_category",
            "predicted_price",
            "confidence_score",
            "model_version",
            "features_used",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["created_at", "updated_at"]


class PricePredictionHistorySerializer(serializers.ModelSerializer):
    auction_title = serializers.ReadOnlyField(source="auction.title")
    accuracy_percentage = serializers.SerializerMethodField()

    class Meta:
        model = PricePredictionHistory
        fields = [
            "id",
            "auction",
            "auction_title",
            "predicted_price",
            "actual_price",
            "confidence_score",
            "accuracy_score",
            "accuracy_percentage",
            "model_version",
            "features_used",
            "created_at",
        ]
        read_only_fields = ["created_at"]

    def get_accuracy_percentage(self, obj):
        if obj.accuracy_score is not None:
            return round(obj.accuracy_score * 100, 2)
        return None


# Chat System Serializers
class ChatMessageSerializer(serializers.ModelSerializer):
    sender_username = serializers.ReadOnlyField(source="sender.username")
    sender_avatar = serializers.SerializerMethodField()
    time_ago = serializers.SerializerMethodField()

    class Meta:
        model = ChatMessage
        fields = [
            "id",
            "room",
            "sender",
            "sender_username",
            "sender_avatar",
            "message",
            "message_type",
            "timestamp",
            "time_ago",
            "is_read",
            "edited_at",
        ]
        read_only_fields = ["timestamp", "sender"]

    def get_sender_avatar(self, obj):
        if hasattr(obj.sender, "profile") and obj.sender.profile.avatar:
            return obj.sender.profile.avatar
        return None

    def get_time_ago(self, obj):
        from django.utils.timesince import timesince

        return timesince(obj.timestamp)


class ChatRoomSerializer(serializers.ModelSerializer):
    auction_title = serializers.ReadOnlyField(source="auction.title")
    auction_image = serializers.ReadOnlyField(source="auction.image")
    participants_count = serializers.SerializerMethodField()
    recent_messages = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()

    class Meta:
        model = ChatRoom
        fields = [
            "id",
            "auction",
            "auction_title",
            "auction_image",
            "participants",
            "participants_count",
            "is_active",
            "recent_messages",
            "unread_count",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["created_at", "updated_at"]

    def validate_auction(self, value):
        """Validate that the auction exists and is valid"""
        try:
            from .models import Auction
            auction = Auction.objects.get(id=value.id if hasattr(value, 'id') else value)
            return auction
        except Auction.DoesNotExist:
            raise serializers.ValidationError("Auction does not exist.")
        except Exception as e:
            print(f"❌ Auction validation error: {e}")
            raise serializers.ValidationError(f"Invalid auction: {str(e)}")

    def create(self, validated_data):
        """Create chat room with proper error handling"""
        try:
            print(f"🔍 Creating chat room with data: {validated_data}")
            return super().create(validated_data)
        except Exception as e:
            print(f"❌ ChatRoom serializer create error: {e}")
            raise serializers.ValidationError(f"Failed to create chat room: {str(e)}")

    def get_participants_count(self, obj):
        return obj.participants.count()

    def get_recent_messages(self, obj):
        recent_messages = obj.messages.order_by("-timestamp")[:5]
        return ChatMessageSerializer(recent_messages, many=True).data

    def get_unread_count(self, obj):
        # Get unread count for current user (if available in context)
        request = self.context.get("request")
        if request and request.user.is_authenticated:
            return (
                obj.messages.filter(is_read=False).exclude(sender=request.user).count()
            )
        return 0


class PrivateMessageSerializer(serializers.ModelSerializer):
    sender_username = serializers.ReadOnlyField(source="sender.username")
    recipient_username = serializers.ReadOnlyField(source="recipient.username")
    auction_title = serializers.ReadOnlyField(source="auction.title")
    time_ago = serializers.SerializerMethodField()
    replies_count = serializers.SerializerMethodField()

    class Meta:
        model = PrivateMessage
        fields = [
            "id",
            "sender",
            "sender_username",
            "recipient",
            "recipient_username",
            "auction",
            "auction_title",
            "subject",
            "message",
            "timestamp",
            "time_ago",
            "is_read",
            "parent_message",
            "replies_count",
        ]
        read_only_fields = ["timestamp", "sender"]

    def get_time_ago(self, obj):
        from django.utils.timesince import timesince

        return timesince(obj.timestamp)

    def get_replies_count(self, obj):
        return obj.replies.count()


# Enhanced Auction Serializer with AI Predictions
class AuctionWithPredictionSerializer(AuctionSerializer):
    price_prediction = PricePredictionSerializer(read_only=True)
    has_chat_room = serializers.SerializerMethodField()

    class Meta(AuctionSerializer.Meta):
        fields = AuctionSerializer.Meta.fields + ["price_prediction", "has_chat_room"]

    def get_has_chat_room(self, obj):
        return hasattr(obj, "chat_room") and obj.chat_room.is_active
