import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import {
  Container,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ert,
  Spinner,
  Badge,
  Form,
} from "react-bootstrap";
import {
  FaCreditCard,
  FaClock,
  FaCheckCircle,
  FaExclamationTriangle,
  FaArrowLeft,
  FaShieldAlt,
} from "react-icons/fa";
import { formatAuctionPrice } from "../utils/currency";
import PaymentGateway from "../components/PaymentGateway";

function PaymentPage() {
  const { auctionId } = useParams();
  const { user, token } = useAuth();
  const navigate = useNavigate();

  const [auction, setAuction] = useState(null);
  const [payment, setPayment] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("stripe");
  const [timeRemaining, setTimeRemaining] = useState("");

  useEffect(() => {
    if (!token || !user) {
      navigate("/login");
      return;
    }
    fetchAuctionAndPayment();
  }, [auctionId, token, user, navigate]);

  useEffect(() => {
    if (payment?.payment_deadline) {
      const timer = setInterval(() => {
        const deadline = new Date(payment.payment_deadline);
        const now = new Date();
        const diff = deadline - now;

        if (diff <= 0) {
          setTimeRemaining("Payment deadline has passed");
        } else {
          const hours = Math.floor(diff / (1000 * 60 * 60));
          const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
          setTimeRemaining(`${hours}h ${minutes}m remaining`);
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [payment]);

  const fetchAuctionAndPayment = async () => {
    try {
      console.log("Starting payment page data fetch...");
      setLoading(true);

      // Fetch auction details
      console.log("Fetching auction details for ID:", auctionId);
      const auctionResponse = await fetch(
        `http://127.0.0.1:8000/api/auctions/${auctionId}/`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (!auctionResponse.ok) {
        throw new Error("Auction not found");
      }

      const auctionData = await auctionResponse.json();
      console.log("Auction data received:", auctionData);
      setAuction(auctionData);

      // Check if user is the winner
      console.log(
        "Checking if user is winner. User ID:",
        user.id,
        "Winner ID:",
        auctionData.winner
      );
      if (auctionData.winner !== user.id) {
        setError("You are not the winner of this auction");
        setLoading(false);
        return;
      }

      // Fetch payment information
      console.log("Fetching payment information...");
      const paymentsResponse = await fetch(
        "http://127.0.0.1:8000/api/payments/",
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (paymentsResponse.ok) {
        const paymentsData = await paymentsResponse.json();
        console.log("Payments data received:", paymentsData);
        const auctionPayment = paymentsData.results?.find(
          (p) => p.auction.id === parseInt(auctionId)
        );
        console.log("Found auction payment:", auctionPayment);
        setPayment(auctionPayment);
      }
    } catch (err) {
      console.error("Error fetching auction data:", err);
      setError(err.message || "Failed to load auction information");
    } finally {
      console.log("Setting loading to false");
      setLoading(false);
    }
  };

  const handlePaymentSuccess = () => {
    // Refresh payment data
    fetchAuctionAndPayment();
    // Show success message
    alert("Payment completed successfully!");
  };

  const handlePaymentFailure = (error) => {
    setError(`Payment failed: ${error}`);
  };

  if (loading) {
    return (
      <Container className="mt-4 text-center">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-2">Loading payment information...</p>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">
          <FaExclamationTriangle className="me-2" />
          {error}
        </Alert>
        <Button variant="outline-primary" onClick={() => navigate(-1)}>
          <FaArrowLeft className="me-2" />
          Go Back
        </Button>
      </Container>
    );
  }

  if (!auction) {
    return (
      <Container className="mt-4">
        <Alert variant="warning">Auction not found</Alert>
      </Container>
    );
  }

  // Check if payment is already completed
  if (payment?.payment_status === "completed") {
    return (
      <Container className="mt-4">
        <Alert variant="success">
          <FaCheckCircle className="me-2" />
          <strong>Payment Completed!</strong> Your payment for this auction has
          been successfully processed.
        </Alert>
        <Card>
          <Card.Body>
            <h5>Payment Details</h5>
            <p>
              <strong>Auction:</strong> {auction.title}
            </p>
            <p>
              <strong>Amount Paid:</strong> {formatAuctionPrice(payment.amount)}
            </p>
            <p>
              <strong>Transaction ID:</strong> {payment.transaction_id}
            </p>
            <p>
              <strong>Payment Date:</strong>{" "}
              {new Date(payment.payment_date).toLocaleString()}
            </p>
          </Card.Body>
        </Card>
        <div className="mt-3">
          <Button variant="primary" onClick={() => navigate("/payments")}>
            View Payment History
          </Button>
          <Button
            variant="outline-primary"
            className="ms-2"
            onClick={() => navigate(`/auction/${auction.id}`)}
          >
            View Auction
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <Container className="mt-4">
      <Row>
        <Col lg={8} className="mx-auto">
          {/* Header */}
          <div className="d-flex align-items-center mb-4">
            <Button
              variant="outline-secondary"
              size="sm"
              onClick={() => navigate(-1)}
              className="me-3"
            >
              <FaArrowLeft />
            </Button>
            <h2 className="mb-0">
              <FaCreditCard className="me-2" />
              Complete Payment
            </h2>
          </div>

          {/* Payment Deadline Warning */}
          {timeRemaining && (
            <Alert variant="warning" className="mb-4">
              <FaClock className="me-2" />
              <strong>Payment Deadline:</strong> {timeRemaining}
            </Alert>
          )}

          {/* Auction Summary */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Auction Summary</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={8}>
                  <h6>{auction.title}</h6>
                  <p className="text-muted mb-2">{auction.description}</p>
                  <div className="d-flex gap-3">
                    <Badge bg="success">Won</Badge>
                    <Badge bg="info">ID: #{auction.id}</Badge>
                  </div>
                </Col>
                <Col md={4} className="text-end">
                  <div className="h4 text-success mb-0">
                    {formatAuctionPrice(auction.current_bid)}
                  </div>
                  <small className="text-muted">Winning Bid</small>
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* Payment Method Selection */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Payment Method</h5>
            </Card.Header>
            <Card.Body>
              <Form.Group>
                <Form.Check
                  type="radio"
                  id="stripe"
                  name="paymentMethod"
                  label="Credit/Debit Card (Stripe)"
                  checked={paymentMethod === "stripe"}
                  onChange={() => setPaymentMethod("stripe")}
                  className="mb-2"
                />
                <small className="text-muted d-block mb-3">
                  Secure payment processing with Stripe. Supports all major
                  credit and debit cards.
                </small>
              </Form.Group>
            </Card.Body>
          </Card>

          {/* Payment Gateway */}
          <Card>
            <Card.Header>
              <h5 className="mb-0">
                <FaShieldAlt className="me-2" />
                Secure Payment
              </h5>
            </Card.Header>
            <Card.Body>
              <PaymentGateway
                auction={auction}
                amount={auction.current_bid}
                onSuccess={handlePaymentSuccess}
                onFailure={handlePaymentFailure}
              />

              <div className="mt-3 text-center">
                <small className="text-muted">
                  <FaShieldAlt className="me-1" />
                  Your payment information is secure and encrypted
                </small>
              </div>
            </Card.Body>
          </Card>

          {/* Security Notice */}
          <Alert variant="info" className="mt-4">
            <FaShieldAlt className="me-2" />
            <strong>Secure Payment:</strong> All transactions are processed
            securely through our payment partners. Your financial information is
            never stored on our servers.
          </Alert>
        </Col>
      </Row>
    </Container>
  );
}

export default PaymentPage;
