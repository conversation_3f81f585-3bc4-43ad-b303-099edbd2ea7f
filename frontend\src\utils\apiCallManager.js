/**
 * API Call Manager - Prevents excessive API calls and implements request deduplication
 * This utility helps prevent multiple simultaneous API calls to the same endpoint
 */

class APICallManager {
  constructor() {
    this.pendingRequests = new Map(); // Track pending requests
    this.cache = new Map(); // Simple cache for responses
    this.requestCounts = new Map(); // Track request counts per endpoint
    this.lastRequestTimes = new Map(); // Track last request times
    this.rateLimits = new Map(); // Rate limit configurations per endpoint
    
    // Default rate limit: max 5 requests per 10 seconds per endpoint
    this.defaultRateLimit = {
      maxRequests: 5,
      timeWindow: 10000, // 10 seconds
      cooldown: 1000 // 1 second minimum between requests
    };
  }

  /**
   * Set rate limit for a specific endpoint
   */
  setRateLimit(endpoint, config) {
    this.rateLimits.set(endpoint, { ...this.defaultRateLimit, ...config });
  }

  /**
   * Check if request is allowed based on rate limiting
   */
  isRequestAllowed(endpoint) {
    const now = Date.now();
    const rateLimit = this.rateLimits.get(endpoint) || this.defaultRateLimit;
    
    // Check cooldown period
    const lastRequestTime = this.lastRequestTimes.get(endpoint);
    if (lastRequestTime && (now - lastRequestTime) < rateLimit.cooldown) {
      console.warn(`🚫 Request to ${endpoint} blocked - cooldown period active`);
      return false;
    }

    // Check rate limit
    const requestHistory = this.requestCounts.get(endpoint) || [];
    const recentRequests = requestHistory.filter(time => (now - time) < rateLimit.timeWindow);
    
    if (recentRequests.length >= rateLimit.maxRequests) {
      console.warn(`🚫 Request to ${endpoint} blocked - rate limit exceeded`);
      return false;
    }

    return true;
  }

  /**
   * Record a request
   */
  recordRequest(endpoint) {
    const now = Date.now();
    this.lastRequestTimes.set(endpoint, now);
    
    const requestHistory = this.requestCounts.get(endpoint) || [];
    requestHistory.push(now);
    
    // Keep only recent requests
    const rateLimit = this.rateLimits.get(endpoint) || this.defaultRateLimit;
    const recentRequests = requestHistory.filter(time => (now - time) < rateLimit.timeWindow);
    this.requestCounts.set(endpoint, recentRequests);
  }

  /**
   * Execute API call with deduplication and rate limiting
   */
  async executeRequest(endpoint, requestFunction, options = {}) {
    const {
      useCache = false,
      cacheTimeout = 5 * 60 * 1000, // 5 minutes default
      forceRefresh = false
    } = options;

    // Check cache first
    if (useCache && !forceRefresh) {
      const cached = this.cache.get(endpoint);
      if (cached && (Date.now() - cached.timestamp) < cacheTimeout) {
        console.log(`📦 Using cached data for ${endpoint}`);
        return cached.data;
      }
    }

    // Check if request is already pending
    if (this.pendingRequests.has(endpoint)) {
      console.log(`⏳ Request to ${endpoint} already pending, waiting for result...`);
      return await this.pendingRequests.get(endpoint);
    }

    // Check rate limiting
    if (!this.isRequestAllowed(endpoint)) {
      throw new Error(`Rate limit exceeded for ${endpoint}`);
    }

    // Record the request
    this.recordRequest(endpoint);

    // Create and execute the request
    const requestPromise = this._executeWithTimeout(requestFunction, endpoint);
    this.pendingRequests.set(endpoint, requestPromise);

    try {
      const result = await requestPromise;
      
      // Cache the result if caching is enabled
      if (useCache) {
        this.cache.set(endpoint, {
          data: result,
          timestamp: Date.now()
        });
      }

      return result;
    } finally {
      // Always remove from pending requests
      this.pendingRequests.delete(endpoint);
    }
  }

  /**
   * Execute request with timeout
   */
  async _executeWithTimeout(requestFunction, endpoint, timeout = 30000) {
    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Request to ${endpoint} timed out after ${timeout}ms`));
      }, timeout);

      try {
        const result = await requestFunction();
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * Clear cache for specific endpoint or all
   */
  clearCache(endpoint = null) {
    if (endpoint) {
      this.cache.delete(endpoint);
      console.log(`🗑️ Cache cleared for ${endpoint}`);
    } else {
      this.cache.clear();
      console.log(`🗑️ All cache cleared`);
    }
  }

  /**
   * Get statistics about API calls
   */
  getStats() {
    return {
      pendingRequests: this.pendingRequests.size,
      cachedEndpoints: this.cache.size,
      trackedEndpoints: this.requestCounts.size,
      requestCounts: Object.fromEntries(
        Array.from(this.requestCounts.entries()).map(([endpoint, requests]) => [
          endpoint,
          requests.length
        ])
      )
    };
  }

  /**
   * Reset all tracking data
   */
  reset() {
    this.pendingRequests.clear();
    this.cache.clear();
    this.requestCounts.clear();
    this.lastRequestTimes.clear();
    console.log('🔄 API Call Manager reset');
  }
}

// Create singleton instance
const apiCallManager = new APICallManager();

// Configure rate limits for specific endpoints
apiCallManager.setRateLimit('/api/landing/data/', {
  maxRequests: 2,
  timeWindow: 60000, // 1 minute
  cooldown: 5000 // 5 seconds between requests
});

apiCallManager.setRateLimit('/api/auctions/', {
  maxRequests: 10,
  timeWindow: 60000, // 1 minute
  cooldown: 1000 // 1 second between requests
});

export default apiCallManager;

/**
 * Helper function to wrap API calls
 */
export const withAPICallManager = (endpoint, requestFunction, options = {}) => {
  return apiCallManager.executeRequest(endpoint, requestFunction, options);
};

/**
 * React hook for API call management
 */
export const useAPICallManager = () => {
  return {
    executeRequest: apiCallManager.executeRequest.bind(apiCallManager),
    clearCache: apiCallManager.clearCache.bind(apiCallManager),
    getStats: apiCallManager.getStats.bind(apiCallManager),
    reset: apiCallManager.reset.bind(apiCallManager)
  };
};
