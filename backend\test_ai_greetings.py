#!/usr/bin/env python3
"""
Test script for AI Chat Greetings and Enhanced Responses
Tests the improved AI chat service with better greeting patterns
"""

import os
import sys
import django
import asyncio
from datetime import datetime, timedelta

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from auction.ai_chat_service import ai_chat_service
from auction.models import Auction, User, Category
from django.utils import timezone
from asgiref.sync import sync_to_async

async def test_ai_greetings():
    """Test various greeting patterns and responses"""
    
    print("🤖 Testing Enhanced AI Chat Greetings")
    print("=" * 50)
    
    # Create test data
    try:
        # Get or create test user
        @sync_to_async
        def get_or_create_user():
            return User.objects.get_or_create(
                username='test_user',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Test',
                    'last_name': 'User'
                }
            )

        @sync_to_async
        def get_or_create_category():
            return Category.objects.get_or_create(
                name='Electronics',
                defaults={'description': 'Electronic items'}
            )

        @sync_to_async
        def get_or_create_auction(user, category):
            return Auction.objects.get_or_create(
                title='Test iPhone 15 Pro',
                defaults={
                    'description': 'Brand new iPhone 15 Pro in excellent condition',
                    'starting_bid': 800.00,
                    'current_bid': 950.00,
                    'start_time': timezone.now() - timedelta(hours=2),
                    'end_time': timezone.now() + timedelta(hours=6),
                    'owner': user,  # Changed from 'seller' to 'owner'
                    'category': 'electronics',  # Use string value instead of object
                    'approved': True
                }
            )

        test_user, created = await get_or_create_user()
        test_category, created = await get_or_create_category()
        test_auction, created = await get_or_create_auction(test_user, test_category)
        
        print(f"✅ Test data ready - Auction: {test_auction.title}")
        print(f"📱 Current bid: ${test_auction.current_bid}")
        print(f"⏰ Time remaining: {test_auction.time_remaining}")
        print()
        
        # Test different greeting patterns
        test_messages = [
            # Basic greetings
            "Hello",
            "Hi there",
            "Hey",
            "Good morning",
            "Good afternoon", 
            "Good evening",
            
            # Casual greetings
            "What's up",
            "Yo",
            "Sup",
            
            # Thank you messages
            "Thank you",
            "Thanks so much",
            "I appreciate your help",
            "You're very helpful",
            "Awesome, thanks!",
            
            # Help requests
            "Can you help me?",
            "I need assistance",
            "What can you do?",
            "How do I use this?",
            
            # Farewell messages
            "Goodbye",
            "Bye",
            "See you later",
            "Have a good day",
            "Take care",
            
            # Price inquiries
            "What's the current price?",
            "How much is this worth?",
            "Price analysis please",
            
            # Bidding help
            "How do I bid?",
            "Bidding tips please",
            "Help me with bidding",
            
            # Quick commands
            "price",
            "info",
            "payment",
            "help"
        ]
        
        print("🧪 Testing AI Responses:")
        print("-" * 30)
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n{i}. 💬 User: \"{message}\"")
            
            # Check if AI should respond
            should_respond = ai_chat_service.should_respond(message)
            print(f"   🤔 Should respond: {should_respond}")
            
            if should_respond:
                # Get AI response
                try:
                    response = await ai_chat_service.generate_response(
                        message, test_auction.id, test_user
                    )
                    
                    if response:
                        # Truncate long responses for display
                        display_response = response[:150] + "..." if len(response) > 150 else response
                        print(f"   🤖 AI: {display_response}")
                    else:
                        print("   ❌ No response generated")
                        
                except Exception as e:
                    print(f"   ❌ Error: {str(e)}")
            else:
                print("   🔇 AI chose not to respond")
            
            print()
        
        print("=" * 50)
        print("✅ AI Greeting Test Complete!")
        
        # Test intent analysis
        print("\n🎯 Testing Intent Analysis:")
        print("-" * 30)
        
        intent_test_messages = [
            ("Hello there!", "greetings"),
            ("Thank you so much", "thanks"),
            ("What's the price?", "price_inquiry"),
            ("How do I bid?", "bidding_help"),
            ("When does it end?", "auction_info"),
            ("Payment methods?", "payment_shipping"),
            ("Can you help me?", "help_requests"),
            ("Goodbye!", "farewell")
        ]
        
        for message, expected_intent in intent_test_messages:
            detected_intent = ai_chat_service._analyze_intent(message)
            status = "✅" if detected_intent == expected_intent else "❌"
            print(f"{status} \"{message}\" -> {detected_intent} (expected: {expected_intent})")
        
        print("\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run the async test
    asyncio.run(test_ai_greetings())
