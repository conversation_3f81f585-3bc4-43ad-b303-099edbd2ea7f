import React, { useState, useEffect } from "react";
import {
  Container,
  Row,
  Col,
  Card,
  Table,
  Badge,
  Button,
  Modal,
  Form,
  Alert,
} from "react-bootstrap";
import {
  FaUsers,
  FaGavel,
  FaDollarSign,
  FaEye,
  FaChartLine,
  FaExclamationTriangle,
} from "react-icons/fa";
import { useAuth } from "../context/AuthContext";
import { formatAuctionPrice } from "../utils/currency";
import FraudAlertDetails from "../components/FraudAlertDetails";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeAuctions: 0,
    totalRevenue: 0,
    totalViews: 0,
  });
  const [recentAuctions, setRecentAuctions] = useState([]);
  const [fraudAlerts, setFraudAlerts] = useState([]);
  const [analyticsData, setAnalyticsData] = useState([]);
  const [showFraudModal, setShowFraudModal] = useState(false);
  const [selectedFraud, setSelectedFraud] = useState(null);
  const [showImageEditModal, setShowImageEditModal] = useState(false);
  const [selectedAuction, setSelectedAuction] = useState(null);
  const [newImageUrl, setNewImageUrl] = useState("");
  const [imageEditLoading, setImageEditLoading] = useState(false);
  const [imageEditMessage, setImageEditMessage] = useState("");

  useEffect(() => {
    if (user?.role === "admin") {
      fetchDashboardData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const fetchDashboardData = async () => {
    try {
      // Fetch dashboard statistics
      const statsResponse = await fetch(
        "http://127.0.0.1:8000/api/analytics/",
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (statsResponse.ok) {
        const analyticsData = await statsResponse.json();
        // Process analytics data to get stats
        processAnalyticsData(analyticsData.results || []);
      }

      // Fetch recent auctions
      const auctionsResponse = await fetch(
        "http://127.0.0.1:8000/api/auctions/?ordering=-created_at&page_size=10",
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );

      if (auctionsResponse.ok) {
        const auctionsData = await auctionsResponse.json();
        setRecentAuctions(auctionsData.results || []);
      }

      // Fetch fraud alerts
      const fraudResponse = await fetch(
        "http://127.0.0.1:8000/api/fraud-detection/?status=pending",
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );

      if (fraudResponse.ok) {
        const fraudData = await fraudResponse.json();
        setFraudAlerts(fraudData.results || []);
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    }
  };

  const processAnalyticsData = (data) => {
    // Process analytics data to extract key metrics
    const userMetrics = data.filter(
      (item) => item.metric_type === "user_engagement"
    );
    const revenueMetrics = data.filter(
      (item) => item.metric_type === "revenue"
    );
    const auctionMetrics = data.filter(
      (item) => item.metric_type === "auction_performance"
    );

    setStats({
      totalUsers:
        userMetrics.find((m) => m.metric_name === "total_users")
          ?.metric_value || 0,
      activeAuctions:
        auctionMetrics.find((m) => m.metric_name === "active_auctions")
          ?.metric_value || 0,
      totalRevenue:
        revenueMetrics.find((m) => m.metric_name === "total_revenue")
          ?.metric_value || 0,
      totalViews:
        auctionMetrics.find((m) => m.metric_name === "total_views")
          ?.metric_value || 0,
    });

    // Prepare chart data
    const chartData = data.slice(0, 7).map((item) => ({
      date: new Date(item.date).toLocaleDateString(),
      value: parseFloat(item.metric_value),
      type: item.metric_type,
    }));
    setAnalyticsData(chartData);
  };

  const handleFraudAction = async (fraudId, action) => {
    try {
      const response = await fetch(
        `http://127.0.0.1:8000/api/fraud-detection/${fraudId}/`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
          body: JSON.stringify({
            status: action,
            resolved_at: new Date().toISOString(),
          }),
        }
      );

      if (response.ok) {
        setFraudAlerts((prev) => prev.filter((alert) => alert.id !== fraudId));
        setShowFraudModal(false);
      }
    } catch (error) {
      console.error("Error updating fraud alert:", error);
    }
  };

  const handleImageEdit = (auction) => {
    setSelectedAuction(auction);
    setNewImageUrl(auction.image || "");
    setImageEditMessage("");
    setShowImageEditModal(true);
  };

  const handleImageUpdate = async () => {
    if (!selectedAuction) return;

    setImageEditLoading(true);
    setImageEditMessage("");

    try {
      const response = await fetch(
        `http://127.0.0.1:8000/api/admin/edit-auction-image/${selectedAuction.id}/`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
          body: JSON.stringify({
            image_url: newImageUrl,
          }),
        }
      );

      const data = await response.json();

      if (response.ok) {
        setImageEditMessage("Image URL updated successfully!");
        // Update the auction in the list
        setRecentAuctions((prev) =>
          prev.map((auction) =>
            auction.id === selectedAuction.id
              ? { ...auction, image: newImageUrl }
              : auction
          )
        );
        setTimeout(() => {
          setShowImageEditModal(false);
        }, 2000);
      } else {
        setImageEditMessage(data.error || "Failed to update image URL");
      }
    } catch (error) {
      console.error("Error updating image:", error);
      setImageEditMessage("Error updating image URL");
    } finally {
      setImageEditLoading(false);
    }
  };

  const StatCard = ({ title, value, icon, color = "primary" }) => (
    <Card className="h-100">
      <Card.Body>
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <h6 className="text-muted mb-1">{title}</h6>
            <h3 className={`text-${color} mb-0`}>{value}</h3>
          </div>
          <div className={`text-${color}`} style={{ fontSize: "2rem" }}>
            {icon}
          </div>
        </div>
      </Card.Body>
    </Card>
  );

  if (user?.role !== "admin") {
    return (
      <Container className="mt-5">
        <div className="text-center">
          <h3>Access Denied</h3>
          <p>You don't have permission to view this page.</p>
        </div>
      </Container>
    );
  }

  return (
    <Container fluid className="mt-4">
      <Row className="mb-4">
        <Col>
          <h2>Admin Dashboard</h2>
          <p className="text-muted">
            Monitor your auction platform performance
          </p>
        </Col>
      </Row>

      {/* Statistics Cards */}
      <Row className="mb-4">
        <Col md={3}>
          <StatCard
            title="Total Users"
            value={stats.totalUsers.toLocaleString()}
            icon={<FaUsers />}
            color="primary"
          />
        </Col>
        <Col md={3}>
          <StatCard
            title="Active Auctions"
            value={stats.activeAuctions.toLocaleString()}
            icon={<FaGavel />}
            color="success"
          />
        </Col>
        <Col md={3}>
          <StatCard
            title="Total Revenue"
            value={formatAuctionPrice(stats.totalRevenue)}
            icon={<FaDollarSign />}
            color="warning"
          />
        </Col>
        <Col md={3}>
          <StatCard
            title="Total Views"
            value={stats.totalViews.toLocaleString()}
            icon={<FaEye />}
            color="info"
          />
        </Col>
      </Row>

      <Row className="mb-4">
        {/* Analytics Chart */}
        <Col md={8}>
          <Card>
            <Card.Header>
              <h5>
                <FaChartLine className="me-2" />
                Analytics Overview
              </h5>
            </Card.Header>
            <Card.Body>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analyticsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#8884d8"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </Card.Body>
          </Card>
        </Col>

        {/* Fraud Alerts */}
        <Col md={4}>
          <Card>
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h5>
                <FaExclamationTriangle className="me-2" />
                Fraud Alerts
              </h5>
              <Badge bg="danger">{fraudAlerts.length}</Badge>
            </Card.Header>
            <Card.Body style={{ maxHeight: "300px", overflowY: "auto" }}>
              {fraudAlerts.length === 0 ? (
                <div className="text-center py-3">
                  <FaExclamationTriangle
                    className="text-muted mb-2"
                    size={24}
                  />
                  <p className="text-muted mb-0">No pending fraud alerts</p>
                  <small className="text-muted">System is secure</small>
                </div>
              ) : (
                fraudAlerts.map((alert) => {
                  const getRiskColor = (score) => {
                    if (score >= 90) return "danger";
                    if (score >= 80) return "warning";
                    if (score >= 60) return "info";
                    return "success";
                  };

                  const getFraudTypeDisplay = (type) => {
                    const typeMap = {
                      suspicious_bidding: {
                        icon: "🎯",
                        title: "Suspicious Bidding",
                      },
                      bot_activity: { icon: "🤖", title: "Bot Activity" },
                      payment_fraud: { icon: "💳", title: "Payment Fraud" },
                      account_takeover: {
                        icon: "🔒",
                        title: "Account Takeover",
                      },
                      fake_listing: { icon: "👁️", title: "Fake Listing" },
                      shill_bidding: { icon: "📈", title: "Shill Bidding" },
                    };
                    return (
                      typeMap[type] || {
                        icon: "⚠️",
                        title: type.replace("_", " "),
                      }
                    );
                  };

                  const typeInfo = getFraudTypeDisplay(alert.fraud_type);
                  const riskColor = getRiskColor(alert.risk_score);

                  return (
                    <div key={alert.id} className="border-bottom pb-2 mb-2">
                      <div className="d-flex justify-content-between align-items-start">
                        <div className="flex-grow-1">
                          <div className="d-flex align-items-center mb-1">
                            <span className="me-2">{typeInfo.icon}</span>
                            <strong>{typeInfo.title}</strong>
                          </div>
                          <div className="d-flex align-items-center mb-1">
                            <Badge bg={riskColor} className="me-2">
                              {alert.risk_score}/100
                            </Badge>
                            <small className="text-muted">
                              User: {alert.user?.username || alert.user}
                            </small>
                          </div>
                          <small className="text-muted">
                            {new Date(
                              alert.detected_at || alert.created_at
                            ).toLocaleDateString()}
                          </small>
                        </div>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => {
                            setSelectedFraud(alert);
                            setShowFraudModal(true);
                          }}
                        >
                          Analyze
                        </Button>
                      </div>
                    </div>
                  );
                })
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Recent Auctions */}
      <Row>
        <Col>
          <Card>
            <Card.Header>
              <h5>Recent Auctions</h5>
            </Card.Header>
            <Card.Body>
              <Table responsive>
                <thead>
                  <tr>
                    <th>Title</th>
                    <th>Owner</th>
                    <th>Current Bid</th>
                    <th>Bids</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {recentAuctions.map((auction) => (
                    <tr key={auction.id}>
                      <td>
                        <div className="d-flex align-items-center">
                          <img
                            src={auction.image || "/placeholder-image.svg"}
                            alt={auction.title}
                            style={{
                              width: "40px",
                              height: "40px",
                              objectFit: "contain",
                              marginRight: "10px",
                              borderRadius: "4px",
                            }}
                          />
                          {auction.title}
                        </div>
                      </td>
                      <td>{auction.owner}</td>
                      <td>{formatAuctionPrice(auction.current_bid)}</td>
                      <td>{auction.total_bids || 0}</td>
                      <td>
                        <Badge bg={auction.is_active ? "success" : "secondary"}>
                          {auction.is_active ? "Active" : "Ended"}
                        </Badge>
                      </td>
                      <td>
                        {new Date(auction.created_at).toLocaleDateString()}
                      </td>
                      <td>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => handleImageEdit(auction)}
                        >
                          Edit Image
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Fraud Alert Modal */}
      <Modal
        show={showFraudModal}
        onHide={() => setShowFraudModal(false)}
        size="lg"
      >
        <Modal.Header closeButton>
          <Modal.Title>
            <FaExclamationTriangle className="me-2 text-warning" />
            Security Alert Analysis
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-0">
          {selectedFraud && <FraudAlertDetails fraud={selectedFraud} />}
        </Modal.Body>
        <Modal.Footer className="bg-light">
          <div className="d-flex justify-content-between w-100">
            <Button
              variant="secondary"
              onClick={() => setShowFraudModal(false)}
            >
              Close
            </Button>
            <div>
              <Button
                variant="success"
                className="me-2"
                onClick={() =>
                  handleFraudAction(selectedFraud?.id, "false_positive")
                }
              >
                Mark as False Positive
              </Button>
              <Button
                variant="danger"
                onClick={() =>
                  handleFraudAction(selectedFraud?.id, "confirmed")
                }
              >
                Confirm Fraud
              </Button>
            </div>
          </div>
        </Modal.Footer>
      </Modal>

      {/* Image Edit Modal */}
      <Modal
        show={showImageEditModal}
        onHide={() => setShowImageEditModal(false)}
      >
        <Modal.Header closeButton>
          <Modal.Title>Edit Auction Image</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedAuction && (
            <div>
              <h6>Auction: {selectedAuction.title}</h6>
              <p className="text-muted">Owner: {selectedAuction.owner}</p>

              {/* Current Image Preview */}
              <div className="mb-3">
                <label className="form-label">Current Image:</label>
                <div className="text-center">
                  <img
                    src={
                      selectedAuction.image &&
                      selectedAuction.image.trim() !== ""
                        ? selectedAuction.image
                        : "/placeholder-image.svg"
                    }
                    alt={selectedAuction.title}
                    style={{
                      maxWidth: "200px",
                      maxHeight: "150px",
                      objectFit: "contain",
                      border: "1px solid #ddd",
                      borderRadius: "4px",
                    }}
                    onError={(e) => {
                      e.target.src = "/placeholder-image.svg";
                    }}
                  />
                </div>
                {selectedAuction.image && (
                  <small className="text-muted d-block mt-1">
                    Current URL: {selectedAuction.image}
                  </small>
                )}
              </div>

              {/* New Image URL Input */}
              <Form.Group className="mb-3">
                <Form.Label>New Image URL:</Form.Label>
                <Form.Control
                  type="url"
                  value={newImageUrl}
                  onChange={(e) => setNewImageUrl(e.target.value)}
                  placeholder="https://example.com/image.jpg"
                />
                <Form.Text className="text-muted">
                  Enter a valid image URL (must start with http:// or https://)
                </Form.Text>
              </Form.Group>

              {/* New Image Preview */}
              {newImageUrl && newImageUrl !== selectedAuction.image && (
                <div className="mb-3">
                  <label className="form-label">Preview New Image:</label>
                  <div className="text-center">
                    <img
                      src={newImageUrl}
                      alt="Preview"
                      style={{
                        maxWidth: "200px",
                        maxHeight: "150px",
                        objectFit: "contain",
                        border: "1px solid #ddd",
                        borderRadius: "4px",
                      }}
                      onError={(e) => {
                        e.target.style.display = "none";
                      }}
                      onLoad={(e) => {
                        e.target.style.display = "block";
                      }}
                    />
                  </div>
                </div>
              )}

              {/* Status Message */}
              {imageEditMessage && (
                <Alert
                  variant={
                    imageEditMessage.includes("success") ? "success" : "danger"
                  }
                >
                  {imageEditMessage}
                </Alert>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="secondary"
            onClick={() => setShowImageEditModal(false)}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleImageUpdate}
            disabled={imageEditLoading || !newImageUrl}
          >
            {imageEditLoading ? "Updating..." : "Update Image"}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default Dashboard;
