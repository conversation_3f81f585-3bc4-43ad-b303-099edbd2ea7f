import React, { useEffect, useState } from "react";
import { usePara<PERSON>, useNavigate, useLocation } from "react-router-dom";
import { fetchBids, placeBid } from "../api/bids";
import { fetchAuction } from "../api/auctions";
import axiosInstance from "../api/axiosInstance";
import { useAuth } from "../context/AuthContext";
import { useWebSocket } from "../components/WebSocketProvider";
import { WebSocketProvider } from "../components/WebSocketProvider";
import ReviewForm from "../components/ReviewForm";
import ReviewList from "../components/ReviewList";
import AIPricePrediction from "../components/AIPricePrediction";
import AuctionChat from "../components/AuctionChat";
import { formatAuctionPrice, formatBidAmount } from "../utils/currency";
import {
  Container,
  Row,
  Col,
  Card,
  Button,
  Badge,
  Carousel,
  Alert,
  Breadcrumb,
} from "react-bootstrap";
import {
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaRegHeart,
  FaEye,
  FaClock,
  FaGavel,
  FaMapMarkerAlt,
  FaShippingFast,
  FaArrowLeft,
  FaHome,
  FaTags,
  FaTrophy,
  FaCreditCard,
} from "react-icons/fa";
import { Link } from "react-router-dom";
import "./AuctionDetail.css";

function AuctionDetailPageWrapper() {
  const { id } = useParams();
  return (
    <WebSocketProvider auctionId={id}>
      <AuctionDetailPage auctionId={id} />
    </WebSocketProvider>
  );
}

function AuctionDetailPage({ auctionId }) {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [auction, setAuction] = useState(null);
  const [bids, setBids] = useState([]);
  const [bidAmount, setBidAmount] = useState("");
  const [autoBid, setAutoBid] = useState(null);
  const [paymentCompleted, setPaymentCompleted] = useState(false);
  const [watchlist, setWatchlist] = useState([]);
  const [isWatched, setIsWatched] = useState(false);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const socket = useWebSocket();

  // Smart back navigation logic
  const getBackNavigationInfo = () => {
    // Check if we have state from navigation (e.g., from category page)
    const fromState = location.state?.from;

    // Check the referrer or previous location
    const referrer = document.referrer;
    const currentOrigin = window.location.origin;

    // Determine where to go back based on various factors
    if (fromState) {
      // If we have explicit state about where we came from
      return {
        path: fromState.path,
        label: fromState.label || "Back",
        icon: fromState.icon || FaArrowLeft,
      };
    } else if (referrer && referrer.startsWith(currentOrigin)) {
      // Parse the referrer to determine the appropriate back action
      const referrerPath = referrer.replace(currentOrigin, "");

      if (referrerPath.includes("/auctions/category/")) {
        const category = referrerPath.split("/auctions/category/")[1];
        return {
          path: referrerPath,
          label: `Back to ${
            category.charAt(0).toUpperCase() + category.slice(1)
          } Auctions`,
          icon: FaTags,
        };
      } else if (
        referrerPath === "/auctions" ||
        referrerPath.startsWith("/auctions?")
      ) {
        return {
          path: "/auctions",
          label: "Back to All Auctions",
          icon: FaGavel,
        };
      } else if (referrerPath === "/home" || referrerPath === "/") {
        return {
          path: "/home",
          label: "Back to Home",
          icon: FaHome,
        };
      }
    }

    // Default fallback
    return {
      path: "/auctions",
      label: "Back to Auctions",
      icon: FaGavel,
    };
  };

  const handleBackNavigation = () => {
    const backInfo = getBackNavigationInfo();

    // Try to use browser back if we came from within the app
    if (
      window.history.length > 1 &&
      document.referrer.startsWith(window.location.origin)
    ) {
      navigate(-1);
    } else {
      // Otherwise navigate to the determined path
      navigate(backInfo.path);
    }
  };

  useEffect(() => {
    fetchAuction(auctionId).then(setAuction);
    fetchBids(auctionId, user?.token)
      .then((bidsData) => {
        // Ensure bids is always an array
        const bidsArray = Array.isArray(bidsData) ? bidsData : [];
        setBids(bidsArray);
      })
      .catch((error) => {
        console.error("Error fetching bids:", error);
        setBids([]); // Set empty array on error
      });

    if (user?.token) {
      axiosInstance
        .get(`autobids/?auction=${auctionId}`)
        .then((response) => {
          // Handle paginated response for autobids
          const autobidsArray = response.data.results || response.data || [];
          if (autobidsArray.length > 0) setAutoBid(autobidsArray[0]);
        })
        .catch((error) => {
          console.error("Error fetching autobids:", error);
        });

      // Load user's watchlist
      axiosInstance
        .get("watchlist/")
        .then((response) => {
          const watchlistData = response.data.results || response.data || [];
          setWatchlist(watchlistData);
          // Check if current auction is in watchlist
          const isCurrentAuctionWatched = watchlistData.some(
            (item) => item.auction === parseInt(auctionId)
          );
          setIsWatched(isCurrentAuctionWatched);
        })
        .catch((error) => {
          console.error("Error fetching watchlist:", error);
          setWatchlist([]);
          setIsWatched(false);
        });
    }
  }, [auctionId, user?.token]);

  useEffect(() => {
    if (!socket) return;
    socket.onmessage = (e) => {
      const data = JSON.parse(e.data);
      setBids((prev) => [
        {
          amount: data.amount,
          user: { username: data.username },
          created_at: data.timestamp,
        },
        ...prev,
      ]);
      setAuction((prev) => ({ ...prev, current_bid: data.amount }));
    };
  }, [socket]);

  const handleBid = async (e) => {
    e.preventDefault();

    if (!user || !user.token) {
      alert("Please log in to place a bid");
      return;
    }

    const amount = parseFloat(bidAmount);

    if (!amount || isNaN(amount)) {
      alert("Please enter a valid bid amount");
      return;
    }

    if (amount <= parseFloat(auction.current_bid)) {
      alert(
        `Bid must be higher than current bid of ${formatAuctionPrice(
          auction.current_bid
        )}`
      );
      return;
    }

    try {
      await placeBid(auction.id, amount, user.token);
      setBidAmount("");

      // Update auction current bid immediately
      setAuction((prev) => ({
        ...prev,
        current_bid: amount,
      }));

      // Refresh bids list
      const updatedBids = await fetchBids(auction.id, user.token);
      setBids(updatedBids);

      alert("Bid placed successfully!");
    } catch (err) {
      console.error("Bid error:", err);
      alert(err.message || "Failed to place bid");
    }
  };

  const handlePayment = async () => {
    const safeBids = Array.isArray(bids) ? bids : [];
    const topBid = safeBids[0];
    if (!topBid) return;

    try {
      await axiosInstance.post(`payments/${topBid.id}/complete/`, {
        transaction_id: "manual-payment",
      });
      alert("Payment successful");
      setPaymentCompleted(true);
    } catch (err) {
      console.error("Payment error:", err);
      alert(err.response?.data?.error || "Payment failed");
    }
  };

  const handleWatchToggle = async () => {
    // Check if user is logged in first
    const token =
      localStorage.getItem("token") || localStorage.getItem("access_token");
    if (!user || !token) {
      alert("Please log in to manage your watchlist");
      return;
    }

    try {
      if (isWatched) {
        // Remove from watchlist
        const watchItem = watchlist.find(
          (item) => item.auction === parseInt(auctionId)
        );
        if (watchItem) {
          await axiosInstance.delete(`watchlist/${watchItem.id}/`);
          setWatchlist((prev) =>
            prev.filter((item) => item.auction !== parseInt(auctionId))
          );
          setIsWatched(false);
          alert("Removed from watchlist");
        }
      } else {
        // Add to watchlist
        const response = await axiosInstance.post("watchlist/", {
          auction: parseInt(auctionId),
        });
        setWatchlist((prev) => [...prev, response.data]);
        setIsWatched(true);
        alert("Added to watchlist");
      }
    } catch (error) {
      console.error("Error toggling watchlist:", error);
      if (error.response?.status === 401) {
        alert("Please log in to manage your watchlist");
      } else {
        alert("Error updating watchlist. Please try again.");
      }
    }
  };

  if (!auction) return <div>Loading auction details...</div>;

  // Ensure bids is always an array before using it
  const safeBids = Array.isArray(bids) ? bids : [];

  const isEnded = new Date(auction.end_time) < new Date();
  const isWinner =
    isEnded &&
    safeBids.length > 0 &&
    safeBids[0].user.username === user?.username;

  // Helper function to get all images for carousel
  const getAllImages = () => {
    const images = [];

    // Add main image if exists
    if (auction.image && auction.image.trim() !== "") {
      images.push(auction.image);
    }

    // Add additional images if they exist
    if (auction.additional_images && Array.isArray(auction.additional_images)) {
      auction.additional_images.forEach((img) => {
        if (img && img.trim() !== "" && !images.includes(img)) {
          images.push(img);
        }
      });
    }

    // If no images, add placeholder
    if (images.length === 0) {
      images.push("/placeholder-image.svg");
    }

    return images;
  };

  const allImages = getAllImages();

  const formatTimeRemaining = (timeRemaining) => {
    if (!timeRemaining) return "Ended";
    const { days, hours, minutes } = timeRemaining;
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const getAuctionTypeColor = (type) => {
    const colors = {
      standard: "primary",
      reserve: "warning",
      buy_now: "success",
      sealed_bid: "info",
      reverse: "danger",
    };
    return colors[type] || "secondary";
  };

  const backInfo = getBackNavigationInfo();

  return (
    <Container className="mt-4 auction-detail-container">
      {/* Back Navigation */}
      <Row className="mb-3">
        <Col>
          <div className="auction-detail-navigation">
            <div className="d-flex align-items-center justify-content-between">
              {/* Back Button */}
              <Button
                variant="outline-secondary"
                size="sm"
                onClick={handleBackNavigation}
                className="d-flex align-items-center back-button"
              >
                {React.createElement(backInfo.icon, { className: "me-2" })}
                {backInfo.label}
              </Button>

              {/* Breadcrumb Navigation */}
              <Breadcrumb className="mb-0 auction-breadcrumb">
                <Breadcrumb.Item
                  onClick={() => navigate("/home")}
                  style={{ cursor: "pointer" }}
                >
                  <FaHome className="me-1" />
                  Home
                </Breadcrumb.Item>
                <Breadcrumb.Item
                  onClick={() => navigate("/auctions")}
                  style={{ cursor: "pointer" }}
                >
                  <FaGavel className="me-1" />
                  Auctions
                </Breadcrumb.Item>
                {auction?.category && (
                  <Breadcrumb.Item
                    onClick={() =>
                      navigate(`/auctions/category/${auction.category}`)
                    }
                    style={{ cursor: "pointer" }}
                  >
                    <FaTags className="me-1" />
                    {auction.category.charAt(0).toUpperCase() +
                      auction.category.slice(1)}
                  </Breadcrumb.Item>
                )}
                <Breadcrumb.Item active>
                  {auction?.title || "Auction Details"}
                </Breadcrumb.Item>
              </Breadcrumb>
            </div>
          </div>
        </Col>
      </Row>

      <Row>
        <Col lg={8}>
          {/* Image Carousel Section */}
          <Card className="mb-4">
            <Card.Body className="p-0">
              <Carousel
                interval={null}
                indicators={allImages.length > 1}
                controls={allImages.length > 1}
                fade={false}
                touch={true}
              >
                {allImages.map((image, index) => (
                  <Carousel.Item key={index}>
                    <div
                      className="carousel-image-container"
                      style={{
                        height: "400px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        backgroundColor: "#f8f9fa",
                        borderRadius: "0.375rem",
                        overflow: "hidden",
                      }}
                    >
                      <img
                        className="carousel-image"
                        src={image}
                        alt={`${auction.title} ${index + 1}`}
                        style={{
                          maxWidth: "100%",
                          maxHeight: "100%",
                          width: "auto",
                          height: "auto",
                          objectFit: "contain",
                          borderRadius: "0.375rem",
                          imageRendering: "high-quality",
                          filter: "none",
                          backfaceVisibility: "hidden",
                          transform: "translateZ(0)",
                          willChange: "auto",
                          opacity: "1",
                          transition: "opacity 0.3s ease-in-out",
                        }}
                        onError={(e) => {
                          e.target.src = "/placeholder-image.svg";
                        }}
                        onLoad={(e) => {
                          // Ensure image is fully loaded and rendered
                          e.target.style.opacity = "1";
                        }}
                        loading="eager"
                        decoding="sync"
                      />
                    </div>
                    {allImages.length > 1 && (
                      <Carousel.Caption
                        className="bg-dark bg-opacity-75 rounded"
                        style={{
                          bottom: "10px",
                          left: "50%",
                          right: "auto",
                          transform: "translateX(-50%)",
                          width: "auto",
                          padding: "5px 15px",
                        }}
                      >
                        <p className="mb-0 small">
                          Image {index + 1} of {allImages.length}
                        </p>
                      </Carousel.Caption>
                    )}
                  </Carousel.Item>
                ))}
              </Carousel>
            </Card.Body>
          </Card>

          {/* Auction Details Section */}
          <Card className="mb-4">
            <Card.Header className="d-flex justify-content-between align-items-center">
              <div>
                <h2 className="mb-1">{auction.title}</h2>
                <div className="d-flex align-items-center gap-2">
                  <Badge bg={getAuctionTypeColor(auction.auction_type)}>
                    {auction.auction_type?.replace("_", " ").toUpperCase() ||
                      "STANDARD"}
                  </Badge>
                  {auction.featured && <Badge bg="danger">FEATURED</Badge>}
                  <Badge bg={auction.is_active ? "success" : "secondary"}>
                    {auction.is_active ? "ACTIVE" : "ENDED"}
                  </Badge>
                </div>
              </div>
              <div className="text-end d-flex align-items-center gap-3">
                <div className="text-muted small">
                  <FaEye className="me-1" />
                  {auction.views_count || 0} views
                </div>
                {/* Watchlist Button */}
                {user && (
                  <Button
                    variant={isWatched ? "danger" : "outline-danger"}
                    size="sm"
                    onClick={handleWatchToggle}
                    className="d-flex align-items-center"
                    title={
                      isWatched ? "Remove from watchlist" : "Add to watchlist"
                    }
                  >
                    {isWatched ? (
                      <FaHeart className="me-1" />
                    ) : (
                      <FaRegHeart className="me-1" />
                    )}
                    {isWatched ? "Watching" : "Watch"}
                  </Button>
                )}
              </div>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={8}>
                  <h6>Description</h6>
                  <p className="text-muted">{auction.description}</p>

                  {auction.location && (
                    <p className="mb-2">
                      <FaMapMarkerAlt className="me-2 text-muted" />
                      <strong>Location:</strong> {auction.location}
                    </p>
                  )}

                  {auction.condition && (
                    <p className="mb-2">
                      <strong>Condition:</strong> {auction.condition}
                    </p>
                  )}

                  {auction.shipping_cost > 0 && (
                    <p className="mb-2">
                      <FaShippingFast className="me-2 text-muted" />
                      <strong>Shipping:</strong>{" "}
                      {formatAuctionPrice(auction.shipping_cost)}
                    </p>
                  )}
                </Col>
                <Col md={4}>
                  <div className="border rounded p-3 bg-light">
                    <div className="text-center mb-3">
                      <h4 className="text-success mb-1">
                        {formatAuctionPrice(auction.current_bid)}
                      </h4>
                      <small className="text-muted">Current Bid</small>
                    </div>

                    {auction.reserve_price && !auction.reserve_met && (
                      <div className="text-warning text-center mb-2">
                        <small>
                          Reserve not met (
                          {formatAuctionPrice(auction.reserve_price)})
                        </small>
                      </div>
                    )}

                    {auction.buy_now_price && (
                      <div className="text-center mb-3">
                        <h5 className="text-primary mb-1">
                          {formatAuctionPrice(auction.buy_now_price)}
                        </h5>
                        <small className="text-muted">Buy It Now</small>
                      </div>
                    )}

                    <div className="d-flex justify-content-between text-muted small mb-2">
                      <span>
                        <FaGavel className="me-1" />
                        {auction.total_bids || 0} bids
                      </span>
                      <span>
                        <FaHeart className="me-1" />
                        {auction.watchers_count || 0} watching
                      </span>
                    </div>

                    <div className="text-center">
                      <div className="text-muted small mb-1">
                        <FaClock className="me-1" />
                        {auction.is_active ? "Ends in:" : "Ended"}
                      </div>
                      <div
                        className={`fw-bold ${
                          auction.is_active ? "text-danger" : "text-muted"
                        }`}
                      >
                        {formatTimeRemaining(auction.time_remaining)}
                      </div>
                    </div>
                  </div>
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* AutoBid Alert */}
          {autoBid && (
            <Alert variant="info">
              <strong>AutoBid Active:</strong> Max bid:{" "}
              {formatAuctionPrice(autoBid.max_bid)}
            </Alert>
          )}

          {/* Bidding Section */}
          {user && !isEnded && (
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">Place Your Bid</h5>
              </Card.Header>
              <Card.Body>
                <form onSubmit={handleBid}>
                  <Row>
                    <Col md={8}>
                      <div className="mb-3">
                        <label className="form-label">Your Bid Amount:</label>
                        <div className="input-group">
                          <span className="input-group-text">$</span>
                          <input
                            type="number"
                            step="0.01"
                            className="form-control"
                            value={bidAmount}
                            onChange={(e) => setBidAmount(e.target.value)}
                            placeholder={`Minimum: ${formatAuctionPrice(
                              auction.current_bid + 1
                            )}`}
                            required
                          />
                        </div>
                        <small className="text-muted">
                          Minimum bid:{" "}
                          {formatAuctionPrice(auction.current_bid + 1)}
                        </small>
                      </div>
                    </Col>
                    <Col md={4} className="d-flex align-items-end">
                      <Button
                        type="submit"
                        variant="primary"
                        size="lg"
                        className="w-100"
                      >
                        <FaGavel className="me-2" />
                        Place Bid
                      </Button>
                    </Col>
                  </Row>
                </form>
              </Card.Body>
            </Card>
          )}

          {/* Winner Payment Section */}
          {isWinner && !paymentCompleted && (
            <div className="mb-4">
              <Alert variant="success">
                <FaTrophy className="me-2" />
                <strong>Congratulations! You won this auction!</strong>
                <br />
                <small>
                  Please complete your payment within 24 hours to secure your
                  item.
                </small>
              </Alert>

              <Card className="border-warning">
                <Card.Header className="bg-warning text-dark">
                  <FaClock className="me-2" />
                  <strong>Payment Required</strong>
                </Card.Header>
                <Card.Body>
                  <Row className="align-items-center">
                    <Col md={8}>
                      <h5 className="mb-1">
                        Amount Due:{" "}
                        <span className="text-success">
                          {formatAuctionPrice(auction.current_bid)}
                        </span>
                      </h5>
                      <p className="text-muted mb-0">
                        Complete your payment to finalize this purchase
                      </p>
                    </Col>
                    <Col md={4} className="text-end">
                      <Button
                        as={Link}
                        to={`/payment/${auction.id}`}
                        variant="success"
                        size="lg"
                        className="w-100"
                      >
                        <FaCreditCard className="me-2" />
                        Pay Now
                      </Button>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
            </div>
          )}

          {/* Review Section */}
          <Card className="mb-4">
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0">Reviews & Feedback</h5>
              {user && isEnded && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={() => setShowReviewForm(true)}
                >
                  Write a Review
                </Button>
              )}
            </Card.Header>
            <Card.Body>
              <ReviewList auctionId={auction.id} showStats={true} />
            </Card.Body>
          </Card>

          {/* Review Form Modal */}
          <ReviewForm
            show={showReviewForm}
            onHide={() => setShowReviewForm(false)}
            auctionId={auction.id}
            revieweeId={auction.owner}
            revieweeName={auction.owner_name || "Seller"}
            auctionTitle={auction.title}
            onReviewSubmitted={() => {
              setShowReviewForm(false);
              // Refresh the review list by re-rendering
              window.location.reload();
            }}
          />

          {/* Bid History Section */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Bid History ({safeBids.length} bids)</h5>
            </Card.Header>
            <Card.Body>
              {safeBids.length === 0 ? (
                <div className="text-center py-4 text-muted">
                  <FaGavel size={48} className="mb-3 opacity-50" />
                  <p>No bids yet. Be the first to bid!</p>
                </div>
              ) : (
                <div className="list-group list-group-flush">
                  {safeBids.map((bid, index) => (
                    <div
                      key={index}
                      className="list-group-item d-flex justify-content-between align-items-center"
                    >
                      <div>
                        <strong className="text-success">
                          {formatBidAmount(bid.amount)}
                        </strong>
                        <span className="text-muted ms-2">
                          by <strong>{bid.user.username}</strong>
                        </span>
                      </div>
                      <small className="text-muted">
                        {new Date(bid.created_at).toLocaleString()}
                      </small>
                    </div>
                  ))}
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* AI & Chat Sidebar */}
        <Col lg={4}>
          {/* AI Price Prediction */}
          <AIPricePrediction
            auctionId={auction.id}
            auctionTitle={auction.title}
          />

          {/* Real-time Chat */}
          <AuctionChat
            auctionId={auction.id}
            auctionTitle={auction.title}
            currentUser={user?.username || "Guest"}
          />
        </Col>
      </Row>
    </Container>
  );
}

export default AuctionDetailPageWrapper;
