#!/usr/bin/env python3
"""
Comprehensive Fraud Detection Demo Data Creator
Creates persistent dummy users and fraud scenarios for project presentation
"""

import os
import sys
import django
from datetime import timedelta
import random

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from auction.models import Auction, Bid, FraudDetection, Category
from django.utils import timezone

class FraudDemoDataCreator:
    def __init__(self):
        self.dummy_users = []
        self.fraud_cases = []
        
    def create_dummy_users(self):
        """Create dummy users for fraud detection scenarios"""
        print("👥 Creating dummy users for fraud detection...")
        
        dummy_user_data = [
            {
                'username': 'suspicious_bidder_1',
                'email': '<EMAIL>',
                'first_name': 'John',
                'last_name': 'Suspicious',
                'password': 'demo123456'
            },
            {
                'username': 'bot_account_2',
                'email': '<EMAIL>', 
                'first_name': 'Bot',
                'last_name': 'Account',
                'password': 'demo123456'
            },
            {
                'username': 'fake_seller_3',
                'email': '<EMAIL>',
                'first_name': 'Fake',
                'last_name': 'Seller',
                'password': 'demo123456'
            },
            {
                'username': 'payment_fraudster_4',
                'email': '<EMAIL>',
                'first_name': 'Payment',
                'last_name': 'Fraudster', 
                'password': 'demo123456'
            },
            {
                'username': 'account_hijacker_5',
                'email': '<EMAIL>',
                'first_name': 'Account',
                'last_name': 'Hijacker',
                'password': 'demo123456'
            },
            {
                'username': 'shill_bidder_6',
                'email': '<EMAIL>',
                'first_name': 'Shill',
                'last_name': 'Bidder',
                'password': 'demo123456'
            }
        ]
        
        for user_data in dummy_user_data:
            # Delete existing user if exists
            User.objects.filter(username=user_data['username']).delete()
            
            # Create new user
            user = User.objects.create_user(
                username=user_data['username'],
                email=user_data['email'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name'],
                password=user_data['password']
            )
            self.dummy_users.append(user)
            print(f"   ✅ Created: {user.username}")
            
        print(f"📊 Created {len(self.dummy_users)} dummy users")
        
    def create_fraud_scenarios(self):
        """Create comprehensive fraud detection scenarios"""
        print("\n🚨 Creating fraud detection scenarios...")
        
        # Clear existing fraud cases from dummy users
        FraudDetection.objects.filter(user__username__in=[u.username for u in self.dummy_users]).delete()
        
        # Get some real auctions for fraud scenarios
        auctions = list(Auction.objects.all()[:10])
        
        fraud_scenarios = [
            # Scenario 1: Suspicious Bidding Pattern
            {
                'user': self.dummy_users[0],  # suspicious_bidder_1
                'auction': auctions[0] if auctions else None,
                'fraud_type': 'suspicious_bidding',
                'risk_score': 87,
                'status': 'pending',
                'details': {
                    'rapid_consecutive_bids': True,
                    'bid_count_in_last_hour': 15,
                    'unusual_timing_pattern': True,
                    'bid_increment_pattern': 'suspicious',
                    'time_between_bids_seconds': [12, 8, 15, 9, 11],
                    'detection_reason': 'Multiple rapid bids with consistent timing pattern',
                    'confidence_level': 'high'
                }
            },
            
            # Scenario 2: Bot Activity Detection
            {
                'user': self.dummy_users[1],  # bot_account_2
                'auction': auctions[1] if auctions else None,
                'fraud_type': 'bot_activity',
                'risk_score': 94,
                'status': 'pending',
                'details': {
                    'automated_behavior': True,
                    'consistent_response_time': True,
                    'no_human_delays': True,
                    'captcha_failures': 8,
                    'user_agent_suspicious': True,
                    'bid_frequency_per_minute': 25,
                    'detection_reason': 'Automated bidding behavior detected',
                    'confidence_level': 'very_high'
                }
            },
            
            # Scenario 3: Fake Listing Detection
            {
                'user': self.dummy_users[2],  # fake_seller_3
                'auction': auctions[2] if auctions else None,
                'fraud_type': 'fake_listing',
                'risk_score': 76,
                'status': 'pending',
                'details': {
                    'stock_photos_detected': True,
                    'price_too_low_for_item': True,
                    'vague_description': True,
                    'new_seller_account': True,
                    'no_seller_history': True,
                    'similar_listings_found': 3,
                    'detection_reason': 'Multiple indicators of fake listing',
                    'confidence_level': 'medium_high'
                }
            },
            
            # Scenario 4: Payment Fraud
            {
                'user': self.dummy_users[3],  # payment_fraudster_4
                'auction': auctions[3] if auctions else None,
                'fraud_type': 'payment_fraud',
                'risk_score': 91,
                'status': 'pending',
                'details': {
                    'stolen_card_indicators': True,
                    'billing_address_mismatch': True,
                    'multiple_failed_payments': True,
                    'high_risk_country': False,
                    'velocity_check_failed': True,
                    'cvv_failures': 4,
                    'detection_reason': 'Payment fraud indicators detected',
                    'confidence_level': 'very_high'
                }
            },
            
            # Scenario 5: Account Takeover
            {
                'user': self.dummy_users[4],  # account_hijacker_5
                'auction': auctions[4] if auctions else None,
                'fraud_type': 'account_takeover',
                'risk_score': 89,
                'status': 'pending',
                'details': {
                    'login_from_new_location': True,
                    'unusual_bidding_behavior': True,
                    'password_recently_changed': True,
                    'different_payment_method': True,
                    'suspicious_ip': '**************',
                    'login_time_unusual': True,
                    'detection_reason': 'Account takeover indicators detected',
                    'confidence_level': 'high'
                }
            },
            
            # Scenario 6: Shill Bidding
            {
                'user': self.dummy_users[5],  # shill_bidder_6
                'auction': auctions[5] if auctions else None,
                'fraud_type': 'suspicious_bidding',
                'risk_score': 83,
                'status': 'pending',
                'details': {
                    'shill_bidding_pattern': True,
                    'bidding_on_same_seller_items': True,
                    'artificial_price_inflation': True,
                    'coordinated_bidding': True,
                    'seller_connection_suspected': True,
                    'bid_timing_coordination': True,
                    'detection_reason': 'Shill bidding pattern detected',
                    'confidence_level': 'high'
                }
            }
        ]
        
        # Create fraud detection records
        for scenario in fraud_scenarios:
            fraud = FraudDetection.objects.create(
                user=scenario['user'],
                auction=scenario['auction'],
                fraud_type=scenario['fraud_type'],
                risk_score=scenario['risk_score'],
                status=scenario['status'],
                details=scenario['details']
            )
            self.fraud_cases.append(fraud)
            print(f"   🚨 Created: {fraud.fraud_type} (Risk: {fraud.risk_score}/100) - {fraud.user.username}")
            
        print(f"📊 Created {len(self.fraud_cases)} fraud detection scenarios")
        
    def create_supporting_bids(self):
        """Create supporting bid data for fraud scenarios"""
        print("\n💰 Creating supporting bid data...")
        
        # Get some auctions
        auctions = list(Auction.objects.all()[:6])
        
        if not auctions:
            print("❌ No auctions found - skipping bid creation")
            return
            
        bid_count = 0
        
        # Create suspicious bidding patterns
        for i, user in enumerate(self.dummy_users[:3]):  # First 3 users
            if i < len(auctions):
                auction = auctions[i]
                
                # Create multiple rapid bids
                for j in range(5):
                    bid_amount = auction.current_bid + (j + 1) * 10
                    bid = Bid.objects.create(
                        auction=auction,
                        user=user,
                        amount=bid_amount
                    )
                    # Update auction current bid
                    auction.current_bid = bid_amount
                    auction.save()
                    bid_count += 1
                    
        print(f"   ✅ Created {bid_count} supporting bids")
        
    def display_summary(self):
        """Display comprehensive summary"""
        print("\n" + "="*60)
        print("🎉 FRAUD DETECTION DEMO DATA CREATED SUCCESSFULLY!")
        print("="*60)
        
        print(f"\n👥 DUMMY USERS CREATED: {len(self.dummy_users)}")
        for user in self.dummy_users:
            print(f"   - {user.username} ({user.first_name} {user.last_name})")
            
        print(f"\n🚨 FRAUD SCENARIOS CREATED: {len(self.fraud_cases)}")
        for fraud in self.fraud_cases:
            print(f"   - {fraud.fraud_type}: Risk {fraud.risk_score}/100 ({fraud.user.username})")
            
        print(f"\n📊 SYSTEM TOTALS:")
        print(f"   Total Users: {User.objects.count()}")
        print(f"   Total Fraud Cases: {FraudDetection.objects.count()}")
        print(f"   Pending Fraud Cases: {FraudDetection.objects.filter(status='pending').count()}")
        
        print(f"\n🎯 PRESENTATION READY!")
        print("   - Login to admin dashboard to see fraud alerts")
        print("   - All fraud cases are in 'pending' status for demonstration")
        print("   - Dummy users can be used for testing fraud resolution")
        
def main():
    """Main execution function"""
    print("🚀 STARTING FRAUD DETECTION DEMO DATA CREATION")
    print("="*60)
    
    creator = FraudDemoDataCreator()
    
    try:
        creator.create_dummy_users()
        creator.create_fraud_scenarios()
        creator.create_supporting_bids()
        creator.display_summary()
        
    except Exception as e:
        print(f"❌ Error creating fraud demo data: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
