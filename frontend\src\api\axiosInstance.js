import axios from "axios";

// Use different base URLs for development and production
const getBaseURL = () => {
  if (process.env.NODE_ENV === "development") {
    // Check if we're running on a non-standard port (like 3001)
    const currentPort = window.location.port;
    if (currentPort && currentPort !== "3000") {
      // If running on non-standard port, directly connect to backend
      return "http://127.0.0.1:8000/api/";
    }
    // Standard development setup with proxy
    return "/api/";
  } else {
    // In production, use relative URL (proxy will handle it)
    return "/api/";
  }
};

const axiosInstance = axios.create({
  baseURL: getBaseURL(),
  headers: {
    "Content-Type": "application/json",
  },
  timeout: 10000,
});

// Request interceptor to add authentication token
axiosInstance.interceptors.request.use(
  (config) => {
    // Reduced logging - only log important requests
    if (
      !config.url?.includes("notifications/") &&
      !config.url?.includes("admin/users/") &&
      !config.url?.includes("health/")
    ) {
      console.log(`📤 Making request to: ${config.url}`);
    }

    // List of endpoints that don't require authentication
    const publicEndpoints = [
      "analytics/",
      "analytics/dashboard/",
      "analytics-dashboard/",
      "analytics-test/",
      "categories/",
      "search/filters/",
      "featured-auctions/",
      "auctions/featured/",
      "auth/register/",
      "auth/login/",
      "auth/password-reset/",
      "contact-messages/",
      "register/",
      "login/",
    ];

    // Check if this is a public endpoint
    const isPublicEndpoint = publicEndpoints.some(
      (endpoint) => config.url && config.url.includes(endpoint)
    );

    // Special handling for auctions endpoint - GET is public, POST/PUT/PATCH/DELETE require auth
    const isAuctionsEndpoint = config.url && config.url.includes("auctions/");
    const isReadOnlyRequest =
      config.method && config.method.toLowerCase() === "get";

    // Special handling for chat endpoints - always require auth for POST/PUT/PATCH/DELETE
    const isChatEndpoint =
      config.url &&
      (config.url.includes("chat-messages/") ||
        config.url.includes("chat-rooms/"));
    const isWriteOperation =
      config.method &&
      !["get", "head", "options"].includes(config.method.toLowerCase());

    // Only add auth token for non-public endpoints OR for write operations on auctions OR for chat operations
    if (
      !isPublicEndpoint ||
      (isAuctionsEndpoint && !isReadOnlyRequest) ||
      (isChatEndpoint && isWriteOperation)
    ) {
      const token =
        localStorage.getItem("token") || localStorage.getItem("access_token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    return config;
  },
  (error) => {
    console.error("❌ Request error:", error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle responses and errors
axiosInstance.interceptors.response.use(
  (response) => {
    // Only log non-routine responses to reduce console spam
    if (
      !response.config.url?.includes("notifications/") &&
      !response.config.url?.includes("admin/users/")
    ) {
      // Special logging for landing data to help debug excessive calls
      if (response.config.url?.includes("landing/data/")) {
        console.log(
          `🚨 LANDING DATA RESPONSE: ${response.status} ${
            response.config.url
          } at ${new Date().toISOString()}`
        );
      } else {
        console.log(
          `✅ Response success: ${response.status} ${response.config.url}`
        );
      }
    }
    return response;
  },
  (error) => {
    console.error(
      `❌ Response error: ${error.response?.status} ${error.config?.url}`,
      error.response?.data
    );

    if (error.response?.status === 401) {
      // Check if this is a protected endpoint that actually requires auth
      const protectedEndpoints = [
        "notifications/",
        "admin/",
        "profile/",
        "autobids/",
        "chat-messages/",
        "chat-rooms/",
        "bids/",
        "payments/",
      ];

      const isProtectedEndpoint = protectedEndpoints.some((endpoint) =>
        error.config?.url?.includes(endpoint)
      );

      // Only handle auth errors for protected endpoints
      if (isProtectedEndpoint) {
        console.log("🔐 Authentication required for protected endpoint");
        localStorage.removeItem("token");
        localStorage.removeItem("access_token");
        localStorage.removeItem("auction_loggedin_user");

        // Only redirect if not already on login page and not in the middle of navigation
        if (
          !window.location.pathname.includes("/login") &&
          !window.location.pathname.includes("/register")
        ) {
          // Use a timeout to prevent immediate redirect loops
          setTimeout(() => {
            window.location.href = "/login";
          }, 100);
        }
      } else {
        // For public endpoints, just log the error but don't redirect
        console.log("ℹ️ 401 on public endpoint, ignoring:", error.config?.url);
      }
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
