"""
Stripe Payment Views
Handles Stripe payment processing, webhooks, and transactions
"""

import json
import logging

import stripe
from django.conf import settings
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response

from .models import Auction, Payment
from .payment_gateways import PaymentGatewayFactory, convert_currency
from .serializers import PaymentSerializer

logger = logging.getLogger(__name__)

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


@api_view(["POST"])
@permission_classes([AllowAny])
def create_stripe_payment_intent(request):
    """Create Stripe Payment Intent for auction payment"""
    try:
        auction_id = request.data.get("auction_id")
        amount = request.data.get("amount")
        currency = request.data.get("currency", "usd")

        if not auction_id or not amount:
            return Response(
                {"error": "Auction ID and amount are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        auction = get_object_or_404(Auction, id=auction_id)

        # Validate amount
        if float(amount) <= 0:
            return Response(
                {"error": "Invalid amount"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Create payment record
        payment = Payment.objects.create(
            user=request.user if request.user.is_authenticated else None,
            auction=auction,
            amount=amount,
            currency=currency.upper(),
            payment_method="stripe",
            payment_status="pending",
        )

        # Create Stripe Payment Intent
        gateway = PaymentGatewayFactory.get_gateway("stripe")
        intent_result = gateway.create_payment_intent(
            amount=amount,
            currency=currency,
            metadata={
                "auction_id": auction_id,
                "payment_id": payment.id,
                "auction_title": auction.title,
            },
        )

        if intent_result["success"]:
            # Update payment with intent details
            payment.gateway_payment_id = intent_result["payment_intent_id"]
            payment.save()

            return Response(
                {
                    "success": True,
                    "client_secret": intent_result["client_secret"],
                    "payment_intent_id": intent_result["payment_intent_id"],
                    "amount": intent_result["amount"],
                    "currency": intent_result["currency"],
                    "payment_id": payment.id,
                    "auction_title": auction.title,
                    "publishable_key": settings.STRIPE_PUBLISHABLE_KEY,
                }
            )
        else:
            payment.payment_status = "failed"
            payment.failure_reason = intent_result.get(
                "error", "Payment Intent creation failed"
            )
            payment.save()

            return Response(
                {
                    "error": intent_result.get(
                        "error", "Failed to create payment intent"
                    )
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    except Exception as e:
        logger.error(f"Stripe Payment Intent creation error: {str(e)}")
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def create_stripe_checkout_session(request):
    """Create Stripe Checkout Session for auction payment"""
    try:
        auction_id = request.data.get("auction_id")
        amount = request.data.get("amount")
        currency = request.data.get("currency", "usd")

        if not auction_id or not amount:
            return Response(
                {"error": "Auction ID and amount are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        auction = get_object_or_404(Auction, id=auction_id)

        # Create payment record
        payment = Payment.objects.create(
            user=request.user if request.user.is_authenticated else None,
            auction=auction,
            amount=amount,
            currency=currency.upper(),
            payment_method="stripe",
            payment_status="pending",
        )

        # Create Stripe Checkout Session
        gateway = PaymentGatewayFactory.get_gateway("stripe")
        session_result = gateway.create_checkout_session(
            amount=amount,
            currency=currency,
            success_url=f"{settings.STRIPE_SUCCESS_URL}?session_id={{CHECKOUT_SESSION_ID}}",
            cancel_url=f"{settings.STRIPE_CANCEL_URL}?payment_id={payment.id}",
            metadata={
                "auction_id": auction_id,
                "payment_id": payment.id,
                "auction_title": auction.title,
            },
        )

        if session_result["success"]:
            # Update payment with session details
            payment.gateway_order_id = session_result["session_id"]
            payment.save()

            return Response(
                {
                    "success": True,
                    "session_id": session_result["session_id"],
                    "checkout_url": session_result["checkout_url"],
                    "payment_id": payment.id,
                }
            )
        else:
            payment.payment_status = "failed"
            payment.failure_reason = session_result.get(
                "error", "Checkout session creation failed"
            )
            payment.save()

            return Response(
                {
                    "error": session_result.get(
                        "error", "Failed to create checkout session"
                    )
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    except Exception as e:
        logger.error(f"Stripe Checkout Session creation error: {str(e)}")
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def verify_stripe_payment(request):
    """Verify Stripe payment"""
    try:
        payment_intent_id = request.data.get("payment_intent_id")
        payment_id = request.data.get("payment_id")

        if not payment_intent_id or not payment_id:
            return Response(
                {"error": "Payment Intent ID and Payment ID are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        payment = get_object_or_404(Payment, id=payment_id)

        # Verify payment with Stripe
        gateway = PaymentGatewayFactory.get_gateway("stripe")
        verify_result = gateway.verify_payment(payment_intent_id)

        if verify_result["success"] and verify_result["verified"]:
            # Update payment status
            payment.payment_status = "completed"
            payment.gateway_payment_id = payment_intent_id
            payment.payment_date = timezone.now()
            payment.save()

            # Update auction winner if this is the winning bid
            auction = payment.auction
            if auction.current_bid == payment.amount:
                auction.winner = payment.user
                auction.save()

            return Response(
                {
                    "success": True,
                    "payment": PaymentSerializer(payment).data,
                    "message": "Payment verified successfully",
                }
            )
        else:
            payment.payment_status = "failed"
            payment.failure_reason = verify_result.get(
                "error", "Payment verification failed"
            )
            payment.save()

            return Response(
                {"error": verify_result.get("error", "Payment verification failed")},
                status=status.HTTP_400_BAD_REQUEST,
            )

    except Exception as e:
        logger.error(f"Stripe payment verification error: {str(e)}")
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@csrf_exempt
@api_view(["POST", "GET"])
@permission_classes([AllowAny])
def stripe_webhook(request):
    """Handle Stripe webhooks"""

    # Handle GET request for webhook URL verification
    if request.method == "GET":
        return JsonResponse(
            {
                "status": "Stripe webhook endpoint is active",
                "timestamp": timezone.now().isoformat(),
                "endpoint": "/api/payments/stripe/webhook/",
            }
        )

    payload = request.body
    sig_header = request.META.get("HTTP_STRIPE_SIGNATURE")
    endpoint_secret = settings.STRIPE_WEBHOOK_SECRET

    # Log webhook attempt
    logger.info(f"Received webhook with signature: {sig_header}")

    try:
        event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
        logger.info(f"Webhook event verified: {event['type']}")
    except ValueError as e:
        logger.error(f"Invalid payload: {e}")
        return HttpResponse("Invalid payload", status=400)
    except stripe.error.SignatureVerificationError as e:
        logger.error(f"Invalid signature: {e}")
        return HttpResponse("Invalid signature", status=400)
    except Exception as e:
        logger.error(f"Webhook error: {e}")
        return HttpResponse("Webhook error", status=400)

    # Handle the event
    try:
        if event["type"] == "payment_intent.succeeded":
            payment_intent = event["data"]["object"]
            handle_payment_success(payment_intent)
        elif event["type"] == "payment_intent.payment_failed":
            payment_intent = event["data"]["object"]
            handle_payment_failure(payment_intent)
        elif event["type"] == "checkout.session.completed":
            session = event["data"]["object"]
            handle_checkout_session_completed(session)
        else:
            logger.info(f"Unhandled event type: {event['type']}")

        return HttpResponse("Webhook handled successfully", status=200)
    except Exception as e:
        logger.error(f"Error handling webhook event: {e}")
        return HttpResponse("Error processing webhook", status=500)


def handle_payment_success(payment_intent):
    """Handle successful payment"""
    try:
        payment_id = payment_intent["metadata"].get("payment_id")
        if payment_id:
            payment = Payment.objects.get(id=payment_id)
            payment.payment_status = "completed"
            payment.gateway_payment_id = payment_intent["id"]
            payment.payment_date = timezone.now()
            payment.save()

            # Update auction winner
            auction = payment.auction
            if auction.current_bid == payment.amount:
                auction.winner = payment.user
                auction.save()

            logger.info(f"Payment {payment_id} marked as completed")
    except Payment.DoesNotExist:
        logger.error(f"Payment not found for payment_intent: {payment_intent['id']}")
    except Exception as e:
        logger.error(f"Error handling payment success: {str(e)}")


def handle_payment_failure(payment_intent):
    """Handle failed payment"""
    try:
        payment_id = payment_intent["metadata"].get("payment_id")
        if payment_id:
            payment = Payment.objects.get(id=payment_id)
            payment.payment_status = "failed"
            payment.failure_reason = "Payment failed via webhook"
            payment.save()

            logger.info(f"Payment {payment_id} marked as failed")
    except Payment.DoesNotExist:
        logger.error(f"Payment not found for payment_intent: {payment_intent['id']}")
    except Exception as e:
        logger.error(f"Error handling payment failure: {str(e)}")


def handle_checkout_session_completed(session):
    """Handle completed checkout session"""
    try:
        payment_id = session["metadata"].get("payment_id")
        if payment_id:
            payment = Payment.objects.get(id=payment_id)
            payment.payment_status = "completed"
            payment.gateway_order_id = session["id"]
            payment.payment_date = timezone.now()
            payment.save()

            logger.info(
                f"Checkout session {session['id']} completed for payment {payment_id}"
            )
    except Payment.DoesNotExist:
        logger.error(f"Payment not found for session: {session['id']}")
    except Exception as e:
        logger.error(f"Error handling checkout session completion: {str(e)}")
