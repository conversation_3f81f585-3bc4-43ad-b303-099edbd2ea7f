{"ast": null, "code": "const _excluded = [\"onEnter\", \"onEntering\", \"onEntered\", \"onExit\", \"onExiting\", \"onExited\", \"addEndListener\", \"children\"];\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.indexOf(n) >= 0) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport { cloneElement, useCallback, useRef } from 'react';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport { getChildRef } from './utils';\n/**\n * Normalizes RTG transition callbacks with nodeRef to better support\n * strict mode.\n *\n * @param props Transition props.\n * @returns Normalized transition props.\n */\nexport default function useRTGTransitionProps(_ref) {\n  let {\n      onEnter,\n      onEntering,\n      onEntered,\n      onExit,\n      onExiting,\n      onExited,\n      addEndListener,\n      children\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const nodeRef = useRef(null);\n  const mergedRef = useMergedRefs(nodeRef, getChildRef(children));\n  const normalize = callback => param => {\n    if (callback && nodeRef.current) {\n      callback(nodeRef.current, param);\n    }\n  };\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  const handleEnter = useCallback(normalize(onEnter), [onEnter]);\n  const handleEntering = useCallback(normalize(onEntering), [onEntering]);\n  const handleEntered = useCallback(normalize(onEntered), [onEntered]);\n  const handleExit = useCallback(normalize(onExit), [onExit]);\n  const handleExiting = useCallback(normalize(onExiting), [onExiting]);\n  const handleExited = useCallback(normalize(onExited), [onExited]);\n  const handleAddEndListener = useCallback(normalize(addEndListener), [addEndListener]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n\n  return Object.assign({}, props, {\n    nodeRef\n  }, onEnter && {\n    onEnter: handleEnter\n  }, onEntering && {\n    onEntering: handleEntering\n  }, onEntered && {\n    onEntered: handleEntered\n  }, onExit && {\n    onExit: handleExit\n  }, onExiting && {\n    onExiting: handleExiting\n  }, onExited && {\n    onExited: handleExited\n  }, addEndListener && {\n    addEndListener: handleAddEndListener\n  }, {\n    children: typeof children === 'function' ? (status, innerProps) =>\n    // TODO: Types for RTG missing innerProps, so need to cast.\n    children(status, Object.assign({}, innerProps, {\n      ref: mergedRef\n    })) : /*#__PURE__*/cloneElement(children, {\n      ref: mergedRef\n    })\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}