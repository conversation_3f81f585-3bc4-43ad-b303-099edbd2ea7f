/* Auction-Specific Enhancements */

/* Auction Status Indicators */
.auction-status {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.auction-status.active {
  background: var(--gradient-success);
  color: white;
  box-shadow: var(--shadow-md);
}

.auction-status.ending-soon {
  background: var(--gradient-accent);
  color: white;
  animation: pulse-glow 2s infinite;
}

.auction-status.ended {
  background: var(--gradient-supporting);
  color: white;
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: var(--shadow-md);
    transform: scale(1);
  }
  50% { 
    box-shadow: var(--shadow-accent);
    transform: scale(1.05);
  }
}

/* Bid Amount Styling */
.bid-amount {
  font-family: 'Inter', monospace;
  font-weight: 700;
  font-size: 1.5rem;
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.bid-amount.current-bid {
  font-size: 2rem;
  background: var(--gradient-premium);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bid-amount.winning-bid {
  animation: bid-highlight 3s ease-in-out;
}

@keyframes bid-highlight {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Time Remaining Indicator */
.time-remaining {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border);
  font-weight: 600;
}

.time-remaining.urgent {
  background: var(--accent-ultra-light);
  border-color: var(--accent-color);
  color: var(--accent-dark);
  animation: time-urgent 1s infinite;
}

@keyframes time-urgent {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Auction Type Badges */
.auction-type-badge {
  position: absolute;
  top: var(--space-md);
  left: var(--space-md);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 2;
}

.auction-type-badge.standard {
  background: var(--gradient-primary);
  color: white;
}

.auction-type-badge.reserve {
  background: var(--gradient-secondary);
  color: white;
}

.auction-type-badge.buy-now {
  background: var(--gradient-success);
  color: white;
}

.auction-type-badge.sealed-bid {
  background: var(--gradient-supporting);
  color: white;
}

.auction-type-badge.reverse {
  background: var(--gradient-accent);
  color: white;
}

/* Featured Auction Styling */
.featured-auction {
  position: relative;
  overflow: hidden;
}

.featured-auction::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: var(--gradient-premium);
  z-index: 1;
}

.featured-auction::after {
  content: '⭐ FEATURED';
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  background: var(--gradient-secondary);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: 0.7rem;
  font-weight: 700;
  z-index: 2;
  animation: featured-glow 3s infinite;
}

@keyframes featured-glow {
  0%, 100% { 
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
  }
  50% { 
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.8);
  }
}

/* Auction Image Enhancements */
.auction-image-container {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-lg);
}

.auction-image {
  transition: var(--transition-smooth);
  width: 100%;
  height: 250px;
  object-fit: contain;
}

.auction-image-container:hover .auction-image {
  transform: scale(1.1);
}

.auction-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg, 
    transparent 0%, 
    rgba(0, 0, 0, 0.1) 50%, 
    rgba(0, 0, 0, 0.7) 100%
  );
  opacity: 0;
  transition: var(--transition-normal);
}

.auction-image-container:hover .auction-image-overlay {
  opacity: 1;
}

/* Bidding Interface */
.bid-interface {
  background: var(--glass-bg);
  backdrop-filter: blur(15px);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-auction);
  padding: var(--space-xl);
  margin: var(--space-lg) 0;
  position: relative;
}

.bid-interface::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-premium);
  border-radius: var(--radius-auction) var(--radius-auction) 0 0;
}

.bid-input {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
  transition: var(--transition-normal);
  background: rgba(255, 255, 255, 0.9);
}

.bid-input:focus {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 4px rgba(212, 175, 55, 0.1);
  background: var(--white);
}

.place-bid-btn {
  background: var(--gradient-secondary);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-lg) var(--space-2xl);
  font-size: 1.1rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: var(--transition-bounce);
  box-shadow: var(--shadow-auction);
  width: 100%;
  margin-top: var(--space-md);
}

.place-bid-btn:hover {
  background: var(--gradient-premium);
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.place-bid-btn:active {
  transform: translateY(0) scale(0.98);
}

/* Watch List Button */
.watch-btn {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-full);
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-bounce);
  z-index: 2;
}

.watch-btn:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
  transform: scale(1.1);
}

.watch-btn.watched {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .bid-amount {
    font-size: 1.2rem;
  }
  
  .bid-amount.current-bid {
    font-size: 1.5rem;
  }
  
  .auction-image {
    height: 200px;
  }
  
  .bid-interface {
    padding: var(--space-lg);
  }
  
  .place-bid-btn {
    padding: var(--space-md) var(--space-lg);
    font-size: 1rem;
  }
}
