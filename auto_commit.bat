@echo off
echo 🚀 Auto-committing all changes...

REM Generate automatic commit message with timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%"

set "auto_message=chore: auto-commit changes on %timestamp%"

echo 📝 Commit message: %auto_message%
echo.

REM Add all changes
git add .

REM Commit with auto-generated message
git commit -m "%auto_message%"

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Nothing to commit or commit failed.
    pause
    exit /b 1
)

REM Push to GitHub
git push origin main

if %ERRORLEVEL% EQU 0 (
    echo ✅ Auto-commit successful! Changes pushed to GitHub.
) else (
    echo ❌ Push failed! Changes committed locally but not pushed.
)

echo.
pause
