#!/usr/bin/env python3
"""
Debug script to check category filtering issues
"""

import os
import sys
import django
import requests

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from auction.models import Auction

def check_auction_categories():
    """Check what category values are actually stored in auctions"""
    print("🔍 Checking Auction Category Values")
    print("=" * 50)
    
    # Get all auctions and their categories
    auctions = Auction.objects.all()
    print(f"📊 Total auctions: {auctions.count()}")
    
    # Group by category
    category_counts = {}
    category_examples = {}
    
    for auction in auctions:
        category = auction.category
        if category:
            if category not in category_counts:
                category_counts[category] = 0
                category_examples[category] = []
            category_counts[category] += 1
            if len(category_examples[category]) < 3:  # Store first 3 examples
                category_examples[category].append({
                    'id': auction.id,
                    'title': auction.title
                })
    
    print(f"\n📂 Categories found in auctions:")
    for category, count in category_counts.items():
        print(f"   '{category}': {count} auctions")
        for example in category_examples[category]:
            print(f"      - {example['title']} (ID: {example['id']})")
    
    return category_counts

def test_api_filtering():
    """Test API filtering for each category"""
    print("\n🌐 Testing API Category Filtering")
    print("=" * 50)
    
    # Get auction categories from database
    category_counts = check_auction_categories()
    
    print(f"\n🧪 Testing API filtering for each category:")
    
    for category in category_counts.keys():
        try:
            url = f"http://127.0.0.1:8000/api/auctions/?category={category}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                api_count = data.get('count', 0)
                db_count = category_counts[category]
                
                if api_count == db_count:
                    print(f"✅ {category}: API={api_count}, DB={db_count} (Match)")
                else:
                    print(f"❌ {category}: API={api_count}, DB={db_count} (Mismatch)")
                    
                # Show first result for debugging
                results = data.get('results', [])
                if results:
                    first_result = results[0]
                    print(f"   First result: {first_result.get('title')} (category: {first_result.get('category')})")
            else:
                print(f"❌ {category}: API error {response.status_code}")
                
        except Exception as e:
            print(f"❌ {category}: Request failed - {e}")

def test_frontend_navigation_flow():
    """Test the complete navigation flow"""
    print("\n🔄 Testing Complete Navigation Flow")
    print("=" * 50)
    
    # Test categories that flip cards would send
    test_categories = [
        'electronics',
        'fashion', 
        'art',
        'collectibles',
        'jewelry',
        'home_garden'  # This is what home page sends after normalization
    ]
    
    print("Testing navigation flow for each category:")
    
    for category in test_categories:
        print(f"\n🎯 Testing: {category}")
        
        # Step 1: Check if auctions exist with this category
        try:
            auctions = Auction.objects.filter(category=category)
            db_count = auctions.count()
            print(f"   📊 Database: {db_count} auctions with category '{category}'")
            
            if db_count > 0:
                # Show examples
                for auction in auctions[:2]:
                    print(f"      - {auction.title}")
            
        except Exception as e:
            print(f"   ❌ Database error: {e}")
        
        # Step 2: Test API filtering
        try:
            url = f"http://127.0.0.1:8000/api/auctions/?category={category}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                api_count = data.get('count', 0)
                print(f"   🌐 API: {api_count} auctions returned")
                
                if api_count != db_count:
                    print(f"   ⚠️ Mismatch: DB={db_count}, API={api_count}")
            else:
                print(f"   ❌ API error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ API request failed: {e}")
        
        # Step 3: Check what frontend would receive
        print(f"   🎯 Frontend route: /auctions/category/{category}")
        print(f"   🔍 Frontend would filter by: '{category}'")

def check_category_case_sensitivity():
    """Check if there are case sensitivity issues"""
    print("\n🔤 Checking Category Case Sensitivity")
    print("=" * 50)
    
    # Get all unique categories
    categories = Auction.objects.values_list('category', flat=True).distinct()
    
    print("All category values (exact case):")
    for category in categories:
        if category:
            print(f"   '{category}'")
            
            # Check variations
            variations = [
                category.lower(),
                category.upper(), 
                category.title(),
                category.capitalize()
            ]
            
            for variation in variations:
                if variation != category:
                    count = Auction.objects.filter(category=variation).count()
                    if count > 0:
                        print(f"      ⚠️ Also found {count} auctions with '{variation}'")

def generate_fix_recommendations():
    """Generate recommendations to fix the filtering issue"""
    print("\n💡 Fix Recommendations")
    print("=" * 50)
    
    # Get actual categories
    categories = list(Auction.objects.values_list('category', flat=True).distinct())
    categories = [c for c in categories if c]  # Remove None values
    
    print("🎯 Issues and Solutions:")
    
    print("\n1. **Category Value Mapping**")
    print("   Current auction categories in database:")
    for category in categories:
        print(f"      '{category}'")
    
    print("\n2. **Frontend Navigation**")
    print("   Home page sends these normalized slugs:")
    test_slugs = ['electronics', 'fashion', 'art', 'collectibles', 'jewelry', 'home_garden']
    for slug in test_slugs:
        if slug in categories:
            print(f"      ✅ '{slug}' - matches database")
        else:
            # Find closest match
            matches = [c for c in categories if slug.lower() in c.lower() or c.lower() in slug.lower()]
            if matches:
                print(f"      ⚠️ '{slug}' - closest match: {matches}")
            else:
                print(f"      ❌ '{slug}' - no match found")
    
    print("\n3. **Recommended Fixes**")
    print("   A. Update auction categories to match frontend slugs")
    print("   B. Add category mapping in frontend")
    print("   C. Make filtering case-insensitive")
    print("   D. Add debug logging to auctions page")

if __name__ == "__main__":
    print("🔍 Category Filtering Debug Analysis")
    print("=" * 60)
    
    # Check database categories
    category_counts = check_auction_categories()
    
    # Test API filtering
    test_api_filtering()
    
    # Test navigation flow
    test_frontend_navigation_flow()
    
    # Check case sensitivity
    check_category_case_sensitivity()
    
    # Generate recommendations
    generate_fix_recommendations()
    
    print("\n" + "=" * 60)
    print("🎉 Debug analysis completed!")
