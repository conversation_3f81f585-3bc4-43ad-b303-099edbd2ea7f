from django.core.management.base import BaseCommand
from django.db import transaction
from auction.models import Auction, Bid, Payment
from decimal import Decimal


class Command(BaseCommand):
    help = 'Convert all auction amounts from INR to USD'

    def add_arguments(self, parser):
        parser.add_argument(
            '--exchange-rate',
            type=float,
            default=0.012,
            help='INR to USD exchange rate (default: 0.012)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be converted without making changes'
        )

    def handle(self, *args, **options):
        exchange_rate = Decimal(str(options['exchange_rate']))
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS(f'Currency Conversion: INR to USD')
        )
        self.stdout.write(f'Exchange Rate: 1 INR = {exchange_rate} USD')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        # Convert Auctions
        auctions = Auction.objects.all()
        auction_count = 0
        
        self.stdout.write('\n📦 Converting Auctions...')
        
        for auction in auctions:
            old_starting_bid = auction.starting_bid
            old_current_bid = auction.current_bid
            old_reserve_price = auction.reserve_price or 0
            old_buy_now_price = auction.buy_now_price or 0
            old_shipping_cost = auction.shipping_cost or 0
            
            new_starting_bid = old_starting_bid * exchange_rate
            new_current_bid = old_current_bid * exchange_rate
            new_reserve_price = old_reserve_price * exchange_rate if old_reserve_price else None
            new_buy_now_price = old_buy_now_price * exchange_rate if old_buy_now_price else None
            new_shipping_cost = old_shipping_cost * exchange_rate if old_shipping_cost else None
            
            self.stdout.write(
                f'  Auction #{auction.id}: {auction.title[:30]}...'
            )
            self.stdout.write(
                f'    Starting Bid: ₹{old_starting_bid} → ${new_starting_bid:.2f}'
            )
            self.stdout.write(
                f'    Current Bid: ₹{old_current_bid} → ${new_current_bid:.2f}'
            )
            
            if not dry_run:
                auction.starting_bid = new_starting_bid
                auction.current_bid = new_current_bid
                auction.reserve_price = new_reserve_price
                auction.buy_now_price = new_buy_now_price
                auction.shipping_cost = new_shipping_cost
                auction.save()
            
            auction_count += 1
        
        # Convert Bids
        bids = Bid.objects.all()
        bid_count = 0
        
        self.stdout.write('\n💰 Converting Bids...')
        
        for bid in bids:
            old_amount = bid.amount
            new_amount = old_amount * exchange_rate
            
            self.stdout.write(
                f'  Bid #{bid.id}: ₹{old_amount} → ${new_amount:.2f}'
            )
            
            if not dry_run:
                bid.amount = new_amount
                bid.save()
            
            bid_count += 1
        
        # Convert Payments
        payments = Payment.objects.all()
        payment_count = 0
        
        self.stdout.write('\n💳 Converting Payments...')
        
        for payment in payments:
            old_amount = payment.amount
            new_amount = old_amount * exchange_rate
            
            self.stdout.write(
                f'  Payment #{payment.id}: ₹{old_amount} → ${new_amount:.2f}'
            )
            
            if not dry_run:
                payment.amount = new_amount
                payment.currency = 'USD'
                payment.save()
            
            payment_count += 1
        
        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(
            self.style.SUCCESS('CONVERSION SUMMARY')
        )
        self.stdout.write(f'Auctions processed: {auction_count}')
        self.stdout.write(f'Bids processed: {bid_count}')
        self.stdout.write(f'Payments processed: {payment_count}')
        self.stdout.write(f'Exchange rate used: 1 INR = {exchange_rate} USD')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('\nThis was a DRY RUN - no changes were made.')
            )
            self.stdout.write('Run without --dry-run to apply changes.')
        else:
            self.stdout.write(
                self.style.SUCCESS('\n✅ Currency conversion completed successfully!')
            )
            self.stdout.write(
                'All amounts have been converted from INR to USD.'
            )
        
        self.stdout.write('\n' + '='*50)
