{"ast": null, "code": "function Step(context, t) {\n  this._context = context;\n  this._t = t;\n}\nStep.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x = this._y = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (0 < this._t && this._t < 1 && this._point === 2) this._context.lineTo(this._x, this._y);\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    if (this._line >= 0) this._t = 1 - this._t, this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n      // falls through\n      default:\n        {\n          if (this._t <= 0) {\n            this._context.lineTo(this._x, y);\n            this._context.lineTo(x, y);\n          } else {\n            var x1 = this._x * (1 - this._t) + x * this._t;\n            this._context.lineTo(x1, this._y);\n            this._context.lineTo(x1, y);\n          }\n          break;\n        }\n    }\n    this._x = x, this._y = y;\n  }\n};\nexport default function (context) {\n  return new Step(context, 0.5);\n}\nexport function stepBefore(context) {\n  return new Step(context, 0);\n}\nexport function stepAfter(context) {\n  return new Step(context, 1);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}