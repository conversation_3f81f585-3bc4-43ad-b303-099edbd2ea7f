.install-prompt-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.install-prompt {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  width: 100%;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.install-prompt-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px 0 20px;
}

.install-prompt-icon {
  font-size: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.install-prompt-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.install-prompt-close:hover {
  background: #f5f5f5;
  color: #333;
}

.install-prompt-content {
  padding: 20px;
}

.install-prompt-content h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
}

.install-prompt-content p {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 1rem;
}

.install-prompt-content ul {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

.install-prompt-content li {
  padding: 6px 0;
  color: #555;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
}

.install-prompt-content li::before {
  content: '';
  width: 6px;
  height: 6px;
  background: #667eea;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
}

.install-prompt-actions {
  padding: 0 20px 20px 20px;
  display: flex;
  gap: 12px;
  flex-direction: column;
}

.install-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.install-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.install-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-outline-secondary {
  border: 1px solid #ddd;
  background: white;
  color: #666;
  padding: 10px 24px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.btn-outline-secondary:hover {
  background: #f8f9fa;
  border-color: #ccc;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .install-prompt-overlay {
    padding: 16px;
  }
  
  .install-prompt {
    max-width: none;
  }
  
  .install-prompt-content h3 {
    font-size: 1.3rem;
  }
  
  .install-prompt-content {
    padding: 16px;
  }
  
  .install-prompt-actions {
    padding: 0 16px 16px 16px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .install-prompt {
    background: #2d3748;
    color: white;
  }
  
  .install-prompt-content h3 {
    color: white;
  }
  
  .install-prompt-content p {
    color: #cbd5e0;
  }
  
  .install-prompt-content li {
    color: #e2e8f0;
  }
  
  .install-prompt-close {
    color: #cbd5e0;
  }
  
  .install-prompt-close:hover {
    background: #4a5568;
    color: white;
  }
  
  .btn-outline-secondary {
    border-color: #4a5568;
    background: #2d3748;
    color: #cbd5e0;
  }
  
  .btn-outline-secondary:hover {
    background: #4a5568;
    border-color: #718096;
  }
}
