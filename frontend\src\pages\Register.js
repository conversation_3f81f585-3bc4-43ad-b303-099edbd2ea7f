import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext"; // ✅ Import your custom AuthContext
import <PERSON>wal from "sweetalert2";
import { Link } from "react-router-dom";

function Register() {
  const { signup } = useAuth(); // ✅ Use signup function from AuthContext
  const navigate = useNavigate();

  const [form, setForm] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    userRole: "bidder", // Default role
  });

  const [error, setError] = useState("");

  const handleChange = (e) => {
    setForm((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const { username, email, password, confirmPassword, userRole } = form;

    // Validation
    if (!username || !email || !password || !confirmPassword || !userRole) {
      return setError("All fields are required.");
    }
    if (!email.includes("@")) {
      return setError("Invalid email address.");
    }
    if (password !== confirmPassword) {
      return setError("Passwords do not match.");
    }

    try {
      await signup(username, email, password, userRole); // Include user role
      Swal.fire({
        icon: "success",
        title: "Registration Successful!",
        text: "You can now login.",
      });
      navigate("/login");
    } catch (err) {
      console.error(err.message);
      setError(err.message);
    }
  };

  return (
    <div className="container mt-5" style={{ maxWidth: "500px" }}>
      <h2 className="mb-4 text-center">Register</h2>
      {error && <div className="alert alert-danger">{error}</div>}

      <form onSubmit={handleSubmit}>
        <div className="form-floating mb-3">
          <input
            className="form-control"
            id="username"
            name="username" // Added for username
            type="text"
            value={form.username}
            onChange={handleChange}
            required
            placeholder="Username"
          />
          <label htmlFor="username">Username</label>
        </div>

        <div className="form-floating mb-3">
          <input
            className="form-control"
            id="email"
            name="email"
            type="email"
            value={form.email}
            onChange={handleChange}
            required
            placeholder="Email address"
          />
          <label htmlFor="email">Email address</label>
        </div>

        <div className="form-floating mb-3">
          <input
            className="form-control"
            id="password"
            name="password"
            type="password"
            value={form.password}
            onChange={handleChange}
            required
            placeholder="Password"
          />
          <label htmlFor="password">Password</label>
        </div>

        <div className="form-floating mb-3">
          <input
            className="form-control"
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            value={form.confirmPassword}
            onChange={handleChange}
            required
            placeholder="Confirm Password"
          />
          <label htmlFor="confirmPassword">Confirm Password</label>
        </div>

        {/* User Role Selection */}
        <div className="form-floating mb-3">
          <select
            className="form-select"
            id="userRole"
            name="userRole"
            value={form.userRole}
            onChange={handleChange}
            required
          >
            <option value="bidder">Bidder - I want to bid on auctions</option>
            <option value="seller">Seller - I want to create auctions</option>
            <option value="both">
              Both - I want to bid and create auctions
            </option>
          </select>
          <label htmlFor="userRole">Account Type</label>
        </div>

        <div className="text-end">
          <Link to="/reset-password" className="small">
            Forgot Password?
          </Link>
        </div>
        <button className="btn btn-success w-100" type="submit">
          Register
        </button>
      </form>
    </div>
  );
}

export default Register;
