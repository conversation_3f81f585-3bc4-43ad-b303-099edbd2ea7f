@echo off
echo ========================================
echo FOLDER-BY-FOLDER COMMIT SCRIPT
echo ========================================
echo.

:: Function to commit a folder if it has changes
:commit_folder
set folder=%1
set description=%2

echo Checking folder: %folder%
git add "%folder%/" 2>nul

:: Check if there are staged changes for this folder
git diff --cached --quiet --exit-code "%folder%/" 2>nul
if %errorlevel% neq 0 (
    echo [COMMIT] Changes found in %folder% - Committing...
    git commit -m "[%folder%] %description%"
    if %errorlevel% equ 0 (
        echo [SUCCESS] %folder% committed successfully
    ) else (
        echo [ERROR] Failed to commit %folder%
    )
) else (
    echo [SKIP] No changes in %folder%
    git reset HEAD "%folder%/" 2>nul
)
echo.
goto :eof

:: Start the folder-by-folder commit process
echo Starting folder-by-folder commit process...
echo.

:: Root level files first
echo Checking root level files...
git add *.md *.bat *.yml *.yaml *.json *.txt 2>nul
git diff --cached --quiet --exit-code
if %errorlevel% neq 0 (
    echo [COMMIT] Root level configuration files have changes
    git commit -m "[root] Update configuration and documentation files"
) else (
    echo [SKIP] No changes in root level files
    git reset HEAD 2>nul
)
echo.

:: Backend folder
call :commit_folder "backend" "Backend API and Django application updates"

:: Backend subfolders
call :commit_folder "backend/OnlineAuctionSystem" "Django project settings and configuration"
call :commit_folder "backend/auction" "Main auction app models, views, and services"
call :commit_folder "backend/auctions" "Additional auction services and utilities"
call :commit_folder "backend/templates" "Email templates and HTML files"
call :commit_folder "backend/static" "Static files and assets"
call :commit_folder "backend/middleware" "Custom middleware components"

:: Frontend folder
call :commit_folder "frontend" "React frontend application updates"

:: Frontend subfolders
call :commit_folder "frontend/src" "React source code and components"
call :commit_folder "frontend/src/components" "React components and UI elements"
call :commit_folder "frontend/src/pages" "React page components and routing"
call :commit_folder "frontend/src/context" "React context providers and state management"
call :commit_folder "frontend/src/styles" "CSS styles and theme files"
call :commit_folder "frontend/src/api" "API service functions and axios configuration"
call :commit_folder "frontend/src/utils" "Utility functions and helpers"
call :commit_folder "frontend/public" "Public assets and static files"

:: Testing suite
call :commit_folder "testing_suite" "Comprehensive testing suite and automation"

:: Scripts folder
call :commit_folder "scripts" "Utility scripts and automation tools"

echo.
echo ========================================
echo FOLDER-BY-FOLDER COMMIT COMPLETED
echo ========================================
echo.

:: Show final status
echo Final repository status:
git status --short

echo.
echo Recent commits:
git log --oneline -5

echo.
echo Script completed! All folders with changes have been committed.
pause
