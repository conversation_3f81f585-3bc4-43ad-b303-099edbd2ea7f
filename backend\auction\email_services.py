"""
Email Services for Online Auction System
Handles all email notifications including reviews, auctions, and system alerts
"""

import os
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
from django.contrib.auth.models import User
from .models import Review, Auction
import logging

logger = logging.getLogger(__name__)

class EmailService:
    """Centralized email service for the auction system"""
    
    @staticmethod
    def get_email_settings():
        """Get email configuration from settings"""
        return {
            'from_email': getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            'admin_email': getattr(settings, 'ADMIN_EMAIL', '<EMAIL>'),
            'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>'),
        }
    
    @staticmethod
    def send_html_email(subject, html_content, recipient_list, from_email=None):
        """Send HTML email with fallback to plain text"""
        try:
            settings_config = EmailService.get_email_settings()
            from_email = from_email or settings_config['from_email']
            
            # Create plain text version
            text_content = strip_tags(html_content)
            
            # Create email message
            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=from_email,
                to=recipient_list
            )
            msg.attach_alternative(html_content, "text/html")
            
            # Send email
            result = msg.send()
            logger.info(f"Email sent successfully to {recipient_list}: {subject}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to send email to {recipient_list}: {str(e)}")
            return False

def send_review_notification_email(review):
    """Send email notification when a new review is received"""
    try:
        # Email context
        context = {
            'review': review,
            'reviewer_name': review.reviewer.get_full_name() or review.reviewer.username,
            'reviewee_name': review.reviewee.get_full_name() or review.reviewee.username,
            'auction_title': review.auction.title,
            'rating': review.rating,
            'rating_stars': '⭐' * review.rating,
            'comment': review.comment,
            'review_type': review.get_review_type_display(),
            'is_verified': review.is_verified,
            'site_url': getattr(settings, 'SITE_URL', 'http://localhost:3000'),
        }
        
        # Email subject
        subject = f"New {review.rating}⭐ Review Received - {review.auction.title}"
        
        # HTML email content
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>New Review Notification</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: #007bff; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background: #f8f9fa; }}
                .review-box {{ background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }}
                .rating {{ font-size: 24px; color: #ffc107; }}
                .verified {{ color: #28a745; font-weight: bold; }}
                .footer {{ text-align: center; padding: 20px; color: #666; }}
                .btn {{ background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 New Review Received!</h1>
                </div>
                
                <div class="content">
                    <h2>Hello {context['reviewee_name']},</h2>
                    
                    <p>You've received a new review for your auction:</p>
                    
                    <div class="review-box">
                        <h3>📦 {context['auction_title']}</h3>
                        
                        <div class="rating">
                            <strong>Rating:</strong> {context['rating_stars']} ({context['rating']}/5)
                        </div>
                        
                        <p><strong>Review Type:</strong> {context['review_type']}</p>
                        
                        {'<p class="verified">✅ Verified Purchase</p>' if context['is_verified'] else ''}
                        
                        <p><strong>From:</strong> {context['reviewer_name']}</p>
                        
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0;">
                            <strong>Review:</strong><br>
                            "{context['comment']}"
                        </div>
                    </div>
                    
                    <p>This review helps build trust in our auction community. Thank you for providing excellent service!</p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{context['site_url']}/user-profile" class="btn">View Your Profile</a>
                    </div>
                </div>
                
                <div class="footer">
                    <p>Best regards,<br>Online Auction System Team</p>
                    <p><small>This is an automated notification. Please do not reply to this email.</small></p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Send email
        return EmailService.send_html_email(
            subject=subject,
            html_content=html_content,
            recipient_list=[review.reviewee.email]
        )
        
    except Exception as e:
        logger.error(f"Failed to send review notification email: {str(e)}")
        return False

def send_review_reminder_email(auction, winner_email):
    """Send reminder email to auction winner to leave a review"""
    try:
        context = {
            'auction_title': auction.title,
            'seller_name': auction.owner.get_full_name() or auction.owner.username,
            'site_url': getattr(settings, 'SITE_URL', 'http://localhost:3000'),
            'auction_id': auction.id,
        }
        
        subject = f"Please Review Your Purchase - {auction.title}"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Review Reminder</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: #28a745; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background: #f8f9fa; }}
                .auction-box {{ background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }}
                .footer {{ text-align: center; padding: 20px; color: #666; }}
                .btn {{ background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>⭐ Share Your Experience</h1>
                </div>
                
                <div class="content">
                    <h2>How was your purchase?</h2>
                    
                    <p>We hope you're enjoying your recent purchase! Your feedback helps other buyers and supports our sellers.</p>
                    
                    <div class="auction-box">
                        <h3>📦 {context['auction_title']}</h3>
                        <p><strong>Seller:</strong> {context['seller_name']}</p>
                    </div>
                    
                    <p>Please take a moment to leave a review about your experience:</p>
                    
                    <ul>
                        <li>How was the item quality?</li>
                        <li>Was the seller responsive?</li>
                        <li>Would you recommend this seller?</li>
                    </ul>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{context['site_url']}/auction/{context['auction_id']}" class="btn">Leave a Review</a>
                    </div>
                </div>
                
                <div class="footer">
                    <p>Thank you for being part of our auction community!</p>
                    <p><small>Online Auction System Team</small></p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return EmailService.send_html_email(
            subject=subject,
            html_content=html_content,
            recipient_list=[winner_email]
        )
        
    except Exception as e:
        logger.error(f"Failed to send review reminder email: {str(e)}")
        return False

def send_admin_review_alert(review):
    """Send alert to admin when a review is reported or needs attention"""
    try:
        settings_config = EmailService.get_email_settings()
        admin_email = settings_config['admin_email']
        
        context = {
            'review': review,
            'reviewer_name': review.reviewer.username,
            'reviewee_name': review.reviewee.username,
            'auction_title': review.auction.title,
            'site_url': getattr(settings, 'SITE_URL', 'http://localhost:3000'),
        }
        
        subject = f"Review Alert - {review.auction.title}"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Review Alert</title>
        </head>
        <body>
            <h2>Review Alert</h2>
            <p><strong>Auction:</strong> {context['auction_title']}</p>
            <p><strong>Reviewer:</strong> {context['reviewer_name']}</p>
            <p><strong>Reviewee:</strong> {context['reviewee_name']}</p>
            <p><strong>Rating:</strong> {review.rating}/5</p>
            <p><strong>Comment:</strong> {review.comment}</p>
            <p><strong>Reported Count:</strong> {review.reported_count}</p>
            
            <a href="{context['site_url']}/admin">Review in Admin Panel</a>
        </body>
        </html>
        """
        
        return EmailService.send_html_email(
            subject=subject,
            html_content=html_content,
            recipient_list=[admin_email]
        )
        
    except Exception as e:
        logger.error(f"Failed to send admin review alert: {str(e)}")
        return False
