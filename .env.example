# Copy this file to .env and fill in your actual values
# NEVER commit .env to git - it contains sensitive information

# Database Configuration
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/auction_db
DB_NAME=auction_db
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_gmail_app_password_here

# Payment Configuration (Stripe)
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Security
SECRET_KEY=your_django_secret_key_here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# AI Services (Optional)
OPENAI_API_KEY=your_openai_api_key_here

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# WebSocket Configuration
CHANNEL_LAYERS_HOST=localhost
CHANNEL_LAYERS_PORT=6379

# File Upload Settings
MEDIA_ROOT=media/
MEDIA_URL=/media/

# Static Files
STATIC_ROOT=staticfiles/
STATIC_URL=/static/

# Logging
LOG_LEVEL=INFO

# Time Zone
TIME_ZONE=Asia/Kolkata

# Currency
DEFAULT_CURRENCY=USD
CURRENCY_SYMBOL=$

# Auction Settings
DEFAULT_AUCTION_DURATION_DAYS=7
MIN_BID_INCREMENT=10
MAX_AUCTION_IMAGES=5

# Notification Settings
ENABLE_EMAIL_NOTIFICATIONS=True
ENABLE_PUSH_NOTIFICATIONS=False

# Security Settings
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
SECURE_SSL_REDIRECT=False

# Development Settings
CORS_ALLOW_ALL_ORIGINS=True
CORS_ALLOW_CREDENTIALS=True
