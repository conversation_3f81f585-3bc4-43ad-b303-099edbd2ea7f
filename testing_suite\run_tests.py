#!/usr/bin/env python3
"""
Comprehensive Test Runner for Online Auction System
Supports unit tests, integration tests, and Selenium automation
"""

import os
import sys
import subprocess
import argparse
import time
from datetime import datetime


class TestRunner:
    """Main test runner class"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.abspath(__file__))
        # Correct backend path to point to the project root backend directory
        self.backend_path = os.path.abspath(os.path.join(self.project_root, '..', 'backend'))
        self.tests_path = os.path.join(self.project_root, 'tests')
        self.results_path = os.path.join(self.project_root, 'test_results')
        
        # Ensure results directory exists
        os.makedirs(self.results_path, exist_ok=True)
    
    def run_unit_tests(self):
        """Run Django unit tests"""
        print("🧪 Running Unit Tests...")
        print("=" * 50)
        
        os.chdir(self.backend_path)
        
        # Run Django tests
        cmd = [
            sys.executable, 'manage.py', 'test',
            '--verbosity=2',
            '--keepdb',
            '--parallel'
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            
            return result.returncode == 0
        except Exception as e:
            print(f"❌ Unit tests failed: {e}")
            return False
    
    def run_api_tests(self):
        """Run API tests using pytest"""
        print("🔌 Running API Tests...")
        print("=" * 50)
        
        os.chdir(self.backend_path)
        
        cmd = [
            'pytest',
            'tests/test_api.py',
            '-v',
            '--tb=short',
            f'--html={self.results_path}/api_test_report.html',
            '--self-contained-html'
        ]
        
        try:
            result = subprocess.run(cmd)
            return result.returncode == 0
        except Exception as e:
            print(f"❌ API tests failed: {e}")
            return False
    
    def run_selenium_tests(self):
        """Run Selenium automation tests"""
        print("🤖 Running Selenium Automation Tests...")
        print("=" * 50)
        
        # Check if Chrome is available
        if not self.check_chrome_driver():
            print("❌ Chrome driver not available. Skipping Selenium tests.")
            return False
        
        os.chdir(self.project_root)
        
        cmd = [
            sys.executable,
            'tests/selenium_automation_tests.py'
        ]
        
        try:
            result = subprocess.run(cmd)
            return result.returncode == 0
        except Exception as e:
            print(f"❌ Selenium tests failed: {e}")
            return False
    
    def run_user_acceptance_tests(self):
        """Run user acceptance tests"""
        print("👥 Running User Acceptance Tests...")
        print("=" * 50)
        
        os.chdir(self.project_root)
        
        cmd = [
            sys.executable,
            'tests/user_acceptance_tests.py'
        ]
        
        try:
            result = subprocess.run(cmd)
            return result.returncode == 0
        except Exception as e:
            print(f"❌ User acceptance tests failed: {e}")
            return False
    
    def run_performance_tests(self):
        """Run basic performance tests"""
        print("⚡ Running Performance Tests...")
        print("=" * 50)
        
        # Simple performance test using requests
        performance_script = f"""
import requests
import time
import statistics

base_url = "http://127.0.0.1:8000"
endpoints = [
    "/api/auctions/",
    "/api/categories/",
    "/api/search/filters/"
]

print("Performance Test Results:")
print("-" * 40)

for endpoint in endpoints:
    times = []
    for i in range(5):
        start = time.time()
        try:
            response = requests.get(base_url + endpoint, timeout=10)
            end = time.time()
            times.append(end - start)
        except Exception as e:
            print(f"Error testing {{endpoint}}: {{e}}")
            break
    
    if times:
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        print(f"{{endpoint}}:")
        print(f"  Average: {{avg_time*1000:.0f}}ms")
        print(f"  Min: {{min_time*1000:.0f}}ms")
        print(f"  Max: {{max_time*1000:.0f}}ms")
"""
        
        try:
            exec(performance_script)
            return True
        except Exception as e:
            print(f"❌ Performance tests failed: {e}")
            return False
    
    def check_chrome_driver(self):
        """Check if Chrome driver is available"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            options = Options()
            options.add_argument("--headless")
            options.add_argument("--no-sandbox")
            
            driver = webdriver.Chrome(options=options)
            driver.quit()
            return True
        except Exception:
            return False
    
    def check_services(self):
        """Check if required services are running"""
        print("🔍 Checking Services...")
        print("=" * 50)
        
        services = {
            "Django Backend": "http://127.0.0.1:8000/api/",
            "React Frontend": "http://localhost:3001"
        }
        
        import requests
        
        all_running = True
        for service, url in services.items():
            try:
                response = requests.get(url, timeout=5)
                if response.status_code < 500:
                    print(f"✅ {service}: Running")
                else:
                    print(f"⚠️ {service}: Responding but may have issues")
                    all_running = False
            except Exception:
                print(f"❌ {service}: Not running")
                all_running = False
        
        return all_running
    
    def generate_summary_report(self, results):
        """Generate summary test report"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report = f"""
# Test Execution Summary
Generated: {timestamp}

## Test Results
"""
        
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result)
        
        for test_type, passed in results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            report += f"- {test_type}: {status}\n"
        
        report += f"""
## Summary
- Total Test Suites: {total_tests}
- Passed: {passed_tests}
- Failed: {total_tests - passed_tests}
- Success Rate: {(passed_tests/total_tests)*100:.1f}%

## Test Artifacts
- Unit Test Results: backend/test_results/
- API Test Report: test_results/api_test_report.html
- Selenium Screenshots: test_results/*.png
- Coverage Report: test_results/coverage_html/index.html
"""
        
        # Save report
        report_file = os.path.join(self.results_path, 'test_summary.md')
        with open(report_file, 'w') as f:
            f.write(report)
        
        print("\n" + "=" * 60)
        print("📊 TEST EXECUTION SUMMARY")
        print("=" * 60)
        print(report)
        print(f"📄 Full report saved: {report_file}")
    
    def run_all_tests(self):
        """Run all test suites"""
        print("🚀 STARTING COMPREHENSIVE TEST EXECUTION")
        print("=" * 60)
        
        start_time = time.time()
        
        # Check services first
        if not self.check_services():
            print("⚠️ Some services are not running. Tests may fail.")
            print("Please ensure Django backend and React frontend are running.")
            input("Press Enter to continue anyway, or Ctrl+C to abort...")
        
        # Run all test suites
        results = {}
        
        results["Unit Tests"] = self.run_unit_tests()
        results["API Tests"] = self.run_api_tests()
        results["Selenium Tests"] = self.run_selenium_tests()
        results["User Acceptance Tests"] = self.run_user_acceptance_tests()
        results["Performance Tests"] = self.run_performance_tests()
        
        # Generate summary
        total_time = time.time() - start_time
        print(f"\n⏱️ Total execution time: {total_time:.2f} seconds")
        
        self.generate_summary_report(results)
        
        return results


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Online Auction System Test Runner")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--api", action="store_true", help="Run API tests only")
    parser.add_argument("--selenium", action="store_true", help="Run Selenium tests only")
    parser.add_argument("--uat", action="store_true", help="Run user acceptance tests only")
    parser.add_argument("--performance", action="store_true", help="Run performance tests only")
    parser.add_argument("--all", action="store_true", help="Run all tests (default)")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    # If no specific test type specified, run all
    if not any([args.unit, args.api, args.selenium, args.uat, args.performance]):
        args.all = True
    
    if args.all:
        results = runner.run_all_tests()
        # Exit with error code if any tests failed
        if not all(results.values()):
            sys.exit(1)
    else:
        if args.unit:
            success = runner.run_unit_tests()
        elif args.api:
            success = runner.run_api_tests()
        elif args.selenium:
            success = runner.run_selenium_tests()
        elif args.uat:
            success = runner.run_user_acceptance_tests()
        elif args.performance:
            success = runner.run_performance_tests()
        
        if not success:
            sys.exit(1)


if __name__ == "__main__":
    main()
