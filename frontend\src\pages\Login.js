import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import Swal from "sweetalert2";

function Login() {
  const [form, setForm] = useState({ username: "", password: "" });
  const [error, setError] = useState("");
  const { login, user } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    setForm((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");

    try {
      const loggedInUser = await login(form.username, form.password);
      // Check if user is a superuser
      if (loggedInUser?.role === "superuser" || loggedInUser?.is_superuser) {
        Swal.fire({
          toast: true,
          icon: "success",
          title: "Super User Login Successful!",
          position: "top-end",
          showConfirmButton: false,
          timer: 3000,
        });
        navigate("/");
      } else {
        Swal.fire({
          toast: true,
          icon: "success",
          title: "Login successful!",
          position: "top-end",
          showConfirmButton: false,
          timer: 3000,
        });
        navigate("/"); // Regular users are sent to auctions page
      }
    } catch (err) {
      console.error(err);
      setError("Invalid username or password.");
    }
  };

  return (
    <div className="container mt-5" style={{ maxWidth: "400px" }}>
      <h2 className="mb-4">Login</h2>
      {error && <div className="alert alert-danger">{error}</div>}
      <form onSubmit={handleSubmit}>
        <div className="mb-3">
          <label>Username</label>
          <input
            className="form-control"
            name="username"
            type="text"
            value={form.username}
            onChange={handleChange}
            required
          />
        </div>
        <div className="mb-3">
          <label>Password</label>
          <input
            className="form-control"
            type="password"
            name="password"
            value={form.password}
            onChange={handleChange}
            required
          />
        </div>
        <div className="text-center mt-3">
          <a href="/forgot-password" style={{ textDecoration: "none" }}>
            Forgot Password?
          </a>
        </div>

        <button className="btn btn-primary w-100" type="submit">
          Login
        </button>
      </form>
      {/* Display username after login */}
      {user && <h3>Welcome, {user.username}</h3>}{" "}
      {/* This will display the username */}
    </div>
  );
}

export default Login;
