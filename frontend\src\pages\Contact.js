﻿import React, { useState } from "react";
import Swal from "sweetalert2";

function Contact() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: "",
  });

  const handleChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const { name, email, message } = formData;

    if (!name || !email || !message) {
      Swal.fire("Error", "Please fill in all fields!", "error");
      return;
    }

    try {
      const res = await fetch("http://127.0.0.1:8000/api/contact-messages/", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, email, message }),
      });

      if (!res.ok) {
        throw new Error("Failed to send message");
      }

      Swal.fire("Thank you!", "Your message has been sent!", "success");

      // Clear form data
      setFormData({ name: "", email: "", message: "" });
    } catch (error) {
      console.error(error);
      Swal.fire("Error", "Failed to send message.", "error");
    }
  };

  return (
    <div className="container mt-5" style={{ maxWidth: "600px" }}>
      <h2 className="mb-4 text-center">Contact Us</h2>
      <form onSubmit={handleSubmit}>
        <div className="form-floating mb-3">
          <input
            type="text"
            name="name"
            className="form-control"
            placeholder="Your Name"
            value={formData.name}
            onChange={handleChange}
          />
          <label>Your Name</label>
        </div>

        <div className="form-floating mb-3">
          <input
            type="email"
            name="email"
            className="form-control"
            placeholder="Your Email"
            value={formData.email}
            onChange={handleChange}
          />
          <label>Your Email</label>
        </div>

        <div className="form-floating mb-3">
          <textarea
            name="message"
            className="form-control"
            placeholder="Message"
            style={{ height: "150px" }}
            value={formData.message}
            onChange={handleChange}
          />
          <label>Message</label>
        </div>

        <button type="submit" className="btn btn-primary w-100">
          Send Message
        </button>
      </form>
    </div>
  );
}

export default Contact;
