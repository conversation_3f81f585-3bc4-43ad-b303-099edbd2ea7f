"""
Stripe Payment Gateway Integration
Supports Stripe payment processing for international transactions
"""

import json
import logging
from decimal import Decimal

import requests
import stripe
from django.conf import settings
from django.utils import timezone

from .models import Payment

logger = logging.getLogger(__name__)

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


class StripeGateway:
    """Stripe Payment Gateway Integration"""

    def __init__(self):
        stripe.api_key = settings.STRIPE_SECRET_KEY

    def create_payment_intent(self, amount, currency="usd", metadata=None):
        """Create Stripe Payment Intent"""
        try:
            # Convert amount to cents (Stripe uses smallest currency unit)
            amount_in_cents = int(float(amount) * 100)

            intent = stripe.PaymentIntent.create(
                amount=amount_in_cents,
                currency=currency,
                metadata=metadata or {},
                automatic_payment_methods={"enabled": True},
            )

            return {
                "success": True,
                "client_secret": intent.client_secret,
                "payment_intent_id": intent.id,
                "amount": intent.amount,
                "currency": intent.currency,
                "status": intent.status,
            }
        except Exception as e:
            logger.error(f"Stripe Payment Intent creation failed: {str(e)}")
            return {"success": False, "error": str(e)}

    def create_checkout_session(
        self, amount, currency="usd", success_url=None, cancel_url=None, metadata=None
    ):
        """Create Stripe Checkout Session"""
        try:
            # Convert amount to cents
            amount_in_cents = int(float(amount) * 100)

            session = stripe.checkout.Session.create(
                payment_method_types=["card"],
                line_items=[
                    {
                        "price_data": {
                            "currency": currency,
                            "product_data": {
                                "name": "Auction Payment",
                            },
                            "unit_amount": amount_in_cents,
                        },
                        "quantity": 1,
                    }
                ],
                mode="payment",
                success_url=success_url or settings.STRIPE_SUCCESS_URL,
                cancel_url=cancel_url or settings.STRIPE_CANCEL_URL,
                metadata=metadata or {},
            )

            return {
                "success": True,
                "session_id": session.id,
                "checkout_url": session.url,
                "payment_intent": session.payment_intent,
            }
        except Exception as e:
            logger.error(f"Stripe Checkout Session creation failed: {str(e)}")
            return {"success": False, "error": str(e)}

    def verify_payment(self, payment_intent_id):
        """Verify Stripe payment"""
        try:
            intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            return {
                "success": True,
                "verified": intent.status == "succeeded",
                "payment_intent": intent,
            }
        except Exception as e:
            logger.error(f"Stripe payment verification failed: {str(e)}")
            return {"success": False, "error": str(e)}

    def refund_payment(self, payment_intent_id, amount=None):
        """Refund Stripe payment"""
        try:
            refund_data = {"payment_intent": payment_intent_id}
            if amount:
                refund_data["amount"] = int(float(amount) * 100)

            refund = stripe.Refund.create(**refund_data)
            return {"success": True, "refund": refund}
        except Exception as e:
            logger.error(f"Stripe refund failed: {str(e)}")
            return {"success": False, "error": str(e)}

    def get_payment_details(self, payment_intent_id):
        """Get Stripe payment details"""
        try:
            intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            return {"success": True, "payment_intent": intent}
        except Exception as e:
            logger.error(f"Failed to fetch Stripe payment details: {str(e)}")
            return {"success": False, "error": str(e)}


# Currency conversion utility
def convert_currency(amount, from_currency, to_currency):
    """Convert currency using exchange rates"""
    # For now, using fixed rates. In production, use live exchange rate API
    exchange_rates = {
        "USD_TO_EUR": 0.85,
        "EUR_TO_USD": 1.18,
        "USD_TO_GBP": 0.73,
        "GBP_TO_USD": 1.37,
        "USD_TO_CAD": 1.25,
        "CAD_TO_USD": 0.80,
        "USD_TO_AUD": 1.35,
        "AUD_TO_USD": 0.74,
    }

    if from_currency == to_currency:
        return amount

    rate_key = f"{from_currency}_TO_{to_currency}"
    rate = exchange_rates.get(rate_key, 1.0)

    return Decimal(str(float(amount) * rate)).quantize(Decimal("0.01"))


class PaymentGatewayFactory:
    """Factory class to get appropriate payment gateway"""

    @staticmethod
    def get_gateway(gateway_type):
        """Get payment gateway instance"""
        gateways = {
            "stripe": StripeGateway,
        }

        gateway_class = gateways.get(gateway_type)
        if gateway_class:
            return gateway_class()
        else:
            raise ValueError(f"Unsupported gateway type: {gateway_type}")
