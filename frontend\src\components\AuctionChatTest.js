import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import axiosInstance from "../api/axiosInstance";

const AuctionChatTest = ({ auctionId }) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const [chatRoom, setChatRoom] = useState(null);
  const [loading, setLoading] = useState(true);

  // Test data to verify rendering
  const testMessages = [
    {
      id: 1,
      message: "Test message 1 - This should be visible!",
      sender_username: "TestUser1",
      timestamp: new Date().toISOString(),
      time_ago: "1 minute"
    },
    {
      id: 2,
      message: "Test message 2 - Another visible message!",
      sender_username: "TestUser2",
      timestamp: new Date().toISOString(),
      time_ago: "2 minutes"
    }
  ];

  useEffect(() => {
    console.log("🧪 AuctionChatTest mounted for auction:", auctionId);
    
    // Set test messages immediately
    setMessages(testMessages);
    setLoading(false);
    
    // Also try to load real messages
    loadRealMessages();
  }, [auctionId]);

  const loadRealMessages = async () => {
    try {
      console.log("🔍 Loading real messages...");
      
      // Get chat room
      const roomResponse = await axiosInstance.get(`chat-rooms/?auction=${auctionId}`);
      if (roomResponse.data.results && roomResponse.data.results.length > 0) {
        const room = roomResponse.data.results[0];
        setChatRoom(room);
        console.log("✅ Found chat room:", room.id);
        
        // Get messages
        const messagesResponse = await axiosInstance.get(
          `chat-messages/?room=${room.id}&ordering=timestamp`
        );
        const realMessages = messagesResponse.data.results || [];
        console.log("✅ Loaded real messages:", realMessages.length);
        
        // Combine test and real messages
        setMessages([...testMessages, ...realMessages]);
      }
    } catch (error) {
      console.error("❌ Error loading real messages:", error);
    }
  };

  const sendTestMessage = async () => {
    if (!newMessage.trim()) return;
    
    const testMsg = {
      id: Date.now(),
      message: newMessage,
      sender_username: user?.username || "Anonymous",
      timestamp: new Date().toISOString(),
      time_ago: "just now"
    };
    
    setMessages(prev => [...prev, testMsg]);
    setNewMessage("");
    console.log("✅ Added test message:", testMsg);
  };

  console.log("🎨 Rendering AuctionChatTest with", messages.length, "messages");

  if (loading) {
    return (
      <div style={{ 
        border: "3px solid orange", 
        padding: "20px", 
        margin: "20px",
        background: "yellow"
      }}>
        <h3>🧪 Test Chat Loading...</h3>
      </div>
    );
  }

  return (
    <div style={{ 
      border: "3px solid green", 
      padding: "20px", 
      margin: "20px",
      background: "lightgreen",
      minHeight: "400px"
    }}>
      <h3>🧪 Test Chat Component</h3>
      <p>Messages count: {messages.length}</p>
      <p>User: {user?.username || "Not logged in"}</p>
      <p>Auction ID: {auctionId}</p>
      
      <div style={{
        border: "2px solid blue",
        padding: "10px",
        margin: "10px 0",
        background: "lightblue",
        maxHeight: "200px",
        overflowY: "auto"
      }}>
        <h4>Messages:</h4>
        {messages.length === 0 ? (
          <p style={{ color: "red", fontSize: "18px" }}>NO MESSAGES FOUND</p>
        ) : (
          messages.map((message, index) => (
            <div 
              key={message.id || index}
              style={{
                border: "1px solid red",
                padding: "8px",
                margin: "5px 0",
                background: "white",
                borderRadius: "5px"
              }}
            >
              <strong>{message.sender_username}:</strong> {message.message}
              <br />
              <small style={{ color: "gray" }}>
                {message.time_ago} ({message.timestamp})
              </small>
            </div>
          ))
        )}
      </div>
      
      <div style={{ marginTop: "10px" }}>
        <input
          type="text"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder="Type a test message..."
          style={{
            padding: "8px",
            width: "70%",
            border: "2px solid black"
          }}
        />
        <button
          onClick={sendTestMessage}
          style={{
            padding: "8px 16px",
            marginLeft: "10px",
            background: "orange",
            border: "2px solid black",
            cursor: "pointer"
          }}
        >
          Send Test
        </button>
      </div>
      
      <div style={{ marginTop: "10px", fontSize: "12px", color: "gray" }}>
        <p>🔍 Debug Info:</p>
        <p>- Component mounted: ✅</p>
        <p>- Messages array length: {messages.length}</p>
        <p>- Chat room: {chatRoom?.id || "Not loaded"}</p>
        <p>- User authenticated: {user ? "✅" : "❌"}</p>
      </div>
    </div>
  );
};

export default AuctionChatTest;
