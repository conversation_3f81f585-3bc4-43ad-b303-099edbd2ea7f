"""
Additional auction views for extended functionality
"""

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db.models import Q, Count, Avg
from datetime import timedelta

from auction.models import Auction, Bid, Category
from auction.serializers import AuctionSerializer


@api_view(['GET'])
def trending_auctions(request):
    """Get trending auctions based on bid activity"""
    try:
        # Get auctions with most bids in last 24 hours
        last_24h = timezone.now() - timedelta(hours=24)
        
        trending = Auction.objects.filter(
            is_active=True,
            end_time__gt=timezone.now()
        ).annotate(
            recent_bid_count=Count(
                'bids',
                filter=Q(bids__timestamp__gte=last_24h)
            )
        ).order_by('-recent_bid_count', '-current_bid')[:10]

        serializer = AuctionSerializer(trending, many=True)
        return Response({
            'success': True,
            'data': serializer.data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def ending_soon_auctions(request):
    """Get auctions ending within next 24 hours"""
    try:
        next_24h = timezone.now() + timedelta(hours=24)
        
        ending_soon = Auction.objects.filter(
            is_active=True,
            end_time__lte=next_24h,
            end_time__gt=timezone.now()
        ).order_by('end_time')[:10]

        serializer = AuctionSerializer(ending_soon, many=True)
        return Response({
            'success': True,
            'data': serializer.data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def popular_categories(request):
    """Get categories with most active auctions"""
    try:
        popular = Category.objects.annotate(
            active_auction_count=Count(
                'auctions',
                filter=Q(auctions__is_active=True)
            )
        ).filter(
            active_auction_count__gt=0
        ).order_by('-active_auction_count')[:8]

        data = []
        for category in popular:
            data.append({
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'active_auctions': category.active_auction_count
            })

        return Response({
            'success': True,
            'data': data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_auction_stats(request):
    """Get auction statistics for the current user"""
    try:
        user = request.user
        
        # User's auctions
        user_auctions = Auction.objects.filter(seller=user)
        active_auctions = user_auctions.filter(is_active=True)
        completed_auctions = user_auctions.filter(is_active=False)
        
        # User's bids
        user_bids = Bid.objects.filter(bidder=user)
        won_auctions = Auction.objects.filter(
            winner=user,
            is_active=False
        )
        
        # Calculate statistics
        total_revenue = sum(
            auction.final_price or 0 
            for auction in completed_auctions 
            if auction.final_price
        )
        
        avg_selling_price = completed_auctions.aggregate(
            avg_price=Avg('final_price')
        )['avg_price'] or 0

        stats = {
            'auctions_created': user_auctions.count(),
            'active_auctions': active_auctions.count(),
            'completed_auctions': completed_auctions.count(),
            'total_revenue': float(total_revenue),
            'average_selling_price': float(avg_selling_price),
            'bids_placed': user_bids.count(),
            'auctions_won': won_auctions.count(),
            'success_rate': (
                (won_auctions.count() / user_bids.values('auction').distinct().count() * 100)
                if user_bids.exists() else 0
            )
        }

        return Response({
            'success': True,
            'data': stats
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def extend_auction(request, auction_id):
    """Extend auction end time (admin only)"""
    try:
        if not request.user.is_staff:
            return Response({
                'success': False,
                'error': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)

        auction = get_object_or_404(Auction, id=auction_id)
        
        if not auction.is_active:
            return Response({
                'success': False,
                'error': 'Cannot extend inactive auction'
            }, status=status.HTTP_400_BAD_REQUEST)

        hours_to_extend = request.data.get('hours', 24)
        
        if not isinstance(hours_to_extend, (int, float)) or hours_to_extend <= 0:
            return Response({
                'success': False,
                'error': 'Invalid extension hours'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Extend the auction
        auction.end_time += timedelta(hours=hours_to_extend)
        auction.save()

        return Response({
            'success': True,
            'message': f'Auction extended by {hours_to_extend} hours',
            'new_end_time': auction.end_time.isoformat()
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def auction_analytics(request, auction_id):
    """Get detailed analytics for a specific auction"""
    try:
        auction = get_object_or_404(Auction, id=auction_id)
        
        # Bid analytics
        bids = auction.bids.all().order_by('timestamp')
        bid_data = []
        
        for bid in bids:
            bid_data.append({
                'amount': float(bid.amount),
                'timestamp': bid.timestamp.isoformat(),
                'bidder': bid.bidder.username if bid.bidder else 'Anonymous'
            })

        # Calculate bid intervals
        bid_intervals = []
        if len(bid_data) > 1:
            for i in range(1, len(bid_data)):
                prev_time = timezone.datetime.fromisoformat(bid_data[i-1]['timestamp'].replace('Z', '+00:00'))
                curr_time = timezone.datetime.fromisoformat(bid_data[i]['timestamp'].replace('Z', '+00:00'))
                interval = (curr_time - prev_time).total_seconds() / 60  # minutes
                bid_intervals.append(interval)

        analytics = {
            'auction_id': auction.id,
            'title': auction.title,
            'total_bids': bids.count(),
            'unique_bidders': bids.values('bidder').distinct().count(),
            'current_bid': float(auction.current_bid),
            'starting_bid': float(auction.starting_bid),
            'bid_increase': float(auction.current_bid - auction.starting_bid),
            'bid_increase_percentage': (
                ((auction.current_bid - auction.starting_bid) / auction.starting_bid * 100)
                if auction.starting_bid > 0 else 0
            ),
            'average_bid_interval': (
                sum(bid_intervals) / len(bid_intervals) if bid_intervals else 0
            ),
            'bid_history': bid_data,
            'time_remaining': (
                (auction.end_time - timezone.now()).total_seconds()
                if auction.is_active else 0
            )
        }

        return Response({
            'success': True,
            'data': analytics
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
