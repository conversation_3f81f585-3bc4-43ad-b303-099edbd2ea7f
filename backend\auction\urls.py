from django.http import JsonResponse
from django.urls import include, path
from django.utils import timezone
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView

from .stripe_views import (create_stripe_checkout_session,
                           create_stripe_payment_intent, stripe_webhook,
                           verify_stripe_payment)
from .views import (AnalyticsViewSet, AuctionViewSet, AuditTrailViewSet,
                    AutoBidViewSet, BidHistoryViewSet, BidViewSet,
                    CategoryViewSet, ChatMessageViewSet, ChatRoomViewSet,
                    ContactMessageViewSet, FraudDetectionViewSet, LogoutView,
                    MyTokenObtainPairView, NotificationViewSet, PaymentViewSet,
                    PricePredictionViewSet, PrivateMessageViewSet,
                    ReviewViewSet, UserProfileView, UserProfileViewSet,
                    UserRegistrationView, WatchlistViewSet,
                    admin_auctions_list, admin_delete_auction,
                    admin_delete_user, admin_edit_auction_image,
                    admin_users_list, advanced_analytics_dashboard,
                    advanced_search, ai_price_prediction, categories_list,
                    fast_dashboard_stats, invalidate_analytics_cache,
                    complete_payment, contact_inquiry, featured_auctions,
                    generate_ai_chat_response, generate_price_prediction, landing_page_data,
                    mark_notifications_read, notifications_list,
                    password_reset_confirm, password_reset_request,
                    platform_stats, price_prediction_analytics,
                    recommended_auctions, search_filters, search_suggestions,
                    test_endpoint, user_notifications)

router = DefaultRouter()
router.register(r"auctions", AuctionViewSet, basename="auction")
router.register(r"bids", BidViewSet)
router.register(r"notifications", NotificationViewSet)
router.register(r"reviews", ReviewViewSet)
router.register(r"autobids", AutoBidViewSet)
router.register(r"audit-trails", AuditTrailViewSet)
router.register(r"payments", PaymentViewSet)
router.register(r"contact-messages", ContactMessageViewSet, basename="contactmessage")
# Enhanced endpoints
router.register(r"user-profiles", UserProfileViewSet)
router.register(r"categories", CategoryViewSet)
router.register(r"watchlist", WatchlistViewSet)
router.register(r"bid-history", BidHistoryViewSet)
router.register(r"fraud-detection", FraudDetectionViewSet)
router.register(r"analytics", AnalyticsViewSet)
# AI and Chat endpoints
router.register(r"price-predictions", PricePredictionViewSet)
router.register(r"chat-rooms", ChatRoomViewSet)
router.register(r"chat-messages", ChatMessageViewSet)
router.register(r"private-messages", PrivateMessageViewSet)


def health_check(request):
    """Health check endpoint for system monitoring"""
    return JsonResponse(
        {
            "status": "healthy",
            "message": "Online Auction System API is running",
            "timestamp": timezone.now().isoformat(),
        }
    )


urlpatterns = [
    # Health check endpoint
    path("health/", health_check, name="health-check"),
    # Custom auction endpoints (must come before router to avoid conflicts)
    path("featured-auctions/", featured_auctions, name="featured-auctions"),
    path(
        "auctions/featured/", featured_auctions, name="featured-auctions-alt"
    ),  # Alternative endpoint
    # Include router URLs
    path("", include(router.urls)),
    # Authentication & user
    path("register/", UserRegistrationView.as_view(), name="register"),
    path("login/", MyTokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("logout/", LogoutView.as_view(), name="logout"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("token/verify/", TokenVerifyView.as_view(), name="token_verify"),
    path("auth/password-reset/", password_reset_request, name="password_reset"),
    path(
        "auth/password-reset-confirm/<uidb64>/<token>/",
        password_reset_confirm,
        name="password_reset_confirm",
    ),
    path("profile/", UserProfileView.as_view(), name="user-profile"),
    path("categories-with-counts/", categories_list, name="categories-list"),
    path("notifications/", notifications_list),
    path("payments/<int:pk>/complete/", complete_payment),
    path("test/", test_endpoint, name="test-endpoint"),
    # Stripe Payment Gateway URLs
    path(
        "payments/stripe/create-intent/",
        create_stripe_payment_intent,
        name="stripe-create-intent",
    ),
    path(
        "payments/stripe/create-checkout/",
        create_stripe_checkout_session,
        name="stripe-create-checkout",
    ),
    path("payments/stripe/verify/", verify_stripe_payment, name="stripe-verify"),
    path("payments/stripe/webhook/", stripe_webhook, name="stripe-webhook"),
    # AI Price Prediction endpoints
    path(
        "ai/predict-price/<int:auction_id>/",
        generate_price_prediction,
        name="generate_price_prediction",
    ),
    path("ai/price-prediction/", ai_price_prediction, name="ai_price_prediction"),
    path(
        "ai/prediction-analytics/",
        price_prediction_analytics,
        name="price_prediction_analytics",
    ),
    # AI Chat Response endpoint
    path(
        "ai/chat-response/",
        generate_ai_chat_response,
        name="generate_ai_chat_response",
    ),
    # Advanced Analytics endpoints (place before router to avoid conflicts)
    path(
        "analytics-dashboard/",
        advanced_analytics_dashboard,
        name="advanced_analytics_dashboard",
    ),
    path(
        "fast-dashboard-stats/",
        fast_dashboard_stats,
        name="fast_dashboard_stats",
    ),
    path(
        "analytics/invalidate-cache/",
        invalidate_analytics_cache,
        name="invalidate_analytics_cache",
    ),
    path(
        "analytics-test/",
        lambda request: JsonResponse({"status": "Analytics API working"}),
        name="analytics_test",
    ),
    # Advanced Search endpoints
    path("search/advanced/", advanced_search, name="advanced_search"),
    path("search/suggestions/", search_suggestions, name="search_suggestions"),
    path("search/filters/", search_filters, name="search_filters"),
    path("search/recommendations/", recommended_auctions, name="recommended_auctions"),
    # New Advanced Search endpoints
    path("new-search/", include("auction.new_search_urls")),
    # Enhanced Notifications endpoints
    path("notifications/user/", user_notifications, name="user_notifications"),
    path(
        "notifications/mark-read/",
        mark_notifications_read,
        name="mark_notifications_read",
    ),
    # Admin Management endpoints
    path("admin/users/", admin_users_list, name="admin_users_list"),
    path("admin/auctions/", admin_auctions_list, name="admin_auctions_list"),
    path(
        "admin/delete-auction/<int:auction_id>/",
        admin_delete_auction,
        name="admin_delete_auction",
    ),
    path(
        "admin/edit-auction-image/<int:auction_id>/",
        admin_edit_auction_image,
        name="admin_edit_auction_image",
    ),
    path(
        "admin/delete-user/<int:user_id>/", admin_delete_user, name="admin_delete_user"
    ),
    # Landing Page endpoints
    path("landing/data/", landing_page_data, name="landing_page_data"),
    path("landing/stats/", platform_stats, name="platform_stats"),
    path("landing/contact/", contact_inquiry, name="contact_inquiry"),
]
