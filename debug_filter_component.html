<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Filter Component</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .test-results {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h1>🔧 Filter Component Debug Panel</h1>
        <p>Debugging the NewAdvancedFilter component and API issues</p>
        
        <button onclick="testFilterAPI()">🧪 Test Filter API</button>
        <button onclick="testFrontendConnection()">🌐 Test Frontend</button>
        <button onclick="checkConsoleErrors()">🚨 Check Console</button>
        <button onclick="clearResults()">🗑️ Clear</button>
    </div>

    <div class="test-results">
        <div class="debug-panel">
            <h3>📊 API Test Results</h3>
            <div id="apiResults"></div>
        </div>
        
        <div class="debug-panel">
            <h3>🌐 Frontend Test Results</h3>
            <div id="frontendResults"></div>
        </div>
    </div>

    <div class="debug-panel">
        <h3>🚨 Console Errors & Logs</h3>
        <div id="consoleResults"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api';
        const FRONTEND_BASE = 'http://localhost:3002';
        
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const logs = [];
        
        function captureConsole() {
            console.log = function(...args) {
                logs.push({type: 'log', message: args.join(' '), time: new Date()});
                originalLog.apply(console, args);
            };
            
            console.error = function(...args) {
                logs.push({type: 'error', message: args.join(' '), time: new Date()});
                originalError.apply(console, args);
            };
            
            console.warn = function(...args) {
                logs.push({type: 'warn', message: args.join(' '), time: new Date()});
                originalWarn.apply(console, args);
            };
        }
        
        async function testFilterAPI() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<div class="info">⏳ Testing filter API...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/new-search/filters/`);
                const data = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="success">✅ API Working</div>
                        <pre>Status: ${response.status}
Categories: ${data.categories?.length || 0} items
Conditions: ${data.conditions?.length || 0} items
Sort Options: ${data.sort_options?.length || 0} items

Sample Data:
${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error">❌ API Failed</div>
                        <pre>Status: ${response.status}
Error: ${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Connection Error</div>
                    <pre>Error: ${error.message}</pre>
                `;
            }
        }
        
        async function testFrontendConnection() {
            const resultsDiv = document.getElementById('frontendResults');
            resultsDiv.innerHTML = '<div class="info">⏳ Testing frontend connection...</div>';
            
            try {
                const response = await fetch(FRONTEND_BASE);
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="success">✅ Frontend Accessible</div>
                        <pre>Status: ${response.status}
URL: ${FRONTEND_BASE}
Content-Type: ${response.headers.get('content-type')}</pre>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="warning">⚠️ Frontend Response Issue</div>
                        <pre>Status: ${response.status}</pre>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Frontend Connection Error</div>
                    <pre>Error: ${error.message}
                    
Make sure React app is running on ${FRONTEND_BASE}</pre>
                `;
            }
        }
        
        function checkConsoleErrors() {
            const resultsDiv = document.getElementById('consoleResults');
            
            if (logs.length === 0) {
                resultsDiv.innerHTML = '<div class="info">ℹ️ No console logs captured yet</div>';
                return;
            }
            
            const errorLogs = logs.filter(log => log.type === 'error');
            const warnLogs = logs.filter(log => log.type === 'warn');
            const infoLogs = logs.filter(log => log.type === 'log');
            
            let html = `
                <div class="info">📊 Console Summary</div>
                <pre>Total Logs: ${logs.length}
Errors: ${errorLogs.length}
Warnings: ${warnLogs.length}
Info: ${infoLogs.length}</pre>
            `;
            
            if (errorLogs.length > 0) {
                html += '<div class="error">🚨 Recent Errors:</div><pre>';
                errorLogs.slice(-5).forEach(log => {
                    html += `[${log.time.toLocaleTimeString()}] ${log.message}\n`;
                });
                html += '</pre>';
            }
            
            if (warnLogs.length > 0) {
                html += '<div class="warning">⚠️ Recent Warnings:</div><pre>';
                warnLogs.slice(-5).forEach(log => {
                    html += `[${log.time.toLocaleTimeString()}] ${log.message}\n`;
                });
                html += '</pre>';
            }
            
            resultsDiv.innerHTML = html;
        }
        
        function clearResults() {
            document.getElementById('apiResults').innerHTML = '';
            document.getElementById('frontendResults').innerHTML = '';
            document.getElementById('consoleResults').innerHTML = '';
            logs.length = 0;
        }
        
        // Initialize console capture
        captureConsole();
        
        // Auto-run tests
        window.addEventListener('load', () => {
            setTimeout(() => {
                testFilterAPI();
                testFrontendConnection();
            }, 1000);
        });
        
        // Periodically check for new console messages
        setInterval(checkConsoleErrors, 5000);
    </script>
</body>
</html>
