{"ast": null, "code": "import { hcl as colorHcl } from \"d3-color\";\nimport color, { hue } from \"./color.js\";\nfunction hcl(hue) {\n  return function (start, end) {\n    var h = hue((start = colorHcl(start)).h, (end = colorHcl(end)).h),\n      c = color(start.c, end.c),\n      l = color(start.l, end.l),\n      opacity = color(start.opacity, end.opacity);\n    return function (t) {\n      start.h = h(t);\n      start.c = c(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  };\n}\nexport default hcl(hue);\nexport var hclLong = hcl(color);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}