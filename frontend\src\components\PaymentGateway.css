.payment-gateway {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.payment-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.payment-header h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 24px;
  font-weight: 600;
}

.payment-amount {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.amount-usd {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 16px;
}

.amount-inr {
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 18px;
}

.payment-methods {
  margin-bottom: 30px;
}

.payment-methods h4 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

.payment-method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  margin-bottom: 12px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.payment-method:hover {
  border-color: #007bff;
  box-shadow: 0 2px 10px rgba(0, 123, 255, 0.1);
  transform: translateY(-2px);
}

.payment-method.selected {
  border-color: #007bff;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

.method-info {
  flex: 1;
}

.method-name {
  font-weight: 600;
  font-size: 16px;
  color: #2c3e50;
  margin-bottom: 5px;
}

.method-description {
  color: #6c757d;
  font-size: 14px;
  margin-bottom: 8px;
}

.method-details {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.fees {
  background: #e8f5e8;
  color: #28a745;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.processing-time {
  background: #fff3cd;
  color: #856404;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.method-radio input[type="radio"] {
  width: 20px;
  height: 20px;
  accent-color: #007bff;
}

.payment-summary {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 25px;
  border-left: 4px solid #007bff;
}

.payment-summary h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.summary-item:last-child {
  border-bottom: none;
  font-weight: 600;
  color: #007bff;
}

.payment-actions {
  text-align: center;
  margin-bottom: 25px;
}

.payment-btn {
  width: 100%;
  padding: 15px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 10px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.payment-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.payment-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.payment-security {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.security-badges {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.security-badges .badge {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.security-text {
  color: #6c757d;
  font-size: 13px;
  margin: 0;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-gateway {
    margin: 10px;
    padding: 15px;
  }
  
  .payment-amount {
    flex-direction: column;
    align-items: center;
  }
  
  .method-details {
    flex-direction: column;
    gap: 8px;
  }
  
  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .security-badges {
    flex-direction: column;
    align-items: center;
  }
}

/* Loading Animation */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Indian Flag Colors Theme */
.payment-gateway.indian-theme {
  border-top: 4px solid #ff9933;
}

.payment-gateway.indian-theme::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, #ff9933 33%, #ffffff 33%, #ffffff 66%, #138808 66%);
}

/* UPI Specific Styles */
.upi-qr-container {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  margin: 15px 0;
}

.upi-instructions {
  background: #e3f2fd;
  padding: 15px;
  border-radius: 8px;
  margin: 10px 0;
  border-left: 4px solid #2196f3;
}

.upi-instructions h5 {
  color: #1976d2;
  margin-bottom: 10px;
}

.upi-instructions ol {
  margin: 0;
  padding-left: 20px;
  color: #424242;
}
