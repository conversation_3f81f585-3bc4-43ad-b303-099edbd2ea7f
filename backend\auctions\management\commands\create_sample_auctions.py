"""
Django management command to create sample auctions for testing
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
import random

from auction.models import Auction, Category

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample auctions for testing purposes'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of sample auctions to create'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing auctions before creating new ones'
        )

    def handle(self, *args, **options):
        count = options['count']
        clear = options['clear']

        if clear:
            self.stdout.write('Clearing existing auctions...')
            Auction.objects.all().delete()

        # Ensure we have categories
        categories = self.create_categories()
        
        # Ensure we have users
        users = self.create_users()

        # Create sample auctions
        self.stdout.write(f'Creating {count} sample auctions...')
        
        sample_items = [
            {
                'title': 'Vintage Camera Collection',
                'description': 'Beautiful vintage camera from the 1960s in excellent condition.',
                'starting_bid': Decimal('150.00'),
                'category': 'Electronics'
            },
            {
                'title': 'Antique Wooden Chair',
                'description': 'Handcrafted wooden chair from the early 1900s.',
                'starting_bid': Decimal('75.00'),
                'category': 'Furniture'
            },
            {
                'title': 'Rare Book Collection',
                'description': 'First edition books in pristine condition.',
                'starting_bid': Decimal('200.00'),
                'category': 'Books'
            },
            {
                'title': 'Designer Watch',
                'description': 'Luxury watch with original packaging.',
                'starting_bid': Decimal('500.00'),
                'category': 'Jewelry'
            },
            {
                'title': 'Vintage Vinyl Records',
                'description': 'Collection of rare vinyl records from the 70s.',
                'starting_bid': Decimal('100.00'),
                'category': 'Music'
            },
            {
                'title': 'Handmade Pottery Set',
                'description': 'Beautiful ceramic pottery set, handmade by local artist.',
                'starting_bid': Decimal('50.00'),
                'category': 'Art'
            },
            {
                'title': 'Sports Memorabilia',
                'description': 'Signed baseball and trading cards.',
                'starting_bid': Decimal('125.00'),
                'category': 'Sports'
            },
            {
                'title': 'Vintage Jewelry Box',
                'description': 'Ornate jewelry box with mirror and compartments.',
                'starting_bid': Decimal('80.00'),
                'category': 'Jewelry'
            },
            {
                'title': 'Classic Board Games',
                'description': 'Collection of vintage board games in original boxes.',
                'starting_bid': Decimal('60.00'),
                'category': 'Toys'
            },
            {
                'title': 'Antique Clock',
                'description': 'Working grandfather clock from the 1800s.',
                'starting_bid': Decimal('300.00'),
                'category': 'Antiques'
            }
        ]

        created_count = 0
        for i in range(count):
            item = sample_items[i % len(sample_items)]
            
            # Get random category
            try:
                category = categories.get(name=item['category'])
            except Category.DoesNotExist:
                category = random.choice(categories)

            # Get random seller
            seller = random.choice(users)

            # Create auction with random end time (1-7 days from now)
            end_time = timezone.now() + timedelta(
                days=random.randint(1, 7),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59)
            )

            auction = Auction.objects.create(
                title=f"{item['title']} #{i+1}",
                description=item['description'],
                starting_bid=item['starting_bid'],
                current_bid=item['starting_bid'],
                end_time=end_time,
                seller=seller,
                category=category,
                is_active=True
            )

            created_count += 1
            
            if created_count % 5 == 0:
                self.stdout.write(f'Created {created_count} auctions...')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_count} sample auctions!'
            )
        )

    def create_categories(self):
        """Create sample categories if they don't exist"""
        category_names = [
            'Electronics', 'Furniture', 'Books', 'Jewelry', 'Music',
            'Art', 'Sports', 'Toys', 'Antiques', 'Collectibles'
        ]
        
        categories = []
        for name in category_names:
            category, created = Category.objects.get_or_create(
                name=name,
                defaults={'description': f'{name} category for auctions'}
            )
            categories.append(category)
            
            if created:
                self.stdout.write(f'Created category: {name}')
        
        return categories

    def create_users(self):
        """Create sample users if they don't exist"""
        users = []
        
        # Create admin user if doesn't exist
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'User',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            self.stdout.write('Created admin user')
        users.append(admin_user)

        # Create sample sellers
        sample_sellers = [
            {'username': 'seller1', 'email': '<EMAIL>', 'first_name': 'John', 'last_name': 'Doe'},
            {'username': 'seller2', 'email': '<EMAIL>', 'first_name': 'Jane', 'last_name': 'Smith'},
            {'username': 'seller3', 'email': '<EMAIL>', 'first_name': 'Bob', 'last_name': 'Johnson'},
        ]

        for seller_data in sample_sellers:
            user, created = User.objects.get_or_create(
                username=seller_data['username'],
                defaults=seller_data
            )
            if created:
                user.set_password('password123')
                user.save()
                self.stdout.write(f'Created user: {seller_data["username"]}')
            users.append(user)

        return users
