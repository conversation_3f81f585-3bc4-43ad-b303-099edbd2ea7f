{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport group, { rollup } from \"./group.js\";\nimport sort from \"./sort.js\";\nexport default function groupSort(values, reduce, key) {\n  return (reduce.length !== 2 ? sort(rollup(values, reduce, key), ([ak, av], [bk, bv]) => ascending(av, bv) || ascending(ak, bk)) : sort(group(values, key), ([ak, av], [bk, bv]) => reduce(av, bv) || ascending(ak, bk))).map(([key]) => key);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}