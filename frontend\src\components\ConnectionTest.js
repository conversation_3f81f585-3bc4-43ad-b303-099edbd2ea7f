import React, { useState, useEffect } from "react";
import axiosInstance from "../api/axiosInstance";
import { useAuth } from "../context/AuthContext";

const ConnectionTest = () => {
  const { user } = useAuth();
  const [connectionStatus, setConnectionStatus] = useState("Testing...");
  const [apiData, setApiData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    testConnection();
  }, []);

  // Only allow admin users to access connection test
  if (!user || user.role !== "admin") {
    return (
      <div className="container mt-5">
        <div className="alert alert-danger text-center">
          <h4>🔒 Access Denied</h4>
          <p>This connection test page is only accessible to admin users.</p>
          <p>Please log in as an admin to test system connections.</p>
        </div>
      </div>
    );
  }

  const testConnection = async () => {
    try {
      setConnectionStatus("Testing connection...");
      setError(null);
      setApiData(null);

      // Test basic API connection
      console.log(
        "Testing API connection to:",
        axiosInstance.defaults.baseURL + "auctions/"
      );
      const response = await axiosInstance.get("auctions/");

      setConnectionStatus("✅ Connected Successfully!");
      setApiData({
        status: response.status,
        count: response.data.count,
        results: response.data.results?.length || 0,
        baseURL: axiosInstance.defaults.baseURL,
        sampleData: response.data.results?.slice(0, 2) || [],
      });
    } catch (err) {
      console.error("Connection test failed:", err);
      setConnectionStatus("❌ Connection Failed");

      let errorDetails = {
        message: err.message,
        baseURL: axiosInstance.defaults.baseURL,
      };

      if (err.response) {
        // Server responded with error status
        errorDetails.status = err.response.status;
        errorDetails.statusText = err.response.statusText;
        errorDetails.data = err.response.data;

        if (err.response.status === 403) {
          errorDetails.suggestion =
            "Authentication required. Try logging in first.";
        } else if (err.response.status === 404) {
          errorDetails.suggestion =
            "API endpoint not found. Check if Django server is running.";
        } else if (err.response.status === 500) {
          errorDetails.suggestion = "Server error. Check Django server logs.";
        }
      } else if (err.request) {
        // Request was made but no response received
        errorDetails.suggestion =
          "No response from server. Check if Django server is running on port 8000.";
      } else {
        // Something else happened
        errorDetails.suggestion = "Network error. Check your connection.";
      }

      setError(errorDetails);
    }
  };

  return (
    <div className="container mt-4">
      <div className="card">
        <div className="card-header">
          <h3>🔗 Frontend-Backend Connection Test (Admin Only)</h3>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <h5>Connection Status:</h5>
              <p
                className={`h4 ${
                  connectionStatus.includes("✅")
                    ? "text-success"
                    : connectionStatus.includes("❌")
                    ? "text-danger"
                    : "text-warning"
                }`}
              >
                {connectionStatus}
              </p>

              <button className="btn btn-primary" onClick={testConnection}>
                🔄 Test Again
              </button>
            </div>

            <div className="col-md-6">
              <h5>Configuration:</h5>
              <ul className="list-group list-group-flush">
                <li className="list-group-item">
                  <strong>Frontend:</strong> http://localhost:3001
                </li>
                <li className="list-group-item">
                  <strong>Backend API:</strong> {axiosInstance.defaults.baseURL}
                </li>
                <li className="list-group-item">
                  <strong>Proxy:</strong> Configured in package.json
                </li>
              </ul>
            </div>
          </div>

          {apiData && (
            <div className="mt-4">
              <h5>✅ API Response Data:</h5>
              <div className="alert alert-success">
                <ul className="mb-0">
                  <li>
                    <strong>HTTP Status:</strong> {apiData.status}
                  </li>
                  <li>
                    <strong>Total Auctions:</strong> {apiData.count}
                  </li>
                  <li>
                    <strong>Results Returned:</strong> {apiData.results}
                  </li>
                  <li>
                    <strong>API Endpoint:</strong> {apiData.baseURL}auctions/
                  </li>
                </ul>
              </div>

              {apiData.sampleData && apiData.sampleData.length > 0 && (
                <div className="mt-3">
                  <h6>📊 Sample Data (First 2 Auctions):</h6>
                  <div className="row">
                    {apiData.sampleData.map((auction, index) => (
                      <div key={index} className="col-md-6 mb-2">
                        <div className="card">
                          <div className="card-body">
                            <h6 className="card-title">{auction.title}</h6>
                            <p className="card-text small">
                              <strong>Current Bid:</strong> $
                              {auction.current_bid}
                              <br />
                              <strong>Status:</strong>{" "}
                              {auction.is_closed ? "Closed" : "Active"}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {error && (
            <div className="mt-4">
              <h5>❌ Connection Error:</h5>
              <div className="alert alert-danger">
                <ul className="mb-0">
                  <li>
                    <strong>Error:</strong> {error.message}
                  </li>
                  {error.status && (
                    <li>
                      <strong>HTTP Status:</strong> {error.status}
                    </li>
                  )}
                  {error.statusText && (
                    <li>
                      <strong>Status Text:</strong> {error.statusText}
                    </li>
                  )}
                  <li>
                    <strong>Trying to connect to:</strong> {error.baseURL}
                    auctions/
                  </li>
                  {error.suggestion && (
                    <li>
                      <strong>Suggestion:</strong> {error.suggestion}
                    </li>
                  )}
                </ul>
              </div>
            </div>
          )}

          <div className="mt-4">
            <h5>🔧 Troubleshooting:</h5>
            <div className="alert alert-info">
              <p>
                <strong>If connection fails, check:</strong>
              </p>
              <ol className="mb-0">
                <li>Django server is running on http://127.0.0.1:8000/</li>
                <li>React server is running on http://localhost:3001/</li>
                <li>CORS is configured in Django settings</li>
                <li>Proxy is set in package.json</li>
                <li>No firewall blocking the ports</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConnectionTest;
