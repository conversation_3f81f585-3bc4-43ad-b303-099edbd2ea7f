import React from "react";
import { useTheme } from "../context/ThemeContext";
import "./CategoryFlipCard.css";

const CategoryFlipCard = ({ category, onClick }) => {
  const { darkMode, theme } = useTheme();

  const cardStyle = {
    "--card-bg": darkMode ? theme.surface : "#ffffff",
    "--card-text": darkMode ? theme.text : "#212529",
    "--card-border": darkMode ? theme.border : "#dee2e6",
    "--card-shadow": darkMode ? theme.shadow : "rgba(0, 0, 0, 0.1)",
    "--primary-color": theme.primary || "#3498db",
    "--secondary-color": darkMode ? theme.textSecondary : "#6c757d",
  };

  const handleClick = () => {
    const slug =
      category.slug || category.name.toLowerCase().replace(/\s+/g, "_");
    console.log("🎯 CategoryFlipCard clicked:", {
      categoryName: category.name,
      originalSlug: category.slug,
      fallbackSlug: slug,
      category: category,
    });
    onClick(slug);
  };

  return (
    <div className="category-flip-card" onClick={handleClick} style={cardStyle}>
      <div className="category-flip-card-inner">
        {/* Front Side */}
        <div className="category-flip-card-front">
          <div className="category-image-container">
            <img
              src={category.image}
              alt={category.name}
              className="category-image"
              onError={(e) => {
                e.target.src =
                  "https://picsum.photos/300/200?random=" +
                  Math.floor(Math.random() * 1000);
              }}
            />
            <div className="category-overlay">
              <h3 className="category-name">{category.name}</h3>
              <div className="category-icon">
                <i className="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>
        </div>

        {/* Back Side */}
        <div className="category-flip-card-back">
          <div className="category-stats">
            <div className="stats-header">
              <i className="fas fa-chart-bar stats-icon"></i>
              <h4 className="stats-title">{category.name}</h4>
            </div>

            <div className="stats-content">
              <div className="stat-item">
                <div className="stat-number">{category.auction_count || 0}</div>
                <div className="stat-label">
                  {(category.auction_count || 0) === 1 ? "Auction" : "Auctions"}
                </div>
              </div>

              {category.popular_auction && (
                <div className="popular-item">
                  <div className="popular-label">Most Popular:</div>
                  <div className="popular-title">
                    {category.popular_auction.title?.substring(0, 30)}
                    {category.popular_auction.title?.length > 30 ? "..." : ""}
                  </div>
                </div>
              )}

              <div className="action-hint">
                <i className="fas fa-mouse-pointer"></i>
                <span>Click to explore</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryFlipCard;
