<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Admin Login Test</h1>
        <p>Test the admin login functionality with the corrected credentials.</p>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" value="aisha_admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="aisha2024!" required>
            </div>
            
            <button type="submit">🚀 Test Login</button>
            <button type="button" onclick="clearResult()">🗑️ Clear</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api';
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '⏳ Testing login...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`${API_BASE}/login/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ LOGIN SUCCESSFUL!
                    
Status: ${response.status}
                    
🔑 Access Token: ${data.access ? data.access.substring(0, 50) + '...' : 'N/A'}
🔄 Refresh Token: ${data.refresh ? data.refresh.substring(0, 50) + '...' : 'N/A'}

👤 User Information:
${JSON.stringify(data.user || {}, null, 2)}

🔍 Admin Status:
- is_staff: ${data.user?.is_staff || false}
- is_superuser: ${data.user?.is_superuser || false}
- role: ${data.user?.role || 'N/A'}
- user_role: ${data.user?.user_role || 'N/A'}

✅ Admin login is working correctly!`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ LOGIN FAILED!
                    
Status: ${response.status}
Error: ${data.detail || 'Unknown error'}

Full Response:
${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ CONNECTION ERROR!
                
Error: ${error.message}

Make sure the Django server is running on http://127.0.0.1:8000`;
            }
        });
        
        function clearResult() {
            document.getElementById('result').innerHTML = '';
            document.getElementById('result').className = '';
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }, 1000);
        });
    </script>
</body>
</html>
