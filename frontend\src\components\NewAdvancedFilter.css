.new-advanced-filter {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e5e7eb;
}

/* Search Section */
.search-section {
  margin-bottom: 20px;
}

.search-input-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-input-container {
  display: flex;
  position: relative;
  flex: 1;
}

.search-input {
  flex: 1;
  padding: 14px 60px 14px 16px;
  border: 2px solid #d1d5db;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.2s ease;
  background: #ffffff;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-50%) scale(1.05);
}

.search-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.search-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.toggle-filters-btn {
  padding: 12px 20px;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 160px;
}

.toggle-filters-btn:hover {
  background: #4f46e5;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.clear-search-btn {
  padding: 12px 20px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.clear-search-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Filters Panel */
.filters-panel {
  border-top: 1px solid #e5e7eb;
  padding-top: 20px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filter-group label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.filter-select,
.filter-input {
  padding: 10px 12px;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  background: white;
}

.filter-select:focus,
.filter-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-input[type="number"] {
  -moz-appearance: textfield;
}

.filter-input[type="number"]::-webkit-outer-spin-button,
.filter-input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Action Buttons */
.filter-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.apply-filters-btn,
.clear-filters-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  min-width: 140px;
}

.apply-filters-btn {
  background: #10b981;
  color: white;
}

.apply-filters-btn:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
}

.apply-filters-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.clear-filters-btn {
  background: #ef4444;
  color: white;
}

.clear-filters-btn:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
}

.clear-filters-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .new-advanced-filter {
    padding: 16px;
    margin-bottom: 16px;
  }

  .search-input-group {
    gap: 12px;
  }

  .search-actions {
    flex-direction: column;
    gap: 8px;
  }

  .toggle-filters-btn,
  .clear-search-btn {
    width: 100%;
    min-width: unset;
  }

  .search-input {
    font-size: 14px;
    padding: 12px 50px 12px 14px;
  }

  .search-btn {
    width: 36px;
    height: 36px;
    right: 6px;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .filter-actions {
    flex-direction: column;
  }

  .apply-filters-btn,
  .clear-filters-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .new-advanced-filter {
    padding: 12px;
    border-radius: 8px;
  }

  .search-input {
    font-size: 14px;
    padding: 10px 12px;
  }

  .toggle-filters-btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .filter-select,
  .filter-input {
    padding: 8px 10px;
    font-size: 13px;
  }

  .apply-filters-btn,
  .clear-filters-btn {
    padding: 10px 20px;
    font-size: 13px;
  }
}

/* Loading State */
.apply-filters-btn:disabled {
  position: relative;
}

.apply-filters-btn:disabled::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  margin: auto;
  border: 2px solid transparent;
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Focus States for Accessibility */
.toggle-filters-btn:focus,
.apply-filters-btn:focus,
.clear-filters-btn:focus,
.clear-search-btn:focus,
.search-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Search Input Enhancements */
.search-input::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.search-input-container:hover .search-input {
  border-color: #9ca3af;
}

/* Button Group Styling */
.search-actions {
  align-items: center;
}

/* Loading Animation for Search Button */
.search-btn .fa-spinner {
  animation: spin 1s linear infinite;
}

/* Enhanced Visual Feedback */
.new-advanced-filter:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
  transition: all 0.3s ease;
}

/* Active Filter Indicator */
.filters-panel.has-active-filters {
  border-left: 4px solid #10b981;
  background: linear-gradient(90deg, rgba(16, 185, 129, 0.05) 0%, transparent 100%);
}

/* Hover Effects */
.filter-select:hover,
.filter-input:hover {
  border-color: #9ca3af;
}

/* Success State */
.filters-panel.success {
  border-left: 4px solid #10b981;
}

/* Error State */
.filters-panel.error {
  border-left: 4px solid #ef4444;
}
