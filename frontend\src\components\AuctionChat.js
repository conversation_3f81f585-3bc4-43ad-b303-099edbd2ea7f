import React, { useState, useEffect, useRef, useCallback } from "react";
import { useAuth } from "../context/AuthContext";
import axiosInstance from "../api/axiosInstance";
import "./AuctionChat.css";

const AuctionChat = ({ auctionId }) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const [chatRoom, setChatRoom] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const messagesEndRef = useRef(null);
  const currentUser = user?.username;

  console.log(
    "🎨 AuctionChat render - Messages:",
    messages.length,
    "User:",
    currentUser
  );

  const initializeChatRoom = useCallback(async () => {
    if (!auctionId || auctionId === "undefined" || auctionId === undefined) {
      setError("Invalid auction ID");
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Get or create chat room
      const roomResponse = await axiosInstance.get(
        `chat-rooms/?auction=${auctionId}`
      );
      let room;

      if (roomResponse.data.results && roomResponse.data.results.length > 0) {
        room = roomResponse.data.results[0];
        console.log("✅ Found existing chat room:", room.id);
      } else {
        // Create new chat room
        const createResponse = await axiosInstance.post("chat-rooms/", {
          auction: auctionId,
          is_active: true,
        });
        room = createResponse.data;
        console.log("✅ Created new chat room:", room.id);
      }

      setChatRoom(room);
      console.log(
        "Chat room initialized: ID",
        room.id,
        "for auction",
        auctionId
      );

      // Load messages
      await loadMessages(room.id);
      setLoading(false);
    } catch (err) {
      console.error("Failed to initialize chat room:", err);
      setError("Failed to load chat. Please try again.");
      setLoading(false);
    }
  }, [auctionId]);

  const loadMessages = async (roomId) => {
    try {
      console.log("📥 Loading messages for room:", roomId);
      const response = await axiosInstance.get(
        `chat-messages/?room=${roomId}&ordering=timestamp`
      );
      const newMessages = response.data.results || [];
      console.log("📥 Loaded", newMessages.length, "messages");
      console.log("📋 Message data sample:", newMessages.slice(0, 2));
      setMessages(newMessages);
      setTimeout(scrollToBottom, 100);
    } catch (err) {
      console.error("❌ Failed to load messages:", err);
    }
  };

  // Initialize chat room
  useEffect(() => {
    initializeChatRoom();
  }, [initializeChatRoom]);

  // Polling for new messages every 10 seconds (reduced frequency)
  useEffect(() => {
    if (!chatRoom?.id) return;

    const pollingInterval = setInterval(() => {
      loadMessages(chatRoom.id);
    }, 10000); // Changed from 3000 to 10000 (10 seconds)

    return () => clearInterval(pollingInterval);
  }, [chatRoom]);

  // Auto-scroll to bottom when new messages arrive (only if user is near bottom)
  useEffect(() => {
    console.log("🎨 Messages updated, count:", messages.length);
    console.log("👤 Current user:", currentUser);
    if (messages.length > 0) {
      console.log("📋 Sample message:", messages[0]);
    }

    // Only auto-scroll if user is near the bottom of chat (within 100px)
    const chatContainer = messagesEndRef.current?.parentElement;
    if (chatContainer) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainer;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

      if (isNearBottom) {
        scrollToBottom();
      }
    }
  }, [messages]);

  const sendMessage = async () => {
    if (!user) {
      alert("Please log in to send messages");
      return;
    }

    if (!newMessage.trim() || !chatRoom?.id) return;

    const messageText = newMessage.trim();
    setNewMessage(""); // Clear input immediately

    try {
      console.log("📤 Sending message:", messageText);
      const response = await axiosInstance.post("chat-messages/", {
        room: chatRoom.id,
        message: messageText,
        message_type: "text",
      });

      console.log("✅ Message sent, response:", response.data);

      // Add message to local state immediately for instant display
      const newMsg = {
        id: response.data.id || Date.now(), // Fallback ID
        message: messageText,
        sender: user.id,
        sender_username: user.username,
        message_type: "text",
        timestamp: response.data.timestamp || new Date().toISOString(),
        time_ago: "just now",
        is_ai: false,
      };

      setMessages((prev) => {
        // Check if message already exists to avoid duplicates
        const exists = prev.some((msg) => msg.id === newMsg.id);
        if (exists) return prev;
        return [...prev, newMsg];
      });

      // Reload messages after a short delay to get server data
      setTimeout(() => {
        loadMessages(chatRoom.id);
      }, 500);

      // Try to get AI response
      setTimeout(() => {
        requestAIResponse(messageText, chatRoom.id);
      }, 1000);

      console.log("✅ Message added to chat");
    } catch (error) {
      console.error("❌ Failed to send message:", error);
      alert("Failed to send message. Please try again.");
      setNewMessage(messageText); // Restore message on error
    }
  };

  const requestAIResponse = async (message, roomId) => {
    try {
      console.log("🤖 Requesting AI response for:", message);

      const response = await axiosInstance.post("ai/chat-response/", {
        message: message,
        room_id: roomId,
      });

      if (response.data.should_respond && response.data.ai_message) {
        console.log("🤖 AI responded:", response.data.ai_message.message);

        // Add AI message to local state immediately
        const aiMsg = {
          ...response.data.ai_message,
          sender_username: "AuctionStore AI",
          is_ai: true,
        };

        setMessages((prev) => {
          const exists = prev.some((msg) => msg.id === aiMsg.id);
          if (exists) return prev;
          return [...prev, aiMsg];
        });

        // Reload messages to ensure consistency
        setTimeout(() => {
          loadMessages(roomId);
        }, 500);
      } else {
        console.log("🤖 AI chose not to respond to this message");
      }
    } catch (error) {
      console.error("❌ Failed to get AI response:", error);
      // Don't show error to user - AI response is optional
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const scrollToBottom = () => {
    // Use a more gentle scroll that doesn't affect the main page
    if (messagesEndRef.current) {
      const chatContainer = messagesEndRef.current.parentElement;
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
    }
  };

  const clearAllMessages = async () => {
    if (!chatRoom?.id) {
      alert("No chat room found to clear");
      return;
    }

    const confirmClear = window.confirm(
      "Are you sure you want to clear all chat messages? This action cannot be undone."
    );

    if (!confirmClear) return;

    try {
      console.log("🗑️ Clearing all messages for room:", chatRoom.id);

      // Call backend API to delete all messages for this room
      const response = await axiosInstance.delete(
        `chat-messages/clear-room/${chatRoom.id}/`
      );

      if (response.status === 200) {
        // Clear local messages immediately
        setMessages([]);
        console.log("✅ All messages cleared successfully");

        // Show success message
        alert("All chat messages have been cleared successfully!");

        // Reload messages to ensure consistency
        setTimeout(() => {
          loadMessages(chatRoom.id);
        }, 500);
      }
    } catch (error) {
      console.error("❌ Failed to clear messages:", error);
      if (error.response?.status === 404) {
        // Fallback: Clear messages locally only
        const fallbackClear = window.confirm(
          "Backend clear endpoint not available. Clear messages locally only? (Messages will reappear on page refresh)"
        );
        if (fallbackClear) {
          setMessages([]);
          alert(
            "Messages cleared locally. They will reappear on page refresh."
          );
        }
      } else {
        alert("Failed to clear messages. Please try again.");
      }
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="auction-chat loading">
        <div className="chat-header">
          <h3>💬 Auction Chat</h3>
        </div>
        <div className="chat-loading">
          <div className="loading-spinner"></div>
          <p>Loading chat...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="auction-chat error">
        <div className="chat-header">
          <h3>💬 Auction Chat</h3>
        </div>
        <div className="chat-error">
          <p>❌ {error}</p>
          <button onClick={initializeChatRoom}>Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div className="auction-chat">
      <div className="chat-header">
        <h3>💬 Auction Chat</h3>
        <div className="connection-status">
          <span className="status-indicator connected">
            🟢 Active (HTTP API)
          </span>
          <button
            onClick={() => chatRoom?.id && loadMessages(chatRoom.id)}
            className="refresh-btn"
            title="Refresh messages"
          >
            🔄
          </button>
          <button
            onClick={clearAllMessages}
            className="clear-btn"
            title="Clear all messages"
          >
            🗑️
          </button>
        </div>
      </div>

      <div className="chat-messages">
        {messages.length === 0 ? (
          <div className="no-messages">
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          messages.map((message, index) => {
            const isOwnMessage = message.sender_username === currentUser;
            const isAIMessage =
              message.is_ai ||
              message.message_type === "ai_response" ||
              message.sender_username === "AuctionStore AI";

            return (
              <div
                key={message.id || index}
                className={`message ${message.message_type || "text"} ${
                  isOwnMessage ? "own-message" : ""
                } ${isAIMessage ? "ai-message" : ""}`}
              >
                {isAIMessage ? (
                  <div className="ai-message-content">
                    <div className="message-header">
                      <span className="sender-name ai-sender">
                        🤖 AuctionStore AI
                      </span>
                      <span className="message-time">
                        {message.time_ago || formatTime(message.timestamp)}
                      </span>
                    </div>
                    <div className="message-text ai-text">
                      {message.message}
                    </div>
                  </div>
                ) : (
                  <div className="user-message">
                    <div className="message-header">
                      <span className="sender-name">
                        {message.sender_username || "Unknown"}
                      </span>
                      <span className="message-time">
                        {message.time_ago || formatTime(message.timestamp)}
                      </span>
                    </div>
                    <div className="message-text">{message.message}</div>
                  </div>
                )}
              </div>
            );
          })
        )}

        <div ref={messagesEndRef} />
      </div>

      <div className="chat-input">
        {!user ? (
          <div className="login-prompt">
            <p>🔐 Please log in to participate in the chat</p>
            <div className="login-actions">
              <button
                onClick={() => (window.location.href = "/login")}
                className="login-btn"
              >
                Login
              </button>
              <button
                onClick={() => (window.location.href = "/register")}
                className="register-btn"
              >
                Register
              </button>
            </div>
          </div>
        ) : (
          <div className="input-container">
            <div className="user-info">
              <span className="logged-in-as">
                💬 Chatting as: <strong>{user.username}</strong>
              </span>
            </div>
            <div>
              <textarea
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Type your message..."
                rows="2"
              />
              <button
                onClick={sendMessage}
                disabled={!newMessage.trim()}
                className="send-button"
              >
                📤
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuctionChat;
