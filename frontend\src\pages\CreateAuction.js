import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import axiosInstance from "../api/axiosInstance";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";

function CreateAuction() {
  const { user } = useAuth(); // Get user from auth context
  const navigate = useNavigate();

  // Check if user has permission to create auctions
  useEffect(() => {
    if (!user) {
      navigate("/login");
      return;
    }

    if (!user.can_create_auctions && !user.is_staff) {
      Swal.fire({
        icon: "error",
        title: "Access Denied",
        text: "Your account type doesn't allow creating auctions. Please contact support to change your role.",
      }).then(() => {
        navigate("/home");
      });
      return;
    }
  }, [user, navigate]);

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    startingBid: "",
    endTime: "",
    category: "",
    condition: "New",
    image: "",
    additional_images: [], // Array for multiple images
  });

  const [aiPrediction, setAiPrediction] = useState(null);
  const [loadingPrediction, setLoadingPrediction] = useState(false);
  const [categories, setCategories] = useState([]);

  // Fetch categories on component mount
  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await axiosInstance.get("categories/");
      setCategories(response.data.categories || []);
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const handleChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));

    // Trigger AI prediction when key fields change
    if (
      ["title", "description", "category", "condition"].includes(e.target.name)
    ) {
      debouncedAiPrediction();
    }
  };

  // Handle additional images
  const handleAdditionalImageAdd = () => {
    setFormData((prev) => ({
      ...prev,
      additional_images: [...prev.additional_images, ""],
    }));
  };

  const handleAdditionalImageChange = (index, value) => {
    setFormData((prev) => ({
      ...prev,
      additional_images: prev.additional_images.map((img, i) =>
        i === index ? value : img
      ),
    }));
  };

  const handleAdditionalImageRemove = (index) => {
    setFormData((prev) => ({
      ...prev,
      additional_images: prev.additional_images.filter((_, i) => i !== index),
    }));
  };

  // Debounced AI prediction to avoid too many API calls
  const debouncedAiPrediction = (() => {
    let timeout;
    return () => {
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        if (formData.title && formData.category) {
          getAiPricePrediction();
        }
      }, 1000);
    };
  })();

  const getAiPricePrediction = async () => {
    if (!formData.title || !formData.category) return;

    setLoadingPrediction(true);
    try {
      const response = await axiosInstance.post("ai/price-prediction/", {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        condition: formData.condition,
      });
      setAiPrediction(response.data);
    } catch (error) {
      console.error("Error getting AI prediction:", error);
    } finally {
      setLoadingPrediction(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const {
      title,
      description,
      startingBid,
      endTime,
      category,
      condition,
      image,
    } = formData;

    if (!title || !description || !startingBid || !endTime || !category) {
      Swal.fire("Error", "Please fill in all required fields!", "error");
      return;
    }

    try {
      if (!user) {
        Swal.fire(
          "Error",
          "You must be logged in to create an auction.",
          "error"
        );
        return;
      }

      console.log("Creating auction with user:", user.username);

      // Filter out empty additional images
      const validAdditionalImages = formData.additional_images.filter(
        (img) => img.trim() !== ""
      );

      const response = await axiosInstance.post("auctions/", {
        title,
        description,
        starting_bid: parseFloat(startingBid),
        end_time: endTime,
        category,
        condition,
        image,
        additional_images: validAdditionalImages,
      });

      Swal.fire({
        toast: true,
        position: "top-end",
        icon: "success",
        title: "Auction created successfully!",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
      });

      console.log("Auction created successfully, navigating to auctions page");

      // Navigate to auctions page with a refresh flag
      navigate("/auctions", {
        state: {
          refresh: true,
          newAuctionId: response.data.id,
          message: "Your auction has been created successfully!",
        },
      });
    } catch (error) {
      console.error("Auction creation error:", error);

      // Extract detailed error message
      let errorMessage = "Failed to create auction";
      if (error.response?.data) {
        if (typeof error.response.data === "string") {
          errorMessage = error.response.data;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else {
          // Handle field-specific errors
          const fieldErrors = [];
          Object.keys(error.response.data).forEach((field) => {
            if (Array.isArray(error.response.data[field])) {
              fieldErrors.push(
                `${field}: ${error.response.data[field].join(", ")}`
              );
            } else {
              fieldErrors.push(`${field}: ${error.response.data[field]}`);
            }
          });
          if (fieldErrors.length > 0) {
            errorMessage = fieldErrors.join("\n");
          }
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      Swal.fire("Error", errorMessage, "error");
    }
  };

  return (
    <div className="container mt-5" style={{ maxWidth: "600px" }}>
      <h2 className="mb-4 text-center">Create New Auction</h2>
      <form onSubmit={handleSubmit}>
        {/* Title */}
        <div className="form-floating mb-3">
          <input
            type="text"
            className="form-control"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="Auction Title"
            required
          />
          <label htmlFor="title">Auction Title</label>
        </div>

        {/* Main Image URL */}
        <div className="form-floating mb-3">
          <input
            type="text"
            className="form-control"
            id="image"
            name="image"
            value={formData.image}
            onChange={handleChange}
            placeholder="Main Image URL"
            required
          />
          <label htmlFor="image">Main Image URL</label>
        </div>

        {/* Additional Images */}
        <div className="mb-3">
          <label className="form-label">Additional Images (Optional)</label>
          {formData.additional_images.map((imageUrl, index) => (
            <div key={index} className="input-group mb-2">
              <input
                type="text"
                className="form-control"
                placeholder={`Additional Image ${index + 1} URL`}
                value={imageUrl}
                onChange={(e) =>
                  handleAdditionalImageChange(index, e.target.value)
                }
              />
              <button
                type="button"
                className="btn btn-outline-danger"
                onClick={() => handleAdditionalImageRemove(index)}
              >
                Remove
              </button>
            </div>
          ))}
          {formData.additional_images.length < 5 && (
            <button
              type="button"
              className="btn btn-outline-primary btn-sm"
              onClick={handleAdditionalImageAdd}
            >
              + Add Another Image
            </button>
          )}
          <small className="text-muted d-block mt-1">
            You can add up to 5 additional images. Total:{" "}
            {formData.additional_images.length + 1} images
          </small>
        </div>

        {/* Image Previews */}
        {(formData.image ||
          formData.additional_images.some((img) => img.trim() !== "")) && (
          <div className="mb-3">
            <label className="form-label">Image Previews</label>
            <div className="row">
              {/* Main Image Preview */}
              {formData.image && (
                <div className="col-md-6 mb-3">
                  <div className="card">
                    <img
                      src={formData.image}
                      alt="Main Preview"
                      className="card-img-top"
                      style={{
                        height: "200px",
                        objectFit: "contain",
                      }}
                      onError={(e) => {
                        e.target.style.display = "none";
                      }}
                    />
                    <div className="card-body p-2">
                      <small className="text-muted">Main Image</small>
                    </div>
                  </div>
                </div>
              )}

              {/* Additional Images Preview */}
              {formData.additional_images.map(
                (imageUrl, index) =>
                  imageUrl.trim() !== "" && (
                    <div key={index} className="col-md-6 mb-3">
                      <div className="card">
                        <img
                          src={imageUrl}
                          alt={`Additional Preview ${index + 1}`}
                          className="card-img-top"
                          style={{
                            height: "200px",
                            objectFit: "contain",
                          }}
                          onError={(e) => {
                            e.target.style.display = "none";
                          }}
                        />
                        <div className="card-body p-2">
                          <small className="text-muted">
                            Additional Image {index + 1}
                          </small>
                        </div>
                      </div>
                    </div>
                  )
              )}
            </div>
          </div>
        )}

        {/* Description */}
        <div className="form-floating mb-3">
          <textarea
            className="form-control"
            id="description"
            name="description"
            rows="4"
            value={formData.description}
            onChange={handleChange}
            placeholder="Description"
            style={{ height: "120px" }}
            required
          ></textarea>
          <label htmlFor="description">Description</label>
        </div>

        {/* Starting Bid */}
        <div className="form-floating mb-3">
          <input
            type="number"
            className="form-control"
            id="startingBid"
            name="startingBid"
            value={formData.startingBid}
            onChange={handleChange}
            placeholder="Starting Bid"
            required
          />
          <label htmlFor="startingBid">Starting Bid</label>
        </div>

        {/* End Time */}
        <div className="form-floating mb-3">
          <input
            type="datetime-local"
            className="form-control"
            id="endTime"
            name="endTime"
            value={formData.endTime}
            onChange={handleChange}
            placeholder="End Time"
            required
          />
          <label htmlFor="endTime">End Time</label>
        </div>

        {/* Category */}
        <div className="form-floating mb-3">
          <select
            className="form-select"
            id="category"
            name="category"
            value={formData.category}
            onChange={handleChange}
            required
          >
            <option value="">Select Category</option>
            {categories.length > 0 ? (
              categories.map((cat, index) => (
                <option key={index} value={cat}>
                  {cat}
                </option>
              ))
            ) : (
              <>
                <option value="Electronics">Electronics</option>
                <option value="Fashion">Fashion</option>
                <option value="Home & Garden">Home & Garden</option>
                <option value="Sports">Sports</option>
                <option value="Books">Books</option>
                <option value="Art">Art</option>
                <option value="Collectibles">Collectibles</option>
                <option value="Vehicles">Vehicles</option>
                <option value="Jewelry">Jewelry</option>
                <option value="Other">Other</option>
              </>
            )}
          </select>
          <label htmlFor="category">Category</label>
        </div>

        {/* Condition */}
        <div className="form-floating mb-3">
          <select
            className="form-select"
            id="condition"
            name="condition"
            value={formData.condition}
            onChange={handleChange}
            required
          >
            <option value="New">New</option>
            <option value="Like New">Like New</option>
            <option value="Good">Good</option>
            <option value="Fair">Fair</option>
            <option value="Poor">Poor</option>
          </select>
          <label htmlFor="condition">Condition</label>
        </div>

        {/* AI Price Prediction */}
        {(aiPrediction || loadingPrediction) && (
          <div className="card mb-3 border-primary">
            <div className="card-header bg-primary text-white">
              <h6 className="mb-0">🤖 AI Price Prediction</h6>
            </div>
            <div className="card-body">
              {loadingPrediction ? (
                <div className="text-center">
                  <div
                    className="spinner-border spinner-border-sm me-2"
                    role="status"
                  >
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  Analyzing market data...
                </div>
              ) : aiPrediction ? (
                <div>
                  <div className="row">
                    <div className="col-md-6">
                      <p className="mb-1">
                        <strong>Predicted Price Range:</strong>
                      </p>
                      <p className="text-success fs-5 mb-2">
                        ${aiPrediction.min_price} - ${aiPrediction.max_price}
                      </p>
                    </div>
                    <div className="col-md-6">
                      <p className="mb-1">
                        <strong>Recommended Starting Bid:</strong>
                      </p>
                      <p className="text-primary fs-5 mb-2">
                        ${aiPrediction.recommended_price}
                      </p>
                    </div>
                  </div>
                  <div className="row">
                    <div className="col-md-6">
                      <p className="mb-1">
                        <strong>Confidence:</strong>
                      </p>
                      <div className="progress mb-2">
                        <div
                          className="progress-bar"
                          role="progressbar"
                          style={{ width: `${aiPrediction.confidence}%` }}
                        >
                          {aiPrediction.confidence}%
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <button
                        type="button"
                        className="btn btn-outline-primary btn-sm"
                        onClick={() => {
                          setFormData((prev) => ({
                            ...prev,
                            startingBid: aiPrediction.recommended_price,
                          }));
                        }}
                      >
                        Use Recommended Price
                      </button>
                    </div>
                  </div>
                  {aiPrediction.market_insights && (
                    <div className="mt-2">
                      <small className="text-muted">
                        <strong>Market Insights:</strong>{" "}
                        {aiPrediction.market_insights}
                      </small>
                    </div>
                  )}
                </div>
              ) : null}
            </div>
          </div>
        )}

        <button type="submit" className="btn btn-primary w-100">
          Create Auction
        </button>
      </form>
    </div>
  );
}

export default CreateAuction;
