{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "animate.css": "^4.1.1", "axios": "^1.9.0", "bootstrap": "^5.3.5", "firebase": "^11.6.1", "jwt-decode": "^3.1.2", "react": "^18.2.0", "react-bootstrap": "^2.10.9", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "recharts": "^2.15.3", "sweetalert2": "^11.19.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "postcss-flexbugs-fixes": "^5.0.2", "postcss-preset-env": "^10.2.0"}, "overrides": {"react-scripts": {"typescript": "^4.9.5"}}, "proxy": "http://127.0.0.1:8000"}