<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Filter Options API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .filter-demo {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        select, input {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Filter Options API Test</h1>
        <p>Testing the new-search/filters/ endpoint and dropdown population.</p>
        
        <button onclick="testFilterOptions()">🚀 Test Filter Options API</button>
        <button onclick="clearResult()">🗑️ Clear</button>
        
        <div id="result"></div>
        
        <div class="filter-demo" id="filterDemo" style="display: none;">
            <h3>📋 Filter Demo</h3>
            <div>
                <label>Category:</label>
                <select id="categorySelect">
                    <option value="">All Categories</option>
                </select>
            </div>
            <div>
                <label>Condition:</label>
                <select id="conditionSelect">
                    <option value="">Any Condition</option>
                </select>
            </div>
            <div>
                <label>Sort By:</label>
                <select id="sortSelect">
                    <option value="">Select Sort Option</option>
                </select>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api';
        
        async function testFilterOptions() {
            const resultDiv = document.getElementById('result');
            const filterDemo = document.getElementById('filterDemo');
            
            resultDiv.innerHTML = '⏳ Testing filter options API...';
            resultDiv.className = 'result';
            filterDemo.style.display = 'none';
            
            try {
                const response = await fetch(`${API_BASE}/new-search/filters/`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ FILTER OPTIONS API SUCCESS!
                    
Status: ${response.status}
                    
📊 API Response:
${JSON.stringify(data, null, 2)}

📈 Summary:
- Categories: ${data.categories?.length || 0} items
- Conditions: ${data.conditions?.length || 0} items  
- Sort Options: ${data.sort_options?.length || 0} items

Categories: ${data.categories?.join(', ') || 'None'}
Conditions: ${data.conditions?.join(', ') || 'None'}`;

                    // Populate demo dropdowns
                    populateDropdowns(data);
                    filterDemo.style.display = 'block';
                    
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ API FAILED!
                    
Status: ${response.status}
Error: ${data.detail || 'Unknown error'}

Full Response:
${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ CONNECTION ERROR!
                
Error: ${error.message}

Make sure the Django server is running on http://127.0.0.1:8000`;
            }
        }
        
        function populateDropdowns(data) {
            // Populate categories
            const categorySelect = document.getElementById('categorySelect');
            categorySelect.innerHTML = '<option value="">All Categories</option>';
            if (data.categories) {
                data.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = formatCategoryName(category);
                    categorySelect.appendChild(option);
                });
            }
            
            // Populate conditions
            const conditionSelect = document.getElementById('conditionSelect');
            conditionSelect.innerHTML = '<option value="">Any Condition</option>';
            if (data.conditions) {
                data.conditions.forEach(condition => {
                    const option = document.createElement('option');
                    option.value = condition;
                    option.textContent = condition;
                    conditionSelect.appendChild(option);
                });
            }
            
            // Populate sort options
            const sortSelect = document.getElementById('sortSelect');
            sortSelect.innerHTML = '<option value="">Select Sort Option</option>';
            if (data.sort_options) {
                data.sort_options.forEach(sortOption => {
                    const option = document.createElement('option');
                    option.value = sortOption.value;
                    option.textContent = sortOption.label;
                    sortSelect.appendChild(option);
                });
            }
        }
        
        function formatCategoryName(category) {
            const categoryMap = {
                electronics: "Electronics",
                fashion: "Fashion & Clothing",
                home_garden: "Home & Garden",
                art: "Art & Collectibles",
                automotive: "Automotive",
                books: "Books & Media",
                collectibles: "Collectibles",
                jewelry: "Jewelry & Watches",
                sports: "Sports & Recreation"
            };
            return categoryMap[category] || category.charAt(0).toUpperCase() + category.slice(1);
        }
        
        function clearResult() {
            document.getElementById('result').innerHTML = '';
            document.getElementById('result').className = '';
            document.getElementById('filterDemo').style.display = 'none';
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testFilterOptions();
            }, 1000);
        });
    </script>
</body>
</html>
