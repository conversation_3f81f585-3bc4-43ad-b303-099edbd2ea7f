"""
User Acceptance Test Cases for Online Auction System
These tests simulate real user scenarios and workflows
"""

import unittest
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class UserAcceptanceTestBase(unittest.TestCase):
    """Base class for user acceptance tests"""
    
    @classmethod
    def setUpClass(cls):
        """Set up Chrome driver with options"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        # Uncomment for headless mode
        # chrome_options.add_argument("--headless")
        
        cls.driver = webdriver.Chrome(options=chrome_options)
        cls.driver.implicitly_wait(10)
        cls.wait = WebDriverWait(cls.driver, 10)
        
        # Test URLs
        cls.base_url = "http://localhost:3001"
        cls.api_url = "http://127.0.0.1:8000"
    
    @classmethod
    def tearDownClass(cls):
        """Clean up driver"""
        cls.driver.quit()
    
    def setUp(self):
        """Navigate to home page before each test"""
        self.driver.get(self.base_url)
        time.sleep(2)  # Allow page to load
    
    def login_user(self, username="testuser", password="TestPass123!"):
        """Helper method to log in a user"""
        try:
            # Click login button
            login_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Login')]"))
            )
            login_btn.click()
            
            # Fill login form
            username_field = self.wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            username_field.send_keys(username)
            
            password_field = self.driver.find_element(By.NAME, "password")
            password_field.send_keys(password)
            
            # Submit form
            submit_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            submit_btn.click()
            
            # Wait for successful login
            self.wait.until(
                EC.presence_of_element_located((By.XPATH, "//a[contains(text(), 'Dashboard')]"))
            )
            return True
            
        except TimeoutException:
            return False
    
    def register_user(self, username, email, password, first_name="Test", last_name="User"):
        """Helper method to register a new user"""
        try:
            # Click register button
            register_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Register')]"))
            )
            register_btn.click()
            
            # Fill registration form
            self.driver.find_element(By.NAME, "username").send_keys(username)
            self.driver.find_element(By.NAME, "email").send_keys(email)
            self.driver.find_element(By.NAME, "first_name").send_keys(first_name)
            self.driver.find_element(By.NAME, "last_name").send_keys(last_name)
            self.driver.find_element(By.NAME, "password").send_keys(password)
            self.driver.find_element(By.NAME, "password2").send_keys(password)
            
            # Submit form
            submit_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            submit_btn.click()
            
            # Wait for success message or redirect
            time.sleep(3)
            return True
            
        except (TimeoutException, NoSuchElementException):
            return False


class UserRegistrationAndLoginTests(UserAcceptanceTestBase):
    """Test user registration and login workflows"""
    
    def test_user_registration_workflow(self):
        """
        UAT-001: User Registration
        As a new user, I want to register an account so that I can participate in auctions
        """
        print("\n🧪 UAT-001: Testing User Registration Workflow")
        
        # Step 1: Navigate to registration page
        success = self.register_user(
            username="newuser123",
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        self.assertTrue(success, "User registration should be successful")
        
        # Step 2: Verify registration success
        # Check for success message or redirect to login
        current_url = self.driver.current_url
        self.assertIn("login", current_url.lower(), "Should redirect to login after registration")
        
        print("✅ User registration workflow completed successfully")
    
    def test_user_login_workflow(self):
        """
        UAT-002: User Login
        As a registered user, I want to log in so that I can access my account
        """
        print("\n🧪 UAT-002: Testing User Login Workflow")
        
        # Prerequisite: Register a user first
        self.register_user("loginuser", "<EMAIL>", "TestPass123!")
        time.sleep(2)
        
        # Step 1: Navigate to login page and log in
        success = self.login_user("loginuser", "TestPass123!")
        self.assertTrue(success, "User login should be successful")
        
        # Step 2: Verify login success
        # Check for dashboard or user menu
        try:
            user_menu = self.wait.until(
                EC.presence_of_element_located((By.XPATH, "//a[contains(text(), 'Dashboard')]"))
            )
            self.assertTrue(user_menu.is_displayed(), "User dashboard should be visible after login")
        except TimeoutException:
            self.fail("Dashboard not found after login")
        
        print("✅ User login workflow completed successfully")
    
    def test_invalid_login_workflow(self):
        """
        UAT-003: Invalid Login
        As a user, I should see an error message when I enter invalid credentials
        """
        print("\n🧪 UAT-003: Testing Invalid Login Workflow")
        
        # Step 1: Attempt login with invalid credentials
        try:
            login_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Login')]"))
            )
            login_btn.click()
            
            # Fill with invalid credentials
            username_field = self.wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            username_field.send_keys("invaliduser")
            
            password_field = self.driver.find_element(By.NAME, "password")
            password_field.send_keys("wrongpassword")
            
            submit_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            submit_btn.click()
            
            # Step 2: Verify error message appears
            error_message = self.wait.until(
                EC.presence_of_element_located((By.CLASS_NAME, "error"))
            )
            self.assertTrue(error_message.is_displayed(), "Error message should be displayed")
            
        except TimeoutException:
            # If no error element found, check if still on login page
            current_url = self.driver.current_url
            self.assertIn("login", current_url.lower(), "Should remain on login page after failed login")
        
        print("✅ Invalid login workflow completed successfully")


class AuctionBrowsingTests(UserAcceptanceTestBase):
    """Test auction browsing and search functionality"""
    
    def test_browse_auctions_workflow(self):
        """
        UAT-004: Browse Auctions
        As a user, I want to browse available auctions so that I can find items of interest
        """
        print("\n🧪 UAT-004: Testing Browse Auctions Workflow")
        
        # Step 1: Navigate to auctions page
        try:
            auctions_link = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Auctions') or contains(text(), 'Browse')]"))
            )
            auctions_link.click()
            
            # Step 2: Verify auctions are displayed
            auction_items = self.wait.until(
                EC.presence_of_all_elements_located((By.CLASS_NAME, "auction-card"))
            )
            self.assertGreater(len(auction_items), 0, "Should display auction items")
            
            # Step 3: Test auction item click
            first_auction = auction_items[0]
            first_auction.click()
            
            # Step 4: Verify auction detail page
            auction_title = self.wait.until(
                EC.presence_of_element_located((By.TAG_NAME, "h1"))
            )
            self.assertTrue(auction_title.is_displayed(), "Auction title should be visible")
            
        except TimeoutException:
            self.fail("Could not browse auctions properly")
        
        print("✅ Browse auctions workflow completed successfully")
    
    def test_search_auctions_workflow(self):
        """
        UAT-005: Search Auctions
        As a user, I want to search for specific auctions so that I can find what I'm looking for
        """
        print("\n🧪 UAT-005: Testing Search Auctions Workflow")
        
        try:
            # Step 1: Find search box
            search_box = self.wait.until(
                EC.presence_of_element_located((By.NAME, "search"))
            )
            
            # Step 2: Enter search term
            search_box.send_keys("electronics")
            search_box.send_keys(Keys.RETURN)
            
            # Step 3: Verify search results
            time.sleep(2)  # Allow search to complete
            
            # Check if results are displayed
            try:
                results = self.driver.find_elements(By.CLASS_NAME, "auction-card")
                # Results might be empty, but search should execute without error
                print(f"Found {len(results)} search results")
            except NoSuchElementException:
                print("No search results found (this is acceptable)")
            
        except TimeoutException:
            self.fail("Could not perform search")
        
        print("✅ Search auctions workflow completed successfully")
    
    def test_filter_auctions_workflow(self):
        """
        UAT-006: Filter Auctions
        As a user, I want to filter auctions by category and price so that I can narrow my search
        """
        print("\n🧪 UAT-006: Testing Filter Auctions Workflow")
        
        try:
            # Step 1: Navigate to search/filter page
            self.driver.get(f"{self.base_url}/search")
            
            # Step 2: Apply category filter
            try:
                category_filter = Select(self.driver.find_element(By.NAME, "category"))
                category_filter.select_by_visible_text("Electronics")
            except NoSuchElementException:
                print("Category filter not found, skipping...")
            
            # Step 3: Apply price filter
            try:
                min_price = self.driver.find_element(By.NAME, "min_price")
                min_price.send_keys("100")
                
                max_price = self.driver.find_element(By.NAME, "max_price")
                max_price.send_keys("1000")
            except NoSuchElementException:
                print("Price filters not found, skipping...")
            
            # Step 4: Apply filters
            try:
                filter_btn = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Filter') or contains(text(), 'Search')]")
                filter_btn.click()
                time.sleep(2)
            except NoSuchElementException:
                print("Filter button not found, skipping...")
            
        except Exception as e:
            print(f"Filter test encountered issue: {e}")
        
        print("✅ Filter auctions workflow completed successfully")


class AuctionParticipationTests(UserAcceptanceTestBase):
    """Test auction participation workflows"""
    
    def test_create_auction_workflow(self):
        """
        UAT-007: Create Auction
        As a registered user, I want to create an auction so that I can sell my items
        """
        print("\n🧪 UAT-007: Testing Create Auction Workflow")
        
        # Prerequisite: Register and login
        self.register_user("seller123", "<EMAIL>", "TestPass123!")
        time.sleep(2)
        success = self.login_user("seller123", "TestPass123!")
        self.assertTrue(success, "Should be able to login")
        
        try:
            # Step 1: Navigate to create auction page
            create_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Create') or contains(text(), 'Sell')]"))
            )
            create_btn.click()
            
            # Step 2: Fill auction form
            title_field = self.wait.until(
                EC.presence_of_element_located((By.NAME, "title"))
            )
            title_field.send_keys("Test Auction Item")
            
            description_field = self.driver.find_element(By.NAME, "description")
            description_field.send_keys("This is a test auction item for UAT testing")
            
            starting_bid_field = self.driver.find_element(By.NAME, "starting_bid")
            starting_bid_field.send_keys("100")
            
            # Select category
            try:
                category_select = Select(self.driver.find_element(By.NAME, "category"))
                category_select.select_by_visible_text("Electronics")
            except NoSuchElementException:
                print("Category select not found")
            
            # Step 3: Submit auction
            submit_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            submit_btn.click()
            
            # Step 4: Verify auction creation
            time.sleep(3)
            success_message = self.driver.find_elements(By.CLASS_NAME, "success")
            if success_message:
                self.assertTrue(success_message[0].is_displayed(), "Success message should be displayed")
            
        except TimeoutException:
            self.fail("Could not create auction")
        
        print("✅ Create auction workflow completed successfully")
    
    def test_place_bid_workflow(self):
        """
        UAT-008: Place Bid
        As a registered user, I want to place bids on auctions so that I can win items
        """
        print("\n🧪 UAT-008: Testing Place Bid Workflow")
        
        # Prerequisite: Register and login as bidder
        self.register_user("bidder123", "<EMAIL>", "TestPass123!")
        time.sleep(2)
        success = self.login_user("bidder123", "TestPass123!")
        self.assertTrue(success, "Should be able to login")
        
        try:
            # Step 1: Navigate to an auction
            self.driver.get(f"{self.base_url}/auctions")
            
            # Find first auction
            auction_items = self.wait.until(
                EC.presence_of_all_elements_located((By.CLASS_NAME, "auction-card"))
            )
            
            if auction_items:
                auction_items[0].click()
                
                # Step 2: Place bid
                bid_amount_field = self.wait.until(
                    EC.presence_of_element_located((By.NAME, "bid_amount"))
                )
                bid_amount_field.send_keys("150")
                
                bid_btn = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Bid') or contains(text(), 'Place')]")
                bid_btn.click()
                
                # Step 3: Verify bid placement
                time.sleep(2)
                # Look for success message or updated bid amount
                try:
                    success_elements = self.driver.find_elements(By.CLASS_NAME, "success")
                    if success_elements:
                        self.assertTrue(success_elements[0].is_displayed(), "Bid success message should appear")
                except NoSuchElementException:
                    print("Success message not found, but bid may have been placed")
            
        except TimeoutException:
            print("No auctions available for bidding test")
        
        print("✅ Place bid workflow completed successfully")


class ResponsiveDesignTests(UserAcceptanceTestBase):
    """Test responsive design and mobile compatibility"""
    
    def test_mobile_responsive_workflow(self):
        """
        UAT-009: Mobile Responsive Design
        As a mobile user, I want the site to work properly on my mobile device
        """
        print("\n🧪 UAT-009: Testing Mobile Responsive Design")
        
        # Step 1: Set mobile viewport
        self.driver.set_window_size(375, 667)  # iPhone 6/7/8 size
        
        # Step 2: Navigate to home page
        self.driver.get(self.base_url)
        time.sleep(2)
        
        # Step 3: Check if mobile menu exists
        try:
            # Look for hamburger menu or mobile navigation
            mobile_menu = self.driver.find_elements(By.CLASS_NAME, "mobile-menu")
            hamburger = self.driver.find_elements(By.CLASS_NAME, "hamburger")
            
            mobile_friendly = len(mobile_menu) > 0 or len(hamburger) > 0
            self.assertTrue(mobile_friendly, "Mobile navigation should be present")
            
        except Exception as e:
            print(f"Mobile menu test issue: {e}")
        
        # Step 4: Test touch-friendly elements
        try:
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            for button in buttons[:3]:  # Test first 3 buttons
                size = button.size
                # Touch targets should be at least 44px
                self.assertGreaterEqual(max(size['height'], size['width']), 40, 
                                      "Touch targets should be large enough")
        except Exception as e:
            print(f"Touch target test issue: {e}")
        
        # Reset window size
        self.driver.set_window_size(1920, 1080)
        
        print("✅ Mobile responsive design workflow completed successfully")


class PWAFunctionalityTests(UserAcceptanceTestBase):
    """Test Progressive Web App functionality"""
    
    def test_pwa_installation_workflow(self):
        """
        UAT-010: PWA Installation
        As a user, I want to install the app on my device for easy access
        """
        print("\n🧪 UAT-010: Testing PWA Installation Workflow")
        
        # Step 1: Check for PWA manifest
        try:
            manifest_link = self.driver.find_element(By.XPATH, "//link[@rel='manifest']")
            self.assertTrue(manifest_link.is_present, "PWA manifest should be present")
        except NoSuchElementException:
            self.fail("PWA manifest not found")
        
        # Step 2: Check for service worker registration
        # This would require JavaScript execution to check
        sw_registered = self.driver.execute_script("""
            return 'serviceWorker' in navigator;
        """)
        self.assertTrue(sw_registered, "Service Worker should be supported")
        
        # Step 3: Check for install prompt (if available)
        try:
            install_prompt = self.driver.find_elements(By.CLASS_NAME, "install-prompt")
            if install_prompt:
                print("Install prompt found")
            else:
                print("Install prompt not currently shown (this is normal)")
        except Exception as e:
            print(f"Install prompt check issue: {e}")
        
        print("✅ PWA installation workflow completed successfully")


if __name__ == "__main__":
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        UserRegistrationAndLoginTests,
        AuctionBrowsingTests,
        AuctionParticipationTests,
        ResponsiveDesignTests,
        PWAFunctionalityTests
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print("USER ACCEPTANCE TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
