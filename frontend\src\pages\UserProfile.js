import React, { useState, useEffect } from "react";
import {
  Container,
  <PERSON>,
  Col,
  Card,
  Form,
  Button,
  Tab,
  Tabs,
  <PERSON>,
  Badge,
  Alert,
} from "react-bootstrap";
import {
  FaUser,
  FaGavel,
  FaHeart,
  FaStar,
  FaEdit,
  FaSave,
  FaTimes,
  FaEye,
  FaTrash,
  FaTrophy,
  FaClock,
  FaCreditCard,
} from "react-icons/fa";
import { useAuth } from "../context/AuthContext";
import { formatAuctionPrice } from "../utils/currency";
import { Link, useLocation } from "react-router-dom";

const UserProfile = () => {
  const { user } = useAuth();
  const location = useLocation();
  const [profile, setProfile] = useState({
    phone_number: "",
    address: "",
    city: "",
    country: "",
    postal_code: "",
    bio: "",
    email_notifications: true,
    sms_notifications: false,
  });
  const [isEditing, setIsEditing] = useState(false);
  const [userAuctions, setUserAuctions] = useState([]);
  const [userBids, setUserBids] = useState([]);
  const [wonAuctions, setWonAuctions] = useState([]);
  const [watchlist, setWatchlist] = useState([]);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState("");
  const [activeTab, setActiveTab] = useState("profile");

  // Get tab from URL parameter
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const tabParam = urlParams.get("tab");
    if (
      tabParam &&
      ["profile", "auctions", "won", "watchlist"].includes(tabParam)
    ) {
      setActiveTab(tabParam);
    }
  }, [location.search]);

  useEffect(() => {
    fetchUserData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchUserData = async () => {
    try {
      setLoading(true);

      // Fetch user profile
      const profileResponse = await fetch(
        "http://127.0.0.1:8000/api/user-profiles/",
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );

      if (profileResponse.ok) {
        const profileData = await profileResponse.json();
        if (profileData.results && profileData.results.length > 0) {
          setProfile(profileData.results[0]);
        }
      }

      // Fetch user auctions (using user ID if available, fallback to username)
      console.log("Fetching auctions for user:", user);
      const ownerParam = user.id || user.username;
      const auctionsResponse = await fetch(
        `http://127.0.0.1:8000/api/auctions/?owner=${ownerParam}&page_size=100`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );

      if (auctionsResponse.ok) {
        const auctionsData = await auctionsResponse.json();
        console.log("Auctions API response:", auctionsData);
        setUserAuctions(auctionsData.results || []);
      } else {
        console.error(
          "Failed to fetch auctions:",
          auctionsResponse.status,
          auctionsResponse.statusText
        );
      }

      // Fetch user bids (with page_size=100)
      const userParam = user.id || user.username;
      const bidsResponse = await fetch(
        `http://127.0.0.1:8000/api/bids/?user=${userParam}&page_size=100`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );

      if (bidsResponse.ok) {
        const bidsData = await bidsResponse.json();
        setUserBids(bidsData.results || []);
      }

      // Fetch watchlist (with page_size=100)
      const watchlistResponse = await fetch(
        "http://127.0.0.1:8000/api/watchlist/?page_size=100",
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );

      if (watchlistResponse.ok) {
        const watchlistData = await watchlistResponse.json();
        setWatchlist(watchlistData.results || []);
      }

      // Fetch won auctions using the new dedicated endpoint
      const wonAuctionsResponse = await fetch(
        "http://127.0.0.1:8000/api/auctions/won_auctions/",
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );

      if (wonAuctionsResponse.ok) {
        const wonAuctionsData = await wonAuctionsResponse.json();
        if (wonAuctionsData.success) {
          setWonAuctions(wonAuctionsData.results || []);
        } else {
          console.error("Failed to fetch won auctions:", wonAuctionsData.error);
          setWonAuctions([]);
        }
      } else {
        console.error("Won auctions API call failed");
        setWonAuctions([]);
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
      setMessage("Error loading profile data");
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    try {
      const method = profile.id ? "PUT" : "POST";
      const url = profile.id
        ? `http://127.0.0.1:8000/api/user-profiles/${profile.id}/`
        : "http://127.0.0.1:8000/api/user-profiles/";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("access_token")}`,
        },
        body: JSON.stringify(profile),
      });

      if (response.ok) {
        const updatedProfile = await response.json();
        setProfile(updatedProfile);
        setIsEditing(false);
        setMessage("Profile updated successfully!");
        setTimeout(() => setMessage(""), 3000);
      } else {
        setMessage("Error updating profile");
      }
    } catch (error) {
      console.error("Error saving profile:", error);
      setMessage("Error saving profile");
    }
  };

  const handleInputChange = (field, value) => {
    setProfile((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleDeleteAuction = async (auctionId) => {
    if (!window.confirm("Are you sure you want to delete this auction?")) {
      return;
    }

    try {
      const response = await fetch(
        `http://127.0.0.1:8000/api/auctions/${auctionId}/`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );

      if (response.ok) {
        setUserAuctions((prev) =>
          prev.filter((auction) => auction.id !== auctionId)
        );
        setMessage("Auction deleted successfully!");
        setTimeout(() => setMessage(""), 3000);
      } else {
        setMessage("Failed to delete auction");
      }
    } catch (error) {
      console.error("Error deleting auction:", error);
      setMessage("Error deleting auction");
    }
  };

  if (loading) {
    return (
      <Container className="mt-5">
        <div className="text-center">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </Container>
    );
  }

  return (
    <Container className="mt-4">
      <Row>
        <Col md={4}>
          <Card>
            <Card.Body className="text-center">
              <div className="mb-3">
                <FaUser size={80} className="text-muted" />
              </div>
              <h4>{user?.username}</h4>
              <p className="text-muted">{user?.email}</p>

              <div className="d-flex justify-content-around mt-3">
                <div className="text-center">
                  <h5>{userAuctions.length}</h5>
                  <small className="text-muted">Created</small>
                </div>
                <div className="text-center">
                  <h5>{userBids.length}</h5>
                  <small className="text-muted">Bids</small>
                </div>
                <div className="text-center">
                  <h5>{wonAuctions.length}</h5>
                  <small className="text-muted">Won</small>
                </div>
                <div className="text-center">
                  <h5>{watchlist.length}</h5>
                  <small className="text-muted">Watching</small>
                </div>
              </div>

              <div className="mt-3">
                <div className="d-flex align-items-center justify-content-center">
                  <FaStar className="text-warning me-1" />
                  <span>{profile.rating || 0} Rating</span>
                </div>
                <small className="text-muted">
                  {profile.total_sales || 0} sales •{" "}
                  {profile.total_purchases || 0} purchases
                </small>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col md={8}>
          {message && (
            <Alert variant={message.includes("Error") ? "danger" : "success"}>
              {message}
            </Alert>
          )}

          <Tabs
            activeKey={activeTab}
            onSelect={(k) => setActiveTab(k)}
            className="mb-3"
          >
            <Tab
              eventKey="profile"
              title={
                <>
                  <FaUser className="me-2" />
                  Profile
                </>
              }
            >
              <Card>
                <Card.Header className="d-flex justify-content-between align-items-center">
                  <h5>Profile Information</h5>
                  <Button
                    variant={isEditing ? "success" : "outline-primary"}
                    size="sm"
                    onClick={
                      isEditing ? handleSaveProfile : () => setIsEditing(true)
                    }
                  >
                    {isEditing ? (
                      <>
                        <FaSave className="me-1" />
                        Save
                      </>
                    ) : (
                      <>
                        <FaEdit className="me-1" />
                        Edit
                      </>
                    )}
                  </Button>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Phone Number</Form.Label>
                        <Form.Control
                          type="text"
                          value={profile.phone_number || ""}
                          onChange={(e) =>
                            handleInputChange("phone_number", e.target.value)
                          }
                          disabled={!isEditing}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>City</Form.Label>
                        <Form.Control
                          type="text"
                          value={profile.city || ""}
                          onChange={(e) =>
                            handleInputChange("city", e.target.value)
                          }
                          disabled={!isEditing}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Country</Form.Label>
                        <Form.Control
                          type="text"
                          value={profile.country || ""}
                          onChange={(e) =>
                            handleInputChange("country", e.target.value)
                          }
                          disabled={!isEditing}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Postal Code</Form.Label>
                        <Form.Control
                          type="text"
                          value={profile.postal_code || ""}
                          onChange={(e) =>
                            handleInputChange("postal_code", e.target.value)
                          }
                          disabled={!isEditing}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Address</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={2}
                      value={profile.address || ""}
                      onChange={(e) =>
                        handleInputChange("address", e.target.value)
                      }
                      disabled={!isEditing}
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Bio</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      value={profile.bio || ""}
                      onChange={(e) => handleInputChange("bio", e.target.value)}
                      disabled={!isEditing}
                      placeholder="Tell us about yourself..."
                    />
                  </Form.Group>

                  <h6>Notification Preferences</h6>
                  <Form.Check
                    type="checkbox"
                    label="Email Notifications"
                    checked={profile.email_notifications}
                    onChange={(e) =>
                      handleInputChange("email_notifications", e.target.checked)
                    }
                    disabled={!isEditing}
                    className="mb-2"
                  />
                  <Form.Check
                    type="checkbox"
                    label="SMS Notifications"
                    checked={profile.sms_notifications}
                    onChange={(e) =>
                      handleInputChange("sms_notifications", e.target.checked)
                    }
                    disabled={!isEditing}
                  />

                  {isEditing && (
                    <div className="mt-3">
                      <Button
                        variant="secondary"
                        onClick={() => setIsEditing(false)}
                        className="me-2"
                      >
                        <FaTimes className="me-1" />
                        Cancel
                      </Button>
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Tab>

            <Tab
              eventKey="auctions"
              title={
                <>
                  <FaGavel className="me-2" />
                  My Auctions
                </>
              }
            >
              <Card>
                <Card.Body>
                  {userAuctions.length === 0 ? (
                    <p className="text-muted">
                      You haven't created any auctions yet.
                    </p>
                  ) : (
                    <Table responsive>
                      <thead>
                        <tr>
                          <th>ID</th>
                          <th>Title</th>
                          <th>Current Bid</th>
                          <th>Bids</th>
                          <th>Status</th>
                          <th>End Date</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {userAuctions.map((auction) => (
                          <tr key={auction.id}>
                            <td>
                              <Badge bg="primary">#{auction.id}</Badge>
                            </td>
                            <td>
                              <Link
                                to={`/auction/${auction.id}`}
                                className="text-decoration-none"
                              >
                                {auction.title}
                              </Link>
                            </td>
                            <td className="text-success fw-bold">
                              {formatAuctionPrice(auction.current_bid)}
                            </td>
                            <td>{auction.total_bids || 0}</td>
                            <td>
                              <Badge
                                bg={auction.is_active ? "success" : "secondary"}
                              >
                                {auction.is_active ? "Active" : "Ended"}
                              </Badge>
                            </td>
                            <td>
                              {new Date(auction.end_time).toLocaleDateString()}
                            </td>
                            <td>
                              <div className="d-flex gap-1">
                                <Button
                                  as={Link}
                                  to={`/auction/${auction.id}`}
                                  size="sm"
                                  variant="outline-primary"
                                  title="View Auction"
                                >
                                  <FaEye />
                                </Button>
                                {auction.is_active && (
                                  <Button
                                    size="sm"
                                    variant="outline-danger"
                                    onClick={() =>
                                      handleDeleteAuction(auction.id)
                                    }
                                    title="Delete Auction"
                                  >
                                    <FaTrash />
                                  </Button>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  )}
                </Card.Body>
              </Card>
            </Tab>

            <Tab
              eventKey="won"
              title={
                <>
                  <FaTrophy className="me-2" />
                  Won Auctions ({wonAuctions.length})
                </>
              }
            >
              <Card>
                <Card.Body>
                  {wonAuctions.length === 0 ? (
                    <Alert variant="info">
                      <FaTrophy className="me-2" />
                      You haven't won any auctions yet.
                    </Alert>
                  ) : (
                    <Table responsive hover>
                      <thead>
                        <tr>
                          <th>Auction</th>
                          <th>Winning Bid</th>
                          <th>End Date</th>
                          <th>Payment Status</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {wonAuctions.map((auction) => (
                          <tr key={auction.id}>
                            <td>
                              <div>
                                <Link
                                  to={`/auction/${auction.id}`}
                                  className="text-decoration-none fw-bold"
                                >
                                  {auction.title}
                                </Link>
                                <br />
                                <small className="text-muted">
                                  ID: #{auction.id}
                                </small>
                              </div>
                            </td>
                            <td className="text-success fw-bold">
                              {formatAuctionPrice(auction.current_bid)}
                            </td>
                            <td>
                              {new Date(auction.end_time).toLocaleDateString()}
                              <br />
                              <small className="text-muted">
                                {new Date(
                                  auction.end_time
                                ).toLocaleTimeString()}
                              </small>
                            </td>
                            <td>
                              <Badge bg="warning">
                                <FaClock className="me-1" />
                                Payment Due
                              </Badge>
                            </td>
                            <td>
                              <div className="d-flex gap-1">
                                <Button
                                  as={Link}
                                  to={`/payment/${auction.id}`}
                                  size="sm"
                                  variant="success"
                                  title="Pay Now"
                                >
                                  <FaCreditCard className="me-1" />
                                  Pay
                                </Button>
                                <Button
                                  as={Link}
                                  to={`/auction/${auction.id}`}
                                  size="sm"
                                  variant="outline-primary"
                                  title="View Auction"
                                >
                                  <FaEye />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  )}
                </Card.Body>
              </Card>
            </Tab>

            <Tab
              eventKey="watchlist"
              title={
                <>
                  <FaHeart className="me-2" />
                  Watchlist
                </>
              }
            >
              <Card>
                <Card.Body>
                  {watchlist.length === 0 ? (
                    <p className="text-muted">Your watchlist is empty.</p>
                  ) : (
                    <Table responsive>
                      <thead>
                        <tr>
                          <th>Auction</th>
                          <th>Current Bid</th>
                          <th>End Date</th>
                          <th>Added</th>
                        </tr>
                      </thead>
                      <tbody>
                        {watchlist.map((item) => (
                          <tr key={item.id}>
                            <td>{item.auction_title}</td>
                            <td>${item.auction_current_bid}</td>
                            <td>
                              {new Date(
                                item.auction_end_time
                              ).toLocaleDateString()}
                            </td>
                            <td>
                              {new Date(item.created_at).toLocaleDateString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  )}
                </Card.Body>
              </Card>
            </Tab>
          </Tabs>
        </Col>
      </Row>
    </Container>
  );
};

export default UserProfile;
