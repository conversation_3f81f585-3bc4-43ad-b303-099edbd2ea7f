#!/usr/bin/env python3
"""
Review System Test Script
Tests the complete review functionality including API endpoints and email services.
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://127.0.0.1:8000/api"
USERNAME = "aisha_admin"
PASSWORD = "aisha2024!"

def test_review_system():
    print("🧪 Testing Complete Review System...")
    
    # Step 1: Login
    print("\n1️⃣ Logging in...")
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    try:
        login_response = requests.post(f"{BASE_URL}/login/", json=login_data)
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data["access"]
            print(f"✅ Login successful! Token: {access_token[:20]}...")
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Step 2: Get available auctions and users
    print("\n2️⃣ Getting test data...")
    try:
        # Get auctions
        auctions_response = requests.get(f"{BASE_URL}/auctions/", headers=headers)
        if auctions_response.status_code == 200:
            auctions_data = auctions_response.json()
            auctions = auctions_data.get('results', auctions_data) if isinstance(auctions_data, dict) else auctions_data
            
            if auctions:
                test_auction = auctions[0]
                auction_id = test_auction['id']
                auction_title = test_auction['title']
                auction_owner = test_auction['owner']
                print(f"✅ Using auction: {auction_title} (ID: {auction_id})")
                print(f"📝 Auction owner: {auction_owner}")
            else:
                print("❌ No auctions found")
                return
        else:
            print(f"❌ Failed to get auctions: {auctions_response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ Error getting test data: {e}")
        return
    
    # Step 3: Test Review Creation
    print("\n3️⃣ Creating a test review...")
    try:
        review_data = {
            "auction": auction_id,
            "reviewee": auction_owner,
            "rating": 5,
            "comment": "Excellent auction! The item was exactly as described and the seller was very responsive. Highly recommended!",
            "review_type": "seller"
        }
        
        create_response = requests.post(f"{BASE_URL}/reviews/", json=review_data, headers=headers)
        
        if create_response.status_code == 201:
            review = create_response.json()
            review_id = review['id']
            print(f"✅ Review created successfully!")
            print(f"📝 Review ID: {review_id}")
            print(f"⭐ Rating: {review['rating']}/5")
            print(f"💬 Comment: {review['comment'][:50]}...")
            print(f"🔍 Review Type: {review['review_type']}")
            print(f"✅ Verified: {review['is_verified']}")
        else:
            print(f"❌ Failed to create review: {create_response.status_code}")
            print(f"Response: {create_response.text}")
            return
            
    except Exception as e:
        print(f"❌ Error creating review: {e}")
        return
    
    # Step 4: Test Review Retrieval
    print("\n4️⃣ Testing review retrieval...")
    try:
        # Get all reviews
        all_reviews_response = requests.get(f"{BASE_URL}/reviews/", headers=headers)
        
        if all_reviews_response.status_code == 200:
            all_reviews_data = all_reviews_response.json()
            all_reviews = all_reviews_data.get('results', all_reviews_data) if isinstance(all_reviews_data, dict) else all_reviews_data
            print(f"✅ Retrieved {len(all_reviews)} total reviews")
        
        # Get reviews for specific auction
        auction_reviews_response = requests.get(f"{BASE_URL}/reviews/?auction={auction_id}", headers=headers)
        
        if auction_reviews_response.status_code == 200:
            auction_reviews_data = auction_reviews_response.json()
            auction_reviews = auction_reviews_data.get('results', auction_reviews_data) if isinstance(auction_reviews_data, dict) else auction_reviews_data
            print(f"✅ Retrieved {len(auction_reviews)} reviews for auction {auction_id}")
            
            if auction_reviews:
                latest_review = auction_reviews[0]
                print(f"📝 Latest review: {latest_review['rating']}⭐ by {latest_review['reviewer_name']}")
        
    except Exception as e:
        print(f"❌ Error retrieving reviews: {e}")
    
    # Step 5: Test Review Statistics
    print("\n5️⃣ Testing review statistics...")
    try:
        stats_response = requests.get(f"{BASE_URL}/reviews/statistics/?auction={auction_id}", headers=headers)
        
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print(f"✅ Review statistics retrieved:")
            print(f"📊 Total reviews: {stats['total_reviews']}")
            print(f"⭐ Average rating: {stats['average_rating']:.1f}/5")
            print(f"✅ Verified reviews: {stats['verified_reviews']}")
            print(f"📈 Recent reviews (30 days): {stats['recent_reviews']}")
            
            print(f"📊 Rating distribution:")
            for star in [5, 4, 3, 2, 1]:
                count = stats['rating_distribution'][f'{star}_star']
                print(f"   {star}⭐: {count} reviews")
        else:
            print(f"❌ Failed to get statistics: {stats_response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting statistics: {e}")
    
    # Step 6: Test Review Actions
    print("\n6️⃣ Testing review actions...")
    try:
        # Test marking as helpful
        helpful_response = requests.post(f"{BASE_URL}/reviews/{review_id}/mark_helpful/", headers=headers)
        
        if helpful_response.status_code == 200:
            helpful_data = helpful_response.json()
            print(f"✅ Marked review as helpful")
            print(f"👍 Helpful count: {helpful_data['helpful_count']}")
        else:
            print(f"❌ Failed to mark as helpful: {helpful_response.status_code}")
        
        # Test reporting (be careful with this in production)
        print("\n⚠️  Testing report functionality...")
        report_response = requests.post(f"{BASE_URL}/reviews/{review_id}/report/", headers=headers)
        
        if report_response.status_code == 200:
            report_data = report_response.json()
            print(f"✅ Review reported")
            print(f"🚨 Report count: {report_data['reported_count']}")
        else:
            print(f"❌ Failed to report review: {report_response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing review actions: {e}")
    
    # Step 7: Test Review Update
    print("\n7️⃣ Testing review update...")
    try:
        update_data = {
            "rating": 4,
            "comment": "Updated review: Good auction overall, minor issues with communication but item was as described.",
            "review_type": "general"
        }
        
        update_response = requests.patch(f"{BASE_URL}/reviews/{review_id}/", json=update_data, headers=headers)
        
        if update_response.status_code == 200:
            updated_review = update_response.json()
            print(f"✅ Review updated successfully")
            print(f"⭐ New rating: {updated_review['rating']}/5")
            print(f"💬 New comment: {updated_review['comment'][:50]}...")
        else:
            print(f"❌ Failed to update review: {update_response.status_code}")
            print(f"Response: {update_response.text}")
            
    except Exception as e:
        print(f"❌ Error updating review: {e}")
    
    # Step 8: Test Email Service (if configured)
    print("\n8️⃣ Testing email service...")
    try:
        from auction.email_services import send_review_notification_email
        from auction.models import Review
        
        # Get the created review
        review_obj = Review.objects.get(id=review_id)
        
        # Test email sending (this will only work if email is configured)
        print("📧 Testing review notification email...")
        email_result = send_review_notification_email(review_obj)
        
        if email_result:
            print("✅ Email notification sent successfully")
        else:
            print("⚠️  Email sending failed (check email configuration)")
            
    except Exception as e:
        print(f"⚠️  Email test skipped: {e}")
    
    # Step 9: Cleanup (optional)
    print("\n9️⃣ Cleanup (optional)...")
    cleanup = input("Do you want to delete the test review? (y/n): ").lower().strip()
    
    if cleanup == 'y':
        try:
            delete_response = requests.delete(f"{BASE_URL}/reviews/{review_id}/", headers=headers)
            
            if delete_response.status_code == 204:
                print("✅ Test review deleted successfully")
            else:
                print(f"❌ Failed to delete review: {delete_response.status_code}")
                
        except Exception as e:
            print(f"❌ Error deleting review: {e}")
    else:
        print("📝 Test review kept for further testing")
    
    # Summary
    print("\n" + "="*50)
    print("🎉 REVIEW SYSTEM TEST COMPLETED!")
    print("="*50)
    print("✅ Review creation: Working")
    print("✅ Review retrieval: Working") 
    print("✅ Review statistics: Working")
    print("✅ Review actions (helpful/report): Working")
    print("✅ Review updates: Working")
    print("📧 Email notifications: Check email configuration")
    print("\n💡 Next steps:")
    print("1. Configure email settings in .env file")
    print("2. Test frontend review components")
    print("3. Test review integration in auction details")
    print("4. Set up email templates customization")
    print("="*50)

if __name__ == "__main__":
    test_review_system()
