from django.core.management.base import BaseCommand
from django.utils.text import slugify

from auction.models import Category


class Command(BaseCommand):
    help = "Populate database with default categories"

    def handle(self, *args, **options):
        """Create default categories for the auction system"""

        categories_data = [
            {
                "name": "Electronics",
                "description": "Smartphones, laptops, gadgets, and electronic devices",
                "image": "https://img.freepik.com/premium-photo/8k-realistic-smartphone-accessories-white-canvas_893571-33631.jpg",
                "sort_order": 1,
            },
            {
                "name": "Fashion",
                "description": "Clothing, shoes, accessories, and fashion items",
                "image": "https://images.unsplash.com/photo-1445205170230-053b83016050?w=500",
                "sort_order": 2,
            },
            {
                "name": "Art",
                "description": "Paintings, sculptures, artwork, and collectible art pieces",
                "image": "https://th.bing.com/th/id/OIP.XXwcURFvVIUMwbkxTazACQHaEz?rs=1&pid=ImgDetMain",
                "sort_order": 3,
            },
            {
                "name": "Collectibles",
                "description": "Antiques, vintage items, rare collectibles, and memorabilia",
                "image": "https://img.freepik.com/psd-gratuit/vase-porcelaine-antique-fleurs-peintes-isolees-fond-transparent_191095-23323.jpg",
                "sort_order": 4,
            },
            {
                "name": "Jewelry",
                "description": "Rings, necklaces, watches, and precious jewelry",
                "image": "https://i.pinimg.com/originals/1e/14/c5/1e14c5229b10f256d44aea92e47f57e5.jpg",
                "sort_order": 5,
            },
            {
                "name": "Home & Garden",
                "description": "Furniture, home decor, garden tools, and household items",
                "image": "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500",
                "sort_order": 6,
            },
            {
                "name": "Sports",
                "description": "Sports equipment, fitness gear, and outdoor activities",
                "image": "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500",
                "sort_order": 7,
            },
            {
                "name": "Books",
                "description": "Rare books, manuscripts, educational materials, and literature",
                "image": "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500",
                "sort_order": 8,
            },
            {
                "name": "Automotive",
                "description": "Cars, motorcycles, auto parts, and vehicle accessories",
                "image": "https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?w=500",
                "sort_order": 9,
            },
            {
                "name": "Other",
                "description": "Miscellaneous items and unique auction pieces",
                "image": "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500",
                "sort_order": 10,
            },
        ]

        created_count = 0
        updated_count = 0

        for category_data in categories_data:
            slug = slugify(category_data["name"])

            # Check if category already exists by name or slug
            try:
                category = Category.objects.get(name=category_data["name"])
                # Update existing category
                category.slug = slug
                category.description = category_data["description"]
                category.image = category_data["image"]
                category.sort_order = category_data["sort_order"]
                category.is_active = True
                category.save()
                created = False
            except Category.DoesNotExist:
                try:
                    category = Category.objects.get(slug=slug)
                    # Update existing category with same slug
                    category.name = category_data["name"]
                    category.description = category_data["description"]
                    category.image = category_data["image"]
                    category.sort_order = category_data["sort_order"]
                    category.is_active = True
                    category.save()
                    created = False
                except Category.DoesNotExist:
                    # Create new category
                    category = Category.objects.create(
                        name=category_data["name"],
                        slug=slug,
                        description=category_data["description"],
                        image=category_data["image"],
                        sort_order=category_data["sort_order"],
                        is_active=True,
                    )
                    created = True

            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Created category: {category.name}")
                )
            else:
                # Update existing category with new data
                category.description = category_data["description"]
                category.image = category_data["image"]
                category.sort_order = category_data["sort_order"]
                category.is_active = True
                category.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f"🔄 Updated category: {category.name}")
                )

        self.stdout.write(
            self.style.SUCCESS(
                f"\n🎉 Categories setup complete!"
                f"\n📊 Created: {created_count} categories"
                f"\n🔄 Updated: {updated_count} categories"
                f"\n📋 Total categories: {Category.objects.filter(is_active=True).count()}"
            )
        )

        # Display all active categories
        self.stdout.write("\n📋 Active Categories:")
        for category in Category.objects.filter(is_active=True).order_by("sort_order"):
            self.stdout.write(f"   • {category.name} ({category.slug})")
