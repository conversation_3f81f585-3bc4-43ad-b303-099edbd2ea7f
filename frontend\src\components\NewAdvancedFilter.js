import React, { useState, useEffect } from "react";
import axiosInstance from "../api/axiosInstance";
import { formatAuctionPrice } from "../utils/currency";
import { useApiCall } from "../hooks/useApiCall";
import "./NewAdvancedFilter.css";

const NewAdvancedFilter = ({ onFilterResults, onClearResults }) => {
  const [filters, setFilters] = useState({
    search: "",
    category: "",
    minPrice: "",
    maxPrice: "",
    condition: "",
    status: "",
    sortBy: "created_at",
    sortOrder: "desc",
  });

  const [filterOptions, setFilterOptions] = useState({
    categories: [],
    conditions: [],
    sortOptions: [],
  });

  const [loading, setLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);

  // Use global API manager for filter options
  const {
    data: filterOptionsData,
    loading: filterOptionsLoading,
    error: filterOptionsError,
  } = useApiCall("new-search/filters/", {
    cacheTime: 10 * 60 * 1000, // Cache for 10 minutes
    onSuccess: (data) => {
      setFilterOptions(data);
    },
    onError: (error) => {
      console.error("Failed to fetch filter options:", error);
      // Set fallback options to prevent undefined errors
      setFilterOptions({
        categories: [],
        conditions: [],
        sortOptions: [
          { value: "created_at", label: "Date Created" },
          { value: "current_bid", label: "Current Bid" },
          { value: "end_time", label: "End Time" },
        ],
      });
    },
  });

  const handleInputChange = (field, value) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Quick search function for search input
  const performQuickSearch = async (searchTerm = null) => {
    setSearchLoading(true);
    try {
      const params = new URLSearchParams();
      const searchValue =
        searchTerm !== null ? searchTerm : filters.search.trim();

      if (searchValue) {
        params.append("q", searchValue);
      }

      const response = await axiosInstance.get(`/new-search/?${params}`);
      const results = response.data.results || [];

      // Pass results to parent component
      if (onFilterResults) {
        onFilterResults(results);
      }
    } catch (error) {
      console.error("Quick search failed:", error);
    } finally {
      setSearchLoading(false);
    }
  };

  // Advanced filter function
  const applyFilters = async () => {
    setLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();

      if (filters.search.trim()) {
        params.append("q", filters.search.trim());
      }
      if (filters.category) {
        params.append("category", filters.category);
      }
      if (filters.minPrice) {
        params.append("min_price", filters.minPrice);
      }
      if (filters.maxPrice) {
        params.append("max_price", filters.maxPrice);
      }
      if (filters.condition) {
        params.append("condition", filters.condition);
      }
      if (filters.status) {
        params.append("status", filters.status);
      }
      if (filters.sortBy) {
        params.append("sort_by", filters.sortBy);
      }
      if (filters.sortOrder) {
        params.append("sort_order", filters.sortOrder);
      }

      const response = await axiosInstance.get(`/new-search/?${params}`);
      const results = response.data.results || [];

      // Pass results to parent component
      if (onFilterResults) {
        onFilterResults(results);
      }
    } catch (error) {
      console.error("Filter search failed:", error);
    } finally {
      setLoading(false);
    }
  };

  const clearAllFilters = () => {
    setFilters({
      search: "",
      category: "",
      minPrice: "",
      maxPrice: "",
      condition: "",
      status: "",
      sortBy: "created_at",
      sortOrder: "desc",
    });

    // Clear results in parent component
    if (onClearResults) {
      onClearResults();
    }
  };

  // Handle Enter key press for search
  const handleSearchKeyPress = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      performQuickSearch();
    }
  };

  const formatCategoryName = (category) => {
    const categoryMap = {
      electronics: "Electronics",
      fashion: "Fashion & Clothing",
      home_garden: "Home & Garden",
      art: "Art & Collectibles",
      books: "Books & Media",
      jewelry: "Jewelry & Watches",
      automotive: "Automotive",
      sports: "Sports & Recreation",
      collectibles: "Collectibles",
      other: "Other",
    };
    return (
      categoryMap[category] ||
      category.charAt(0).toUpperCase() + category.slice(1).replace("_", " ")
    );
  };

  return (
    <div className="new-advanced-filter">
      {/* Main Search Bar */}
      <div className="search-section">
        <div className="search-input-group">
          <div className="search-input-container">
            <input
              type="text"
              className="search-input"
              placeholder="Search auctions by title, description, or category..."
              value={filters.search}
              onChange={(e) => handleInputChange("search", e.target.value)}
              onKeyPress={handleSearchKeyPress}
            />
            <button
              className="search-btn"
              onClick={() => performQuickSearch()}
              disabled={searchLoading}
              title="Search"
            >
              {searchLoading ? (
                <i className="fas fa-spinner fa-spin"></i>
              ) : (
                <i className="fas fa-search"></i>
              )}
            </button>
          </div>

          <div className="search-actions">
            <button
              className="toggle-filters-btn"
              onClick={() => setShowFilters(!showFilters)}
              title={
                showFilters ? "Hide Advanced Filters" : "Show Advanced Filters"
              }
            >
              <i className="fas fa-filter me-2"></i>
              {showFilters ? "Hide Filters" : "Advanced Filters"}
            </button>

            <button
              className="clear-search-btn"
              onClick={clearAllFilters}
              title="Clear All Filters"
            >
              <i className="fas fa-times me-2"></i>
              Clear All
            </button>
          </div>
        </div>
      </div>

      {/* Advanced Filters Panel */}
      {showFilters && (
        <div className="filters-panel">
          <div className="filters-grid">
            {/* Category Filter */}
            <div className="filter-group">
              <label htmlFor="category">Category</label>
              <select
                id="category"
                value={filters.category}
                onChange={(e) => handleInputChange("category", e.target.value)}
                className="filter-select"
              >
                <option value="">All Categories</option>
                {Array.isArray(filterOptions.categories) &&
                  filterOptions.categories.map((category) => (
                    <option key={category} value={category}>
                      {formatCategoryName(category)}
                    </option>
                  ))}
              </select>
            </div>

            {/* Price Range */}
            <div className="filter-group">
              <label htmlFor="minPrice">Min Price</label>
              <input
                type="number"
                id="minPrice"
                placeholder="0"
                min="0"
                value={filters.minPrice}
                onChange={(e) => handleInputChange("minPrice", e.target.value)}
                className="filter-input"
              />
            </div>

            <div className="filter-group">
              <label htmlFor="maxPrice">Max Price</label>
              <input
                type="number"
                id="maxPrice"
                placeholder="No limit"
                min="0"
                value={filters.maxPrice}
                onChange={(e) => handleInputChange("maxPrice", e.target.value)}
                className="filter-input"
              />
            </div>

            {/* Condition Filter */}
            <div className="filter-group">
              <label htmlFor="condition">Condition</label>
              <select
                id="condition"
                value={filters.condition}
                onChange={(e) => handleInputChange("condition", e.target.value)}
                className="filter-select"
              >
                <option value="">Any Condition</option>
                {filterOptions.conditions &&
                  filterOptions.conditions.map((condition) => (
                    <option key={condition} value={condition}>
                      {condition}
                    </option>
                  ))}
              </select>
            </div>

            {/* Status Filter */}
            <div className="filter-group">
              <label htmlFor="status">Status</label>
              <select
                id="status"
                value={filters.status}
                onChange={(e) => handleInputChange("status", e.target.value)}
                className="filter-select"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="ending_soon">Ending Soon</option>
                <option value="ended">Ended</option>
              </select>
            </div>

            {/* Sort Options */}
            <div className="filter-group">
              <label htmlFor="sortBy">Sort By</label>
              <select
                id="sortBy"
                value={filters.sortBy}
                onChange={(e) => handleInputChange("sortBy", e.target.value)}
                className="filter-select"
              >
                {filterOptions.sortOptions &&
                  filterOptions.sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
              </select>
            </div>

            {/* Sort Order */}
            <div className="filter-group">
              <label htmlFor="sortOrder">Order</label>
              <select
                id="sortOrder"
                value={filters.sortOrder}
                onChange={(e) => handleInputChange("sortOrder", e.target.value)}
                className="filter-select"
              >
                <option value="desc">Descending</option>
                <option value="asc">Ascending</option>
              </select>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="filter-actions">
            <button
              className="apply-filters-btn"
              onClick={applyFilters}
              disabled={loading}
            >
              {loading ? "🔄 Applying..." : "🔍 Apply Filters"}
            </button>
            <button
              className="clear-filters-btn"
              onClick={clearAllFilters}
              disabled={loading}
            >
              🗑️ Clear All
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NewAdvancedFilter;
