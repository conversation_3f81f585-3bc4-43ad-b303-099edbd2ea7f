from django.apps import AppConfig


class AuctionConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "auction"

    def ready(self):
        """Import signals when Django starts"""
        try:
            import auction.signals  # This loads the signals
            print("Auction signals loaded successfully")
        except ImportError as e:
            print(f"Error loading auction signals: {e}")
    verbose_name = "Auction Management"
