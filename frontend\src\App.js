import React from "react";
import { Routes, Route, useLocation, Navigate } from "react-router-dom";
import Navbar from "./components/Navbar";
import AdminRoute from "./routes/AdminRoute"; // Protects Admin Dashboard
import LandingPage from "./pages/LandingPage";
import Home from "./pages/Home";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Profile from "./pages/Profile";
import UserProfile from "./pages/UserProfile";
import Watchlist from "./pages/Watchlist";
import Dashboard from "./pages/Dashboard";
import Auctions from "./pages/Auctions";
import AdminDashboard from "./pages/AdminDashboard";
import CreateAuction from "./pages/CreateAuction";
import AuctionDetailPage from "./pages/AuctionDetail";
import { AuthProvider, useAuth } from "./context/AuthContext"; // Using AuthProvider for localStorage user management
import { AuctionProvider } from "./context/AuctionContext"; // ✅ New Auction context
import { ThemeProvider } from "./context/ThemeContext"; // Theme context for dark mode
import ProtectedRoute from "./routes/ProtectedRoute"; // Protects routes for logged-in users
import Contact from "./pages/Contact";
import ForgotPassword from "./pages/ForgotPassword";
import PasswordResetSent from "./pages/PasswordResetSent";
import ResetPasswordConfirm from "./pages/ResetPasswordConfirm";
import ScrollToTop from "./components/ScrollToTop"; // Smooth scrolling between routes
import Footer from "./components/Footer"; // Footer component

import AdminAuctionManager from "./pages/AdminAuctionManager";
import PaymentPage from "./pages/PaymentPage";
import "./App.css";
import "./styles/dark-mode.css";
import "animate.css";
import Notifications from "./components/Notification";
import Payments from "./pages/Payments";
import { NotificationProvider } from "./context/NotificationContext";
import ConnectionTest from "./components/ConnectionTest";
import InstallPrompt from "./components/InstallPrompt";
import ErrorBoundary from "./components/ErrorBoundary";
import DebugEnv from "./pages/DebugEnv";

// Component to handle root route logic
function RootRoute() {
  const { user } = useAuth();

  // If user is logged in, redirect to home page
  if (user) {
    return <Navigate to="/home" replace />;
  }

  // If not logged in, show landing page
  return <LandingPage />;
}

function AppContent() {
  const location = useLocation();
  const { user } = useAuth();

  // Show landing page layout only for landing page or root when not logged in
  const isLandingPage =
    location.pathname === "/landing" || (location.pathname === "/" && !user);

  return (
    <>
      {!isLandingPage && <Navbar />}
      <Notifications />
      <InstallPrompt />
      <Routes>
        {/* Root Route - Smart redirect based on auth status */}
        <Route path="/" element={<RootRoute />} />

        {/* Public Routes */}
        <Route path="/landing" element={<LandingPage />} />
        <Route path="/home" element={<Home />} />
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/auctions" element={<Auctions />} />
        <Route path="/auctions/category/:category" element={<Auctions />} />
        <Route path="/connection-test" element={<ConnectionTest />} />
        <Route path="/debug-env" element={<DebugEnv />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/PasswordResetSent" element={<PasswordResetSent />} />
        <Route
          path="/reset-password-confirm/:uidb64/:token"
          element={<ResetPasswordConfirm />}
        />
        {/* Protected Routes */}
        <Route
          path="/create-auction"
          element={
            <ProtectedRoute>
              <CreateAuction />
            </ProtectedRoute>
          }
        />
        <Route
          path="/profile"
          element={
            <ProtectedRoute>
              <Profile />
            </ProtectedRoute>
          }
        />
        <Route
          path="/user-profile"
          element={
            <ProtectedRoute>
              <UserProfile />
            </ProtectedRoute>
          }
        />
        <Route
          path="/watchlist"
          element={
            <ProtectedRoute>
              <Watchlist />
            </ProtectedRoute>
          }
        />
        <Route
          path="/payments"
          element={
            <ProtectedRoute>
              <Payments />
            </ProtectedRoute>
          }
        />
        <Route
          path="/payment/:auctionId"
          element={
            <ProtectedRoute>
              <PaymentPage />
            </ProtectedRoute>
          }
        />

        {/* Admin Protected Route */}
        <Route
          path="/admin-dashboard"
          element={
            <AdminRoute>
              <AdminDashboard />
            </AdminRoute>
          }
        />
        <Route
          path="/dashboard"
          element={
            <AdminRoute>
              <Dashboard />
            </AdminRoute>
          }
        />
        <Route
          path="/admin/auctions"
          element={
            <AdminRoute>
              <AdminAuctionManager />
            </AdminRoute>
          }
        />

        {/* Auction Detail Page */}
        <Route path="/auction/:id" element={<AuctionDetailPage />} />
      </Routes>
      {!isLandingPage && (
        <Footer className="bg-dark text-light text-center text-lg-start mt-5" />
      )}
      <ScrollToTop /> {/* Scrolls the page back to the top on route change */}
    </>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <NotificationProvider>
          <AuthProvider>
            <AuctionProvider>
              <AppContent />
            </AuctionProvider>
          </AuthProvider>
        </NotificationProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
