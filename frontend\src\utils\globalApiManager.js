/**
 * Global API Call Manager
 * Prevents duplicate API calls, implements rate limiting, caching, and circuit breaker patterns
 * across the entire application
 */

class GlobalApiManager {
  constructor() {
    this.pendingRequests = new Map(); // Track ongoing requests
    this.cache = new Map(); // Cache responses
    this.rateLimits = new Map(); // Track rate limits per endpoint
    this.circuitBreakers = new Map(); // Circuit breaker state per endpoint
    this.subscribers = new Map(); // Subscribers for real-time updates
    
    // Configuration
    this.config = {
      defaultCacheTime: 5 * 60 * 1000, // 5 minutes
      maxRetries: 3,
      retryDelay: 1000, // 1 second base delay
      requestTimeout: 10000, // 10 seconds
      rateLimitWindow: 60000, // 1 minute
      maxRequestsPerWindow: 30, // Max 30 requests per minute per endpoint
      circuitBreakerThreshold: 5, // Open circuit after 5 failures
      circuitBreakerTimeout: 30000, // 30 seconds before trying again
    };

    // Bind methods
    this.makeRequest = this.makeRequest.bind(this);
    this.clearCache = this.clearCache.bind(this);
    this.subscribe = this.subscribe.bind(this);
    this.unsubscribe = this.unsubscribe.bind(this);
    
    console.log("🌐 Global API Manager initialized");
  }

  /**
   * Generate a unique key for the request
   */
  generateRequestKey(url, method = 'GET', params = {}) {
    const paramString = Object.keys(params).length > 0 ? JSON.stringify(params) : '';
    return `${method}:${url}:${paramString}`;
  }

  /**
   * Check if request is rate limited
   */
  isRateLimited(endpoint) {
    const now = Date.now();
    const rateLimit = this.rateLimits.get(endpoint);
    
    if (!rateLimit) {
      this.rateLimits.set(endpoint, {
        requests: [now],
        count: 1
      });
      return false;
    }

    // Clean old requests outside the window
    rateLimit.requests = rateLimit.requests.filter(
      time => now - time < this.config.rateLimitWindow
    );

    if (rateLimit.requests.length >= this.config.maxRequestsPerWindow) {
      console.warn(`🚫 Rate limit exceeded for ${endpoint}`);
      return true;
    }

    rateLimit.requests.push(now);
    return false;
  }

  /**
   * Check circuit breaker state
   */
  isCircuitOpen(endpoint) {
    const breaker = this.circuitBreakers.get(endpoint);
    if (!breaker) return false;

    if (breaker.state === 'open') {
      if (Date.now() - breaker.lastFailure > this.config.circuitBreakerTimeout) {
        breaker.state = 'half-open';
        console.log(`🔄 Circuit breaker half-open for ${endpoint}`);
        return false;
      }
      console.warn(`🚫 Circuit breaker open for ${endpoint}`);
      return true;
    }

    return false;
  }

  /**
   * Update circuit breaker state
   */
  updateCircuitBreaker(endpoint, success) {
    let breaker = this.circuitBreakers.get(endpoint);
    
    if (!breaker) {
      breaker = {
        failures: 0,
        state: 'closed',
        lastFailure: null
      };
      this.circuitBreakers.set(endpoint, breaker);
    }

    if (success) {
      breaker.failures = 0;
      breaker.state = 'closed';
    } else {
      breaker.failures++;
      breaker.lastFailure = Date.now();
      
      if (breaker.failures >= this.config.circuitBreakerThreshold) {
        breaker.state = 'open';
        console.error(`🚨 Circuit breaker opened for ${endpoint} after ${breaker.failures} failures`);
      }
    }
  }

  /**
   * Get cached response
   */
  getCachedResponse(key, cacheTime = this.config.defaultCacheTime) {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > cacheTime) {
      this.cache.delete(key);
      return null;
    }

    console.log(`📦 Using cached response for ${key}`);
    return cached.data;
  }

  /**
   * Cache response
   */
  setCachedResponse(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Main method to make API requests with all protections
   */
  async makeRequest(axiosInstance, url, options = {}) {
    const {
      method = 'GET',
      params = {},
      data = null,
      cacheTime = this.config.defaultCacheTime,
      skipCache = false,
      skipRateLimit = false,
      skipCircuitBreaker = false,
      retries = this.config.maxRetries
    } = options;

    const requestKey = this.generateRequestKey(url, method, params);
    const endpoint = url.split('?')[0]; // Base endpoint for rate limiting

    console.log(`🌐 API Request: ${method} ${url}`);

    // Check cache first (for GET requests)
    if (method === 'GET' && !skipCache) {
      const cached = this.getCachedResponse(requestKey, cacheTime);
      if (cached) {
        this.notifySubscribers(requestKey, cached, false);
        return cached;
      }
    }

    // Check if request is already pending
    if (this.pendingRequests.has(requestKey)) {
      console.log(`🔄 Request already pending for ${requestKey}`);
      return this.pendingRequests.get(requestKey);
    }

    // Rate limiting check
    if (!skipRateLimit && this.isRateLimited(endpoint)) {
      throw new Error(`Rate limit exceeded for ${endpoint}`);
    }

    // Circuit breaker check
    if (!skipCircuitBreaker && this.isCircuitOpen(endpoint)) {
      throw new Error(`Circuit breaker open for ${endpoint}`);
    }

    // Create the request promise
    const requestPromise = this.executeRequest(
      axiosInstance, url, method, params, data, retries, endpoint
    );

    // Store pending request
    this.pendingRequests.set(requestKey, requestPromise);

    try {
      const result = await requestPromise;
      
      // Cache successful GET requests
      if (method === 'GET' && !skipCache) {
        this.setCachedResponse(requestKey, result);
      }

      // Update circuit breaker
      this.updateCircuitBreaker(endpoint, true);

      // Notify subscribers
      this.notifySubscribers(requestKey, result, false);

      return result;
    } catch (error) {
      // Update circuit breaker
      this.updateCircuitBreaker(endpoint, false);

      // Notify subscribers of error
      this.notifySubscribers(requestKey, null, true, error);

      throw error;
    } finally {
      // Remove from pending requests
      this.pendingRequests.delete(requestKey);
    }
  }

  /**
   * Execute the actual HTTP request with retries
   */
  async executeRequest(axiosInstance, url, method, params, data, retries, endpoint) {
    let lastError;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const config = {
          method,
          url,
          timeout: this.config.requestTimeout,
          params: method === 'GET' ? params : undefined,
          data: method !== 'GET' ? data : undefined
        };

        const response = await axiosInstance(config);
        
        console.log(`✅ API Success: ${method} ${url} (attempt ${attempt + 1})`);
        return response.data;

      } catch (error) {
        lastError = error;
        console.error(`❌ API Error: ${method} ${url} (attempt ${attempt + 1}):`, error.message);

        // Don't retry on certain errors
        if (error.response?.status === 401 || error.response?.status === 403) {
          break;
        }

        // Wait before retry (exponential backoff)
        if (attempt < retries) {
          const delay = this.config.retryDelay * Math.pow(2, attempt);
          console.log(`⏳ Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  /**
   * Subscribe to API call updates
   */
  subscribe(requestKey, callback) {
    if (!this.subscribers.has(requestKey)) {
      this.subscribers.set(requestKey, new Set());
    }
    this.subscribers.get(requestKey).add(callback);

    // Return unsubscribe function
    return () => this.unsubscribe(requestKey, callback);
  }

  /**
   * Unsubscribe from API call updates
   */
  unsubscribe(requestKey, callback) {
    const subs = this.subscribers.get(requestKey);
    if (subs) {
      subs.delete(callback);
      if (subs.size === 0) {
        this.subscribers.delete(requestKey);
      }
    }
  }

  /**
   * Notify subscribers of updates
   */
  notifySubscribers(requestKey, data, isError, error = null) {
    const subs = this.subscribers.get(requestKey);
    if (subs) {
      subs.forEach(callback => {
        try {
          callback({ data, isError, error, requestKey });
        } catch (err) {
          console.error('Error in subscriber callback:', err);
        }
      });
    }
  }

  /**
   * Clear cache for specific key or all cache
   */
  clearCache(key = null) {
    if (key) {
      this.cache.delete(key);
      console.log(`🗑️ Cleared cache for ${key}`);
    } else {
      this.cache.clear();
      console.log('🗑️ Cleared all cache');
    }
  }

  /**
   * Get statistics
   */
  getStats() {
    return {
      pendingRequests: this.pendingRequests.size,
      cachedItems: this.cache.size,
      rateLimits: this.rateLimits.size,
      circuitBreakers: Array.from(this.circuitBreakers.entries()).map(([endpoint, breaker]) => ({
        endpoint,
        state: breaker.state,
        failures: breaker.failures
      }))
    };
  }

  /**
   * Reset all state (for testing)
   */
  reset() {
    this.pendingRequests.clear();
    this.cache.clear();
    this.rateLimits.clear();
    this.circuitBreakers.clear();
    this.subscribers.clear();
    console.log('🔄 Global API Manager reset');
  }
}

// Create and export singleton instance
const globalApiManager = new GlobalApiManager();

export default globalApiManager;
