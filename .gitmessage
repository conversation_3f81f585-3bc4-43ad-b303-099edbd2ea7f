# Commit Message Template
# 
# Format: <type>(<scope>): <subject>
#
# Types:
# feat: A new feature
# fix: A bug fix
# docs: Documentation only changes
# style: Changes that do not affect the meaning of the code
# refactor: A code change that neither fixes a bug nor adds a feature
# perf: A code change that improves performance
# test: Adding missing tests or correcting existing tests
# chore: Changes to the build process or auxiliary tools
#
# Example:
# feat(auction): add watchlist functionality to auction details
# fix(payment): resolve currency conversion issue
# docs(readme): update installation instructions
#
# Remember:
# - Use the imperative mood in the subject line
# - Limit the subject line to 50 characters
# - Separate subject from body with a blank line
# - Wrap the body at 72 characters
# - Use the body to explain what and why vs. how
