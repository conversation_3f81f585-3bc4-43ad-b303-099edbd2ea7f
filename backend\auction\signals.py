"""
Django signals for automatic analytics updates
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

from .models import Auction, Bid, User, Analytics


@receiver(post_save, sender=Auction)
def auction_saved_handler(sender, instance, created, **kwargs):
    """Update analytics when auction is created/updated"""
    try:
        # Update analytics data
        update_analytics_metrics()

        # Send WebSocket updates
        if created:
            # New auction created
            send_admin_dashboard_update()
            print(f"Analytics updated: New auction created - {instance.title}")
        else:
            # Auction updated (including views increment)
            send_auction_analytics_update(instance.id)
            send_admin_dashboard_update()  # Update dashboard for view counts

            # Check if views_count was updated
            if 'views_count' in kwargs.get('update_fields', []) or not kwargs.get('update_fields'):
                print(f"Analytics updated: Auction viewed - {instance.title} (Views: {instance.views_count})")
            else:
                print(f"Analytics updated: Auction updated - {instance.title}")

    except Exception as e:
        print(f"Error in auction signal handler: {e}")


@receiver(post_save, sender=Bid)
def bid_saved_handler(sender, instance, created, **kwargs):
    """Update analytics when bid is placed"""
    try:
        if created:
            # Update analytics
            update_analytics_metrics()
            
            # Send real-time updates
            send_auction_analytics_update(instance.auction.id)
            send_admin_dashboard_update()
            send_trending_update(instance.auction.id)
            
            print(f"Analytics updated: New bid placed - ₹{instance.amount} on {instance.auction.title}")
            
    except Exception as e:
        print(f"Error in bid signal handler: {e}")


@receiver(post_save, sender=User)
def user_saved_handler(sender, instance, created, **kwargs):
    """Update analytics when user is created"""
    try:
        if created:
            # Update user metrics
            update_analytics_metrics()
            send_admin_dashboard_update()
            
            print(f"Analytics updated: New user registered - {instance.username}")
            
    except Exception as e:
        print(f"Error in user signal handler: {e}")


def update_analytics_metrics():
    """Update analytics metrics in database"""
    try:
        from .analytics_services import advanced_analytics_service
        
        today = timezone.now().date()
        
        # Get current metrics
        dashboard_data = advanced_analytics_service.get_comprehensive_dashboard_data()
        
        # Store key metrics in Analytics model
        metrics_to_store = [
            ('total_auctions', dashboard_data['basic_metrics']['total_auctions'], 'auction_performance'),
            ('active_auctions', dashboard_data['basic_metrics']['active_auctions'], 'auction_performance'),
            ('total_users', dashboard_data['basic_metrics']['total_users'], 'user_engagement'),
            ('total_bids', dashboard_data['basic_metrics']['total_bids'], 'auction_performance'),
            ('total_views', dashboard_data['basic_metrics']['total_views'], 'auction_performance'),
            ('total_revenue', dashboard_data['revenue_data']['total_revenue'], 'revenue'),
        ]
        
        for metric_name, metric_value, metric_type in metrics_to_store:
            Analytics.objects.update_or_create(
                metric_name=metric_name,
                date=today,
                defaults={
                    'metric_value': metric_value,
                    'metric_type': metric_type,
                    'metadata': {'updated_at': timezone.now().isoformat()}
                }
            )
            
        # Invalidate cache
        from django.core.cache import cache
        cache_keys = [
            'dashboard_analytics_data',
            'revenue_metrics',
            'engagement_metrics',
            'ai_performance_metrics'
        ]
        cache.delete_many(cache_keys)
            
    except Exception as e:
        print(f"Error updating analytics metrics: {e}")


def send_admin_dashboard_update():
    """Send dashboard statistics update to admin users"""
    try:
        from .analytics_services import advanced_analytics_service
        
        # Get fresh analytics data
        dashboard_data = advanced_analytics_service.get_comprehensive_dashboard_data()
        
        # Get channel layer
        channel_layer = get_channel_layer()
        if not channel_layer:
            print("No channel layer available for WebSocket updates")
            return
        
        # Send to admin group
        async_to_sync(channel_layer.group_send)(
            "admin_dashboard",
            {
                "type": "dashboard_update",
                "data": {
                    "basic_metrics": dashboard_data.get("basic_metrics", {}),
                    "revenue_data": dashboard_data.get("revenue_data", {}),
                    "timestamp": timezone.now().isoformat(),
                }
            }
        )
        
        print("Admin dashboard update sent via WebSocket")
        
    except Exception as e:
        print(f"Error sending admin dashboard update: {e}")


def send_auction_analytics_update(auction_id):
    """Send real-time analytics update for specific auction"""
    try:
        auction = Auction.objects.get(id=auction_id)
        bids = auction.bids.all()
        
        analytics_data = {
            "auction_id": auction_id,
            "total_bids": bids.count(),
            "unique_bidders": bids.values('bidder').distinct().count(),
            "current_bid": float(auction.current_bid),
            "bid_increase": float(auction.current_bid - auction.starting_bid),
            "time_remaining": get_time_remaining(auction),
            "last_bid_time": bids.last().timestamp.isoformat() if bids.exists() else None,
        }
        
        # Get channel layer
        channel_layer = get_channel_layer()
        if not channel_layer:
            return
        
        # Send to auction-specific group
        async_to_sync(channel_layer.group_send)(
            f"auction_{auction_id}_analytics",
            {
                "type": "analytics_update",
                "data": analytics_data,
            }
        )
        
        print(f"Auction analytics update sent for auction {auction_id}")
        
    except Auction.DoesNotExist:
        print(f"Auction {auction_id} not found for analytics update")
    except Exception as e:
        print(f"Error sending auction analytics update: {e}")


def send_trending_update(auction_id):
    """Send trending auction update to all connected clients"""
    try:
        auction = Auction.objects.get(id=auction_id)
        
        # Get channel layer
        channel_layer = get_channel_layer()
        if not channel_layer:
            return
        
        # Send to general auction updates group
        async_to_sync(channel_layer.group_send)(
            "auction_updates",
            {
                "type": "trending_update",
                "auction_id": auction_id,
                "title": auction.title,
                "current_bid": float(auction.current_bid),
                "bid_count": auction.bids.count(),
                "time_remaining": get_time_remaining(auction),
            }
        )
        
        print(f"Trending update sent for auction {auction_id}")
        
    except Auction.DoesNotExist:
        print(f"Auction {auction_id} not found for trending update")
    except Exception as e:
        print(f"Error sending trending update: {e}")


def get_time_remaining(auction):
    """Calculate time remaining in seconds"""
    if not auction.is_active:
        return 0
    
    now = timezone.now()
    if auction.end_time <= now:
        return 0
    
    return int((auction.end_time - now).total_seconds())
