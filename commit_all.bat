@echo off
echo ====================================================
echo     SMART COMMIT - ONLY ACTUAL CHANGES
echo ====================================================
echo.

REM Show current status
echo 🔍 Checking for actual changes (no duplicates)...
echo ====================================================
git status --porcelain
echo ====================================================
echo.

REM Check if there are any changes
git diff-index --quiet HEAD --
if %ERRORLEVEL% EQU 0 (
    echo ✅ No changes detected. Repository is up to date.
    echo 💡 Git prevents committing duplicates automatically!
    pause
    exit /b 0
)

echo 📊 DETAILED CHANGE ANALYSIS:
echo ====================================================
echo 🆕 NEW FILES:
git ls-files --others --exclude-standard
echo.
echo 📝 MODIFIED FILES:
git diff --name-only
echo.
echo 🗑️ DELETED FILES:
git diff --name-only --diff-filter=D
echo ====================================================

echo 📝 Changes detected! Ready to commit.
echo.

REM Get commit message from user
set /p commit_msg="Enter your commit message: "

if "%commit_msg%"=="" (
    echo ❌ Commit message cannot be empty!
    pause
    exit /b 1
)

echo.
echo 📦 Adding all files...
git add .

echo 💾 Committing with message: "%commit_msg%"
git commit -m "%commit_msg%"

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Commit failed! Please check for errors.
    pause
    exit /b 1
)

echo.
set /p push_confirm="🚀 Push to GitHub? (y/n): "

if /i "%push_confirm%"=="y" (
    echo 🚀 Pushing to GitHub...
    git push origin main
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ✅ SUCCESS! All changes committed and pushed to GitHub.
        echo 🎉 Your repository is now updated.
    ) else (
        echo ❌ Push failed! Please check your internet connection and GitHub credentials.
    )
) else (
    echo 📝 Changes committed locally but not pushed to GitHub.
    echo 💡 Run 'git push origin main' when ready to push.
)

echo.
pause
