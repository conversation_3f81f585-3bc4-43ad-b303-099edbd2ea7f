{"ast": null, "code": "/**\n * Global API Call Manager\n * Prevents duplicate API calls, implements rate limiting, caching, and circuit breaker patterns\n * across the entire application\n */\n\nclass GlobalApiManager {\n  constructor() {\n    this.pendingRequests = new Map(); // Track ongoing requests\n    this.cache = new Map(); // Cache responses\n    this.rateLimits = new Map(); // Track rate limits per endpoint\n    this.circuitBreakers = new Map(); // Circuit breaker state per endpoint\n    this.subscribers = new Map(); // Subscribers for real-time updates\n\n    // Configuration\n    this.config = {\n      defaultCacheTime: 5 * 60 * 1000,\n      // 5 minutes\n      maxRetries: 3,\n      retryDelay: 1000,\n      // 1 second base delay\n      requestTimeout: 10000,\n      // 10 seconds\n      rateLimitWindow: 60000,\n      // 1 minute\n      maxRequestsPerWindow: 30,\n      // Max 30 requests per minute per endpoint\n      circuitBreakerThreshold: 5,\n      // Open circuit after 5 failures\n      circuitBreakerTimeout: 30000 // 30 seconds before trying again\n    };\n\n    // Bind methods\n    this.makeRequest = this.makeRequest.bind(this);\n    this.clearCache = this.clearCache.bind(this);\n    this.subscribe = this.subscribe.bind(this);\n    this.unsubscribe = this.unsubscribe.bind(this);\n    console.log(\"🌐 Global API Manager initialized\");\n  }\n\n  /**\n   * Generate a unique key for the request\n   */\n  generateRequestKey(url, method = \"GET\", params = {}) {\n    const paramString = Object.keys(params).length > 0 ? JSON.stringify(params) : \"\";\n    return `${method}:${url}:${paramString}`;\n  }\n\n  /**\n   * Check if request is rate limited\n   */\n  isRateLimited(endpoint) {\n    const now = Date.now();\n    const rateLimit = this.rateLimits.get(endpoint);\n    if (!rateLimit) {\n      this.rateLimits.set(endpoint, {\n        requests: [now],\n        count: 1\n      });\n      return false;\n    }\n\n    // Clean old requests outside the window\n    rateLimit.requests = rateLimit.requests.filter(time => now - time < this.config.rateLimitWindow);\n    if (rateLimit.requests.length >= this.config.maxRequestsPerWindow) {\n      console.warn(`🚫 Rate limit exceeded for ${endpoint}`);\n      return true;\n    }\n    rateLimit.requests.push(now);\n    return false;\n  }\n\n  /**\n   * Check circuit breaker state\n   */\n  isCircuitOpen(endpoint) {\n    const breaker = this.circuitBreakers.get(endpoint);\n    if (!breaker) return false;\n    if (breaker.state === \"open\") {\n      if (Date.now() - breaker.lastFailure > this.config.circuitBreakerTimeout) {\n        breaker.state = \"half-open\";\n        console.log(`🔄 Circuit breaker half-open for ${endpoint}`);\n        return false;\n      }\n      console.warn(`🚫 Circuit breaker open for ${endpoint}`);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Update circuit breaker state\n   */\n  updateCircuitBreaker(endpoint, success) {\n    let breaker = this.circuitBreakers.get(endpoint);\n    if (!breaker) {\n      breaker = {\n        failures: 0,\n        state: \"closed\",\n        lastFailure: null\n      };\n      this.circuitBreakers.set(endpoint, breaker);\n    }\n    if (success) {\n      breaker.failures = 0;\n      breaker.state = \"closed\";\n    } else {\n      breaker.failures++;\n      breaker.lastFailure = Date.now();\n      if (breaker.failures >= this.config.circuitBreakerThreshold) {\n        breaker.state = \"open\";\n        console.error(`🚨 Circuit breaker opened for ${endpoint} after ${breaker.failures} failures`);\n      }\n    }\n  }\n\n  /**\n   * Get cached response\n   */\n  getCachedResponse(key, cacheTime = this.config.defaultCacheTime) {\n    const cached = this.cache.get(key);\n    if (!cached) return null;\n    if (Date.now() - cached.timestamp > cacheTime) {\n      this.cache.delete(key);\n      return null;\n    }\n    console.log(`📦 Using cached response for ${key}`);\n    return cached.data;\n  }\n\n  /**\n   * Cache response\n   */\n  setCachedResponse(key, data) {\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now()\n    });\n  }\n\n  /**\n   * Main method to make API requests with all protections\n   */\n  async makeRequest(axiosInstance, url, options = {}) {\n    const {\n      method = \"GET\",\n      params = {},\n      data = null,\n      cacheTime = this.config.defaultCacheTime,\n      skipCache = false,\n      skipRateLimit = false,\n      skipCircuitBreaker = false,\n      retries = this.config.maxRetries\n    } = options;\n    const requestKey = this.generateRequestKey(url, method, params);\n    const endpoint = url.split(\"?\")[0]; // Base endpoint for rate limiting\n\n    console.log(`🌐 API Request: ${method} ${url}`);\n\n    // Check cache first (for GET requests)\n    if (method === \"GET\" && !skipCache) {\n      const cached = this.getCachedResponse(requestKey, cacheTime);\n      if (cached) {\n        this.notifySubscribers(requestKey, cached, false);\n        return cached;\n      }\n    }\n\n    // Check if request is already pending\n    if (this.pendingRequests.has(requestKey)) {\n      console.log(`🔄 Request already pending for ${requestKey}`);\n      return this.pendingRequests.get(requestKey);\n    }\n\n    // Rate limiting check\n    if (!skipRateLimit && this.isRateLimited(endpoint)) {\n      throw new Error(`Rate limit exceeded for ${endpoint}`);\n    }\n\n    // Circuit breaker check\n    if (!skipCircuitBreaker && this.isCircuitOpen(endpoint)) {\n      throw new Error(`Circuit breaker open for ${endpoint}`);\n    }\n\n    // Create the request promise\n    const requestPromise = this.executeRequest(axiosInstance, url, method, params, data, retries, endpoint);\n\n    // Store pending request\n    this.pendingRequests.set(requestKey, requestPromise);\n    try {\n      const result = await requestPromise;\n\n      // Cache successful GET requests\n      if (method === \"GET\" && !skipCache) {\n        this.setCachedResponse(requestKey, result);\n      }\n\n      // Update circuit breaker\n      this.updateCircuitBreaker(endpoint, true);\n\n      // Notify subscribers\n      this.notifySubscribers(requestKey, result, false);\n      return result;\n    } catch (error) {\n      // Update circuit breaker\n      this.updateCircuitBreaker(endpoint, false);\n\n      // Notify subscribers of error\n      this.notifySubscribers(requestKey, null, true, error);\n      throw error;\n    } finally {\n      // Remove from pending requests\n      this.pendingRequests.delete(requestKey);\n    }\n  }\n\n  /**\n   * Execute the actual HTTP request with retries\n   */\n  async executeRequest(axiosInstance, url, method, params, data, retries, endpoint) {\n    let lastError;\n    for (let attempt = 0; attempt <= retries; attempt++) {\n      try {\n        const config = {\n          method,\n          url,\n          timeout: this.config.requestTimeout,\n          params: method === \"GET\" ? params : undefined,\n          data: method !== \"GET\" ? data : undefined\n        };\n\n        // Use axiosInstance properly - it should be the axios instance, not a function\n        const response = await axiosInstance.request(config);\n        console.log(`✅ API Success: ${method} ${url} (attempt ${attempt + 1})`);\n        return response.data;\n      } catch (error) {\n        var _error$response, _error$response2;\n        lastError = error;\n        console.error(`❌ API Error: ${method} ${url} (attempt ${attempt + 1}):`, error.message);\n\n        // Don't retry on certain errors\n        if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 403) {\n          break;\n        }\n\n        // Wait before retry (exponential backoff)\n        if (attempt < retries) {\n          const delay = this.config.retryDelay * Math.pow(2, attempt);\n          console.log(`⏳ Retrying in ${delay}ms...`);\n          await new Promise(resolve => setTimeout(resolve, delay));\n        }\n      }\n    }\n    throw lastError;\n  }\n\n  /**\n   * Subscribe to API call updates\n   */\n  subscribe(requestKey, callback) {\n    if (!this.subscribers.has(requestKey)) {\n      this.subscribers.set(requestKey, new Set());\n    }\n    this.subscribers.get(requestKey).add(callback);\n\n    // Return unsubscribe function\n    return () => this.unsubscribe(requestKey, callback);\n  }\n\n  /**\n   * Unsubscribe from API call updates\n   */\n  unsubscribe(requestKey, callback) {\n    const subs = this.subscribers.get(requestKey);\n    if (subs) {\n      subs.delete(callback);\n      if (subs.size === 0) {\n        this.subscribers.delete(requestKey);\n      }\n    }\n  }\n\n  /**\n   * Notify subscribers of updates\n   */\n  notifySubscribers(requestKey, data, isError, error = null) {\n    const subs = this.subscribers.get(requestKey);\n    if (subs) {\n      subs.forEach(callback => {\n        try {\n          callback({\n            data,\n            isError,\n            error,\n            requestKey\n          });\n        } catch (err) {\n          console.error(\"Error in subscriber callback:\", err);\n        }\n      });\n    }\n  }\n\n  /**\n   * Clear cache for specific key or all cache\n   */\n  clearCache(key = null) {\n    if (key) {\n      this.cache.delete(key);\n      console.log(`🗑️ Cleared cache for ${key}`);\n    } else {\n      this.cache.clear();\n      console.log(\"🗑️ Cleared all cache\");\n    }\n  }\n\n  /**\n   * Get statistics\n   */\n  getStats() {\n    return {\n      pendingRequests: this.pendingRequests.size,\n      cachedItems: this.cache.size,\n      rateLimits: this.rateLimits.size,\n      circuitBreakers: Array.from(this.circuitBreakers.entries()).map(([endpoint, breaker]) => ({\n        endpoint,\n        state: breaker.state,\n        failures: breaker.failures\n      }))\n    };\n  }\n\n  /**\n   * Reset all state (for testing)\n   */\n  reset() {\n    this.pendingRequests.clear();\n    this.cache.clear();\n    this.rateLimits.clear();\n    this.circuitBreakers.clear();\n    this.subscribers.clear();\n    console.log(\"🔄 Global API Manager reset\");\n  }\n}\n\n// Create and export singleton instance\nconst globalApiManager = new GlobalApiManager();\nexport default globalApiManager;", "map": {"version": 3, "names": ["GlobalApiManager", "constructor", "pendingRequests", "Map", "cache", "rateLimits", "circuitBreakers", "subscribers", "config", "defaultCacheTime", "maxRetries", "retry<PERSON><PERSON><PERSON>", "requestTimeout", "rateLimitWindow", "maxRequestsPerWindow", "circuitBreakerThreshold", "circuitBreakerTimeout", "makeRequest", "bind", "clearCache", "subscribe", "unsubscribe", "console", "log", "generateRequestKey", "url", "method", "params", "paramString", "Object", "keys", "length", "JSON", "stringify", "isRateLimited", "endpoint", "now", "Date", "rateLimit", "get", "set", "requests", "count", "filter", "time", "warn", "push", "isCircuitOpen", "breaker", "state", "lastFailure", "updateCircuitBreaker", "success", "failures", "error", "getCachedResponse", "key", "cacheTime", "cached", "timestamp", "delete", "data", "setCachedResponse", "axiosInstance", "options", "<PERSON><PERSON><PERSON>", "skipRateLimit", "skipCircuitBreaker", "retries", "request<PERSON>ey", "split", "notifySubscribers", "has", "Error", "requestPromise", "executeRequest", "result", "lastError", "attempt", "timeout", "undefined", "response", "request", "_error$response", "_error$response2", "message", "status", "delay", "Math", "pow", "Promise", "resolve", "setTimeout", "callback", "Set", "add", "subs", "size", "isError", "for<PERSON>ach", "err", "clear", "getStats", "cachedItems", "Array", "from", "entries", "map", "reset", "globalApiManager"], "sources": ["C:/Users/<USER>/Desktop/Online_Auction_System/frontend/src/utils/globalApiManager.js"], "sourcesContent": ["/**\n * Global API Call Manager\n * Prevents duplicate API calls, implements rate limiting, caching, and circuit breaker patterns\n * across the entire application\n */\n\nclass GlobalApiManager {\n  constructor() {\n    this.pendingRequests = new Map(); // Track ongoing requests\n    this.cache = new Map(); // Cache responses\n    this.rateLimits = new Map(); // Track rate limits per endpoint\n    this.circuitBreakers = new Map(); // Circuit breaker state per endpoint\n    this.subscribers = new Map(); // Subscribers for real-time updates\n\n    // Configuration\n    this.config = {\n      defaultCacheTime: 5 * 60 * 1000, // 5 minutes\n      maxRetries: 3,\n      retryDelay: 1000, // 1 second base delay\n      requestTimeout: 10000, // 10 seconds\n      rateLimitWindow: 60000, // 1 minute\n      maxRequestsPerWindow: 30, // Max 30 requests per minute per endpoint\n      circuitBreakerThreshold: 5, // Open circuit after 5 failures\n      circuitBreakerTimeout: 30000, // 30 seconds before trying again\n    };\n\n    // Bind methods\n    this.makeRequest = this.makeRequest.bind(this);\n    this.clearCache = this.clearCache.bind(this);\n    this.subscribe = this.subscribe.bind(this);\n    this.unsubscribe = this.unsubscribe.bind(this);\n\n    console.log(\"🌐 Global API Manager initialized\");\n  }\n\n  /**\n   * Generate a unique key for the request\n   */\n  generateRequestKey(url, method = \"GET\", params = {}) {\n    const paramString =\n      Object.keys(params).length > 0 ? JSON.stringify(params) : \"\";\n    return `${method}:${url}:${paramString}`;\n  }\n\n  /**\n   * Check if request is rate limited\n   */\n  isRateLimited(endpoint) {\n    const now = Date.now();\n    const rateLimit = this.rateLimits.get(endpoint);\n\n    if (!rateLimit) {\n      this.rateLimits.set(endpoint, {\n        requests: [now],\n        count: 1,\n      });\n      return false;\n    }\n\n    // Clean old requests outside the window\n    rateLimit.requests = rateLimit.requests.filter(\n      (time) => now - time < this.config.rateLimitWindow\n    );\n\n    if (rateLimit.requests.length >= this.config.maxRequestsPerWindow) {\n      console.warn(`🚫 Rate limit exceeded for ${endpoint}`);\n      return true;\n    }\n\n    rateLimit.requests.push(now);\n    return false;\n  }\n\n  /**\n   * Check circuit breaker state\n   */\n  isCircuitOpen(endpoint) {\n    const breaker = this.circuitBreakers.get(endpoint);\n    if (!breaker) return false;\n\n    if (breaker.state === \"open\") {\n      if (\n        Date.now() - breaker.lastFailure >\n        this.config.circuitBreakerTimeout\n      ) {\n        breaker.state = \"half-open\";\n        console.log(`🔄 Circuit breaker half-open for ${endpoint}`);\n        return false;\n      }\n      console.warn(`🚫 Circuit breaker open for ${endpoint}`);\n      return true;\n    }\n\n    return false;\n  }\n\n  /**\n   * Update circuit breaker state\n   */\n  updateCircuitBreaker(endpoint, success) {\n    let breaker = this.circuitBreakers.get(endpoint);\n\n    if (!breaker) {\n      breaker = {\n        failures: 0,\n        state: \"closed\",\n        lastFailure: null,\n      };\n      this.circuitBreakers.set(endpoint, breaker);\n    }\n\n    if (success) {\n      breaker.failures = 0;\n      breaker.state = \"closed\";\n    } else {\n      breaker.failures++;\n      breaker.lastFailure = Date.now();\n\n      if (breaker.failures >= this.config.circuitBreakerThreshold) {\n        breaker.state = \"open\";\n        console.error(\n          `🚨 Circuit breaker opened for ${endpoint} after ${breaker.failures} failures`\n        );\n      }\n    }\n  }\n\n  /**\n   * Get cached response\n   */\n  getCachedResponse(key, cacheTime = this.config.defaultCacheTime) {\n    const cached = this.cache.get(key);\n    if (!cached) return null;\n\n    if (Date.now() - cached.timestamp > cacheTime) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    console.log(`📦 Using cached response for ${key}`);\n    return cached.data;\n  }\n\n  /**\n   * Cache response\n   */\n  setCachedResponse(key, data) {\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n    });\n  }\n\n  /**\n   * Main method to make API requests with all protections\n   */\n  async makeRequest(axiosInstance, url, options = {}) {\n    const {\n      method = \"GET\",\n      params = {},\n      data = null,\n      cacheTime = this.config.defaultCacheTime,\n      skipCache = false,\n      skipRateLimit = false,\n      skipCircuitBreaker = false,\n      retries = this.config.maxRetries,\n    } = options;\n\n    const requestKey = this.generateRequestKey(url, method, params);\n    const endpoint = url.split(\"?\")[0]; // Base endpoint for rate limiting\n\n    console.log(`🌐 API Request: ${method} ${url}`);\n\n    // Check cache first (for GET requests)\n    if (method === \"GET\" && !skipCache) {\n      const cached = this.getCachedResponse(requestKey, cacheTime);\n      if (cached) {\n        this.notifySubscribers(requestKey, cached, false);\n        return cached;\n      }\n    }\n\n    // Check if request is already pending\n    if (this.pendingRequests.has(requestKey)) {\n      console.log(`🔄 Request already pending for ${requestKey}`);\n      return this.pendingRequests.get(requestKey);\n    }\n\n    // Rate limiting check\n    if (!skipRateLimit && this.isRateLimited(endpoint)) {\n      throw new Error(`Rate limit exceeded for ${endpoint}`);\n    }\n\n    // Circuit breaker check\n    if (!skipCircuitBreaker && this.isCircuitOpen(endpoint)) {\n      throw new Error(`Circuit breaker open for ${endpoint}`);\n    }\n\n    // Create the request promise\n    const requestPromise = this.executeRequest(\n      axiosInstance,\n      url,\n      method,\n      params,\n      data,\n      retries,\n      endpoint\n    );\n\n    // Store pending request\n    this.pendingRequests.set(requestKey, requestPromise);\n\n    try {\n      const result = await requestPromise;\n\n      // Cache successful GET requests\n      if (method === \"GET\" && !skipCache) {\n        this.setCachedResponse(requestKey, result);\n      }\n\n      // Update circuit breaker\n      this.updateCircuitBreaker(endpoint, true);\n\n      // Notify subscribers\n      this.notifySubscribers(requestKey, result, false);\n\n      return result;\n    } catch (error) {\n      // Update circuit breaker\n      this.updateCircuitBreaker(endpoint, false);\n\n      // Notify subscribers of error\n      this.notifySubscribers(requestKey, null, true, error);\n\n      throw error;\n    } finally {\n      // Remove from pending requests\n      this.pendingRequests.delete(requestKey);\n    }\n  }\n\n  /**\n   * Execute the actual HTTP request with retries\n   */\n  async executeRequest(\n    axiosInstance,\n    url,\n    method,\n    params,\n    data,\n    retries,\n    endpoint\n  ) {\n    let lastError;\n\n    for (let attempt = 0; attempt <= retries; attempt++) {\n      try {\n        const config = {\n          method,\n          url,\n          timeout: this.config.requestTimeout,\n          params: method === \"GET\" ? params : undefined,\n          data: method !== \"GET\" ? data : undefined,\n        };\n\n        // Use axiosInstance properly - it should be the axios instance, not a function\n        const response = await axiosInstance.request(config);\n\n        console.log(\n          `✅ API Success: ${method} ${url} (attempt ${attempt + 1})`\n        );\n        return response.data;\n      } catch (error) {\n        lastError = error;\n        console.error(\n          `❌ API Error: ${method} ${url} (attempt ${attempt + 1}):`,\n          error.message\n        );\n\n        // Don't retry on certain errors\n        if (error.response?.status === 401 || error.response?.status === 403) {\n          break;\n        }\n\n        // Wait before retry (exponential backoff)\n        if (attempt < retries) {\n          const delay = this.config.retryDelay * Math.pow(2, attempt);\n          console.log(`⏳ Retrying in ${delay}ms...`);\n          await new Promise((resolve) => setTimeout(resolve, delay));\n        }\n      }\n    }\n\n    throw lastError;\n  }\n\n  /**\n   * Subscribe to API call updates\n   */\n  subscribe(requestKey, callback) {\n    if (!this.subscribers.has(requestKey)) {\n      this.subscribers.set(requestKey, new Set());\n    }\n    this.subscribers.get(requestKey).add(callback);\n\n    // Return unsubscribe function\n    return () => this.unsubscribe(requestKey, callback);\n  }\n\n  /**\n   * Unsubscribe from API call updates\n   */\n  unsubscribe(requestKey, callback) {\n    const subs = this.subscribers.get(requestKey);\n    if (subs) {\n      subs.delete(callback);\n      if (subs.size === 0) {\n        this.subscribers.delete(requestKey);\n      }\n    }\n  }\n\n  /**\n   * Notify subscribers of updates\n   */\n  notifySubscribers(requestKey, data, isError, error = null) {\n    const subs = this.subscribers.get(requestKey);\n    if (subs) {\n      subs.forEach((callback) => {\n        try {\n          callback({ data, isError, error, requestKey });\n        } catch (err) {\n          console.error(\"Error in subscriber callback:\", err);\n        }\n      });\n    }\n  }\n\n  /**\n   * Clear cache for specific key or all cache\n   */\n  clearCache(key = null) {\n    if (key) {\n      this.cache.delete(key);\n      console.log(`🗑️ Cleared cache for ${key}`);\n    } else {\n      this.cache.clear();\n      console.log(\"🗑️ Cleared all cache\");\n    }\n  }\n\n  /**\n   * Get statistics\n   */\n  getStats() {\n    return {\n      pendingRequests: this.pendingRequests.size,\n      cachedItems: this.cache.size,\n      rateLimits: this.rateLimits.size,\n      circuitBreakers: Array.from(this.circuitBreakers.entries()).map(\n        ([endpoint, breaker]) => ({\n          endpoint,\n          state: breaker.state,\n          failures: breaker.failures,\n        })\n      ),\n    };\n  }\n\n  /**\n   * Reset all state (for testing)\n   */\n  reset() {\n    this.pendingRequests.clear();\n    this.cache.clear();\n    this.rateLimits.clear();\n    this.circuitBreakers.clear();\n    this.subscribers.clear();\n    console.log(\"🔄 Global API Manager reset\");\n  }\n}\n\n// Create and export singleton instance\nconst globalApiManager = new GlobalApiManager();\n\nexport default globalApiManager;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,CAACC,KAAK,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;IACxB,IAAI,CAACE,UAAU,GAAG,IAAIF,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACG,eAAe,GAAG,IAAIH,GAAG,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,CAACI,WAAW,GAAG,IAAIJ,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE9B;IACA,IAAI,CAACK,MAAM,GAAG;MACZC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;MAAE;MACjCC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,IAAI;MAAE;MAClBC,cAAc,EAAE,KAAK;MAAE;MACvBC,eAAe,EAAE,KAAK;MAAE;MACxBC,oBAAoB,EAAE,EAAE;MAAE;MAC1BC,uBAAuB,EAAE,CAAC;MAAE;MAC5BC,qBAAqB,EAAE,KAAK,CAAE;IAChC,CAAC;;IAED;IACA,IAAI,CAACC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACF,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACG,WAAW,GAAG,IAAI,CAACA,WAAW,CAACH,IAAI,CAAC,IAAI,CAAC;IAE9CI,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;EAClD;;EAEA;AACF;AACA;EACEC,kBAAkBA,CAACC,GAAG,EAAEC,MAAM,GAAG,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;IACnD,MAAMC,WAAW,GACfC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,SAAS,CAACN,MAAM,CAAC,GAAG,EAAE;IAC9D,OAAO,GAAGD,MAAM,IAAID,GAAG,IAAIG,WAAW,EAAE;EAC1C;;EAEA;AACF;AACA;EACEM,aAAaA,CAACC,QAAQ,EAAE;IACtB,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAME,SAAS,GAAG,IAAI,CAACjC,UAAU,CAACkC,GAAG,CAACJ,QAAQ,CAAC;IAE/C,IAAI,CAACG,SAAS,EAAE;MACd,IAAI,CAACjC,UAAU,CAACmC,GAAG,CAACL,QAAQ,EAAE;QAC5BM,QAAQ,EAAE,CAACL,GAAG,CAAC;QACfM,KAAK,EAAE;MACT,CAAC,CAAC;MACF,OAAO,KAAK;IACd;;IAEA;IACAJ,SAAS,CAACG,QAAQ,GAAGH,SAAS,CAACG,QAAQ,CAACE,MAAM,CAC3CC,IAAI,IAAKR,GAAG,GAAGQ,IAAI,GAAG,IAAI,CAACpC,MAAM,CAACK,eACrC,CAAC;IAED,IAAIyB,SAAS,CAACG,QAAQ,CAACV,MAAM,IAAI,IAAI,CAACvB,MAAM,CAACM,oBAAoB,EAAE;MACjEQ,OAAO,CAACuB,IAAI,CAAC,8BAA8BV,QAAQ,EAAE,CAAC;MACtD,OAAO,IAAI;IACb;IAEAG,SAAS,CAACG,QAAQ,CAACK,IAAI,CAACV,GAAG,CAAC;IAC5B,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACEW,aAAaA,CAACZ,QAAQ,EAAE;IACtB,MAAMa,OAAO,GAAG,IAAI,CAAC1C,eAAe,CAACiC,GAAG,CAACJ,QAAQ,CAAC;IAClD,IAAI,CAACa,OAAO,EAAE,OAAO,KAAK;IAE1B,IAAIA,OAAO,CAACC,KAAK,KAAK,MAAM,EAAE;MAC5B,IACEZ,IAAI,CAACD,GAAG,CAAC,CAAC,GAAGY,OAAO,CAACE,WAAW,GAChC,IAAI,CAAC1C,MAAM,CAACQ,qBAAqB,EACjC;QACAgC,OAAO,CAACC,KAAK,GAAG,WAAW;QAC3B3B,OAAO,CAACC,GAAG,CAAC,oCAAoCY,QAAQ,EAAE,CAAC;QAC3D,OAAO,KAAK;MACd;MACAb,OAAO,CAACuB,IAAI,CAAC,+BAA+BV,QAAQ,EAAE,CAAC;MACvD,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACEgB,oBAAoBA,CAAChB,QAAQ,EAAEiB,OAAO,EAAE;IACtC,IAAIJ,OAAO,GAAG,IAAI,CAAC1C,eAAe,CAACiC,GAAG,CAACJ,QAAQ,CAAC;IAEhD,IAAI,CAACa,OAAO,EAAE;MACZA,OAAO,GAAG;QACRK,QAAQ,EAAE,CAAC;QACXJ,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE;MACf,CAAC;MACD,IAAI,CAAC5C,eAAe,CAACkC,GAAG,CAACL,QAAQ,EAAEa,OAAO,CAAC;IAC7C;IAEA,IAAII,OAAO,EAAE;MACXJ,OAAO,CAACK,QAAQ,GAAG,CAAC;MACpBL,OAAO,CAACC,KAAK,GAAG,QAAQ;IAC1B,CAAC,MAAM;MACLD,OAAO,CAACK,QAAQ,EAAE;MAClBL,OAAO,CAACE,WAAW,GAAGb,IAAI,CAACD,GAAG,CAAC,CAAC;MAEhC,IAAIY,OAAO,CAACK,QAAQ,IAAI,IAAI,CAAC7C,MAAM,CAACO,uBAAuB,EAAE;QAC3DiC,OAAO,CAACC,KAAK,GAAG,MAAM;QACtB3B,OAAO,CAACgC,KAAK,CACX,iCAAiCnB,QAAQ,UAAUa,OAAO,CAACK,QAAQ,WACrE,CAAC;MACH;IACF;EACF;;EAEA;AACF;AACA;EACEE,iBAAiBA,CAACC,GAAG,EAAEC,SAAS,GAAG,IAAI,CAACjD,MAAM,CAACC,gBAAgB,EAAE;IAC/D,MAAMiD,MAAM,GAAG,IAAI,CAACtD,KAAK,CAACmC,GAAG,CAACiB,GAAG,CAAC;IAClC,IAAI,CAACE,MAAM,EAAE,OAAO,IAAI;IAExB,IAAIrB,IAAI,CAACD,GAAG,CAAC,CAAC,GAAGsB,MAAM,CAACC,SAAS,GAAGF,SAAS,EAAE;MAC7C,IAAI,CAACrD,KAAK,CAACwD,MAAM,CAACJ,GAAG,CAAC;MACtB,OAAO,IAAI;IACb;IAEAlC,OAAO,CAACC,GAAG,CAAC,gCAAgCiC,GAAG,EAAE,CAAC;IAClD,OAAOE,MAAM,CAACG,IAAI;EACpB;;EAEA;AACF;AACA;EACEC,iBAAiBA,CAACN,GAAG,EAAEK,IAAI,EAAE;IAC3B,IAAI,CAACzD,KAAK,CAACoC,GAAG,CAACgB,GAAG,EAAE;MAClBK,IAAI;MACJF,SAAS,EAAEtB,IAAI,CAACD,GAAG,CAAC;IACtB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMnB,WAAWA,CAAC8C,aAAa,EAAEtC,GAAG,EAAEuC,OAAO,GAAG,CAAC,CAAC,EAAE;IAClD,MAAM;MACJtC,MAAM,GAAG,KAAK;MACdC,MAAM,GAAG,CAAC,CAAC;MACXkC,IAAI,GAAG,IAAI;MACXJ,SAAS,GAAG,IAAI,CAACjD,MAAM,CAACC,gBAAgB;MACxCwD,SAAS,GAAG,KAAK;MACjBC,aAAa,GAAG,KAAK;MACrBC,kBAAkB,GAAG,KAAK;MAC1BC,OAAO,GAAG,IAAI,CAAC5D,MAAM,CAACE;IACxB,CAAC,GAAGsD,OAAO;IAEX,MAAMK,UAAU,GAAG,IAAI,CAAC7C,kBAAkB,CAACC,GAAG,EAAEC,MAAM,EAAEC,MAAM,CAAC;IAC/D,MAAMQ,QAAQ,GAAGV,GAAG,CAAC6C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEpChD,OAAO,CAACC,GAAG,CAAC,mBAAmBG,MAAM,IAAID,GAAG,EAAE,CAAC;;IAE/C;IACA,IAAIC,MAAM,KAAK,KAAK,IAAI,CAACuC,SAAS,EAAE;MAClC,MAAMP,MAAM,GAAG,IAAI,CAACH,iBAAiB,CAACc,UAAU,EAAEZ,SAAS,CAAC;MAC5D,IAAIC,MAAM,EAAE;QACV,IAAI,CAACa,iBAAiB,CAACF,UAAU,EAAEX,MAAM,EAAE,KAAK,CAAC;QACjD,OAAOA,MAAM;MACf;IACF;;IAEA;IACA,IAAI,IAAI,CAACxD,eAAe,CAACsE,GAAG,CAACH,UAAU,CAAC,EAAE;MACxC/C,OAAO,CAACC,GAAG,CAAC,kCAAkC8C,UAAU,EAAE,CAAC;MAC3D,OAAO,IAAI,CAACnE,eAAe,CAACqC,GAAG,CAAC8B,UAAU,CAAC;IAC7C;;IAEA;IACA,IAAI,CAACH,aAAa,IAAI,IAAI,CAAChC,aAAa,CAACC,QAAQ,CAAC,EAAE;MAClD,MAAM,IAAIsC,KAAK,CAAC,2BAA2BtC,QAAQ,EAAE,CAAC;IACxD;;IAEA;IACA,IAAI,CAACgC,kBAAkB,IAAI,IAAI,CAACpB,aAAa,CAACZ,QAAQ,CAAC,EAAE;MACvD,MAAM,IAAIsC,KAAK,CAAC,4BAA4BtC,QAAQ,EAAE,CAAC;IACzD;;IAEA;IACA,MAAMuC,cAAc,GAAG,IAAI,CAACC,cAAc,CACxCZ,aAAa,EACbtC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNkC,IAAI,EACJO,OAAO,EACPjC,QACF,CAAC;;IAED;IACA,IAAI,CAACjC,eAAe,CAACsC,GAAG,CAAC6B,UAAU,EAAEK,cAAc,CAAC;IAEpD,IAAI;MACF,MAAME,MAAM,GAAG,MAAMF,cAAc;;MAEnC;MACA,IAAIhD,MAAM,KAAK,KAAK,IAAI,CAACuC,SAAS,EAAE;QAClC,IAAI,CAACH,iBAAiB,CAACO,UAAU,EAAEO,MAAM,CAAC;MAC5C;;MAEA;MACA,IAAI,CAACzB,oBAAoB,CAAChB,QAAQ,EAAE,IAAI,CAAC;;MAEzC;MACA,IAAI,CAACoC,iBAAiB,CAACF,UAAU,EAAEO,MAAM,EAAE,KAAK,CAAC;MAEjD,OAAOA,MAAM;IACf,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACd;MACA,IAAI,CAACH,oBAAoB,CAAChB,QAAQ,EAAE,KAAK,CAAC;;MAE1C;MACA,IAAI,CAACoC,iBAAiB,CAACF,UAAU,EAAE,IAAI,EAAE,IAAI,EAAEf,KAAK,CAAC;MAErD,MAAMA,KAAK;IACb,CAAC,SAAS;MACR;MACA,IAAI,CAACpD,eAAe,CAAC0D,MAAM,CAACS,UAAU,CAAC;IACzC;EACF;;EAEA;AACF;AACA;EACE,MAAMM,cAAcA,CAClBZ,aAAa,EACbtC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNkC,IAAI,EACJO,OAAO,EACPjC,QAAQ,EACR;IACA,IAAI0C,SAAS;IAEb,KAAK,IAAIC,OAAO,GAAG,CAAC,EAAEA,OAAO,IAAIV,OAAO,EAAEU,OAAO,EAAE,EAAE;MACnD,IAAI;QACF,MAAMtE,MAAM,GAAG;UACbkB,MAAM;UACND,GAAG;UACHsD,OAAO,EAAE,IAAI,CAACvE,MAAM,CAACI,cAAc;UACnCe,MAAM,EAAED,MAAM,KAAK,KAAK,GAAGC,MAAM,GAAGqD,SAAS;UAC7CnB,IAAI,EAAEnC,MAAM,KAAK,KAAK,GAAGmC,IAAI,GAAGmB;QAClC,CAAC;;QAED;QACA,MAAMC,QAAQ,GAAG,MAAMlB,aAAa,CAACmB,OAAO,CAAC1E,MAAM,CAAC;QAEpDc,OAAO,CAACC,GAAG,CACT,kBAAkBG,MAAM,IAAID,GAAG,aAAaqD,OAAO,GAAG,CAAC,GACzD,CAAC;QACD,OAAOG,QAAQ,CAACpB,IAAI;MACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;QAAA,IAAA6B,eAAA,EAAAC,gBAAA;QACdP,SAAS,GAAGvB,KAAK;QACjBhC,OAAO,CAACgC,KAAK,CACX,gBAAgB5B,MAAM,IAAID,GAAG,aAAaqD,OAAO,GAAG,CAAC,IAAI,EACzDxB,KAAK,CAAC+B,OACR,CAAC;;QAED;QACA,IAAI,EAAAF,eAAA,GAAA7B,KAAK,CAAC2B,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBG,MAAM,MAAK,GAAG,IAAI,EAAAF,gBAAA,GAAA9B,KAAK,CAAC2B,QAAQ,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBE,MAAM,MAAK,GAAG,EAAE;UACpE;QACF;;QAEA;QACA,IAAIR,OAAO,GAAGV,OAAO,EAAE;UACrB,MAAMmB,KAAK,GAAG,IAAI,CAAC/E,MAAM,CAACG,UAAU,GAAG6E,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEX,OAAO,CAAC;UAC3DxD,OAAO,CAACC,GAAG,CAAC,iBAAiBgE,KAAK,OAAO,CAAC;UAC1C,MAAM,IAAIG,OAAO,CAAEC,OAAO,IAAKC,UAAU,CAACD,OAAO,EAAEJ,KAAK,CAAC,CAAC;QAC5D;MACF;IACF;IAEA,MAAMV,SAAS;EACjB;;EAEA;AACF;AACA;EACEzD,SAASA,CAACiD,UAAU,EAAEwB,QAAQ,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACtF,WAAW,CAACiE,GAAG,CAACH,UAAU,CAAC,EAAE;MACrC,IAAI,CAAC9D,WAAW,CAACiC,GAAG,CAAC6B,UAAU,EAAE,IAAIyB,GAAG,CAAC,CAAC,CAAC;IAC7C;IACA,IAAI,CAACvF,WAAW,CAACgC,GAAG,CAAC8B,UAAU,CAAC,CAAC0B,GAAG,CAACF,QAAQ,CAAC;;IAE9C;IACA,OAAO,MAAM,IAAI,CAACxE,WAAW,CAACgD,UAAU,EAAEwB,QAAQ,CAAC;EACrD;;EAEA;AACF;AACA;EACExE,WAAWA,CAACgD,UAAU,EAAEwB,QAAQ,EAAE;IAChC,MAAMG,IAAI,GAAG,IAAI,CAACzF,WAAW,CAACgC,GAAG,CAAC8B,UAAU,CAAC;IAC7C,IAAI2B,IAAI,EAAE;MACRA,IAAI,CAACpC,MAAM,CAACiC,QAAQ,CAAC;MACrB,IAAIG,IAAI,CAACC,IAAI,KAAK,CAAC,EAAE;QACnB,IAAI,CAAC1F,WAAW,CAACqD,MAAM,CAACS,UAAU,CAAC;MACrC;IACF;EACF;;EAEA;AACF;AACA;EACEE,iBAAiBA,CAACF,UAAU,EAAER,IAAI,EAAEqC,OAAO,EAAE5C,KAAK,GAAG,IAAI,EAAE;IACzD,MAAM0C,IAAI,GAAG,IAAI,CAACzF,WAAW,CAACgC,GAAG,CAAC8B,UAAU,CAAC;IAC7C,IAAI2B,IAAI,EAAE;MACRA,IAAI,CAACG,OAAO,CAAEN,QAAQ,IAAK;QACzB,IAAI;UACFA,QAAQ,CAAC;YAAEhC,IAAI;YAAEqC,OAAO;YAAE5C,KAAK;YAAEe;UAAW,CAAC,CAAC;QAChD,CAAC,CAAC,OAAO+B,GAAG,EAAE;UACZ9E,OAAO,CAACgC,KAAK,CAAC,+BAA+B,EAAE8C,GAAG,CAAC;QACrD;MACF,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;EACEjF,UAAUA,CAACqC,GAAG,GAAG,IAAI,EAAE;IACrB,IAAIA,GAAG,EAAE;MACP,IAAI,CAACpD,KAAK,CAACwD,MAAM,CAACJ,GAAG,CAAC;MACtBlC,OAAO,CAACC,GAAG,CAAC,yBAAyBiC,GAAG,EAAE,CAAC;IAC7C,CAAC,MAAM;MACL,IAAI,CAACpD,KAAK,CAACiG,KAAK,CAAC,CAAC;MAClB/E,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACtC;EACF;;EAEA;AACF;AACA;EACE+E,QAAQA,CAAA,EAAG;IACT,OAAO;MACLpG,eAAe,EAAE,IAAI,CAACA,eAAe,CAAC+F,IAAI;MAC1CM,WAAW,EAAE,IAAI,CAACnG,KAAK,CAAC6F,IAAI;MAC5B5F,UAAU,EAAE,IAAI,CAACA,UAAU,CAAC4F,IAAI;MAChC3F,eAAe,EAAEkG,KAAK,CAACC,IAAI,CAAC,IAAI,CAACnG,eAAe,CAACoG,OAAO,CAAC,CAAC,CAAC,CAACC,GAAG,CAC7D,CAAC,CAACxE,QAAQ,EAAEa,OAAO,CAAC,MAAM;QACxBb,QAAQ;QACRc,KAAK,EAAED,OAAO,CAACC,KAAK;QACpBI,QAAQ,EAAEL,OAAO,CAACK;MACpB,CAAC,CACH;IACF,CAAC;EACH;;EAEA;AACF;AACA;EACEuD,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC1G,eAAe,CAACmG,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACjG,KAAK,CAACiG,KAAK,CAAC,CAAC;IAClB,IAAI,CAAChG,UAAU,CAACgG,KAAK,CAAC,CAAC;IACvB,IAAI,CAAC/F,eAAe,CAAC+F,KAAK,CAAC,CAAC;IAC5B,IAAI,CAAC9F,WAAW,CAAC8F,KAAK,CAAC,CAAC;IACxB/E,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;EAC5C;AACF;;AAEA;AACA,MAAMsF,gBAAgB,GAAG,IAAI7G,gBAAgB,CAAC,CAAC;AAE/C,eAAe6G,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}