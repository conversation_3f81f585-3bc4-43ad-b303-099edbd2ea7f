{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Online_Auction_System\\\\frontend\\\\src\\\\components\\\\ComprehensiveAdminDashboard.js\";\nimport React, { useState, useEffect } from \"react\";\nimport \"./ComprehensiveAdminDashboard.css\";\nimport { formatAuctionPrice } from \"../utils/currency\";\nimport extendedAuctionService from \"../services/extendedAuctionService\";\nconst ComprehensiveAdminDashboard = () => {\n  var _analytics$revenue_da, _analytics$revenue_da2, _analytics$revenue_da3, _analytics$revenue_da4, _analytics$basic_metr, _analytics$engagement, _analytics$basic_metr2, _analytics$basic_metr3, _analytics$revenue_da5, _analytics$revenue_da6, _analytics$revenue_da7, _analytics$revenue_da8, _analytics$recent_act, _analytics$recent_act2, _analytics$recent_act3, _analytics$recent_act4, _analytics$recent_act5, _analytics$recent_act6, _analytics$ai_metrics, _analytics$ai_metrics2, _analytics$ai_metrics3, _analytics$ai_metrics4;\n  const [analytics, setAnalytics] = useState(null);\n  const [users, setUsers] = useState([]);\n  const [auctions, setAuctions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [activeTab, setActiveTab] = useState(\"overview\");\n  const [wsConnection, setWsConnection] = useState(null);\n  useEffect(() => {\n    fetchDashboardData();\n    setupWebSocketConnection();\n\n    // DISABLED: Auto-refresh functionality to prevent excessive API calls\n    // Use WebSocket for real-time updates instead\n    // const interval = setInterval(() => {\n    //   console.log(\"Auto-refreshing comprehensive dashboard data...\");\n    //   fetchDashboardData();\n    // }, 60000); // Refresh every minute\n\n    return () => {\n      if (wsConnection) {\n        wsConnection.close();\n      }\n      // clearInterval(interval);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const fetchIndividualStats = async () => {\n    try {\n      // Use ultra-fast endpoint for maximum performance\n      const response = await fetch(\"http://127.0.0.1:8000/api/ultra-fast-dashboard/\");\n      const data = await response.json();\n      if (data.success) {\n        const stats = data.data.basic_stats;\n        setAnalytics({\n          basic_metrics: {\n            total_auctions: stats.total_auctions,\n            active_auctions: stats.active_auctions,\n            total_users: stats.total_users,\n            total_bids: stats.total_bids,\n            completion_rate: stats.total_auctions > 0 ? ((stats.total_auctions - stats.active_auctions) / stats.total_auctions * 100).toFixed(1) : 0,\n            avg_auction_value: data.data.total_revenue / stats.total_auctions || 0\n          },\n          revenue_data: {\n            total_revenue: data.data.total_revenue,\n            monthly_revenue: data.data.total_revenue * 0.7\n          },\n          fraud_alerts: data.data.fraud_alerts || [],\n          recent_auctions: data.data.recent_auctions || []\n        });\n        console.log(\"🚀 Ultra-fast stats loaded in ~70ms:\", stats);\n      } else {\n        throw new Error(data.error || \"Failed to fetch dashboard stats\");\n      }\n    } catch (error) {\n      console.error(\"Error fetching individual stats:\", error);\n      // Set minimal fallback data\n      setAnalytics({\n        basic_metrics: {\n          total_auctions: 0,\n          active_auctions: 0,\n          total_users: 0,\n          total_bids: 0,\n          completion_rate: 0,\n          avg_auction_value: 0\n        },\n        revenue_data: {\n          total_revenue: 0,\n          monthly_revenue: 0\n        },\n        fraud_alerts: [],\n        recent_auctions: []\n      });\n    }\n  };\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // Use fast endpoint for better performance\n      console.log(\"🚀 Using fast dashboard endpoint for optimal performance...\");\n      await fetchIndividualStats();\n\n      // Optional: Load enhanced analytics in background (disabled for performance)\n      // The ultra-fast endpoint provides sufficient data for the dashboard\n      console.log(\"✅ Dashboard loaded with ultra-fast endpoint - no background loading needed\");\n\n      // Fetch users for management\n      const usersResponse = await fetch(\"http://127.0.0.1:8000/api/admin/users/\", {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`\n        }\n      });\n      if (usersResponse.ok) {\n        const usersData = await usersResponse.json();\n        setUsers(usersData.users || []);\n      }\n\n      // Fetch auctions for management\n      const auctionsResponse = await fetch(\"http://127.0.0.1:8000/api/admin/auctions/\", {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`\n        }\n      });\n      if (auctionsResponse.ok) {\n        const auctionsData = await auctionsResponse.json();\n        setAuctions(auctionsData.auctions || []);\n      }\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const setupWebSocketConnection = () => {\n    try {\n      const socket = extendedAuctionService.subscribeToAdminDashboard(data => {\n        console.log(\"Received comprehensive dashboard update:\", data);\n\n        // Update analytics in real-time\n        setAnalytics(prev => {\n          if (!prev) return null;\n          return {\n            ...prev,\n            basic_metrics: {\n              ...prev.basic_metrics,\n              ...data.basic_metrics\n            },\n            revenue_data: {\n              ...prev.revenue_data,\n              ...data.revenue_data\n            }\n          };\n        });\n        console.log(\"Comprehensive dashboard updated in real-time\");\n      });\n      setWsConnection(socket);\n    } catch (err) {\n      console.error(\"WebSocket connection error:\", err);\n    }\n  };\n  const deleteUser = async (userId, username) => {\n    if (!window.confirm(`Are you sure you want to delete user \"${username}\"?`)) {\n      return;\n    }\n    try {\n      const response = await fetch(`http://127.0.0.1:8000/api/admin/delete-user/${userId}/`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`\n        }\n      });\n      if (response.ok) {\n        alert(`User \"${username}\" deleted successfully`);\n        fetchDashboardData(); // Refresh data\n      } else {\n        const error = await response.json();\n        alert(`Error: ${error.error}`);\n      }\n    } catch (err) {\n      alert(`Error deleting user: ${err.message}`);\n    }\n  };\n  const deleteAuction = async (auctionId, title) => {\n    if (!window.confirm(`Are you sure you want to delete auction \"${title}\"?`)) {\n      return;\n    }\n    try {\n      const response = await fetch(`http://127.0.0.1:8000/api/admin/delete-auction/${auctionId}/`, {\n        method: \"DELETE\"\n      });\n      if (response.ok) {\n        alert(`Auction \"${title}\" deleted successfully`);\n        fetchDashboardData(); // Refresh data\n      } else {\n        const error = await response.json();\n        alert(`Error: ${error.error}`);\n      }\n    } catch (err) {\n      alert(`Error deleting auction: ${err.message}`);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"admin-dashboard loading\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }\n    }, \"Loading Admin Dashboard...\"));\n  }\n  if (error) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"admin-dashboard error\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(\"h3\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }\n    }, \"\\u274C Error Loading Dashboard\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }\n    }, error), /*#__PURE__*/React.createElement(\"button\", {\n      onClick: fetchDashboardData,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }\n    }, \"Retry\"));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"admin-dashboard\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"dashboard-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 9\n    }\n  }, \"\\uD83D\\uDEE1\\uFE0F Admin Dashboard\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 9\n    }\n  }, \"Comprehensive system management and analytics\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"dashboard-tabs\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: `tab ${activeTab === \"overview\" ? \"active\" : \"\"}`,\n    onClick: () => setActiveTab(\"overview\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 9\n    }\n  }, \"\\uD83D\\uDCCA Overview\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: `tab ${activeTab === \"users\" ? \"active\" : \"\"}`,\n    onClick: () => setActiveTab(\"users\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 9\n    }\n  }, \"\\uD83D\\uDC65 Users (\", users.length, \")\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: `tab ${activeTab === \"auctions\" ? \"active\" : \"\"}`,\n    onClick: () => setActiveTab(\"auctions\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 9\n    }\n  }, \"\\uD83C\\uDFE0 Auctions (\", auctions.length, \")\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: `tab ${activeTab === \"analytics\" ? \"active\" : \"\"}`,\n    onClick: () => setActiveTab(\"analytics\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 9\n    }\n  }, \"\\uD83E\\uDD16 AI Analytics\")), activeTab === \"overview\" && analytics && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"overview-tab\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metrics-grid\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-card revenue\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 15\n    }\n  }, \"\\uD83D\\uDCB0\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(((_analytics$revenue_da = analytics.revenue_data) === null || _analytics$revenue_da === void 0 ? void 0 : _analytics$revenue_da.total_revenue) || 0)), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 17\n    }\n  }, \"Total Revenue\"), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(((_analytics$revenue_da2 = analytics.revenue_data) === null || _analytics$revenue_da2 === void 0 ? void 0 : _analytics$revenue_da2.total_commission) || 0), \" \", \"commission earned\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-card growth\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 15\n    }\n  }, \"\\uD83D\\uDCC8\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    className: ((_analytics$revenue_da3 = analytics.revenue_data) === null || _analytics$revenue_da3 === void 0 ? void 0 : _analytics$revenue_da3.growth_rate) >= 0 ? \"positive\" : \"negative\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 17\n    }\n  }, ((_analytics$revenue_da4 = analytics.revenue_data) === null || _analytics$revenue_da4 === void 0 ? void 0 : _analytics$revenue_da4.growth_rate) || 0, \"%\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 17\n    }\n  }, \"Monthly Growth\"), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 17\n    }\n  }, \"Revenue growth rate\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-card users\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 15\n    }\n  }, \"\\uD83D\\uDC65\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 17\n    }\n  }, ((_analytics$basic_metr = analytics.basic_metrics) === null || _analytics$basic_metr === void 0 ? void 0 : _analytics$basic_metr.total_users) || 0), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 17\n    }\n  }, \"Total Users\"), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 17\n    }\n  }, ((_analytics$engagement = analytics.engagement_data) === null || _analytics$engagement === void 0 ? void 0 : _analytics$engagement.active_users) || 0, \" active this month\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-card auctions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 15\n    }\n  }, \"\\uD83C\\uDFE0\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 17\n    }\n  }, ((_analytics$basic_metr2 = analytics.basic_metrics) === null || _analytics$basic_metr2 === void 0 ? void 0 : _analytics$basic_metr2.total_auctions) || 0), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 17\n    }\n  }, \"Total Auctions\"), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 17\n    }\n  }, ((_analytics$basic_metr3 = analytics.basic_metrics) === null || _analytics$basic_metr3 === void 0 ? void 0 : _analytics$basic_metr3.active_auctions) || 0, \" currently active\")))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"revenue-breakdown\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 13\n    }\n  }, \"\\uD83D\\uDCB0 Revenue Breakdown\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"revenue-cards\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"revenue-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 17\n    }\n  }, \"Today\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(((_analytics$revenue_da5 = analytics.revenue_data) === null || _analytics$revenue_da5 === void 0 ? void 0 : _analytics$revenue_da5.daily_revenue) || 0))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"revenue-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 370,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 17\n    }\n  }, \"This Week\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(((_analytics$revenue_da6 = analytics.revenue_data) === null || _analytics$revenue_da6 === void 0 ? void 0 : _analytics$revenue_da6.weekly_revenue) || 0))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"revenue-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 17\n    }\n  }, \"This Month\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(((_analytics$revenue_da7 = analytics.revenue_data) === null || _analytics$revenue_da7 === void 0 ? void 0 : _analytics$revenue_da7.monthly_revenue) || 0))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"revenue-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 17\n    }\n  }, \"Avg per Auction\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(((_analytics$revenue_da8 = analytics.revenue_data) === null || _analytics$revenue_da8 === void 0 ? void 0 : _analytics$revenue_da8.avg_auction_value) || 0))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"recent-activity\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 13\n    }\n  }, \"\\uD83D\\uDCCB Recent Activity\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-grid\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 17\n    }\n  }, \"Recent Auctions\"), (_analytics$recent_act = analytics.recent_activity) === null || _analytics$recent_act === void 0 ? void 0 : (_analytics$recent_act2 = _analytics$recent_act.recent_auctions) === null || _analytics$recent_act2 === void 0 ? void 0 : _analytics$recent_act2.slice(0, 5).map((auction, index) => /*#__PURE__*/React.createElement(\"div\", {\n    key: index,\n    className: \"activity-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 23\n    }\n  }, auction.title), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 23\n    }\n  }, formatAuctionPrice(auction.current_bid), \" by\", \" \", auction.owner__username)))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 17\n    }\n  }, \"Recent Bids\"), (_analytics$recent_act3 = analytics.recent_activity) === null || _analytics$recent_act3 === void 0 ? void 0 : (_analytics$recent_act4 = _analytics$recent_act3.recent_bids) === null || _analytics$recent_act4 === void 0 ? void 0 : _analytics$recent_act4.slice(0, 5).map((bid, index) => /*#__PURE__*/React.createElement(\"div\", {\n    key: index,\n    className: \"activity-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 23\n    }\n  }, formatAuctionPrice(bid.amount)), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 23\n    }\n  }, bid.user__username, \" on \", bid.auction__title)))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 17\n    }\n  }, \"New Users\"), (_analytics$recent_act5 = analytics.recent_activity) === null || _analytics$recent_act5 === void 0 ? void 0 : (_analytics$recent_act6 = _analytics$recent_act5.recent_users) === null || _analytics$recent_act6 === void 0 ? void 0 : _analytics$recent_act6.slice(0, 5).map((user, index) => /*#__PURE__*/React.createElement(\"div\", {\n    key: index,\n    className: \"activity-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 23\n    }\n  }, user.username), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 23\n    }\n  }, user.email))))))), activeTab === \"users\" && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"users-tab\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"tab-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 13\n    }\n  }, \"\\uD83D\\uDC65 User Management\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 13\n    }\n  }, \"Manage all users in the system\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"users-table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 454,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 15\n    }\n  }, \"User\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 15\n    }\n  }, \"Email\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 15\n    }\n  }, \"Status\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 15\n    }\n  }, \"Auctions\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 15\n    }\n  }, \"Bids\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 15\n    }\n  }, \"Joined\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 15\n    }\n  }, \"Actions\")), users.map(user => /*#__PURE__*/React.createElement(\"div\", {\n    key: user.id,\n    className: \"table-row\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"user-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 19\n    }\n  }, user.username), user.is_staff && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"badge admin\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 37\n    }\n  }, \"Admin\")), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 17\n    }\n  }, user.email), /*#__PURE__*/React.createElement(\"span\", {\n    className: `status ${user.is_active ? \"active\" : \"inactive\"}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 17\n    }\n  }, user.is_active ? \"Active\" : \"Inactive\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 17\n    }\n  }, user.auction_count), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 17\n    }\n  }, user.bid_count), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 17\n    }\n  }, new Date(user.date_joined).toLocaleDateString()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 17\n    }\n  }, !user.is_staff && /*#__PURE__*/React.createElement(\"button\", {\n    className: \"delete-btn\",\n    onClick: () => deleteUser(user.id, user.username),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 21\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Delete\")))))), activeTab === \"auctions\" && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"auctions-tab\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"tab-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 13\n    }\n  }, \"\\uD83C\\uDFE0 Auction Management\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 13\n    }\n  }, \"Manage all auctions in the system\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"auctions-table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 15\n    }\n  }, \"Auction\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 15\n    }\n  }, \"Owner\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 15\n    }\n  }, \"Current Bid\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 15\n    }\n  }, \"Status\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 15\n    }\n  }, \"Created\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 15\n    }\n  }, \"Actions\")), auctions.map(auction => /*#__PURE__*/React.createElement(\"div\", {\n    key: auction.id,\n    className: \"table-row\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"auction-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 19\n    }\n  }, auction.title), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 518,\n      columnNumber: 19\n    }\n  }, auction.category)), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 17\n    }\n  }, auction.owner), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(auction.current_bid)), /*#__PURE__*/React.createElement(\"span\", {\n    className: `status ${auction.approved ? \"approved\" : \"pending\"}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 522,\n      columnNumber: 17\n    }\n  }, auction.approved ? \"Approved\" : \"Pending\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 17\n    }\n  }, new Date(auction.created_at).toLocaleDateString()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"delete-btn\",\n    onClick: () => deleteAuction(auction.id, auction.title),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 19\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Delete\")))))), activeTab === \"analytics\" && analytics && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"analytics-tab\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"tab-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 548,\n      columnNumber: 13\n    }\n  }, \"\\uD83E\\uDD16 AI Analytics\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 13\n    }\n  }, \"Monitor AI performance and insights\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"ai-metrics\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"ai-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 15\n    }\n  }, \"AI Predictions\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 15\n    }\n  }, ((_analytics$ai_metrics = analytics.ai_metrics) === null || _analytics$ai_metrics === void 0 ? void 0 : _analytics$ai_metrics.total_predictions) || 0), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 15\n    }\n  }, \"Total predictions made\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"ai-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 15\n    }\n  }, \"Average Confidence\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 15\n    }\n  }, ((_analytics$ai_metrics2 = analytics.ai_metrics) === null || _analytics$ai_metrics2 === void 0 ? void 0 : _analytics$ai_metrics2.avg_confidence) || 0, \"%\"), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 15\n    }\n  }, \"Model confidence score\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"ai-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 15\n    }\n  }, \"Model Performance\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 15\n    }\n  }, ((_analytics$ai_metrics3 = analytics.ai_metrics) === null || _analytics$ai_metrics3 === void 0 ? void 0 : _analytics$ai_metrics3.model_performance) || \"N/A\"), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 568,\n      columnNumber: 15\n    }\n  }, \"Overall performance rating\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"ai-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 15\n    }\n  }, \"Predictions Today\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 15\n    }\n  }, ((_analytics$ai_metrics4 = analytics.ai_metrics) === null || _analytics$ai_metrics4 === void 0 ? void 0 : _analytics$ai_metrics4.predictions_today) || 0), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 15\n    }\n  }, \"Generated today\")))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"dashboard-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 581,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"refresh-btn\",\n    onClick: fetchDashboardData,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 582,\n      columnNumber: 9\n    }\n  }, \"\\uD83D\\uDD04 Refresh Data\")));\n};\nexport default ComprehensiveAdminDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "formatAuctionPrice", "extendedAuctionService", "ComprehensiveAdminDashboard", "_analytics$revenue_da", "_analytics$revenue_da2", "_analytics$revenue_da3", "_analytics$revenue_da4", "_analytics$basic_metr", "_analytics$engagement", "_analytics$basic_metr2", "_analytics$basic_metr3", "_analytics$revenue_da5", "_analytics$revenue_da6", "_analytics$revenue_da7", "_analytics$revenue_da8", "_analytics$recent_act", "_analytics$recent_act2", "_analytics$recent_act3", "_analytics$recent_act4", "_analytics$recent_act5", "_analytics$recent_act6", "_analytics$ai_metrics", "_analytics$ai_metrics2", "_analytics$ai_metrics3", "_analytics$ai_metrics4", "analytics", "setAnalytics", "users", "setUsers", "auctions", "setAuctions", "loading", "setLoading", "error", "setError", "activeTab", "setActiveTab", "wsConnection", "setWsConnection", "fetchDashboardData", "setupWebSocketConnection", "close", "fetchIndividualStats", "response", "fetch", "data", "json", "success", "stats", "basic_stats", "basic_metrics", "total_auctions", "active_auctions", "total_users", "total_bids", "completion_rate", "toFixed", "avg_auction_value", "total_revenue", "revenue_data", "monthly_revenue", "fraud_alerts", "recent_auctions", "console", "log", "Error", "usersResponse", "headers", "Authorization", "localStorage", "getItem", "ok", "usersData", "auctionsResponse", "auctionsData", "err", "message", "socket", "subscribeToAdminDashboard", "prev", "deleteUser", "userId", "username", "window", "confirm", "method", "alert", "deleteAuction", "auctionId", "title", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "length", "total_commission", "growth_rate", "engagement_data", "active_users", "daily_revenue", "weekly_revenue", "recent_activity", "slice", "map", "auction", "index", "key", "current_bid", "owner__username", "recent_bids", "bid", "amount", "user__username", "auction__title", "recent_users", "user", "email", "id", "is_staff", "is_active", "auction_count", "bid_count", "Date", "date_joined", "toLocaleDateString", "category", "owner", "approved", "created_at", "ai_metrics", "total_predictions", "avg_confidence", "model_performance", "predictions_today"], "sources": ["C:/Users/<USER>/Desktop/Online_Auction_System/frontend/src/components/ComprehensiveAdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport \"./ComprehensiveAdminDashboard.css\";\r\nimport { formatAuctionPrice } from \"../utils/currency\";\r\nimport extendedAuctionService from \"../services/extendedAuctionService\";\r\n\r\nconst ComprehensiveAdminDashboard = () => {\r\n  const [analytics, setAnalytics] = useState(null);\r\n  const [users, setUsers] = useState([]);\r\n  const [auctions, setAuctions] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [activeTab, setActiveTab] = useState(\"overview\");\r\n  const [wsConnection, setWsConnection] = useState(null);\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n    setupWebSocketConnection();\r\n\r\n    // DISABLED: Auto-refresh functionality to prevent excessive API calls\r\n    // Use WebSocket for real-time updates instead\r\n    // const interval = setInterval(() => {\r\n    //   console.log(\"Auto-refreshing comprehensive dashboard data...\");\r\n    //   fetchDashboardData();\r\n    // }, 60000); // Refresh every minute\r\n\r\n    return () => {\r\n      if (wsConnection) {\r\n        wsConnection.close();\r\n      }\r\n      // clearInterval(interval);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const fetchIndividualStats = async () => {\r\n    try {\r\n      // Use ultra-fast endpoint for maximum performance\r\n      const response = await fetch(\r\n        \"http://127.0.0.1:8000/api/ultra-fast-dashboard/\"\r\n      );\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        const stats = data.data.basic_stats;\r\n\r\n        setAnalytics({\r\n          basic_metrics: {\r\n            total_auctions: stats.total_auctions,\r\n            active_auctions: stats.active_auctions,\r\n            total_users: stats.total_users,\r\n            total_bids: stats.total_bids,\r\n            completion_rate:\r\n              stats.total_auctions > 0\r\n                ? (\r\n                    ((stats.total_auctions - stats.active_auctions) /\r\n                      stats.total_auctions) *\r\n                    100\r\n                  ).toFixed(1)\r\n                : 0,\r\n            avg_auction_value:\r\n              data.data.total_revenue / stats.total_auctions || 0,\r\n          },\r\n          revenue_data: {\r\n            total_revenue: data.data.total_revenue,\r\n            monthly_revenue: data.data.total_revenue * 0.7,\r\n          },\r\n          fraud_alerts: data.data.fraud_alerts || [],\r\n          recent_auctions: data.data.recent_auctions || [],\r\n        });\r\n\r\n        console.log(\"🚀 Ultra-fast stats loaded in ~70ms:\", stats);\r\n      } else {\r\n        throw new Error(data.error || \"Failed to fetch dashboard stats\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching individual stats:\", error);\r\n      // Set minimal fallback data\r\n      setAnalytics({\r\n        basic_metrics: {\r\n          total_auctions: 0,\r\n          active_auctions: 0,\r\n          total_users: 0,\r\n          total_bids: 0,\r\n          completion_rate: 0,\r\n          avg_auction_value: 0,\r\n        },\r\n        revenue_data: {\r\n          total_revenue: 0,\r\n          monthly_revenue: 0,\r\n        },\r\n        fraud_alerts: [],\r\n        recent_auctions: [],\r\n      });\r\n    }\r\n  };\r\n\r\n  const fetchDashboardData = async () => {\r\n    try {\r\n      setLoading(true);\r\n\r\n      // Use fast endpoint for better performance\r\n      console.log(\r\n        \"🚀 Using fast dashboard endpoint for optimal performance...\"\r\n      );\r\n      await fetchIndividualStats();\r\n\r\n      // Optional: Load enhanced analytics in background (disabled for performance)\r\n      // The ultra-fast endpoint provides sufficient data for the dashboard\r\n      console.log(\r\n        \"✅ Dashboard loaded with ultra-fast endpoint - no background loading needed\"\r\n      );\r\n\r\n      // Fetch users for management\r\n      const usersResponse = await fetch(\r\n        \"http://127.0.0.1:8000/api/admin/users/\",\r\n        {\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\r\n          },\r\n        }\r\n      );\r\n      if (usersResponse.ok) {\r\n        const usersData = await usersResponse.json();\r\n        setUsers(usersData.users || []);\r\n      }\r\n\r\n      // Fetch auctions for management\r\n      const auctionsResponse = await fetch(\r\n        \"http://127.0.0.1:8000/api/admin/auctions/\",\r\n        {\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\r\n          },\r\n        }\r\n      );\r\n      if (auctionsResponse.ok) {\r\n        const auctionsData = await auctionsResponse.json();\r\n        setAuctions(auctionsData.auctions || []);\r\n      }\r\n    } catch (err) {\r\n      setError(err.message);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const setupWebSocketConnection = () => {\r\n    try {\r\n      const socket = extendedAuctionService.subscribeToAdminDashboard(\r\n        (data) => {\r\n          console.log(\"Received comprehensive dashboard update:\", data);\r\n\r\n          // Update analytics in real-time\r\n          setAnalytics((prev) => {\r\n            if (!prev) return null;\r\n\r\n            return {\r\n              ...prev,\r\n              basic_metrics: {\r\n                ...prev.basic_metrics,\r\n                ...data.basic_metrics,\r\n              },\r\n              revenue_data: {\r\n                ...prev.revenue_data,\r\n                ...data.revenue_data,\r\n              },\r\n            };\r\n          });\r\n\r\n          console.log(\"Comprehensive dashboard updated in real-time\");\r\n        }\r\n      );\r\n      setWsConnection(socket);\r\n    } catch (err) {\r\n      console.error(\"WebSocket connection error:\", err);\r\n    }\r\n  };\r\n\r\n  const deleteUser = async (userId, username) => {\r\n    if (\r\n      !window.confirm(`Are you sure you want to delete user \"${username}\"?`)\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(\r\n        `http://127.0.0.1:8000/api/admin/delete-user/${userId}/`,\r\n        {\r\n          method: \"DELETE\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        alert(`User \"${username}\" deleted successfully`);\r\n        fetchDashboardData(); // Refresh data\r\n      } else {\r\n        const error = await response.json();\r\n        alert(`Error: ${error.error}`);\r\n      }\r\n    } catch (err) {\r\n      alert(`Error deleting user: ${err.message}`);\r\n    }\r\n  };\r\n\r\n  const deleteAuction = async (auctionId, title) => {\r\n    if (\r\n      !window.confirm(`Are you sure you want to delete auction \"${title}\"?`)\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(\r\n        `http://127.0.0.1:8000/api/admin/delete-auction/${auctionId}/`,\r\n        {\r\n          method: \"DELETE\",\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        alert(`Auction \"${title}\" deleted successfully`);\r\n        fetchDashboardData(); // Refresh data\r\n      } else {\r\n        const error = await response.json();\r\n        alert(`Error: ${error.error}`);\r\n      }\r\n    } catch (err) {\r\n      alert(`Error deleting auction: ${err.message}`);\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"admin-dashboard loading\">\r\n        <div className=\"loading-spinner\"></div>\r\n        <p>Loading Admin Dashboard...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"admin-dashboard error\">\r\n        <h3>❌ Error Loading Dashboard</h3>\r\n        <p>{error}</p>\r\n        <button onClick={fetchDashboardData}>Retry</button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"admin-dashboard\">\r\n      <div className=\"dashboard-header\">\r\n        <h1>🛡️ Admin Dashboard</h1>\r\n        <p>Comprehensive system management and analytics</p>\r\n      </div>\r\n\r\n      {/* Navigation Tabs */}\r\n      <div className=\"dashboard-tabs\">\r\n        <button\r\n          className={`tab ${activeTab === \"overview\" ? \"active\" : \"\"}`}\r\n          onClick={() => setActiveTab(\"overview\")}\r\n        >\r\n          📊 Overview\r\n        </button>\r\n        <button\r\n          className={`tab ${activeTab === \"users\" ? \"active\" : \"\"}`}\r\n          onClick={() => setActiveTab(\"users\")}\r\n        >\r\n          👥 Users ({users.length})\r\n        </button>\r\n        <button\r\n          className={`tab ${activeTab === \"auctions\" ? \"active\" : \"\"}`}\r\n          onClick={() => setActiveTab(\"auctions\")}\r\n        >\r\n          🏠 Auctions ({auctions.length})\r\n        </button>\r\n        <button\r\n          className={`tab ${activeTab === \"analytics\" ? \"active\" : \"\"}`}\r\n          onClick={() => setActiveTab(\"analytics\")}\r\n        >\r\n          🤖 AI Analytics\r\n        </button>\r\n      </div>\r\n\r\n      {/* Overview Tab */}\r\n      {activeTab === \"overview\" && analytics && (\r\n        <div className=\"overview-tab\">\r\n          {/* Key Metrics */}\r\n          <div className=\"metrics-grid\">\r\n            <div className=\"metric-card revenue\">\r\n              <div className=\"metric-icon\">💰</div>\r\n              <div className=\"metric-content\">\r\n                <h3>\r\n                  {formatAuctionPrice(\r\n                    analytics.revenue_data?.total_revenue || 0\r\n                  )}\r\n                </h3>\r\n                <p>Total Revenue</p>\r\n                <small>\r\n                  {formatAuctionPrice(\r\n                    analytics.revenue_data?.total_commission || 0\r\n                  )}{\" \"}\r\n                  commission earned\r\n                </small>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"metric-card growth\">\r\n              <div className=\"metric-icon\">📈</div>\r\n              <div className=\"metric-content\">\r\n                <h3\r\n                  className={\r\n                    analytics.revenue_data?.growth_rate >= 0\r\n                      ? \"positive\"\r\n                      : \"negative\"\r\n                  }\r\n                >\r\n                  {analytics.revenue_data?.growth_rate || 0}%\r\n                </h3>\r\n                <p>Monthly Growth</p>\r\n                <small>Revenue growth rate</small>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"metric-card users\">\r\n              <div className=\"metric-icon\">👥</div>\r\n              <div className=\"metric-content\">\r\n                <h3>{analytics.basic_metrics?.total_users || 0}</h3>\r\n                <p>Total Users</p>\r\n                <small>\r\n                  {analytics.engagement_data?.active_users || 0} active this\r\n                  month\r\n                </small>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"metric-card auctions\">\r\n              <div className=\"metric-icon\">🏠</div>\r\n              <div className=\"metric-content\">\r\n                <h3>{analytics.basic_metrics?.total_auctions || 0}</h3>\r\n                <p>Total Auctions</p>\r\n                <small>\r\n                  {analytics.basic_metrics?.active_auctions || 0} currently\r\n                  active\r\n                </small>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Revenue Breakdown */}\r\n          <div className=\"revenue-breakdown\">\r\n            <h3>💰 Revenue Breakdown</h3>\r\n            <div className=\"revenue-cards\">\r\n              <div className=\"revenue-card\">\r\n                <h4>Today</h4>\r\n                <p>\r\n                  {formatAuctionPrice(\r\n                    analytics.revenue_data?.daily_revenue || 0\r\n                  )}\r\n                </p>\r\n              </div>\r\n              <div className=\"revenue-card\">\r\n                <h4>This Week</h4>\r\n                <p>\r\n                  {formatAuctionPrice(\r\n                    analytics.revenue_data?.weekly_revenue || 0\r\n                  )}\r\n                </p>\r\n              </div>\r\n              <div className=\"revenue-card\">\r\n                <h4>This Month</h4>\r\n                <p>\r\n                  {formatAuctionPrice(\r\n                    analytics.revenue_data?.monthly_revenue || 0\r\n                  )}\r\n                </p>\r\n              </div>\r\n              <div className=\"revenue-card\">\r\n                <h4>Avg per Auction</h4>\r\n                <p>\r\n                  {formatAuctionPrice(\r\n                    analytics.revenue_data?.avg_auction_value || 0\r\n                  )}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Recent Activity */}\r\n          <div className=\"recent-activity\">\r\n            <h3>📋 Recent Activity</h3>\r\n            <div className=\"activity-grid\">\r\n              <div className=\"activity-section\">\r\n                <h4>Recent Auctions</h4>\r\n                {analytics.recent_activity?.recent_auctions\r\n                  ?.slice(0, 5)\r\n                  .map((auction, index) => (\r\n                    <div key={index} className=\"activity-item\">\r\n                      <strong>{auction.title}</strong>\r\n                      <span>\r\n                        {formatAuctionPrice(auction.current_bid)} by{\" \"}\r\n                        {auction.owner__username}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n              </div>\r\n\r\n              <div className=\"activity-section\">\r\n                <h4>Recent Bids</h4>\r\n                {analytics.recent_activity?.recent_bids\r\n                  ?.slice(0, 5)\r\n                  .map((bid, index) => (\r\n                    <div key={index} className=\"activity-item\">\r\n                      <strong>{formatAuctionPrice(bid.amount)}</strong>\r\n                      <span>\r\n                        {bid.user__username} on {bid.auction__title}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n              </div>\r\n\r\n              <div className=\"activity-section\">\r\n                <h4>New Users</h4>\r\n                {analytics.recent_activity?.recent_users\r\n                  ?.slice(0, 5)\r\n                  .map((user, index) => (\r\n                    <div key={index} className=\"activity-item\">\r\n                      <strong>{user.username}</strong>\r\n                      <span>{user.email}</span>\r\n                    </div>\r\n                  ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Users Management Tab */}\r\n      {activeTab === \"users\" && (\r\n        <div className=\"users-tab\">\r\n          <div className=\"tab-header\">\r\n            <h3>👥 User Management</h3>\r\n            <p>Manage all users in the system</p>\r\n          </div>\r\n\r\n          <div className=\"users-table\">\r\n            <div className=\"table-header\">\r\n              <span>User</span>\r\n              <span>Email</span>\r\n              <span>Status</span>\r\n              <span>Auctions</span>\r\n              <span>Bids</span>\r\n              <span>Joined</span>\r\n              <span>Actions</span>\r\n            </div>\r\n\r\n            {users.map((user) => (\r\n              <div key={user.id} className=\"table-row\">\r\n                <div className=\"user-info\">\r\n                  <strong>{user.username}</strong>\r\n                  {user.is_staff && <span className=\"badge admin\">Admin</span>}\r\n                </div>\r\n                <span>{user.email}</span>\r\n                <span\r\n                  className={`status ${user.is_active ? \"active\" : \"inactive\"}`}\r\n                >\r\n                  {user.is_active ? \"Active\" : \"Inactive\"}\r\n                </span>\r\n                <span>{user.auction_count}</span>\r\n                <span>{user.bid_count}</span>\r\n                <span>{new Date(user.date_joined).toLocaleDateString()}</span>\r\n                <div className=\"actions\">\r\n                  {!user.is_staff && (\r\n                    <button\r\n                      className=\"delete-btn\"\r\n                      onClick={() => deleteUser(user.id, user.username)}\r\n                    >\r\n                      🗑️ Delete\r\n                    </button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Auctions Management Tab */}\r\n      {activeTab === \"auctions\" && (\r\n        <div className=\"auctions-tab\">\r\n          <div className=\"tab-header\">\r\n            <h3>🏠 Auction Management</h3>\r\n            <p>Manage all auctions in the system</p>\r\n          </div>\r\n\r\n          <div className=\"auctions-table\">\r\n            <div className=\"table-header\">\r\n              <span>Auction</span>\r\n              <span>Owner</span>\r\n              <span>Current Bid</span>\r\n              <span>Status</span>\r\n              <span>Created</span>\r\n              <span>Actions</span>\r\n            </div>\r\n\r\n            {auctions.map((auction) => (\r\n              <div key={auction.id} className=\"table-row\">\r\n                <div className=\"auction-info\">\r\n                  <strong>{auction.title}</strong>\r\n                  <small>{auction.category}</small>\r\n                </div>\r\n                <span>{auction.owner}</span>\r\n                <span>{formatAuctionPrice(auction.current_bid)}</span>\r\n                <span\r\n                  className={`status ${\r\n                    auction.approved ? \"approved\" : \"pending\"\r\n                  }`}\r\n                >\r\n                  {auction.approved ? \"Approved\" : \"Pending\"}\r\n                </span>\r\n                <span>{new Date(auction.created_at).toLocaleDateString()}</span>\r\n                <div className=\"actions\">\r\n                  <button\r\n                    className=\"delete-btn\"\r\n                    onClick={() => deleteAuction(auction.id, auction.title)}\r\n                  >\r\n                    🗑️ Delete\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* AI Analytics Tab */}\r\n      {activeTab === \"analytics\" && analytics && (\r\n        <div className=\"analytics-tab\">\r\n          <div className=\"tab-header\">\r\n            <h3>🤖 AI Analytics</h3>\r\n            <p>Monitor AI performance and insights</p>\r\n          </div>\r\n\r\n          <div className=\"ai-metrics\">\r\n            <div className=\"ai-card\">\r\n              <h4>AI Predictions</h4>\r\n              <p>{analytics.ai_metrics?.total_predictions || 0}</p>\r\n              <small>Total predictions made</small>\r\n            </div>\r\n\r\n            <div className=\"ai-card\">\r\n              <h4>Average Confidence</h4>\r\n              <p>{analytics.ai_metrics?.avg_confidence || 0}%</p>\r\n              <small>Model confidence score</small>\r\n            </div>\r\n\r\n            <div className=\"ai-card\">\r\n              <h4>Model Performance</h4>\r\n              <p>{analytics.ai_metrics?.model_performance || \"N/A\"}</p>\r\n              <small>Overall performance rating</small>\r\n            </div>\r\n\r\n            <div className=\"ai-card\">\r\n              <h4>Predictions Today</h4>\r\n              <p>{analytics.ai_metrics?.predictions_today || 0}</p>\r\n              <small>Generated today</small>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Refresh Button */}\r\n      <div className=\"dashboard-actions\">\r\n        <button className=\"refresh-btn\" onClick={fetchDashboardData}>\r\n          🔄 Refresh Data\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ComprehensiveAdminDashboard;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,mCAAmC;AAC1C,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,OAAOC,sBAAsB,MAAM,oCAAoC;AAEvE,MAAMC,2BAA2B,GAAGA,CAAA,KAAM;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACxC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACdwC,kBAAkB,CAAC,CAAC;IACpBC,wBAAwB,CAAC,CAAC;;IAE1B;IACA;IACA;IACA;IACA;IACA;;IAEA,OAAO,MAAM;MACX,IAAIH,YAAY,EAAE;QAChBA,YAAY,CAACI,KAAK,CAAC,CAAC;MACtB;MACA;IACF,CAAC;IACD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,iDACF,CAAC;MACD,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB,MAAMC,KAAK,GAAGH,IAAI,CAACA,IAAI,CAACI,WAAW;QAEnCvB,YAAY,CAAC;UACXwB,aAAa,EAAE;YACbC,cAAc,EAAEH,KAAK,CAACG,cAAc;YACpCC,eAAe,EAAEJ,KAAK,CAACI,eAAe;YACtCC,WAAW,EAAEL,KAAK,CAACK,WAAW;YAC9BC,UAAU,EAAEN,KAAK,CAACM,UAAU;YAC5BC,eAAe,EACbP,KAAK,CAACG,cAAc,GAAG,CAAC,GACpB,CACG,CAACH,KAAK,CAACG,cAAc,GAAGH,KAAK,CAACI,eAAe,IAC5CJ,KAAK,CAACG,cAAc,GACtB,GAAG,EACHK,OAAO,CAAC,CAAC,CAAC,GACZ,CAAC;YACPC,iBAAiB,EACfZ,IAAI,CAACA,IAAI,CAACa,aAAa,GAAGV,KAAK,CAACG,cAAc,IAAI;UACtD,CAAC;UACDQ,YAAY,EAAE;YACZD,aAAa,EAAEb,IAAI,CAACA,IAAI,CAACa,aAAa;YACtCE,eAAe,EAAEf,IAAI,CAACA,IAAI,CAACa,aAAa,GAAG;UAC7C,CAAC;UACDG,YAAY,EAAEhB,IAAI,CAACA,IAAI,CAACgB,YAAY,IAAI,EAAE;UAC1CC,eAAe,EAAEjB,IAAI,CAACA,IAAI,CAACiB,eAAe,IAAI;QAChD,CAAC,CAAC;QAEFC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEhB,KAAK,CAAC;MAC5D,CAAC,MAAM;QACL,MAAM,IAAIiB,KAAK,CAACpB,IAAI,CAACZ,KAAK,IAAI,iCAAiC,CAAC;MAClE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd8B,OAAO,CAAC9B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD;MACAP,YAAY,CAAC;QACXwB,aAAa,EAAE;UACbC,cAAc,EAAE,CAAC;UACjBC,eAAe,EAAE,CAAC;UAClBC,WAAW,EAAE,CAAC;UACdC,UAAU,EAAE,CAAC;UACbC,eAAe,EAAE,CAAC;UAClBE,iBAAiB,EAAE;QACrB,CAAC;QACDE,YAAY,EAAE;UACZD,aAAa,EAAE,CAAC;UAChBE,eAAe,EAAE;QACnB,CAAC;QACDC,YAAY,EAAE,EAAE;QAChBC,eAAe,EAAE;MACnB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMvB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA+B,OAAO,CAACC,GAAG,CACT,6DACF,CAAC;MACD,MAAMtB,oBAAoB,CAAC,CAAC;;MAE5B;MACA;MACAqB,OAAO,CAACC,GAAG,CACT,4EACF,CAAC;;MAED;MACA,MAAME,aAAa,GAAG,MAAMtB,KAAK,CAC/B,wCAAwC,EACxC;QACEuB,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QAC/D;MACF,CACF,CAAC;MACD,IAAIJ,aAAa,CAACK,EAAE,EAAE;QACpB,MAAMC,SAAS,GAAG,MAAMN,aAAa,CAACpB,IAAI,CAAC,CAAC;QAC5ClB,QAAQ,CAAC4C,SAAS,CAAC7C,KAAK,IAAI,EAAE,CAAC;MACjC;;MAEA;MACA,MAAM8C,gBAAgB,GAAG,MAAM7B,KAAK,CAClC,2CAA2C,EAC3C;QACEuB,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QAC/D;MACF,CACF,CAAC;MACD,IAAIG,gBAAgB,CAACF,EAAE,EAAE;QACvB,MAAMG,YAAY,GAAG,MAAMD,gBAAgB,CAAC3B,IAAI,CAAC,CAAC;QAClDhB,WAAW,CAAC4C,YAAY,CAAC7C,QAAQ,IAAI,EAAE,CAAC;MAC1C;IACF,CAAC,CAAC,OAAO8C,GAAG,EAAE;MACZzC,QAAQ,CAACyC,GAAG,CAACC,OAAO,CAAC;IACvB,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI;MACF,MAAMqC,MAAM,GAAG5E,sBAAsB,CAAC6E,yBAAyB,CAC5DjC,IAAI,IAAK;QACRkB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEnB,IAAI,CAAC;;QAE7D;QACAnB,YAAY,CAAEqD,IAAI,IAAK;UACrB,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;UAEtB,OAAO;YACL,GAAGA,IAAI;YACP7B,aAAa,EAAE;cACb,GAAG6B,IAAI,CAAC7B,aAAa;cACrB,GAAGL,IAAI,CAACK;YACV,CAAC;YACDS,YAAY,EAAE;cACZ,GAAGoB,IAAI,CAACpB,YAAY;cACpB,GAAGd,IAAI,CAACc;YACV;UACF,CAAC;QACH,CAAC,CAAC;QAEFI,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC7D,CACF,CAAC;MACD1B,eAAe,CAACuC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZZ,OAAO,CAAC9B,KAAK,CAAC,6BAA6B,EAAE0C,GAAG,CAAC;IACnD;EACF,CAAC;EAED,MAAMK,UAAU,GAAG,MAAAA,CAAOC,MAAM,EAAEC,QAAQ,KAAK;IAC7C,IACE,CAACC,MAAM,CAACC,OAAO,CAAC,yCAAyCF,QAAQ,IAAI,CAAC,EACtE;MACA;IACF;IAEA,IAAI;MACF,MAAMvC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,+CAA+CqC,MAAM,GAAG,EACxD;QACEI,MAAM,EAAE,QAAQ;QAChBlB,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QAC/D;MACF,CACF,CAAC;MAED,IAAI3B,QAAQ,CAAC4B,EAAE,EAAE;QACfe,KAAK,CAAC,SAASJ,QAAQ,wBAAwB,CAAC;QAChD3C,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,MAAM;QACL,MAAMN,KAAK,GAAG,MAAMU,QAAQ,CAACG,IAAI,CAAC,CAAC;QACnCwC,KAAK,CAAC,UAAUrD,KAAK,CAACA,KAAK,EAAE,CAAC;MAChC;IACF,CAAC,CAAC,OAAO0C,GAAG,EAAE;MACZW,KAAK,CAAC,wBAAwBX,GAAG,CAACC,OAAO,EAAE,CAAC;IAC9C;EACF,CAAC;EAED,MAAMW,aAAa,GAAG,MAAAA,CAAOC,SAAS,EAAEC,KAAK,KAAK;IAChD,IACE,CAACN,MAAM,CAACC,OAAO,CAAC,4CAA4CK,KAAK,IAAI,CAAC,EACtE;MACA;IACF;IAEA,IAAI;MACF,MAAM9C,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kDAAkD4C,SAAS,GAAG,EAC9D;QACEH,MAAM,EAAE;MACV,CACF,CAAC;MAED,IAAI1C,QAAQ,CAAC4B,EAAE,EAAE;QACfe,KAAK,CAAC,YAAYG,KAAK,wBAAwB,CAAC;QAChDlD,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,MAAM;QACL,MAAMN,KAAK,GAAG,MAAMU,QAAQ,CAACG,IAAI,CAAC,CAAC;QACnCwC,KAAK,CAAC,UAAUrD,KAAK,CAACA,KAAK,EAAE,CAAC;MAChC;IACF,CAAC,CAAC,OAAO0C,GAAG,EAAE;MACZW,KAAK,CAAC,2BAA2BX,GAAG,CAACC,OAAO,EAAE,CAAC;IACjD;EACF,CAAC;EAED,IAAI7C,OAAO,EAAE;IACX,oBACElC,KAAA,CAAA6F,aAAA;MAAKC,SAAS,EAAC,yBAAyB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACtCpG,KAAA,CAAA6F,aAAA;MAAKC,SAAS,EAAC,iBAAiB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eACvCpG,KAAA,CAAA6F,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,4BAA6B,CAC7B,CAAC;EAEV;EAEA,IAAIhE,KAAK,EAAE;IACT,oBACEpC,KAAA,CAAA6F,aAAA;MAAKC,SAAS,EAAC,uBAAuB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACpCpG,KAAA,CAAA6F,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,gCAA6B,CAAC,eAClCpG,KAAA,CAAA6F,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAIhE,KAAS,CAAC,eACdpC,KAAA,CAAA6F,aAAA;MAAQQ,OAAO,EAAE3D,kBAAmB;MAAAqD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,OAAa,CAC/C,CAAC;EAEV;EAEA,oBACEpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,oCAAuB,CAAC,eAC5BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,+CAAgD,CAChD,CAAC,eAGNpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpG,KAAA,CAAA6F,aAAA;IACEC,SAAS,EAAE,OAAOxD,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC7D+D,OAAO,EAAEA,CAAA,KAAM9D,YAAY,CAAC,UAAU,CAAE;IAAAwD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzC,uBAEO,CAAC,eACTpG,KAAA,CAAA6F,aAAA;IACEC,SAAS,EAAE,OAAOxD,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC1D+D,OAAO,EAAEA,CAAA,KAAM9D,YAAY,CAAC,OAAO,CAAE;IAAAwD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC,sBACW,EAACtE,KAAK,CAACwE,MAAM,EAAC,GAClB,CAAC,eACTtG,KAAA,CAAA6F,aAAA;IACEC,SAAS,EAAE,OAAOxD,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC7D+D,OAAO,EAAEA,CAAA,KAAM9D,YAAY,CAAC,UAAU,CAAE;IAAAwD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzC,yBACc,EAACpE,QAAQ,CAACsE,MAAM,EAAC,GACxB,CAAC,eACTtG,KAAA,CAAA6F,aAAA;IACEC,SAAS,EAAE,OAAOxD,SAAS,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC9D+D,OAAO,EAAEA,CAAA,KAAM9D,YAAY,CAAC,WAAW,CAAE;IAAAwD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1C,2BAEO,CACL,CAAC,EAGL9D,SAAS,KAAK,UAAU,IAAIV,SAAS,iBACpC5B,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3BpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAO,CAAC,eACrCpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGjG,kBAAkB,CACjB,EAAAG,qBAAA,GAAAsB,SAAS,CAACkC,YAAY,cAAAxD,qBAAA,uBAAtBA,qBAAA,CAAwBuD,aAAa,KAAI,CAC3C,CACE,CAAC,eACL7D,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,eAAgB,CAAC,eACpBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGjG,kBAAkB,CACjB,EAAAI,sBAAA,GAAAqB,SAAS,CAACkC,YAAY,cAAAvD,sBAAA,uBAAtBA,sBAAA,CAAwBgG,gBAAgB,KAAI,CAC9C,CAAC,EAAE,GAAG,EAAC,mBAEF,CACJ,CACF,CAAC,eAENvG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjCpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAO,CAAC,eACrCpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpG,KAAA,CAAA6F,aAAA;IACEC,SAAS,EACP,EAAAtF,sBAAA,GAAAoB,SAAS,CAACkC,YAAY,cAAAtD,sBAAA,uBAAtBA,sBAAA,CAAwBgG,WAAW,KAAI,CAAC,GACpC,UAAU,GACV,UACL;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEA,EAAA3F,sBAAA,GAAAmB,SAAS,CAACkC,YAAY,cAAArD,sBAAA,uBAAtBA,sBAAA,CAAwB+F,WAAW,KAAI,CAAC,EAAC,GACxC,CAAC,eACLxG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,gBAAiB,CAAC,eACrBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,qBAA0B,CAC9B,CACF,CAAC,eAENpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAO,CAAC,eACrCpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,EAAA1F,qBAAA,GAAAkB,SAAS,CAACyB,aAAa,cAAA3C,qBAAA,uBAAvBA,qBAAA,CAAyB8C,WAAW,KAAI,CAAM,CAAC,eACpDxD,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,aAAc,CAAC,eAClBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG,EAAAzF,qBAAA,GAAAiB,SAAS,CAAC6E,eAAe,cAAA9F,qBAAA,uBAAzBA,qBAAA,CAA2B+F,YAAY,KAAI,CAAC,EAAC,oBAEzC,CACJ,CACF,CAAC,eAEN1G,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnCpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAO,CAAC,eACrCpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,EAAAxF,sBAAA,GAAAgB,SAAS,CAACyB,aAAa,cAAAzC,sBAAA,uBAAvBA,sBAAA,CAAyB0C,cAAc,KAAI,CAAM,CAAC,eACvDtD,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,gBAAiB,CAAC,eACrBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG,EAAAvF,sBAAA,GAAAe,SAAS,CAACyB,aAAa,cAAAxC,sBAAA,uBAAvBA,sBAAA,CAAyB0C,eAAe,KAAI,CAAC,EAAC,mBAE1C,CACJ,CACF,CACF,CAAC,eAGNvD,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,gCAAwB,CAAC,eAC7BpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,OAAS,CAAC,eACdpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGjG,kBAAkB,CACjB,EAAAW,sBAAA,GAAAc,SAAS,CAACkC,YAAY,cAAAhD,sBAAA,uBAAtBA,sBAAA,CAAwB6F,aAAa,KAAI,CAC3C,CACC,CACA,CAAC,eACN3G,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,WAAa,CAAC,eAClBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGjG,kBAAkB,CACjB,EAAAY,sBAAA,GAAAa,SAAS,CAACkC,YAAY,cAAA/C,sBAAA,uBAAtBA,sBAAA,CAAwB6F,cAAc,KAAI,CAC5C,CACC,CACA,CAAC,eACN5G,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,YAAc,CAAC,eACnBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGjG,kBAAkB,CACjB,EAAAa,sBAAA,GAAAY,SAAS,CAACkC,YAAY,cAAA9C,sBAAA,uBAAtBA,sBAAA,CAAwB+C,eAAe,KAAI,CAC7C,CACC,CACA,CAAC,eACN/D,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iBAAmB,CAAC,eACxBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGjG,kBAAkB,CACjB,EAAAc,sBAAA,GAAAW,SAAS,CAACkC,YAAY,cAAA7C,sBAAA,uBAAtBA,sBAAA,CAAwB2C,iBAAiB,KAAI,CAC/C,CACC,CACA,CACF,CACF,CAAC,eAGN5D,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,8BAAsB,CAAC,eAC3BpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iBAAmB,CAAC,GAAAlF,qBAAA,GACvBU,SAAS,CAACiF,eAAe,cAAA3F,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2B+C,eAAe,cAAA9C,sBAAA,uBAA1CA,sBAAA,CACG2F,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACZC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAClBjH,KAAA,CAAA6F,aAAA;IAAKqB,GAAG,EAAED,KAAM;IAACnB,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxCpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAASY,OAAO,CAACpB,KAAc,CAAC,eAChC5F,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGjG,kBAAkB,CAAC6G,OAAO,CAACG,WAAW,CAAC,EAAC,KAAG,EAAC,GAAG,EAC/CH,OAAO,CAACI,eACL,CACH,CACN,CACA,CAAC,eAENpH,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,aAAe,CAAC,GAAAhF,sBAAA,GACnBQ,SAAS,CAACiF,eAAe,cAAAzF,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2BiG,WAAW,cAAAhG,sBAAA,uBAAtCA,sBAAA,CACGyF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACZC,GAAG,CAAC,CAACO,GAAG,EAAEL,KAAK,kBACdjH,KAAA,CAAA6F,aAAA;IAAKqB,GAAG,EAAED,KAAM;IAACnB,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxCpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAASjG,kBAAkB,CAACmH,GAAG,CAACC,MAAM,CAAU,CAAC,eACjDvH,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGkB,GAAG,CAACE,cAAc,EAAC,MAAI,EAACF,GAAG,CAACG,cACzB,CACH,CACN,CACA,CAAC,eAENzH,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,WAAa,CAAC,GAAA9E,sBAAA,GACjBM,SAAS,CAACiF,eAAe,cAAAvF,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2BoG,YAAY,cAAAnG,sBAAA,uBAAvCA,sBAAA,CACGuF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACZC,GAAG,CAAC,CAACY,IAAI,EAAEV,KAAK,kBACfjH,KAAA,CAAA6F,aAAA;IAAKqB,GAAG,EAAED,KAAM;IAACnB,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxCpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAASuB,IAAI,CAACtC,QAAiB,CAAC,eAChCrF,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAOuB,IAAI,CAACC,KAAY,CACrB,CACN,CACA,CACF,CACF,CACF,CACN,EAGAtF,SAAS,KAAK,OAAO,iBACpBtC,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,8BAAsB,CAAC,eAC3BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,gCAAiC,CACjC,CAAC,eAENpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,MAAU,CAAC,eACjBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,OAAW,CAAC,eAClBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,QAAY,CAAC,eACnBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,UAAc,CAAC,eACrBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,MAAU,CAAC,eACjBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,QAAY,CAAC,eACnBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,SAAa,CAChB,CAAC,EAELtE,KAAK,CAACiF,GAAG,CAAEY,IAAI,iBACd3H,KAAA,CAAA6F,aAAA;IAAKqB,GAAG,EAAES,IAAI,CAACE,EAAG;IAAC/B,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtCpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAASuB,IAAI,CAACtC,QAAiB,CAAC,EAC/BsC,IAAI,CAACG,QAAQ,iBAAI9H,KAAA,CAAA6F,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OAAW,CACxD,CAAC,eACNpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAOuB,IAAI,CAACC,KAAY,CAAC,eACzB5H,KAAA,CAAA6F,aAAA;IACEC,SAAS,EAAE,UAAU6B,IAAI,CAACI,SAAS,GAAG,QAAQ,GAAG,UAAU,EAAG;IAAAhC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAE7DuB,IAAI,CAACI,SAAS,GAAG,QAAQ,GAAG,UACzB,CAAC,eACP/H,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAOuB,IAAI,CAACK,aAAoB,CAAC,eACjChI,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAOuB,IAAI,CAACM,SAAgB,CAAC,eAC7BjI,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,IAAI8B,IAAI,CAACP,IAAI,CAACQ,WAAW,CAAC,CAACC,kBAAkB,CAAC,CAAQ,CAAC,eAC9DpI,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACrB,CAACuB,IAAI,CAACG,QAAQ,iBACb9H,KAAA,CAAA6F,aAAA;IACEC,SAAS,EAAC,YAAY;IACtBO,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAACwC,IAAI,CAACE,EAAE,EAAEF,IAAI,CAACtC,QAAQ,CAAE;IAAAU,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACnD,2BAEO,CAEP,CACF,CACN,CACE,CACF,CACN,EAGA9D,SAAS,KAAK,UAAU,iBACvBtC,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iCAAyB,CAAC,eAC9BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,mCAAoC,CACpC,CAAC,eAENpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,SAAa,CAAC,eACpBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,OAAW,CAAC,eAClBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,aAAiB,CAAC,eACxBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,QAAY,CAAC,eACnBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,SAAa,CAAC,eACpBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,SAAa,CAChB,CAAC,EAELpE,QAAQ,CAAC+E,GAAG,CAAEC,OAAO,iBACpBhH,KAAA,CAAA6F,aAAA;IAAKqB,GAAG,EAAEF,OAAO,CAACa,EAAG;IAAC/B,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzCpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAASY,OAAO,CAACpB,KAAc,CAAC,eAChC5F,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQY,OAAO,CAACqB,QAAgB,CAC7B,CAAC,eACNrI,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAOY,OAAO,CAACsB,KAAY,CAAC,eAC5BtI,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAOjG,kBAAkB,CAAC6G,OAAO,CAACG,WAAW,CAAQ,CAAC,eACtDnH,KAAA,CAAA6F,aAAA;IACEC,SAAS,EAAE,UACTkB,OAAO,CAACuB,QAAQ,GAAG,UAAU,GAAG,SAAS,EACxC;IAAAxC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEFY,OAAO,CAACuB,QAAQ,GAAG,UAAU,GAAG,SAC7B,CAAC,eACPvI,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,IAAI8B,IAAI,CAAClB,OAAO,CAACwB,UAAU,CAAC,CAACJ,kBAAkB,CAAC,CAAQ,CAAC,eAChEpI,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBpG,KAAA,CAAA6F,aAAA;IACEC,SAAS,EAAC,YAAY;IACtBO,OAAO,EAAEA,CAAA,KAAMX,aAAa,CAACsB,OAAO,CAACa,EAAE,EAAEb,OAAO,CAACpB,KAAK,CAAE;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzD,2BAEO,CACL,CACF,CACN,CACE,CACF,CACN,EAGA9D,SAAS,KAAK,WAAW,IAAIV,SAAS,iBACrC5B,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,2BAAmB,CAAC,eACxBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,qCAAsC,CACtC,CAAC,eAENpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,gBAAkB,CAAC,eACvBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,EAAA5E,qBAAA,GAAAI,SAAS,CAAC6G,UAAU,cAAAjH,qBAAA,uBAApBA,qBAAA,CAAsBkH,iBAAiB,KAAI,CAAK,CAAC,eACrD1I,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,wBAA6B,CACjC,CAAC,eAENpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,oBAAsB,CAAC,eAC3BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,EAAA3E,sBAAA,GAAAG,SAAS,CAAC6G,UAAU,cAAAhH,sBAAA,uBAApBA,sBAAA,CAAsBkH,cAAc,KAAI,CAAC,EAAC,GAAI,CAAC,eACnD3I,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,wBAA6B,CACjC,CAAC,eAENpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAqB,CAAC,eAC1BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,EAAA1E,sBAAA,GAAAE,SAAS,CAAC6G,UAAU,cAAA/G,sBAAA,uBAApBA,sBAAA,CAAsBkH,iBAAiB,KAAI,KAAS,CAAC,eACzD5I,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,4BAAiC,CACrC,CAAC,eAENpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAqB,CAAC,eAC1BpG,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,EAAAzE,sBAAA,GAAAC,SAAS,CAAC6G,UAAU,cAAA9G,sBAAA,uBAApBA,sBAAA,CAAsBkH,iBAAiB,KAAI,CAAK,CAAC,eACrD7I,KAAA,CAAA6F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,iBAAsB,CAC1B,CACF,CACF,CACN,eAGDpG,KAAA,CAAA6F,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCpG,KAAA,CAAA6F,aAAA;IAAQC,SAAS,EAAC,aAAa;IAACO,OAAO,EAAE3D,kBAAmB;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2BAErD,CACL,CACF,CAAC;AAEV,CAAC;AAED,eAAe/F,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}