"""
Unit Tests for Auction Models
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta

from auction.models import Auction, Bid, Payment, Notification, UserProfile


class UserProfileModelTest(TestCase):
    """Test UserProfile model"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_user_profile_creation(self):
        """Test UserProfile is created automatically"""
        profile = UserProfile.objects.get(user=self.user)
        self.assertEqual(profile.user, self.user)
        self.assertEqual(profile.phone_number, '')
        self.assertEqual(profile.address, '')

    def test_user_profile_str(self):
        """Test UserProfile string representation"""
        profile = UserProfile.objects.get(user=self.user)
        self.assertEqual(str(profile), f"Profile of {self.user.username}")

    def test_phone_number_validation(self):
        """Test phone number validation"""
        profile = UserProfile.objects.get(user=self.user)
        profile.phone_number = '9876543210'
        profile.full_clean()  # Should not raise ValidationError

        # Test invalid phone number
        profile.phone_number = '123'
        with self.assertRaises(ValidationError):
            profile.full_clean()


class AuctionModelTest(TestCase):
    """Test Auction model"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='auctioneer',
            email='<EMAIL>',
            password='testpass123'
        )
        self.future_time = timezone.now() + timedelta(days=7)

    def test_auction_creation(self):
        """Test auction creation with valid data"""
        auction = Auction.objects.create(
            title='Test Auction',
            description='A test auction item',
            starting_bid=Decimal('100.00'),
            current_bid=Decimal('100.00'),
            category='Electronics',
            condition='Excellent',
            owner=self.user,
            end_time=self.future_time
        )

        self.assertEqual(auction.title, 'Test Auction')
        self.assertEqual(auction.starting_bid, Decimal('100.00'))
        self.assertEqual(auction.current_bid, Decimal('100.00'))
        self.assertEqual(auction.owner, self.user)
        self.assertTrue(auction.is_active())

    def test_auction_str(self):
        """Test auction string representation"""
        auction = Auction.objects.create(
            title='Test Item',
            description='Test description',
            starting_bid=Decimal('50.00'),
            owner=self.user,
            end_time=self.future_time
        )
        self.assertEqual(str(auction), 'Test Item')

    def test_auction_is_active(self):
        """Test auction active status"""
        # Active auction
        active_auction = Auction.objects.create(
            title='Active Auction',
            description='Test',
            starting_bid=Decimal('100.00'),
            owner=self.user,
            end_time=self.future_time
        )
        self.assertTrue(active_auction.is_active())

        # Ended auction
        past_time = timezone.now() - timedelta(days=1)
        ended_auction = Auction.objects.create(
            title='Ended Auction',
            description='Test',
            starting_bid=Decimal('100.00'),
            owner=self.user,
            end_time=past_time
        )
        self.assertFalse(ended_auction.is_active())

    def test_auction_time_remaining(self):
        """Test time remaining calculation"""
        auction = Auction.objects.create(
            title='Test Auction',
            description='Test',
            starting_bid=Decimal('100.00'),
            owner=self.user,
            end_time=self.future_time
        )

        time_remaining = auction.time_remaining()
        self.assertIsNotNone(time_remaining)
        self.assertGreater(time_remaining.total_seconds(), 0)

    def test_auction_validation(self):
        """Test auction field validation"""
        # Test negative starting bid
        with self.assertRaises(ValidationError):
            auction = Auction(
                title='Invalid Auction',
                description='Test',
                starting_bid=Decimal('-10.00'),
                owner=self.user,
                end_time=self.future_time
            )
            auction.full_clean()

        # Test past end time
        past_time = timezone.now() - timedelta(days=1)
        with self.assertRaises(ValidationError):
            auction = Auction(
                title='Past Auction',
                description='Test',
                starting_bid=Decimal('100.00'),
                owner=self.user,
                end_time=past_time
            )
            auction.full_clean()


class BidModelTest(TestCase):
    """Test Bid model"""

    def setUp(self):
        self.owner = User.objects.create_user(
            username='owner',
            email='<EMAIL>',
            password='testpass123'
        )
        self.bidder = User.objects.create_user(
            username='bidder',
            email='<EMAIL>',
            password='testpass123'
        )
        self.auction = Auction.objects.create(
            title='Test Auction',
            description='Test auction for bidding',
            starting_bid=Decimal('100.00'),
            current_bid=Decimal('100.00'),
            category='Electronics',
            owner=self.owner,
            end_time=timezone.now() + timedelta(days=7)
        )

    def test_bid_creation(self):
        """Test bid creation"""
        bid = Bid.objects.create(
            auction=self.auction,
            user=self.bidder,
            amount=Decimal('150.00')
        )

        self.assertEqual(bid.auction, self.auction)
        self.assertEqual(bid.user, self.bidder)
        self.assertEqual(bid.amount, Decimal('150.00'))

    def test_bid_str(self):
        """Test bid string representation"""
        bid = Bid.objects.create(
            auction=self.auction,
            user=self.bidder,
            amount=Decimal('150.00')
        )
        expected = f"Bid of ₹150.00 on {self.auction.title} by {self.bidder.username}"
        self.assertEqual(str(bid), expected)

    def test_bid_validation(self):
        """Test bid validation"""
        # Test negative bid amount
        with self.assertRaises(ValidationError):
            bid = Bid(
                auction=self.auction,
                user=self.bidder,
                amount=Decimal('-50.00')
            )
            bid.full_clean()

        # Test bid lower than current bid
        with self.assertRaises(ValidationError):
            bid = Bid(
                auction=self.auction,
                user=self.bidder,
                amount=Decimal('50.00')  # Lower than starting bid
            )
            bid.full_clean()

    def test_owner_cannot_bid_on_own_auction(self):
        """Test that auction owner cannot bid on their own auction"""
        with self.assertRaises(ValidationError):
            bid = Bid(
                auction=self.auction,
                user=self.owner,  # Owner trying to bid
                amount=Decimal('150.00')
            )
            bid.full_clean()


class PaymentModelTest(TestCase):
    """Test Payment model"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='payer',
            email='<EMAIL>',
            password='testpass123'
        )
        self.owner = User.objects.create_user(
            username='seller',
            email='<EMAIL>',
            password='testpass123'
        )
        self.auction = Auction.objects.create(
            title='Paid Auction',
            description='Test auction for payment',
            starting_bid=Decimal('100.00'),
            owner=self.owner,
            end_time=timezone.now() + timedelta(days=7)
        )

    def test_payment_creation(self):
        """Test payment creation"""
        payment = Payment.objects.create(
            user=self.user,
            auction=self.auction,
            amount=Decimal('200.00'),
            payment_method='razorpay',
            transaction_id='txn_123456789'
        )

        self.assertEqual(payment.user, self.user)
        self.assertEqual(payment.auction, self.auction)
        self.assertEqual(payment.amount, Decimal('200.00'))
        self.assertEqual(payment.status, 'pending')

    def test_payment_str(self):
        """Test payment string representation"""
        payment = Payment.objects.create(
            user=self.user,
            auction=self.auction,
            amount=Decimal('200.00'),
            payment_method='razorpay',
            transaction_id='txn_123456789'
        )
        expected = f"Payment of ₹200.00 by {self.user.username} for {self.auction.title}"
        self.assertEqual(str(payment), expected)

    def test_payment_status_choices(self):
        """Test payment status choices"""
        payment = Payment.objects.create(
            user=self.user,
            auction=self.auction,
            amount=Decimal('200.00'),
            payment_method='razorpay'
        )

        # Test valid status changes
        valid_statuses = ['pending', 'completed', 'failed', 'refunded']
        for status in valid_statuses:
            payment.status = status
            payment.full_clean()  # Should not raise ValidationError


class NotificationModelTest(TestCase):
    """Test Notification model"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='notified_user',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_notification_creation(self):
        """Test notification creation"""
        notification = Notification.objects.create(
            user=self.user,
            title='Test Notification',
            message='This is a test notification',
            notification_type='bid'
        )

        self.assertEqual(notification.user, self.user)
        self.assertEqual(notification.title, 'Test Notification')
        self.assertFalse(notification.is_read)

    def test_notification_str(self):
        """Test notification string representation"""
        notification = Notification.objects.create(
            user=self.user,
            title='Test Notification',
            message='Test message',
            notification_type='bid'
        )
        expected = f"Test Notification for {self.user.username}"
        self.assertEqual(str(notification), expected)

    def test_notification_mark_as_read(self):
        """Test marking notification as read"""
        notification = Notification.objects.create(
            user=self.user,
            title='Test Notification',
            message='Test message',
            notification_type='bid'
        )

        self.assertFalse(notification.is_read)
        notification.is_read = True
        notification.save()
        self.assertTrue(notification.is_read)

    def test_notification_types(self):
        """Test different notification types"""
        types = ['bid', 'auction_end', 'payment', 'general']

        for notif_type in types:
            notification = Notification.objects.create(
                user=self.user,
                title=f'Test {notif_type}',
                message=f'Test {notif_type} message',
                notification_type=notif_type
            )
            notification.full_clean()  # Should not raise ValidationError
