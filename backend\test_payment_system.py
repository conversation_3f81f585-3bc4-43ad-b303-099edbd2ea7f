#!/usr/bin/env python3
"""
Comprehensive Payment System Test
Creates dummy user and auction to test payment functionality
"""

import os
import sys
import django
from datetime import timedelta
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from auction.models import Auction, Bid, Payment, Category
from django.utils import timezone

class PaymentSystemTester:
    def __init__(self):
        self.test_user = None
        self.test_auction = None
        self.test_results = []
        
    def create_test_user(self):
        """Create dummy user for payment testing"""
        print("👤 CREATING TEST USER FOR PAYMENT")
        print("="*40)
        
        # Delete existing test user if exists
        User.objects.filter(username='payment_test_user').delete()
        User.objects.filter(email='<EMAIL>').delete()
        
        # Create test user with specified email
        self.test_user = User.objects.create_user(
            username='payment_test_user',
            email='<EMAIL>',
            first_name='Payment',
            last_name='Tester',
            password='testpassword123'
        )
        
        print(f"   ✅ Created test user: {self.test_user.username}")
        print(f"   📧 Email: {self.test_user.email}")
        print(f"   🆔 User ID: {self.test_user.id}")
        
    def create_test_auction(self):
        """Create dummy auction for payment testing"""
        print("\n🏷️ CREATING TEST AUCTION FOR PAYMENT")
        print("="*40)
        
        # Get or create a category
        category, created = Category.objects.get_or_create(
            name='Electronics',
            defaults={'description': 'Electronic items'}
        )
        
        # Create test auction
        self.test_auction = Auction.objects.create(
            title='Payment Test Auction - Premium Laptop',
            description='High-end laptop for payment system testing. This is a dummy auction created specifically for testing the payment functionality.',
            starting_bid=299.99,
            current_bid=299.99,
            end_time=timezone.now() + timedelta(days=1),
            category=category,
            condition='New',
            image='https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500',
            owner=self.test_user,
            approved=True,
            featured=True
        )
        
        print(f"   ✅ Created test auction: {self.test_auction.title}")
        print(f"   💰 Starting bid: ${self.test_auction.starting_bid}")
        print(f"   🆔 Auction ID: {self.test_auction.id}")
        print(f"   ⏰ End time: {self.test_auction.end_time}")
        
    def create_test_bid(self):
        """Create a test bid to simulate winning"""
        print("\n💰 CREATING TEST BID")
        print("="*25)
        
        # Create a bid from the test user
        bid_amount = self.test_auction.starting_bid + 50.00  # $349.99
        
        test_bid = Bid.objects.create(
            auction=self.test_auction,
            user=self.test_user,
            amount=bid_amount
        )
        
        # Update auction current bid
        self.test_auction.current_bid = bid_amount
        self.test_auction.save()
        
        print(f"   ✅ Created test bid: ${test_bid.amount}")
        print(f"   🆔 Bid ID: {test_bid.id}")
        print(f"   📊 Updated auction current bid: ${self.test_auction.current_bid}")
        
    def test_payment_endpoints(self):
        """Test all payment-related endpoints"""
        print("\n🔗 TESTING PAYMENT ENDPOINTS")
        print("="*35)
        
        base_url = 'http://127.0.0.1:8000/api'
        test_amount = float(self.test_auction.current_bid)
        
        endpoints_to_test = [
            {
                'name': 'Get Payment Methods',
                'url': f'{base_url}/payments/methods/',
                'method': 'GET',
                'data': None
            },
            {
                'name': 'Create Stripe Payment Intent',
                'url': f'{base_url}/payments/stripe/create-intent/',
                'method': 'POST',
                'data': {
                    'auction_id': self.test_auction.id,
                    'amount': test_amount,
                    'currency': 'usd'
                }
            },
            {
                'name': 'Create Stripe Checkout Session',
                'url': f'{base_url}/payments/stripe/create-checkout/',
                'method': 'POST',
                'data': {
                    'auction_id': self.test_auction.id,
                    'amount': test_amount,
                    'currency': 'usd'
                }
            }
        ]
        
        for endpoint in endpoints_to_test:
            print(f"\n🔍 Testing: {endpoint['name']}")
            try:
                if endpoint['method'] == 'GET':
                    response = requests.get(endpoint['url'], timeout=10)
                else:
                    response = requests.post(
                        endpoint['url'],
                        json=endpoint['data'],
                        headers={'Content-Type': 'application/json'},
                        timeout=10
                    )
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ Success: {endpoint['name']}")
                    
                    # Log specific response details
                    if 'payment_methods' in data:
                        print(f"   📊 Available methods: {len(data['payment_methods'])}")
                    elif 'client_secret' in data:
                        print(f"   🔑 Payment intent created")
                        print(f"   💰 Amount: ${data.get('amount', 0)/100}")
                    elif 'checkout_url' in data:
                        print(f"   🔗 Checkout session created")
                        print(f"   🌐 Checkout URL available")
                    
                    self.test_results.append({
                        'endpoint': endpoint['name'],
                        'status': 'SUCCESS',
                        'response_code': response.status_code
                    })
                else:
                    print(f"   ❌ Failed: HTTP {response.status_code}")
                    print(f"   Error: {response.text[:100]}")
                    self.test_results.append({
                        'endpoint': endpoint['name'],
                        'status': 'FAILED',
                        'response_code': response.status_code,
                        'error': response.text[:100]
                    })
                    
            except Exception as e:
                print(f"   ❌ Request failed: {e}")
                self.test_results.append({
                    'endpoint': endpoint['name'],
                    'status': 'ERROR',
                    'error': str(e)
                })
                
    def test_payment_models(self):
        """Test payment model functionality"""
        print("\n💳 TESTING PAYMENT MODELS")
        print("="*30)
        
        try:
            # Create a test payment record
            test_payment = Payment.objects.create(
                user=self.test_user,
                auction=self.test_auction,
                amount=self.test_auction.current_bid,
                currency='USD',
                payment_method='stripe',
                payment_status='pending'
            )
            
            print(f"   ✅ Payment record created: ID {test_payment.id}")
            print(f"   💰 Amount: {test_payment.formatted_amount}")
            print(f"   📊 Status: {test_payment.payment_status}")
            print(f"   🔧 Method: {test_payment.payment_method}")
            
            # Test payment methods
            print(f"   📅 Is overdue: {test_payment.is_overdue()}")
            
            # Update payment status
            test_payment.payment_status = 'completed'
            test_payment.payment_date = timezone.now()
            test_payment.save()
            
            print(f"   ✅ Payment status updated to: {test_payment.payment_status}")
            
            self.test_results.append({
                'test': 'Payment Model',
                'status': 'SUCCESS',
                'payment_id': test_payment.id
            })
            
        except Exception as e:
            print(f"   ❌ Payment model test failed: {e}")
            self.test_results.append({
                'test': 'Payment Model',
                'status': 'FAILED',
                'error': str(e)
            })
            
    def check_stripe_configuration(self):
        """Check Stripe configuration"""
        print("\n🔧 CHECKING STRIPE CONFIGURATION")
        print("="*35)
        
        try:
            from django.conf import settings
            
            stripe_settings = {
                'STRIPE_PUBLISHABLE_KEY': getattr(settings, 'STRIPE_PUBLISHABLE_KEY', None),
                'STRIPE_SECRET_KEY': getattr(settings, 'STRIPE_SECRET_KEY', None),
                'STRIPE_WEBHOOK_SECRET': getattr(settings, 'STRIPE_WEBHOOK_SECRET', None),
                'STRIPE_SUCCESS_URL': getattr(settings, 'STRIPE_SUCCESS_URL', None),
                'STRIPE_CANCEL_URL': getattr(settings, 'STRIPE_CANCEL_URL', None),
            }
            
            for key, value in stripe_settings.items():
                if value:
                    if 'SECRET' in key or 'WEBHOOK' in key:
                        print(f"   ✅ {key}: {'*' * 20} (configured)")
                    else:
                        print(f"   ✅ {key}: {value}")
                else:
                    print(f"   ❌ {key}: Not configured")
                    
            # Check if Stripe is properly imported
            import stripe
            print(f"   ✅ Stripe library version: {stripe.version.VERSION}")
            
        except Exception as e:
            print(f"   ❌ Stripe configuration error: {e}")
            
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*60)
        print("📋 PAYMENT SYSTEM TEST REPORT")
        print("="*60)
        
        print(f"\n👤 TEST USER DETAILS:")
        print(f"   Username: {self.test_user.username}")
        print(f"   Email: {self.test_user.email}")
        print(f"   User ID: {self.test_user.id}")
        
        print(f"\n🏷️ TEST AUCTION DETAILS:")
        print(f"   Title: {self.test_auction.title}")
        print(f"   Current Bid: ${self.test_auction.current_bid}")
        print(f"   Auction ID: {self.test_auction.id}")
        print(f"   Status: {'Active' if self.test_auction.end_time > timezone.now() else 'Ended'}")
        
        print(f"\n🔍 TEST RESULTS SUMMARY:")
        success_count = len([r for r in self.test_results if r.get('status') == 'SUCCESS'])
        total_tests = len(self.test_results)
        
        print(f"   Total Tests: {total_tests}")
        print(f"   Successful: {success_count}")
        print(f"   Failed: {total_tests - success_count}")
        print(f"   Success Rate: {(success_count/total_tests*100):.1f}%" if total_tests > 0 else "   Success Rate: 0%")
        
        print(f"\n📊 DETAILED RESULTS:")
        for result in self.test_results:
            status_icon = "✅" if result.get('status') == 'SUCCESS' else "❌"
            test_name = result.get('endpoint', result.get('test', 'Unknown'))
            print(f"   {status_icon} {test_name}: {result.get('status')}")
            if result.get('error'):
                print(f"      Error: {result['error']}")
                
        print(f"\n🎯 PAYMENT SYSTEM STATUS:")
        if success_count >= total_tests * 0.8:  # 80% success rate
            print("   ✅ Payment system is working well!")
            print("   🚀 Ready for production use")
        else:
            print("   ⚠️ Payment system has some issues")
            print("   🔧 Requires attention before production")
            
        print(f"\n📧 TEST USER EMAIL: <EMAIL>")
        print(f"💰 TEST AUCTION AMOUNT: ${self.test_auction.current_bid}")
        print(f"🔗 Frontend URL: http://localhost:3001/auction/{self.test_auction.id}")
        
def main():
    """Main execution function"""
    print("🚀 STARTING COMPREHENSIVE PAYMENT SYSTEM TEST")
    print("="*60)
    
    tester = PaymentSystemTester()
    
    try:
        tester.create_test_user()
        tester.create_test_auction()
        tester.create_test_bid()
        tester.check_stripe_configuration()
        tester.test_payment_models()
        tester.test_payment_endpoints()
        tester.generate_test_report()
        
    except Exception as e:
        print(f"❌ Error during payment system test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
