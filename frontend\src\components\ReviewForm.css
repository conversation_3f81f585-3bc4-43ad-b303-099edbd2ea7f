/* Review Form Styles */

.rating-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stars-wrapper {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #ddd;
}

.star.filled {
  color: #ffc107;
}

.star:hover {
  transform: scale(1.1);
}

.rating-text {
  font-weight: 600;
  color: #495057;
}

.review-guidelines {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #007bff;
  margin-top: 15px;
}

.review-guidelines h6 {
  color: #007bff;
  margin-bottom: 10px;
  font-weight: 600;
}

.review-guidelines ul {
  margin-bottom: 0;
  padding-left: 20px;
}

.review-guidelines li {
  margin-bottom: 5px;
}

/* Modal customizations */
.modal-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border-bottom: none;
}

.modal-header .btn-close {
  filter: invert(1);
}

.modal-title {
  font-weight: 600;
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  padding: 1rem 2rem;
}

/* Card styling */
.card {
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-body {
  padding: 1.5rem;
}

/* Form styling */
.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control:focus,
.form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-text {
  font-size: 0.875rem;
}

/* Button styling */
.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  font-weight: 600;
  padding: 0.5rem 1.5rem;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-primary:disabled {
  background: #6c757d;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  font-weight: 600;
  padding: 0.5rem 1.5rem;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  transform: translateY(-1px);
}

/* Alert styling */
.alert {
  border: none;
  border-radius: 8px;
  font-weight: 500;
}

.alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-body {
    padding: 1rem;
  }
  
  .modal-footer {
    padding: 1rem;
  }
  
  .stars-wrapper {
    justify-content: center;
  }
  
  .rating-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .rating-text {
    margin-top: 0.5rem;
  }
}

/* Animation for success state */
.alert-success {
  animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state */
.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Focus states for accessibility */
.star:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
  border-radius: 2px;
}

.form-control:focus,
.form-select:focus {
  outline: none;
}

/* Hover effects */
.card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  transition: box-shadow 0.3s ease;
}

/* Text styling */
.text-muted {
  color: #6c757d !important;
}

h5, h6 {
  color: #495057;
}

/* Character counter styling */
.form-text {
  text-align: right;
  margin-top: 0.25rem;
}

/* Review type select styling */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
}

/* Textarea styling */
textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

/* Guidelines box styling */
.review-guidelines {
  position: relative;
  overflow: hidden;
}

.review-guidelines::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #007bff, #0056b3);
}

/* Icon styling */
.fa-user,
.fa-check-circle {
  color: inherit;
}

/* Modal backdrop */
.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.5);
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}
