# Generated by Django 5.0.6 on 2025-05-27 18:27

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("auction", "0006_auction_additional_images_auction_auction_type_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="payment",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="payment",
            name="currency",
            field=models.CharField(
                choices=[
                    ("INR", "Indian Rupee"),
                    ("USD", "US Dollar"),
                    ("EUR", "Euro"),
                ],
                default="INR",
                max_length=3,
            ),
        ),
        migrations.AddField(
            model_name="payment",
            name="failure_reason",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="gateway_order_id",
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name="payment",
            name="gateway_payment_id",
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name="payment",
            name="gateway_signature",
            field=models.CharField(blank=True, max_length=500),
        ),
        migrations.AddField(
            model_name="payment",
            name="payment_date",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="payment_method",
            field=models.CharField(
                choices=[
                    ("razorpay", "Razorpay"),
                    ("paytm", "Paytm"),
                    ("cashfree", "Cashfree"),
                    ("instamojo", "Instamojo"),
                    ("upi", "UPI"),
                    ("card", "Credit/Debit Card"),
                    ("netbanking", "Net Banking"),
                    ("wallet", "Digital Wallet"),
                    ("manual", "Manual"),
                ],
                default="razorpay",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="payment",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name="payment",
            name="amount",
            field=models.DecimalField(decimal_places=2, max_digits=12),
        ),
        migrations.AlterField(
            model_name="payment",
            name="payment_status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("processing", "Processing"),
                    ("completed", "Completed"),
                    ("failed", "Failed"),
                    ("refunded", "Refunded"),
                    ("cancelled", "Cancelled"),
                ],
                default="pending",
                max_length=20,
            ),
        ),
    ]
