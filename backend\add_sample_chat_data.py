#!/usr/bin/env python
"""
Add sample chat data to demonstrate AI chat functionality
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from django.contrib.auth.models import User
from auction.models import Auction, ChatRoom, ChatMessage

def add_sample_chat_data():
    """Add sample chat messages to demonstrate AI functionality"""
    print("🤖 Adding sample chat data...")
    
    try:
        # Get the first approved auction that hasn't ended
        from django.utils import timezone
        now = timezone.now()
        auction = Auction.objects.filter(
            approved=True,
            end_time__gt=now
        ).first()
        if not auction:
            print("❌ No active auctions found. Please create an auction first.")
            return
        
        print(f"📦 Using auction: {auction.title}")

        # Calculate bid count
        bid_count = auction.bids.count()

        # Get or create chat room for this auction
        chat_room, created = ChatRoom.objects.get_or_create(
            auction=auction,
            defaults={
                'is_active': True
            }
        )

        if created:
            print(f"✅ Created new chat room for: {auction.title}")
        else:
            print(f"📝 Using existing chat room for: {auction.title}")
        
        # Sample user messages and AI responses
        sample_conversations = [
            {
                'user_message': 'Hello! What can you tell me about this auction?',
                'ai_response': f"👋 **Hello! Welcome to the auction chat!**\n\nI'm your AI assistant for **{auction.title}**\n\nI can help you with:\n\n💰 **Price Questions:** 'What's the predicted price?' or 'Price analysis'\n🎯 **Bidding Help:** 'How to bid?' or 'Bidding tips'\nℹ️ **Auction Info:** 'When does it end?' or 'Seller info'\n💳 **Payment & Shipping:** 'Payment methods?' or 'Shipping info'\n\n**Quick Commands:**\n• Type 'price' for price analysis\n• Type 'help bid' for bidding assistance\n• Type 'info' for auction details\n• Type 'payment' for payment information\n\n**Current Auction:** {auction.title}\n**Current Bid:** ₹{auction.current_bid or auction.starting_bid:,.2f}\n**Time Left:** Active\n\n🔥 **This auction is live!** Place your bid now!\n\n💡 *Just ask me anything about this auction!*"
            },
            {
                'user_message': 'What is the current price?',
                'ai_response': f"💰 **Price Information for {auction.title}**\n\n• **Current Bid:** ₹{auction.current_bid or auction.starting_bid:,.2f}\n• **Starting Bid:** ₹{auction.starting_bid:,.2f}\n• **Total Bids:** {bid_count}\n⏰ **Time Remaining:** Active\n\n📊 Moderate bidding activity so far.\n\n💡 *For detailed AI price predictions, please check the auction details page.*"
            },
            {
                'user_message': 'How do I place a bid?',
                'ai_response': f"🎯 **Bidding Information for {auction.title}**\n\n• **Current Highest Bid:** ₹{auction.current_bid or auction.starting_bid:,.2f}\n• **Minimum Next Bid:** ₹{(auction.current_bid or auction.starting_bid) + 10:,.2f}\n• **Time Remaining:** Active\n\n📝 **How to Bid:**\n1. Enter your bid amount (must be higher than current bid)\n2. Click 'Place Bid' button\n3. You'll receive instant confirmation\n4. Watch for real-time updates from other bidders\n\n💡 **Bidding Tips:**\n• Bid strategically - don't reveal your maximum early\n• Watch the auction closely in final minutes\n• Consider the AI price prediction for guidance\n• Remember: highest bid when auction ends wins!"
            },
            {
                'user_message': 'What payment methods do you accept?',
                'ai_response': "💳 **Payment & Shipping Information**\n\n💰 **Payment Process:**\n• Winner has 24 hours to complete payment\n• Secure payment via Stripe (Credit/Debit cards)\n• Payment confirmation sent via email\n• No payment = auction re-listed automatically\n\n📦 **Shipping & Delivery:**\n• Shipping details provided by seller\n• Pickup options may be available\n• Contact seller after winning for arrangements\n• Shipping costs typically paid by buyer\n\n🔒 **Security:**\n• All payments processed securely via Stripe\n• Buyer protection policies apply\n• Report any issues to admin immediately"
            }
        ]
        
        # Get or create a sample user
        sample_user, created = User.objects.get_or_create(
            username='sample_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Sample',
                'last_name': 'User'
            }
        )
        
        if created:
            print(f"✅ Created sample user: {sample_user.username}")
        
        # Add sample messages
        for i, conversation in enumerate(sample_conversations):
            # Add user message
            user_msg = ChatMessage.objects.create(
                room=chat_room,
                sender=sample_user,
                message=conversation['user_message'],
                message_type='text'
            )
            print(f"📝 Added user message {i+1}: {conversation['user_message'][:50]}...")
            
            # Add AI response
            ai_msg = ChatMessage.objects.create(
                room=chat_room,
                sender=None,  # AI messages don't have a human sender
                message=conversation['ai_response'],
                message_type='ai_response'
            )
            print(f"🤖 Added AI response {i+1}")
        
        print(f"\n✅ Successfully added {len(sample_conversations)} sample conversations!")
        print(f"🎯 Chat room ID: {chat_room.id}")
        print(f"📦 Auction ID: {auction.id}")
        print(f"\n💡 You can now test the chat functionality on auction: {auction.title}")
        
    except Exception as e:
        print(f"❌ Error adding sample chat data: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    add_sample_chat_data()
