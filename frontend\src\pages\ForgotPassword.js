﻿import React, { useState } from "react";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom"; 

function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
  e.preventDefault();

  if (!email) {
    Swal.fire({
      icon: "error",
      title: "Email Required",
      text: "Please enter a valid email address.",
    });
    return;
  }

  try {
    const res = await fetch("/api/auth/password-reset/", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email }),
    });

    if (!res.ok) {
      const err = await res.json();
      throw new Error(err.error || "Failed to send reset email");
    }

    Swal.fire({
      icon: "success",
      title: "Password reset email sent!",
      text: "Please check your inbox or spam folder.",
    });

    setEmail("");
    setMessage("Password reset link has been sent to your email.");
    navigate("/reset-email-sent");
  } catch (error) {
    console.error(error);
    Swal.fire({
      icon: "error",
      title: "Failed to process the request!",
      text: error.message || "There was an error sending the reset email. Please try again.",
    });
    setMessage("Failed to process the request. Try again.");
  }
};

  return (
    <div className="container mt-5" style={{ maxWidth: "400px" }}>
      <h2 className="mb-4 text-center">Forgot Password</h2>

      <form onSubmit={handleSubmit}>
        <div className="mb-3">
          <label>Enter your registered email address</label>
          <input
            type="email"
            className="form-control"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
        </div>

        <button type="submit" className="btn btn-primary w-100">
          Send Reset Link
        </button>
      </form>

      {message && <p className="mt-3 text-center text-success">{message}</p>}
    </div>
  );
}

export default ForgotPassword;
