{"summary": {"total_tests": 8, "passed_tests": 8, "failed_tests": 0, "success_rate": 100.0, "duration": 6.285144805908203, "timestamp": "2025-05-28T11:41:17.333214"}, "test_results": [{"test": "Backend Health", "success": true, "message": "Django API is running", "timestamp": "2025-05-28T11:41:11.101453"}, {"test": "Frontend Health", "success": true, "message": "React app is running", "timestamp": "2025-05-28T11:41:13.656083"}, {"test": "Database Connection", "success": true, "message": "Database is accessible", "timestamp": "2025-05-28T11:41:14.315775"}, {"test": "Auctions API", "success": true, "message": "Found 10 auctions", "timestamp": "2025-05-28T11:41:14.975327"}, {"test": "Auction Data Structure", "success": true, "message": "All required fields present", "timestamp": "2025-05-28T11:41:14.975623"}, {"test": "Categories API", "success": true, "message": "Found 1 categories", "timestamp": "2025-05-28T11:41:15.578344"}, {"test": "Search API", "success": true, "message": "Search endpoint is working", "timestamp": "2025-05-28T11:41:16.170519"}, {"test": "Dummy Data Quality", "success": true, "message": "5/5 auctions have realistic data", "timestamp": "2025-05-28T11:41:16.828195"}]}