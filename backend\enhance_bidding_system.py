#!/usr/bin/env python3
"""
Enhanced Bidding System - Ensure All Users Can Bid on Others' Auctions
Creates comprehensive bid history with realistic bidding patterns
"""

import os
import sys
import django
import random
from datetime import datetime, timedelta
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from django.contrib.auth.models import User
from auction.models import Auction, Bid, BidHistory
from django.utils import timezone

class BiddingSystemEnhancer:
    def __init__(self):
        self.bid_patterns = {
            'aggressive': {'min_increase': 50, 'max_increase': 200, 'frequency': 0.8},
            'conservative': {'min_increase': 10, 'max_increase': 50, 'frequency': 0.4},
            'strategic': {'min_increase': 25, 'max_increase': 100, 'frequency': 0.6},
            'last_minute': {'min_increase': 100, 'max_increase': 300, 'frequency': 0.9}
        }
        
    def analyze_current_bidding(self):
        """Analyze current bidding patterns"""
        print("🔍 ANALYZING CURRENT BIDDING SYSTEM")
        print("="*50)
        
        users = User.objects.all()
        auctions = Auction.objects.filter(approved=True)
        bids = Bid.objects.all()
        
        print(f"📊 System Overview:")
        print(f"   Total Users: {users.count()}")
        print(f"   Total Auctions: {auctions.count()}")
        print(f"   Total Bids: {bids.count()}")
        
        # Analyze bidding participation
        users_with_bids = User.objects.filter(bid__isnull=False).distinct().count()
        users_with_auctions = User.objects.filter(auctions__isnull=False).distinct().count()
        
        print(f"\n👥 User Participation:")
        print(f"   Users who have bid: {users_with_bids}")
        print(f"   Users who created auctions: {users_with_auctions}")
        print(f"   Users who only bid: {users_with_bids - users_with_auctions}")
        
        # Analyze auction bid distribution
        auctions_with_bids = auctions.filter(bids__isnull=False).distinct().count()
        auctions_without_bids = auctions.count() - auctions_with_bids
        
        print(f"\n🏷️ Auction Bid Distribution:")
        print(f"   Auctions with bids: {auctions_with_bids}")
        print(f"   Auctions without bids: {auctions_without_bids}")
        
        # Show users and their bidding activity
        print(f"\n👤 User Bidding Analysis:")
        for user in users:
            user_auctions = Auction.objects.filter(owner=user).count()
            user_bids = Bid.objects.filter(user=user).count()
            eligible_auctions = auctions.exclude(owner=user).count()
            
            print(f"   • {user.username}:")
            print(f"     - Own auctions: {user_auctions}")
            print(f"     - Bids placed: {user_bids}")
            print(f"     - Can bid on: {eligible_auctions} auctions")
        
        return {
            'total_users': users.count(),
            'total_auctions': auctions.count(),
            'total_bids': bids.count(),
            'users_with_bids': users_with_bids,
            'auctions_without_bids': auctions_without_bids
        }
    
    def create_comprehensive_bidding(self):
        """Create comprehensive bidding for all users on eligible auctions"""
        print(f"\n🎯 CREATING COMPREHENSIVE BIDDING SYSTEM")
        print("-" * 45)
        
        users = User.objects.all()
        auctions = Auction.objects.filter(approved=True)
        
        bids_created = 0
        
        for user in users:
            # Get auctions this user can bid on (not their own)
            eligible_auctions = auctions.exclude(owner=user)
            
            if eligible_auctions.count() == 0:
                print(f"   ⏭️  {user.username}: No eligible auctions to bid on")
                continue
            
            # Determine user's bidding pattern
            patterns = list(self.bid_patterns.keys())
            user_pattern = random.choice(patterns)
            pattern_config = self.bid_patterns[user_pattern]
            
            print(f"   👤 {user.username} ({user_pattern} bidder):")
            
            # Select random auctions to bid on (30-70% of eligible auctions)
            num_auctions_to_bid = random.randint(
                max(1, int(eligible_auctions.count() * 0.3)),
                min(eligible_auctions.count(), int(eligible_auctions.count() * 0.7))
            )
            
            selected_auctions = random.sample(list(eligible_auctions), num_auctions_to_bid)
            
            for auction in selected_auctions:
                # Check if user already has a bid on this auction
                existing_bid = Bid.objects.filter(user=user, auction=auction).first()
                
                if existing_bid:
                    print(f"      ⏭️  Already bid on: {auction.title[:30]}...")
                    continue
                
                # Calculate bid amount based on pattern
                current_bid = float(auction.current_bid)
                min_increase = pattern_config['min_increase']
                max_increase = pattern_config['max_increase']
                
                # Add some randomness based on auction value
                if current_bid > 500:
                    min_increase *= 1.5
                    max_increase *= 1.5
                elif current_bid < 100:
                    min_increase *= 0.7
                    max_increase *= 0.7
                
                bid_increase = random.randint(int(min_increase), int(max_increase))
                new_bid_amount = Decimal(str(current_bid + bid_increase))
                
                # Only bid if we should based on frequency
                if random.random() > pattern_config['frequency']:
                    continue
                
                try:
                    # Create the bid
                    bid = Bid.objects.create(
                        user=user,
                        auction=auction,
                        amount=new_bid_amount
                    )
                    
                    # Update auction current bid
                    auction.current_bid = new_bid_amount
                    auction.save()
                    
                    # Create bid history entry
                    BidHistory.objects.create(
                        auction=auction,
                        user=user,
                        bid_amount=new_bid_amount,
                        previous_bid=Decimal(str(current_bid)),
                        bid_type='manual'
                    )
                    
                    bids_created += 1
                    print(f"      ✅ Bid ${new_bid_amount} on: {auction.title[:30]}...")
                    
                except Exception as e:
                    print(f"      ❌ Failed to bid on {auction.title[:30]}...: {e}")
        
        print(f"\n📊 Bidding Enhancement Complete:")
        print(f"   Total new bids created: {bids_created}")
        
        return bids_created
    
    def create_realistic_bid_competition(self):
        """Create realistic bid competition on popular auctions"""
        print(f"\n🏆 CREATING REALISTIC BID COMPETITION")
        print("-" * 40)
        
        # Get high-value auctions for competition
        high_value_auctions = Auction.objects.filter(
            approved=True,
            current_bid__gte=200
        ).order_by('-current_bid')[:10]
        
        users = list(User.objects.all())
        competition_bids = 0
        
        for auction in high_value_auctions:
            print(f"   🏷️ Creating competition for: {auction.title[:35]}...")
            
            # Get users who can bid (not the owner)
            eligible_users = [u for u in users if u != auction.owner]
            
            # Create 2-5 competitive bids
            num_competitive_bids = random.randint(2, 5)
            selected_users = random.sample(eligible_users, min(num_competitive_bids, len(eligible_users)))
            
            current_bid = float(auction.current_bid)
            
            for i, user in enumerate(selected_users):
                # Check if user already bid
                if Bid.objects.filter(user=user, auction=auction).exists():
                    continue
                
                # Create escalating bids
                bid_increase = random.randint(20, 80) * (i + 1)
                new_bid = Decimal(str(current_bid + bid_increase))
                
                try:
                    bid = Bid.objects.create(
                        user=user,
                        auction=auction,
                        amount=new_bid
                    )
                    
                    # Update current bid
                    current_bid = float(new_bid)
                    auction.current_bid = new_bid
                    auction.save()
                    
                    competition_bids += 1
                    print(f"      💰 {user.username}: ${new_bid}")
                    
                except Exception as e:
                    print(f"      ❌ {user.username}: Failed - {e}")
        
        print(f"\n🏆 Competition bids created: {competition_bids}")
        return competition_bids
    
    def generate_final_statistics(self):
        """Generate final bidding statistics"""
        print(f"\n📈 FINAL BIDDING SYSTEM STATISTICS")
        print("="*50)
        
        users = User.objects.all()
        auctions = Auction.objects.filter(approved=True)
        bids = Bid.objects.all()
        
        print(f"📊 Enhanced System Overview:")
        print(f"   Total Users: {users.count()}")
        print(f"   Total Auctions: {auctions.count()}")
        print(f"   Total Bids: {bids.count()}")
        
        # User participation analysis
        print(f"\n👥 User Participation Analysis:")
        for user in users:
            user_bids = Bid.objects.filter(user=user).count()
            user_auctions = Auction.objects.filter(owner=user).count()
            
            if user_bids > 0 or user_auctions > 0:
                print(f"   • {user.username}: {user_bids} bids, {user_auctions} auctions")
        
        # Auction bid distribution
        print(f"\n🏷️ Top Auctions by Bid Count:")
        auctions_with_bid_counts = []
        for auction in auctions:
            bid_count = Bid.objects.filter(auction=auction).count()
            if bid_count > 0:
                auctions_with_bid_counts.append((auction, bid_count))
        
        # Sort by bid count
        auctions_with_bid_counts.sort(key=lambda x: x[1], reverse=True)
        
        for auction, bid_count in auctions_with_bid_counts[:10]:
            print(f"   • {auction.title[:40]}... - {bid_count} bids (${auction.current_bid})")
        
        # Calculate statistics
        total_bid_value = sum(float(bid.amount) for bid in bids)
        avg_bid_value = total_bid_value / bids.count() if bids.count() > 0 else 0
        
        auctions_with_bids = auctions.filter(bids__isnull=False).distinct().count()
        bid_coverage = (auctions_with_bids / auctions.count() * 100) if auctions.count() > 0 else 0
        
        print(f"\n💰 Financial Statistics:")
        print(f"   Total bid value: ${total_bid_value:,.2f}")
        print(f"   Average bid value: ${avg_bid_value:.2f}")
        print(f"   Auction bid coverage: {bid_coverage:.1f}%")
        
        print(f"\n✅ BIDDING SYSTEM ENHANCEMENT COMPLETE!")
        print(f"🎯 All users can now bid on eligible auctions")
        print(f"📊 Comprehensive bid history created")
        print(f"🏆 Realistic competition established")

def main():
    """Main execution function"""
    print("🚀 STARTING BIDDING SYSTEM ENHANCEMENT")
    print("="*60)
    
    enhancer = BiddingSystemEnhancer()
    
    try:
        # Analyze current system
        current_stats = enhancer.analyze_current_bidding()
        
        # Create comprehensive bidding
        new_bids = enhancer.create_comprehensive_bidding()
        
        # Create realistic competition
        competition_bids = enhancer.create_realistic_bid_competition()
        
        # Generate final statistics
        enhancer.generate_final_statistics()
        
        print(f"\n🎉 ENHANCEMENT SUMMARY:")
        print(f"   New bids created: {new_bids}")
        print(f"   Competition bids: {competition_bids}")
        print(f"   Total enhancement: {new_bids + competition_bids} bids")
        
    except Exception as e:
        print(f"❌ Error during bidding enhancement: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
