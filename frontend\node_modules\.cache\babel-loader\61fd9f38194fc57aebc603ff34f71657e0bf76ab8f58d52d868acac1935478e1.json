{"ast": null, "code": "import { lab as colorLab } from \"d3-color\";\nimport color from \"./color.js\";\nexport default function lab(start, end) {\n  var l = color((start = colorLab(start)).l, (end = colorLab(end)).l),\n    a = color(start.a, end.a),\n    b = color(start.b, end.b),\n    opacity = color(start.opacity, end.opacity);\n  return function (t) {\n    start.l = l(t);\n    start.a = a(t);\n    start.b = b(t);\n    start.opacity = opacity(t);\n    return start + \"\";\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}