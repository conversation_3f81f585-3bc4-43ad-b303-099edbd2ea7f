# Generated by Django 5.0.6 on 2025-05-27 10:31

from decimal import Decimal

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("auction", "0005_contactmessage_auction_category_auction_image_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="auction",
            name="additional_images",
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name="auction",
            name="auction_type",
            field=models.CharField(
                choices=[
                    ("standard", "Standard Auction"),
                    ("reserve", "Reserve Auction"),
                    ("buy_now", "Buy It Now"),
                    ("sealed_bid", "Sealed Bid"),
                    ("reverse", "Reverse Auction"),
                ],
                default="standard",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="auction",
            name="buy_now_price",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="auction",
            name="condition",
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name="auction",
            name="featured",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="auction",
            name="location",
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name="auction",
            name="reserve_price",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="auction",
            name="shipping_cost",
            field=models.DecimalField(decimal_places=2, default=0, max_digits=8),
        ),
        migrations.AddField(
            model_name="auction",
            name="start_time",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name="auction",
            name="views_count",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="auction",
            name="watchers",
            field=models.ManyToManyField(
                blank=True, related_name="watched_auctions", to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AlterField(
            model_name="auction",
            name="category",
            field=models.CharField(
                choices=[
                    ("electronics", "Electronics"),
                    ("fashion", "Fashion & Clothing"),
                    ("home_garden", "Home & Garden"),
                    ("sports", "Sports & Recreation"),
                    ("collectibles", "Collectibles"),
                    ("automotive", "Automotive"),
                    ("books", "Books & Media"),
                    ("art", "Art & Crafts"),
                    ("jewelry", "Jewelry & Watches"),
                    ("other", "Other"),
                ],
                default="other",
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="auction",
            name="starting_bid",
            field=models.DecimalField(
                decimal_places=2,
                max_digits=10,
                validators=[django.core.validators.MinValueValidator(Decimal("0.01"))],
            ),
        ),
        migrations.CreateModel(
            name="Analytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("metric_name", models.CharField(max_length=100)),
                ("metric_value", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "metric_type",
                    models.CharField(
                        choices=[
                            ("user_engagement", "User Engagement"),
                            ("auction_performance", "Auction Performance"),
                            ("revenue", "Revenue"),
                            ("system_health", "System Health"),
                        ],
                        max_length=50,
                    ),
                ),
                ("date", models.DateField()),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "unique_together": {("metric_name", "date")},
            },
        ),
        migrations.CreateModel(
            name="BidHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("bid_amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "previous_bid",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "bid_type",
                    models.CharField(
                        choices=[
                            ("manual", "Manual Bid"),
                            ("auto", "Auto Bid"),
                            ("proxy", "Proxy Bid"),
                        ],
                        default="manual",
                        max_length=20,
                    ),
                ),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "auction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bid_history",
                        to="auction.auction",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("slug", models.SlugField(unique=True)),
                ("description", models.TextField(blank=True)),
                ("image", models.URLField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("sort_order", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subcategories",
                        to="auction.category",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Categories",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="FraudDetection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "fraud_type",
                    models.CharField(
                        choices=[
                            ("suspicious_bidding", "Suspicious Bidding Pattern"),
                            ("fake_listing", "Fake Listing"),
                            ("payment_fraud", "Payment Fraud"),
                            ("account_takeover", "Account Takeover"),
                            ("bot_activity", "Bot Activity"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "risk_score",
                    models.IntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ]
                    ),
                ),
                ("details", models.JSONField(default=dict)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("resolved", "Resolved"),
                            ("confirmed", "Confirmed Fraud"),
                            ("false_positive", "False Positive"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "auction",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="auction.auction",
                    ),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_fraud_cases",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("phone_number", models.CharField(blank=True, max_length=20)),
                ("address", models.TextField(blank=True)),
                ("city", models.CharField(blank=True, max_length=100)),
                ("country", models.CharField(blank=True, max_length=100)),
                ("postal_code", models.CharField(blank=True, max_length=20)),
                ("date_of_birth", models.DateField(blank=True, null=True)),
                ("avatar", models.URLField(blank=True)),
                ("bio", models.TextField(blank=True, max_length=500)),
                ("verified", models.BooleanField(default=False)),
                (
                    "rating",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=3),
                ),
                ("total_sales", models.PositiveIntegerField(default=0)),
                ("total_purchases", models.PositiveIntegerField(default=0)),
                ("preferred_language", models.CharField(default="en", max_length=10)),
                ("preferred_currency", models.CharField(default="USD", max_length=3)),
                ("email_notifications", models.BooleanField(default=True)),
                ("sms_notifications", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Watchlist",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "auction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="auction.auction",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("user", "auction")},
            },
        ),
    ]
