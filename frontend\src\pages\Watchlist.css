/* Watchlist Page Styles */

.watchlist-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border: 1px solid #e0e0e0;
}

.watchlist-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.watchlist-card .card-img-top {
  transition: transform 0.3s ease-in-out;
}

.watchlist-card:hover .card-img-top {
  transform: scale(1.02);
}

.watchlist-card .position-absolute.top-0.end-0 {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.watchlist-card:hover .position-absolute.top-0.end-0 {
  opacity: 1;
}

.watchlist-card .badge {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
}

/* Empty state styling */
.watchlist-card .card-body .fa-reg-heart {
  opacity: 0.3;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .watchlist-card .position-absolute.top-0.end-0 {
    opacity: 1; /* Always show on mobile */
  }
}

/* Animation for remove button */
.watchlist-card .btn-danger {
  transition: all 0.2s ease-in-out;
}

.watchlist-card .btn-danger:hover {
  transform: scale(1.1);
}

/* Status badge positioning */
.watchlist-card .badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
}

/* Watchlist header styling */
.watchlist-header {
  border-bottom: 2px solid #f8f9fa;
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

/* Clear all button styling */
.btn-outline-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

/* Card title truncation */
.watchlist-card .card-title {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.3;
  max-height: 2.6rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Price display styling */
.text-success {
  color: #28a745 !important;
  font-weight: 600;
}

/* Time remaining styling */
.text-danger {
  color: #dc3545 !important;
  font-weight: 600;
}

/* Loading spinner container */
.spinner-container {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

/* Empty state card */
.empty-watchlist-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px dashed #dee2e6;
}

.empty-watchlist-card .card-body {
  padding: 3rem 2rem;
}

/* Watchlist stats */
.watchlist-stats {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.watchlist-stats h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
}

.watchlist-stats p {
  margin: 0;
  opacity: 0.9;
}

/* Action buttons */
.action-buttons {
  gap: 0.5rem;
}

.action-buttons .btn {
  flex: 1;
  font-size: 0.875rem;
}

/* Responsive grid adjustments */
@media (min-width: 1200px) {
  .watchlist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .watchlist-card {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .watchlist-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  .empty-watchlist-card {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    border-color: #4a5568;
    color: #e2e8f0;
  }
}

/* Smooth transitions for all interactive elements */
* {
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}
