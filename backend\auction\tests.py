from datetime import timedelta
from decimal import Decimal

from django.contrib.auth.models import User
from django.test import TestCase
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APITestCase

from .models import Auction, AutoBid, Bid, Notification, Payment, FraudDetection
from .utils.auction_closer import close_ended_auctions
from .utils.autobid import evaluate_auto_bids


class AuctionBidTest(TestCase):

    def setUp(self):
        self.user1 = User.objects.create_user("user1", password="pass")
        self.user2 = User.objects.create_user("user2", password="pass")
        self.auction = Auction.objects.create(
            title="Test Auction",
            description="Test Desc",
            starting_bid=10.00,
            current_bid=10.00,
            end_time=timezone.now() + timedelta(days=1),
            owner=self.user1,
        )
        AutoBid.objects.create(user=self.user1, auction=self.auction, max_bid=50.00)
        AutoBid.objects.create(user=self.user2, auction=self.auction, max_bid=60.00)

    def test_autobid(self):
        # Initial manual bid
        Bid.objects.create(auction=self.auction, user=self.user1, amount=15.00)
        self.auction.current_bid = 15.00
        self.auction.save()

        # Evaluate auto bids
        evaluate_auto_bids(self.auction)

        # The current bid should increase (from AutoBid logic)
        self.auction.refresh_from_db()
        self.assertTrue(self.auction.current_bid > 15.00)


class AuctionModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

    def test_auction_creation(self):
        auction = Auction.objects.create(
            title="Test Auction",
            description="Test Description",
            starting_bid=Decimal("10.00"),
            end_time=timezone.now() + timedelta(days=1),
            owner=self.user,
        )
        self.assertEqual(auction.title, "Test Auction")
        self.assertEqual(auction.current_bid, auction.starting_bid)
        self.assertFalse(auction.is_closed)


class BidModelTest(TestCase):
    def setUp(self):
        self.user1 = User.objects.create_user(
            username="user1", email="<EMAIL>", password="testpass123"
        )
        self.user2 = User.objects.create_user(
            username="user2", email="<EMAIL>", password="testpass123"
        )
        self.auction = Auction.objects.create(
            title="Test Auction",
            description="Test Description",
            starting_bid=Decimal("10.00"),
            end_time=timezone.now() + timedelta(days=1),
            owner=self.user1,
        )

    def test_bid_creation(self):
        bid = Bid.objects.create(
            auction=self.auction, user=self.user2, amount=Decimal("15.00")
        )
        self.assertEqual(bid.amount, Decimal("15.00"))
        self.assertEqual(bid.user, self.user2)
        self.assertEqual(bid.auction, self.auction)


class AuctionClosureTest(TestCase):
    def setUp(self):
        self.user1 = User.objects.create_user(
            username="owner", email="<EMAIL>", password="testpass123"
        )
        self.user2 = User.objects.create_user(
            username="bidder", email="<EMAIL>", password="testpass123"
        )

    def test_auction_closure_with_bids(self):
        # Create an auction that has already ended
        auction = Auction.objects.create(
            title="Ended Auction",
            description="Test Description",
            starting_bid=Decimal("10.00"),
            end_time=timezone.now() - timedelta(hours=1),  # Ended 1 hour ago
            owner=self.user1,
        )

        # Place a bid
        bid = Bid.objects.create(
            auction=auction, user=self.user2, amount=Decimal("20.00")
        )

        # Update auction current bid
        auction.current_bid = bid.amount
        auction.save()

        # Run the closure function
        close_ended_auctions()

        # Check if auction is closed
        auction.refresh_from_db()
        self.assertTrue(auction.is_closed)

        # Check if payment was created
        payment = Payment.objects.filter(auction=auction, user=self.user2).first()
        self.assertIsNotNone(payment)
        self.assertEqual(payment.amount, Decimal("20.00"))
        self.assertEqual(payment.payment_status, "pending")


class FraudDetectionModelTest(TestCase):
    """Test FraudDetection model functionality"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='fraud_testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.admin_user = User.objects.create_user(
            username='fraud_admin',
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True
        )
        self.auction = Auction.objects.create(
            title='Test Fraud Auction',
            description='Test auction for fraud detection',
            starting_bid=Decimal('100.00'),
            current_bid=Decimal('100.00'),
            category='Electronics',
            condition='Excellent',
            owner=self.user,
            end_time=timezone.now() + timedelta(days=7)
        )

    def test_fraud_detection_creation(self):
        """Test creating a fraud detection record"""
        fraud_detection = FraudDetection.objects.create(
            user=self.user,
            auction=self.auction,
            fraud_type='suspicious_bidding',
            risk_score=75,
            details={'pattern': 'rapid_bidding', 'count': 10},
            status='pending'
        )

        self.assertEqual(fraud_detection.user, self.user)
        self.assertEqual(fraud_detection.auction, self.auction)
        self.assertEqual(fraud_detection.fraud_type, 'suspicious_bidding')
        self.assertEqual(fraud_detection.risk_score, 75)
        self.assertEqual(fraud_detection.status, 'pending')
        self.assertIsNotNone(fraud_detection.created_at)

    def test_fraud_detection_str_representation(self):
        """Test string representation of fraud detection"""
        fraud_detection = FraudDetection.objects.create(
            user=self.user,
            fraud_type='bot_activity',
            risk_score=90
        )
        expected = f"Fraud Alert: bot_activity - {self.user.username}"
        self.assertEqual(str(fraud_detection), expected)

    def test_fraud_type_choices(self):
        """Test all fraud type choices are valid"""
        fraud_types = [
            'suspicious_bidding',
            'fake_listing',
            'payment_fraud',
            'account_takeover',
            'bot_activity'
        ]

        for fraud_type in fraud_types:
            fraud_detection = FraudDetection.objects.create(
                user=self.user,
                fraud_type=fraud_type,
                risk_score=50
            )
            self.assertEqual(fraud_detection.fraud_type, fraud_type)

    def test_status_choices(self):
        """Test all status choices are valid"""
        statuses = ['pending', 'resolved', 'confirmed', 'false_positive']

        for status_choice in statuses:
            fraud_detection = FraudDetection.objects.create(
                user=self.user,
                fraud_type='suspicious_bidding',
                risk_score=60,
                status=status_choice
            )
            self.assertEqual(fraud_detection.status, status_choice)

    def test_fraud_detection_without_auction(self):
        """Test fraud detection can be created without specific auction"""
        fraud_detection = FraudDetection.objects.create(
            user=self.user,
            fraud_type='account_takeover',
            risk_score=85,
            details={'login_attempts': 5, 'ip_changes': 3}
        )

        self.assertIsNone(fraud_detection.auction)
        self.assertEqual(fraud_detection.fraud_type, 'account_takeover')

    def test_fraud_detection_resolution(self):
        """Test fraud detection resolution process"""
        fraud_detection = FraudDetection.objects.create(
            user=self.user,
            fraud_type='payment_fraud',
            risk_score=95,
            status='pending'
        )

        # Resolve the fraud case
        fraud_detection.status = 'resolved'
        fraud_detection.resolved_at = timezone.now()
        fraud_detection.resolved_by = self.admin_user
        fraud_detection.save()

        self.assertEqual(fraud_detection.status, 'resolved')
        self.assertIsNotNone(fraud_detection.resolved_at)
        self.assertEqual(fraud_detection.resolved_by, self.admin_user)

    def test_fraud_detection_details_json_field(self):
        """Test JSON details field functionality"""
        complex_details = {
            'bidding_pattern': {
                'rapid_bids': True,
                'bid_count': 15,
                'time_span_minutes': 5
            },
            'user_behavior': {
                'new_account': True,
                'suspicious_email': '<EMAIL>'
            },
            'risk_factors': ['new_user', 'rapid_bidding', 'suspicious_email']
        }

        fraud_detection = FraudDetection.objects.create(
            user=self.user,
            fraud_type='suspicious_bidding',
            risk_score=88,
            details=complex_details
        )

        # Retrieve and verify JSON data
        retrieved = FraudDetection.objects.get(id=fraud_detection.id)
        self.assertEqual(retrieved.details, complex_details)
        self.assertTrue(retrieved.details['bidding_pattern']['rapid_bids'])
        self.assertEqual(retrieved.details['user_behavior']['suspicious_email'], '<EMAIL>')

    def test_fraud_scenarios(self):
        """Test different fraud detection scenarios"""
        # Scenario 1: High-risk bot activity
        bot_fraud = FraudDetection.objects.create(
            user=self.user,
            fraud_type='bot_activity',
            risk_score=92,
            details={
                'automated_bidding': True,
                'consistent_timing': True,
                'captcha_failures': 5
            }
        )
        self.assertGreaterEqual(bot_fraud.risk_score, 90)

        # Scenario 2: Fake listing
        fake_fraud = FraudDetection.objects.create(
            user=self.user,
            fraud_type='fake_listing',
            risk_score=78,
            details={
                'price_too_low': True,
                'suspicious_images': True
            }
        )
        self.assertEqual(fake_fraud.fraud_type, 'fake_listing')

        # Scenario 3: Account takeover
        takeover_fraud = FraudDetection.objects.create(
            user=self.user,
            fraud_type='account_takeover',
            risk_score=95,
            details={
                'login_from_new_location': True,
                'failed_login_attempts': 8
            }
        )
        self.assertGreaterEqual(takeover_fraud.risk_score, 95)
