#!/usr/bin/env python3
"""
Test script for Won Auctions functionality
Tests the auction winner assignment and won auctions API
"""

import os
import sys
import django
from datetime import datetime, timed<PERSON>ta

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from auction.models import Auction, User, Bid, Payment
from django.utils import timezone
from decimal import Decimal

def test_won_auctions():
    """Test the won auctions functionality"""
    
    print("🧪 Testing Won Auctions Functionality")
    print("=" * 50)
    
    try:
        # Create test users
        test_user1, created = User.objects.get_or_create(
            username='test_bidder1',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'Bidder1'
            }
        )
        
        test_user2, created = User.objects.get_or_create(
            username='test_bidder2',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'Bidder2'
            }
        )
        
        test_seller, created = User.objects.get_or_create(
            username='test_seller',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'Seller'
            }
        )
        
        print(f"✅ Test users created/found")
        
        # Create a test auction that has ended
        test_auction, created = Auction.objects.get_or_create(
            title='Test Auction for Winner Testing',
            defaults={
                'description': 'Test auction to verify winner assignment',
                'starting_bid': Decimal('100.00'),
                'current_bid': Decimal('100.00'),
                'start_time': timezone.now() - timedelta(hours=25),
                'end_time': timezone.now() - timedelta(hours=1),  # Ended 1 hour ago
                'owner': test_seller,
                'category': 'electronics',
                'approved': True,
                'is_closed': False  # Not closed yet to test the closing process
            }
        )

        # If auction already exists, update its end time to be in the past
        if not created:
            test_auction.end_time = timezone.now() - timedelta(hours=1)
            test_auction.is_closed = False
            test_auction.save()
        
        print(f"✅ Test auction created: {test_auction.title}")
        print(f"   Start time: {test_auction.start_time}")
        print(f"   End time: {test_auction.end_time}")
        print(f"   Is closed: {test_auction.is_closed}")
        
        # Create some bids
        bid1, created = Bid.objects.get_or_create(
            auction=test_auction,
            user=test_user1,
            defaults={'amount': Decimal('150.00')}
        )
        
        bid2, created = Bid.objects.get_or_create(
            auction=test_auction,
            user=test_user2,
            defaults={'amount': Decimal('200.00')}
        )
        
        bid3, created = Bid.objects.get_or_create(
            auction=test_auction,
            user=test_user1,
            defaults={'amount': Decimal('250.00')}
        )
        
        print(f"✅ Test bids created:")
        print(f"   Bid 1: {test_user1.username} - ${bid1.amount}")
        print(f"   Bid 2: {test_user2.username} - ${bid2.amount}")
        print(f"   Bid 3: {test_user1.username} - ${bid3.amount}")
        
        # Update auction current bid
        test_auction.current_bid = Decimal('250.00')
        test_auction.save()
        
        # Test the auction closer functionality
        print("\n🔧 Testing auction closing logic...")
        
        from auction.utils.auction_closer import close_ended_auctions
        close_ended_auctions()
        
        # Refresh auction from database
        test_auction.refresh_from_db()
        
        print(f"✅ Auction closing completed")
        print(f"   Is closed: {test_auction.is_closed}")
        print(f"   Winner: {test_auction.winner.username if test_auction.winner else 'None'}")
        print(f"   Current bid: ${test_auction.current_bid}")
        
        # Test the won auctions API endpoint
        print("\n🌐 Testing won auctions API...")
        
        from django.test import Client
        from django.contrib.auth import authenticate
        
        client = Client()
        
        # Get JWT token for test_user1 (should be the winner)
        from rest_framework_simplejwt.tokens import RefreshToken
        refresh = RefreshToken.for_user(test_user1)
        access_token = str(refresh.access_token)
        
        # Test the won auctions endpoint
        response = client.get(
            '/api/auctions/won_auctions/',
            HTTP_AUTHORIZATION=f'Bearer {access_token}'
        )
        
        print(f"   API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success', False)}")
            print(f"   Won auctions count: {data.get('count', 0)}")
            
            if data.get('results'):
                for auction in data['results']:
                    print(f"   - {auction['title']} (Winner: {auction.get('winner', 'None')})")
            else:
                print("   No won auctions found")
        else:
            print(f"   API Error: {response.content}")
        
        # Test payment creation
        print("\n💳 Testing payment creation...")
        
        payments = Payment.objects.filter(auction=test_auction)
        print(f"   Payment records found: {payments.count()}")
        
        for payment in payments:
            print(f"   - User: {payment.user.username}")
            print(f"   - Amount: ${payment.amount}")
            print(f"   - Status: {payment.payment_status}")
        
        # Test with different user (should have no won auctions)
        print("\n🔍 Testing with non-winner user...")
        
        refresh2 = RefreshToken.for_user(test_user2)
        access_token2 = str(refresh2.access_token)
        
        response2 = client.get(
            '/api/auctions/won_auctions/',
            HTTP_AUTHORIZATION=f'Bearer {access_token2}'
        )
        
        if response2.status_code == 200:
            data2 = response2.json()
            print(f"   Won auctions for {test_user2.username}: {data2.get('count', 0)}")
        
        print("\n🎉 Won auctions test completed!")
        
        # Summary
        print("\n📊 Test Summary:")
        print(f"   ✅ Auction created and ended")
        print(f"   ✅ Bids placed by multiple users")
        print(f"   ✅ Winner assigned: {test_auction.winner.username if test_auction.winner else 'None'}")
        print(f"   ✅ Payment record created")
        print(f"   ✅ Won auctions API working")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_won_auctions()
