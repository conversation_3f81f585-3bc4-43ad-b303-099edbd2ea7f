services:
  # PostgreSQL Database
  - type: pserv
    name: auction-database
    env: docker
    plan: free
    dockerfilePath: ./database/Dockerfile
    envVars:
      - key: POSTGRES_DB
        value: online_auction_db
      - key: POSTGRES_USER
        value: auction_user
      - key: POSTGRES_PASSWORD
        generateValue: true
    disk:
      name: auction-db-disk
      mountPath: /var/lib/postgresql/data
      sizeGB: 1

  # Redis Cache
  - type: redis
    name: auction-redis
    plan: free
    maxmemoryPolicy: allkeys-lru

  # Django Backend API
  - type: web
    name: auction-backend
    env: python
    plan: free
    buildCommand: |
      cd backend &&
      pip install -r requirements.txt &&
      python manage.py collectstatic --noinput &&
      python manage.py migrate
    startCommand: |
      cd backend &&
      gunicorn OnlineAuctionSystem.wsgi:application --bind 0.0.0.0:$PORT --workers 2
    envVars:
      - key: DJANGO_SETTINGS_MODULE
        value: OnlineAuctionSystem.settings_production
      - key: SECRET_KEY
        generateValue: true
      - key: DEBUG
        value: False
      - key: ALLOWED_HOSTS
        value: "*"
      - key: DATABASE_URL
        fromService:
          type: pserv
          name: auction-database
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: auction-redis
          property: connectionString
      - key: STRIPE_PUBLISHABLE_KEY
        sync: false
      - key: STRIPE_SECRET_KEY
        sync: false
      - key: STRIPE_WEBHOOK_SECRET
        sync: false
      - key: EMAIL_HOST_USER
        value: <EMAIL>
      - key: EMAIL_HOST_PASSWORD
        sync: false
      - key: FRONTEND_URL
        value: https://auction-frontend.onrender.com
    healthCheckPath: /api/test/

  # React Frontend
  - type: web
    name: auction-frontend
    env: static
    plan: free
    buildCommand: |
      cd frontend &&
      npm ci &&
      npm run build
    staticPublishPath: ./frontend/build
    envVars:
      - key: REACT_APP_API_URL
        value: https://auction-backend.onrender.com/api
      - key: REACT_APP_WS_URL
        value: wss://auction-backend.onrender.com/ws
      - key: NODE_ENV
        value: production
    headers:
      - path: /*
        name: X-Frame-Options
        value: DENY
      - path: /*
        name: X-Content-Type-Options
        value: nosniff
    routes:
      - type: rewrite
        source: /*
        destination: /index.html

  # Background Worker (Celery)
  - type: worker
    name: auction-worker
    env: python
    plan: free
    buildCommand: |
      cd backend &&
      pip install -r requirements.txt
    startCommand: |
      cd backend &&
      celery -A OnlineAuctionSystem worker --loglevel=info
    envVars:
      - key: DJANGO_SETTINGS_MODULE
        value: OnlineAuctionSystem.settings_production
      - key: SECRET_KEY
        fromService:
          type: web
          name: auction-backend
          envVarKey: SECRET_KEY
      - key: DATABASE_URL
        fromService:
          type: pserv
          name: auction-database
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: auction-redis
          property: connectionString

  # Celery Beat Scheduler
  - type: worker
    name: auction-scheduler
    env: python
    plan: free
    buildCommand: |
      cd backend &&
      pip install -r requirements.txt
    startCommand: |
      cd backend &&
      celery -A OnlineAuctionSystem beat --loglevel=info
    envVars:
      - key: DJANGO_SETTINGS_MODULE
        value: OnlineAuctionSystem.settings_production
      - key: SECRET_KEY
        fromService:
          type: web
          name: auction-backend
          envVarKey: SECRET_KEY
      - key: DATABASE_URL
        fromService:
          type: pserv
          name: auction-database
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: auction-redis
          property: connectionString
