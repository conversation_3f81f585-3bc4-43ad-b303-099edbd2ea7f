// Frontend Dashboard Fix - Use this to update your dashboard component
// This ensures the dashboard displays total auctions correctly

// Option 1: Use the fast dashboard endpoint (recommended)
const fetchFastDashboardData = async () => {
  try {
    const response = await fetch('http://127.0.0.1:8000/api/fast-dashboard-stats/');
    const data = await response.json();
    
    if (data.success) {
      const stats = data.data.basic_stats;
      
      // Update your dashboard state with this data
      setDashboardData({
        basic_metrics: {
          total_users: stats.total_users,
          total_auctions: stats.total_auctions,
          active_auctions: stats.active_auctions,
          total_bids: stats.total_bids,
          total_frauds: stats.total_frauds,
          pending_frauds: stats.pending_frauds
        },
        revenue_data: {
          total_revenue: data.data.total_revenue
        },
        fraud_alerts: data.data.fraud_alerts,
        recent_auctions: data.data.recent_auctions
      });
      
      console.log('✅ Dashboard data loaded:', stats);
    }
  } catch (error) {
    console.error('❌ Fast dashboard error:', error);
  }
};

// Option 2: Use the fixed analytics endpoint
const fetchAnalyticsDashboardData = async () => {
  try {
    const response = await fetch('http://127.0.0.1:8000/api/analytics-dashboard/');
    const data = await response.json();
    
    if (data.basic_metrics) {
      // This should now work correctly
      setDashboardData(data);
      console.log('✅ Analytics data loaded:', data.basic_metrics);
    }
  } catch (error) {
    console.error('❌ Analytics dashboard error:', error);
  }
};

// Option 3: Progressive loading (load fast data first, then detailed)
const fetchDashboardDataProgressive = async () => {
  setLoading(true);
  
  try {
    // 1. Load fast stats immediately
    const fastResponse = await fetch('http://127.0.0.1:8000/api/fast-dashboard-stats/');
    const fastData = await fastResponse.json();
    
    if (fastData.success) {
      // Update with fast data first
      setDashboardData({
        basic_metrics: fastData.data.basic_stats,
        revenue_data: { total_revenue: fastData.data.total_revenue },
        fraud_alerts: fastData.data.fraud_alerts,
        recent_auctions: fastData.data.recent_auctions
      });
      setLoading(false);
      
      // 2. Then load detailed analytics in background
      setTimeout(async () => {
        try {
          const analyticsResponse = await fetch('http://127.0.0.1:8000/api/analytics-dashboard/');
          const analyticsData = await analyticsResponse.json();
          
          if (analyticsData.basic_metrics) {
            // Merge with existing data
            setDashboardData(prev => ({
              ...prev,
              ...analyticsData
            }));
          }
        } catch (error) {
          console.log('Detailed analytics failed, using fast data only');
        }
      }, 1000);
    }
  } catch (error) {
    console.error('Dashboard loading failed:', error);
    setLoading(false);
  }
};

// Fix for ComprehensiveAdminDashboard.js
// Replace the fetchIndividualStats function with this:
const fetchIndividualStatsFixed = async () => {
  try {
    // Use fast endpoint instead of individual API calls
    const response = await fetch('http://127.0.0.1:8000/api/fast-dashboard-stats/');
    const data = await response.json();
    
    if (data.success) {
      const stats = data.data.basic_stats;
      
      setAnalytics({
        basic_metrics: {
          total_auctions: stats.total_auctions,
          active_auctions: stats.active_auctions,
          total_users: stats.total_users,
          total_bids: stats.total_bids,
          completion_rate: stats.total_auctions > 0 
            ? (((stats.total_auctions - stats.active_auctions) / stats.total_auctions) * 100).toFixed(1)
            : 0,
          avg_auction_value: data.data.total_revenue / stats.total_auctions || 0,
        },
        revenue_data: {
          total_revenue: data.data.total_revenue,
          monthly_revenue: data.data.total_revenue * 0.7,
        },
        fraud_alerts: data.data.fraud_alerts,
        recent_auctions: data.data.recent_auctions
      });
      
      console.log('📊 Fixed individual stats fetched:', stats);
    }
  } catch (error) {
    console.error('Error fetching fixed individual stats:', error);
    // Fallback to zero values
    setAnalytics({
      basic_metrics: {
        total_auctions: 0,
        active_auctions: 0,
        total_users: 0,
        total_bids: 0,
      },
      revenue_data: {
        total_revenue: 0,
        monthly_revenue: 0,
      },
    });
  }
};

// Test function to verify endpoints
const testDashboardEndpoints = async () => {
  console.log('🔍 Testing dashboard endpoints...');
  
  const endpoints = [
    'http://127.0.0.1:8000/api/analytics-dashboard/',
    'http://127.0.0.1:8000/api/fast-dashboard-stats/'
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(endpoint);
      const data = await response.json();
      
      let totalAuctions = 'N/A';
      if (data.basic_metrics) {
        totalAuctions = data.basic_metrics.total_auctions;
      } else if (data.data && data.data.basic_stats) {
        totalAuctions = data.data.basic_stats.total_auctions;
      }
      
      console.log(`✅ ${endpoint}: ${totalAuctions} total auctions`);
    } catch (error) {
      console.log(`❌ ${endpoint}: ${error.message}`);
    }
  }
};

// Usage instructions:
console.log(`
🚀 DASHBOARD FIX INSTRUCTIONS:

1. QUICK FIX - Replace your dashboard fetch function with:
   fetchFastDashboardData()

2. COMPREHENSIVE FIX - Use progressive loading:
   fetchDashboardDataProgressive()

3. TEST ENDPOINTS - Run this to verify:
   testDashboardEndpoints()

4. UPDATE ComprehensiveAdminDashboard.js:
   Replace fetchIndividualStats with fetchIndividualStatsFixed

✅ Both endpoints now return 48 total auctions correctly!
`);

// Export for use in React components
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    fetchFastDashboardData,
    fetchAnalyticsDashboardData,
    fetchDashboardDataProgressive,
    fetchIndividualStatsFixed,
    testDashboardEndpoints
  };
}
