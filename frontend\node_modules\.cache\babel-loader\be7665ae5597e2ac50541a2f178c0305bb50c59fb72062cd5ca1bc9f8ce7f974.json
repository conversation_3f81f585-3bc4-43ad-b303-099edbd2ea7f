{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Online_Auction_System\\\\frontend\\\\src\\\\pages\\\\LandingPage.js\";\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { Container, Row, Col, <PERSON>, Card } from \"react-bootstrap\";\nimport { FaGavel, FaShieldAlt, FaRocket, FaUsers, FaClock, FaChartLine, FaHeart, FaSearch } from \"react-icons/fa\";\nimport axiosInstance from \"../api/axiosInstance\";\nimport globalApiManager from \"../utils/globalApiManager\";\nimport \"./LandingPage.css\";\n\n// Simple landing data fetcher using global API manager\nconst fetchLandingData = async () => {\n  try {\n    console.log(\"🌐 Fetching landing data with global API manager...\");\n    const response = await globalApiManager.makeRequest(axiosInstance, \"landing/data/\", {\n      cacheTime: 5 * 60 * 1000,\n      // Cache for 5 minutes\n      maxRetries: 2,\n      skipRateLimit: false,\n      skipCircuitBreaker: false\n    });\n    if (response && response.success) {\n      console.log(\"✅ Landing data fetched successfully\");\n      return response.data;\n    } else {\n      console.warn(\"⚠️ API returned success=false:\", response);\n      return null;\n    }\n  } catch (error) {\n    console.error(\"❌ Failed to fetch landing data:\", error);\n    return null;\n  }\n};\nconst LandingPage = () => {\n  const [currentFeature, setCurrentFeature] = useState(0);\n  const [landingData, setLandingData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  const navigate = useNavigate();\n  const componentId = useRef(`landing-${Date.now()}-${Math.random()}`); // Unique component ID for debugging\n\n  // Check for dark mode from body class\n  useEffect(() => {\n    const checkDarkMode = () => {\n      const isDark = document.body.classList.contains(\"dark-mode-bg\");\n      setIsDarkMode(isDark);\n    };\n\n    // Initial check\n    checkDarkMode();\n\n    // Listen for class changes on body\n    const observer = new MutationObserver(checkDarkMode);\n    observer.observe(document.body, {\n      attributes: true,\n      attributeFilter: [\"class\"]\n    });\n    return () => observer.disconnect();\n  }, []);\n  const features = [{\n    icon: /*#__PURE__*/React.createElement(FaGavel, {\n      className: \"feature-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 13\n      }\n    }),\n    title: \"Live Auctions\",\n    description: \"Participate in real-time bidding with instant updates and notifications\"\n  }, {\n    icon: /*#__PURE__*/React.createElement(FaShieldAlt, {\n      className: \"feature-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 13\n      }\n    }),\n    title: \"Secure Payments\",\n    description: \"Safe and secure transactions with Stripe payment integration\"\n  }, {\n    icon: /*#__PURE__*/React.createElement(FaRocket, {\n      className: \"feature-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 13\n      }\n    }),\n    title: \"AI Price Prediction\",\n    description: \"Get intelligent price predictions powered by advanced AI algorithms\"\n  }, {\n    icon: /*#__PURE__*/React.createElement(FaUsers, {\n      className: \"feature-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 13\n      }\n    }),\n    title: \"Community Driven\",\n    description: \"Join thousands of buyers and sellers in our trusted marketplace\"\n  }, {\n    icon: /*#__PURE__*/React.createElement(FaClock, {\n      className: \"feature-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 13\n      }\n    }),\n    title: \"24/7 Availability\",\n    description: \"Bid anytime, anywhere with our always-available platform\"\n  }, {\n    icon: /*#__PURE__*/React.createElement(FaChartLine, {\n      className: \"feature-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 13\n      }\n    }),\n    title: \"Market Analytics\",\n    description: \"Track trends and make informed decisions with detailed analytics\"\n  }];\n\n  // Default stats (will be replaced by API data)\n  const defaultStats = [{\n    number: \"10,000+\",\n    label: \"Active Users\"\n  }, {\n    number: \"50,000+\",\n    label: \"Auctions Completed\"\n  }, {\n    number: \"99.9%\",\n    label: \"Uptime\"\n  }, {\n    number: \"$2M+\",\n    label: \"Total Sales\"\n  }];\n\n  // Use API data if available, otherwise use defaults\n  const stats = landingData !== null && landingData !== void 0 && landingData.stats ? [{\n    number: `${landingData.stats.total_users.toLocaleString()}+`,\n    label: \"Active Users\"\n  }, {\n    number: `${landingData.stats.total_auctions.toLocaleString()}+`,\n    label: \"Auctions Completed\"\n  }, {\n    number: landingData.stats.uptime,\n    label: \"Uptime\"\n  }, {\n    number: `$${(landingData.stats.total_sales / 1000000).toFixed(1)}M+`,\n    label: \"Total Sales\"\n  }] : defaultStats;\n\n  // Initialize and subscribe to landing data manager\n  useEffect(() => {\n    let isMounted = true; // Flag to prevent state updates after unmount\n\n    console.log(`🔧 LandingPage component ${componentId.current} mounting`);\n\n    // Initialize from cache first\n    const cacheInitialized = landingDataManager.initializeFromCache();\n\n    // Set initial state only if component is still mounted\n    if (isMounted) {\n      setLandingData(landingDataManager.data);\n      setLoading(landingDataManager.loading);\n    }\n\n    // Subscribe to updates\n    const unsubscribe = landingDataManager.subscribe((data, loading) => {\n      if (isMounted) {\n        console.log(`📡 LandingPage ${componentId.current} received update:`, {\n          data: !!data,\n          loading\n        });\n        setLandingData(data);\n        setLoading(loading);\n      }\n    });\n\n    // Fetch fresh data if needed (only if cache wasn't sufficient)\n    if (!cacheInitialized || !landingDataManager.data) {\n      landingDataManager.fetchData().catch(error => {\n        if (isMounted) {\n          console.error(`❌ LandingPage ${componentId.current} fetch error:`, error);\n          // Set loading to false on error to prevent infinite loading state\n          setLoading(false);\n        }\n      });\n    }\n    return () => {\n      console.log(`🔧 LandingPage component ${componentId.current} unmounting`);\n      isMounted = false; // Prevent further state updates\n      unsubscribe();\n    };\n  }, []); // Empty dependency array - only run once on mount\n\n  // Auto-rotate features\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentFeature(prev => (prev + 1) % features.length);\n    }, 3000);\n    return () => clearInterval(interval);\n  }, []); // Remove features.length dependency since features array is constant\n\n  const handleGetStarted = () => {\n    navigate(\"/home\");\n  };\n  const handleLearnMore = () => {\n    var _document$getElementB;\n    // Scroll to features section\n    (_document$getElementB = document.getElementById(\"features-section\")) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"landing-page\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"section\", {\n    className: \"hero-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Container, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    className: \"align-items-center min-vh-100\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    lg: 6,\n    className: \"hero-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"logo-section mb-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"logo-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaGavel, {\n    className: \"logo-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(\"h1\", {\n    className: \"logo-text\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 19\n    }\n  }, \"AuctionStore\")), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"tagline\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 17\n    }\n  }, \"Where Every Bid Tells a Story\")), /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"hero-title\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 15\n    }\n  }, \"Discover, Bid, and Win\", /*#__PURE__*/React.createElement(\"span\", {\n    className: \"highlight\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 17\n    }\n  }, \" Amazing Items\")), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"hero-description\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 15\n    }\n  }, \"Join the world's most trusted online auction platform. From rare collectibles to everyday treasures, find exactly what you're looking for or discover something unexpected.\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"hero-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    size: \"lg\",\n    className: \"cta-button primary\",\n    onClick: handleGetStarted,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaRocket, {\n    className: \"me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 19\n    }\n  }), \"Get Started\"), /*#__PURE__*/React.createElement(Button, {\n    size: \"lg\",\n    variant: \"outline-light\",\n    className: \"cta-button secondary\",\n    onClick: handleLearnMore,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 17\n    }\n  }, \"Learn More\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"trust-indicators\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"trust-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaShieldAlt, {\n    className: \"trust-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 19\n    }\n  }, \"Secure & Trusted\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"trust-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaUsers, {\n    className: \"trust-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 19\n    }\n  }, \"10,000+ Users\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"trust-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaHeart, {\n    className: \"trust-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 19\n    }\n  }, \"99% Satisfaction\")))), /*#__PURE__*/React.createElement(Col, {\n    lg: 6,\n    className: \"hero-visual\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"feature-showcase\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"feature-card active\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 17\n    }\n  }, features[currentFeature].icon, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 19\n    }\n  }, features[currentFeature].title), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 19\n    }\n  }, features[currentFeature].description)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"feature-dots\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 17\n    }\n  }, features.map((_, index) => /*#__PURE__*/React.createElement(\"button\", {\n    key: index,\n    className: `dot ${index === currentFeature ? \"active\" : \"\"}`,\n    onClick: () => setCurrentFeature(index),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 21\n    }\n  })))))))), /*#__PURE__*/React.createElement(\"section\", {\n    className: \"stats-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Container, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 11\n    }\n  }, stats.map((stat, index) => /*#__PURE__*/React.createElement(Col, {\n    md: 3,\n    key: index,\n    className: \"text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 19\n    }\n  }, stat.number), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 19\n    }\n  }, stat.label))))))), /*#__PURE__*/React.createElement(\"section\", {\n    id: \"features-section\",\n    className: \"features-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Container, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    lg: 12,\n    className: \"text-center mb-5\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"section-title\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 15\n    }\n  }, \"Why Choose AuctionStore?\"), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"section-subtitle\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 15\n    }\n  }, \"Experience the future of online auctions with our cutting-edge features\"))), /*#__PURE__*/React.createElement(Row, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 11\n    }\n  }, features.map((feature, index) => /*#__PURE__*/React.createElement(Col, {\n    md: 4,\n    key: index,\n    className: \"mb-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Card, {\n    className: \"feature-card h-100\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(Card.Body, {\n    className: \"text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 19\n    }\n  }, feature.icon, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 21\n    }\n  }, feature.title), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 21\n    }\n  }, feature.description)))))))), /*#__PURE__*/React.createElement(\"section\", {\n    className: \"cta-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Container, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    lg: 12,\n    className: \"text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: {\n      color: \"white\"\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 15\n    }\n  }, \"Ready to Start Your Auction Journey?\"), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      color: \"white\"\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 15\n    }\n  }, \"Join thousands of satisfied users and discover amazing deals today!\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"cta-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    size: \"lg\",\n    className: \"cta-button primary me-3\",\n    onClick: handleGetStarted,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaSearch, {\n    className: \"me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 19\n    }\n  }), \"Explore Auctions\"), /*#__PURE__*/React.createElement(Link, {\n    to: \"/register\",\n    className: \"btn btn-outline-light btn-lg cta-button\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 17\n    }\n  }, \"Sign Up Free\")))))), /*#__PURE__*/React.createElement(\"footer\", {\n    className: \"landing-footer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Container, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    lg: 12,\n    className: \"text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"footer-logo\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaGavel, {\n    className: \"footer-logo-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 17\n    }\n  }, \"AuctionStore\")), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 15\n    }\n  }, \"\\xA9 2024 AuctionStore. All rights reserved.\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"footer-links\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Link, {\n    to: \"/home\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 17\n    }\n  }, \"Home\"), /*#__PURE__*/React.createElement(Link, {\n    to: \"/auctions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 17\n    }\n  }, \"Auctions\"), /*#__PURE__*/React.createElement(Link, {\n    to: \"/login\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 17\n    }\n  }, \"Login\"), /*#__PURE__*/React.createElement(Link, {\n    to: \"/register\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 17\n    }\n  }, \"Register\")))))));\n};\nexport default /*#__PURE__*/React.memo(LandingPage);", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Link", "useNavigate", "Container", "Row", "Col", "<PERSON><PERSON>", "Card", "FaGavel", "FaShieldAlt", "FaRocket", "FaUsers", "FaClock", "FaChartLine", "FaHeart", "FaSearch", "axiosInstance", "globalApiManager", "fetchLandingData", "console", "log", "response", "makeRequest", "cacheTime", "maxRetries", "skipRateLimit", "skipCircuitBreaker", "success", "data", "warn", "error", "LandingPage", "currentFeature", "setCurrentFeature", "landingData", "setLandingData", "loading", "setLoading", "isDarkMode", "setIsDarkMode", "navigate", "componentId", "Date", "now", "Math", "random", "checkDarkMode", "isDark", "document", "body", "classList", "contains", "observer", "MutationObserver", "observe", "attributes", "attributeFilter", "disconnect", "features", "icon", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "defaultStats", "number", "label", "stats", "total_users", "toLocaleString", "total_auctions", "uptime", "total_sales", "toFixed", "isMounted", "current", "cacheInitialized", "landingDataManager", "initializeFromCache", "unsubscribe", "subscribe", "fetchData", "catch", "interval", "setInterval", "prev", "length", "clearInterval", "handleGetStarted", "handleLearnMore", "_document$getElementB", "getElementById", "scrollIntoView", "behavior", "lg", "size", "onClick", "variant", "map", "_", "index", "key", "stat", "md", "id", "feature", "Body", "style", "color", "to", "memo"], "sources": ["C:/Users/<USER>/Desktop/Online_Auction_System/frontend/src/pages/LandingPage.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { Container, Row, Col, Button, Card } from \"react-bootstrap\";\r\nimport {\r\n  FaGavel,\r\n  FaShieldAlt,\r\n  FaRocket,\r\n  FaUsers,\r\n  FaClock,\r\n  FaChartLine,\r\n  FaHeart,\r\n  FaSearch,\r\n} from \"react-icons/fa\";\r\nimport axiosInstance from \"../api/axiosInstance\";\r\nimport globalApiManager from \"../utils/globalApiManager\";\r\nimport \"./LandingPage.css\";\r\n\r\n// Simple landing data fetcher using global API manager\r\nconst fetchLandingData = async () => {\r\n  try {\r\n    console.log(\"🌐 Fetching landing data with global API manager...\");\r\n\r\n    const response = await globalApiManager.makeRequest(\r\n      axiosInstance,\r\n      \"landing/data/\",\r\n      {\r\n        cacheTime: 5 * 60 * 1000, // Cache for 5 minutes\r\n        maxRetries: 2,\r\n        skipRateLimit: false,\r\n        skipCircuitBreaker: false,\r\n      }\r\n    );\r\n\r\n    if (response && response.success) {\r\n      console.log(\"✅ Landing data fetched successfully\");\r\n      return response.data;\r\n    } else {\r\n      console.warn(\"⚠️ API returned success=false:\", response);\r\n      return null;\r\n    }\r\n  } catch (error) {\r\n    console.error(\"❌ Failed to fetch landing data:\", error);\r\n    return null;\r\n  }\r\n};\r\n\r\nconst LandingPage = () => {\r\n  const [currentFeature, setCurrentFeature] = useState(0);\r\n  const [landingData, setLandingData] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [isDarkMode, setIsDarkMode] = useState(false);\r\n  const navigate = useNavigate();\r\n  const componentId = useRef(`landing-${Date.now()}-${Math.random()}`); // Unique component ID for debugging\r\n\r\n  // Check for dark mode from body class\r\n  useEffect(() => {\r\n    const checkDarkMode = () => {\r\n      const isDark = document.body.classList.contains(\"dark-mode-bg\");\r\n      setIsDarkMode(isDark);\r\n    };\r\n\r\n    // Initial check\r\n    checkDarkMode();\r\n\r\n    // Listen for class changes on body\r\n    const observer = new MutationObserver(checkDarkMode);\r\n    observer.observe(document.body, {\r\n      attributes: true,\r\n      attributeFilter: [\"class\"],\r\n    });\r\n\r\n    return () => observer.disconnect();\r\n  }, []);\r\n\r\n  const features = [\r\n    {\r\n      icon: <FaGavel className=\"feature-icon\" />,\r\n      title: \"Live Auctions\",\r\n      description:\r\n        \"Participate in real-time bidding with instant updates and notifications\",\r\n    },\r\n    {\r\n      icon: <FaShieldAlt className=\"feature-icon\" />,\r\n      title: \"Secure Payments\",\r\n      description:\r\n        \"Safe and secure transactions with Stripe payment integration\",\r\n    },\r\n    {\r\n      icon: <FaRocket className=\"feature-icon\" />,\r\n      title: \"AI Price Prediction\",\r\n      description:\r\n        \"Get intelligent price predictions powered by advanced AI algorithms\",\r\n    },\r\n    {\r\n      icon: <FaUsers className=\"feature-icon\" />,\r\n      title: \"Community Driven\",\r\n      description:\r\n        \"Join thousands of buyers and sellers in our trusted marketplace\",\r\n    },\r\n    {\r\n      icon: <FaClock className=\"feature-icon\" />,\r\n      title: \"24/7 Availability\",\r\n      description: \"Bid anytime, anywhere with our always-available platform\",\r\n    },\r\n    {\r\n      icon: <FaChartLine className=\"feature-icon\" />,\r\n      title: \"Market Analytics\",\r\n      description:\r\n        \"Track trends and make informed decisions with detailed analytics\",\r\n    },\r\n  ];\r\n\r\n  // Default stats (will be replaced by API data)\r\n  const defaultStats = [\r\n    { number: \"10,000+\", label: \"Active Users\" },\r\n    { number: \"50,000+\", label: \"Auctions Completed\" },\r\n    { number: \"99.9%\", label: \"Uptime\" },\r\n    { number: \"$2M+\", label: \"Total Sales\" },\r\n  ];\r\n\r\n  // Use API data if available, otherwise use defaults\r\n  const stats = landingData?.stats\r\n    ? [\r\n        {\r\n          number: `${landingData.stats.total_users.toLocaleString()}+`,\r\n          label: \"Active Users\",\r\n        },\r\n        {\r\n          number: `${landingData.stats.total_auctions.toLocaleString()}+`,\r\n          label: \"Auctions Completed\",\r\n        },\r\n        { number: landingData.stats.uptime, label: \"Uptime\" },\r\n        {\r\n          number: `$${(landingData.stats.total_sales / 1000000).toFixed(1)}M+`,\r\n          label: \"Total Sales\",\r\n        },\r\n      ]\r\n    : defaultStats;\r\n\r\n  // Initialize and subscribe to landing data manager\r\n  useEffect(() => {\r\n    let isMounted = true; // Flag to prevent state updates after unmount\r\n\r\n    console.log(`🔧 LandingPage component ${componentId.current} mounting`);\r\n\r\n    // Initialize from cache first\r\n    const cacheInitialized = landingDataManager.initializeFromCache();\r\n\r\n    // Set initial state only if component is still mounted\r\n    if (isMounted) {\r\n      setLandingData(landingDataManager.data);\r\n      setLoading(landingDataManager.loading);\r\n    }\r\n\r\n    // Subscribe to updates\r\n    const unsubscribe = landingDataManager.subscribe((data, loading) => {\r\n      if (isMounted) {\r\n        console.log(`📡 LandingPage ${componentId.current} received update:`, {\r\n          data: !!data,\r\n          loading,\r\n        });\r\n        setLandingData(data);\r\n        setLoading(loading);\r\n      }\r\n    });\r\n\r\n    // Fetch fresh data if needed (only if cache wasn't sufficient)\r\n    if (!cacheInitialized || !landingDataManager.data) {\r\n      landingDataManager.fetchData().catch((error) => {\r\n        if (isMounted) {\r\n          console.error(\r\n            `❌ LandingPage ${componentId.current} fetch error:`,\r\n            error\r\n          );\r\n          // Set loading to false on error to prevent infinite loading state\r\n          setLoading(false);\r\n        }\r\n      });\r\n    }\r\n\r\n    return () => {\r\n      console.log(`🔧 LandingPage component ${componentId.current} unmounting`);\r\n      isMounted = false; // Prevent further state updates\r\n      unsubscribe();\r\n    };\r\n  }, []); // Empty dependency array - only run once on mount\r\n\r\n  // Auto-rotate features\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      setCurrentFeature((prev) => (prev + 1) % features.length);\r\n    }, 3000);\r\n    return () => clearInterval(interval);\r\n  }, []); // Remove features.length dependency since features array is constant\r\n\r\n  const handleGetStarted = () => {\r\n    navigate(\"/home\");\r\n  };\r\n\r\n  const handleLearnMore = () => {\r\n    // Scroll to features section\r\n    document.getElementById(\"features-section\")?.scrollIntoView({\r\n      behavior: \"smooth\",\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"landing-page\">\r\n      {/* Hero Section */}\r\n      <section className=\"hero-section\">\r\n        <Container>\r\n          <Row className=\"align-items-center min-vh-100\">\r\n            <Col lg={6} className=\"hero-content\">\r\n              <div className=\"logo-section mb-4\">\r\n                <div className=\"logo-container\">\r\n                  <FaGavel className=\"logo-icon\" />\r\n                  <h1 className=\"logo-text\">AuctionStore</h1>\r\n                </div>\r\n                <p className=\"tagline\">Where Every Bid Tells a Story</p>\r\n              </div>\r\n\r\n              <h2 className=\"hero-title\">\r\n                Discover, Bid, and Win\r\n                <span className=\"highlight\"> Amazing Items</span>\r\n              </h2>\r\n\r\n              <p className=\"hero-description\">\r\n                Join the world's most trusted online auction platform. From rare\r\n                collectibles to everyday treasures, find exactly what you're\r\n                looking for or discover something unexpected.\r\n              </p>\r\n\r\n              <div className=\"hero-buttons\">\r\n                <Button\r\n                  size=\"lg\"\r\n                  className=\"cta-button primary\"\r\n                  onClick={handleGetStarted}\r\n                >\r\n                  <FaRocket className=\"me-2\" />\r\n                  Get Started\r\n                </Button>\r\n                <Button\r\n                  size=\"lg\"\r\n                  variant=\"outline-light\"\r\n                  className=\"cta-button secondary\"\r\n                  onClick={handleLearnMore}\r\n                >\r\n                  Learn More\r\n                </Button>\r\n              </div>\r\n\r\n              <div className=\"trust-indicators\">\r\n                <div className=\"trust-item\">\r\n                  <FaShieldAlt className=\"trust-icon\" />\r\n                  <span>Secure & Trusted</span>\r\n                </div>\r\n                <div className=\"trust-item\">\r\n                  <FaUsers className=\"trust-icon\" />\r\n                  <span>10,000+ Users</span>\r\n                </div>\r\n                <div className=\"trust-item\">\r\n                  <FaHeart className=\"trust-icon\" />\r\n                  <span>99% Satisfaction</span>\r\n                </div>\r\n              </div>\r\n            </Col>\r\n\r\n            <Col lg={6} className=\"hero-visual\">\r\n              <div className=\"feature-showcase\">\r\n                <div className=\"feature-card active\">\r\n                  {features[currentFeature].icon}\r\n                  <h3>{features[currentFeature].title}</h3>\r\n                  <p>{features[currentFeature].description}</p>\r\n                </div>\r\n\r\n                <div className=\"feature-dots\">\r\n                  {features.map((_, index) => (\r\n                    <button\r\n                      key={index}\r\n                      className={`dot ${\r\n                        index === currentFeature ? \"active\" : \"\"\r\n                      }`}\r\n                      onClick={() => setCurrentFeature(index)}\r\n                    />\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </section>\r\n\r\n      {/* Stats Section */}\r\n      <section className=\"stats-section\">\r\n        <Container>\r\n          <Row>\r\n            {stats.map((stat, index) => (\r\n              <Col md={3} key={index} className=\"text-center\">\r\n                <div className=\"stat-item\">\r\n                  <h3 className=\"stat-number\">{stat.number}</h3>\r\n                  <p className=\"stat-label\">{stat.label}</p>\r\n                </div>\r\n              </Col>\r\n            ))}\r\n          </Row>\r\n        </Container>\r\n      </section>\r\n\r\n      {/* Features Section */}\r\n      <section id=\"features-section\" className=\"features-section\">\r\n        <Container>\r\n          <Row>\r\n            <Col lg={12} className=\"text-center mb-5\">\r\n              <h2 className=\"section-title\">Why Choose AuctionStore?</h2>\r\n              <p className=\"section-subtitle\">\r\n                Experience the future of online auctions with our cutting-edge\r\n                features\r\n              </p>\r\n            </Col>\r\n          </Row>\r\n\r\n          <Row>\r\n            {features.map((feature, index) => (\r\n              <Col md={4} key={index} className=\"mb-4\">\r\n                <Card className=\"feature-card h-100\">\r\n                  <Card.Body className=\"text-center\">\r\n                    {feature.icon}\r\n                    <h4>{feature.title}</h4>\r\n                    <p>{feature.description}</p>\r\n                  </Card.Body>\r\n                </Card>\r\n              </Col>\r\n            ))}\r\n          </Row>\r\n        </Container>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"cta-section\">\r\n        <Container>\r\n          <Row>\r\n            <Col lg={12} className=\"text-center\">\r\n              <h2 style={{ color: \"white\" }}>\r\n                Ready to Start Your Auction Journey?\r\n              </h2>\r\n              <p style={{ color: \"white\" }}>\r\n                Join thousands of satisfied users and discover amazing deals\r\n                today!\r\n              </p>\r\n              <div className=\"cta-buttons\">\r\n                <Button\r\n                  size=\"lg\"\r\n                  className=\"cta-button primary me-3\"\r\n                  onClick={handleGetStarted}\r\n                >\r\n                  <FaSearch className=\"me-2\" />\r\n                  Explore Auctions\r\n                </Button>\r\n                <Link\r\n                  to=\"/register\"\r\n                  className=\"btn btn-outline-light btn-lg cta-button\"\r\n                >\r\n                  Sign Up Free\r\n                </Link>\r\n              </div>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"landing-footer\">\r\n        <Container>\r\n          <Row>\r\n            <Col lg={12} className=\"text-center\">\r\n              <div className=\"footer-logo\">\r\n                <FaGavel className=\"footer-logo-icon\" />\r\n                <span>AuctionStore</span>\r\n              </div>\r\n              <p>&copy; 2024 AuctionStore. All rights reserved.</p>\r\n              <div className=\"footer-links\">\r\n                <Link to=\"/home\">Home</Link>\r\n                <Link to=\"/auctions\">Auctions</Link>\r\n                <Link to=\"/login\">Login</Link>\r\n                <Link to=\"/register\">Register</Link>\r\n              </div>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default React.memo(LandingPage);\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,QAAQ,iBAAiB;AACnE,SACEC,OAAO,EACPC,WAAW,EACXC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,WAAW,EACXC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAO,mBAAmB;;AAE1B;AACA,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EACnC,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IAElE,MAAMC,QAAQ,GAAG,MAAMJ,gBAAgB,CAACK,WAAW,CACjDN,aAAa,EACb,eAAe,EACf;MACEO,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;MAAE;MAC1BC,UAAU,EAAE,CAAC;MACbC,aAAa,EAAE,KAAK;MACpBC,kBAAkB,EAAE;IACtB,CACF,CAAC;IAED,IAAIL,QAAQ,IAAIA,QAAQ,CAACM,OAAO,EAAE;MAChCR,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAOC,QAAQ,CAACO,IAAI;IACtB,CAAC,MAAM;MACLT,OAAO,CAACU,IAAI,CAAC,gCAAgC,EAAER,QAAQ,CAAC;MACxD,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAOS,KAAK,EAAE;IACdX,OAAO,CAACW,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,OAAO,IAAI;EACb;AACF,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM0C,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,WAAW,GAAGzC,MAAM,CAAC,WAAW0C,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEtE;EACA9C,SAAS,CAAC,MAAM;IACd,MAAM+C,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,cAAc,CAAC;MAC/DZ,aAAa,CAACQ,MAAM,CAAC;IACvB,CAAC;;IAED;IACAD,aAAa,CAAC,CAAC;;IAEf;IACA,MAAMM,QAAQ,GAAG,IAAIC,gBAAgB,CAACP,aAAa,CAAC;IACpDM,QAAQ,CAACE,OAAO,CAACN,QAAQ,CAACC,IAAI,EAAE;MAC9BM,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,CAAC,OAAO;IAC3B,CAAC,CAAC;IAEF,OAAO,MAAMJ,QAAQ,CAACK,UAAU,CAAC,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,eAAE9D,KAAA,CAAA+D,aAAA,CAACpD,OAAO;MAACqD,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC1CC,KAAK,EAAE,eAAe;IACtBC,WAAW,EACT;EACJ,CAAC,EACD;IACEV,IAAI,eAAE9D,KAAA,CAAA+D,aAAA,CAACnD,WAAW;MAACoD,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC9CC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EACT;EACJ,CAAC,EACD;IACEV,IAAI,eAAE9D,KAAA,CAAA+D,aAAA,CAAClD,QAAQ;MAACmD,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC3CC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EACT;EACJ,CAAC,EACD;IACEV,IAAI,eAAE9D,KAAA,CAAA+D,aAAA,CAACjD,OAAO;MAACkD,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC1CC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EACT;EACJ,CAAC,EACD;IACEV,IAAI,eAAE9D,KAAA,CAAA+D,aAAA,CAAChD,OAAO;MAACiD,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC1CC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEV,IAAI,eAAE9D,KAAA,CAAA+D,aAAA,CAAC/C,WAAW;MAACgD,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC9CC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EACT;EACJ,CAAC,CACF;;EAED;EACA,MAAMC,YAAY,GAAG,CACnB;IAAEC,MAAM,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC5C;IAAED,MAAM,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAqB,CAAC,EAClD;IAAED,MAAM,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAc,CAAC,CACzC;;EAED;EACA,MAAMC,KAAK,GAAGvC,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEuC,KAAK,GAC5B,CACE;IACEF,MAAM,EAAE,GAAGrC,WAAW,CAACuC,KAAK,CAACC,WAAW,CAACC,cAAc,CAAC,CAAC,GAAG;IAC5DH,KAAK,EAAE;EACT,CAAC,EACD;IACED,MAAM,EAAE,GAAGrC,WAAW,CAACuC,KAAK,CAACG,cAAc,CAACD,cAAc,CAAC,CAAC,GAAG;IAC/DH,KAAK,EAAE;EACT,CAAC,EACD;IAAED,MAAM,EAAErC,WAAW,CAACuC,KAAK,CAACI,MAAM;IAAEL,KAAK,EAAE;EAAS,CAAC,EACrD;IACED,MAAM,EAAE,IAAI,CAACrC,WAAW,CAACuC,KAAK,CAACK,WAAW,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;IACpEP,KAAK,EAAE;EACT,CAAC,CACF,GACDF,YAAY;;EAEhB;EACAvE,SAAS,CAAC,MAAM;IACd,IAAIiF,SAAS,GAAG,IAAI,CAAC,CAAC;;IAEtB7D,OAAO,CAACC,GAAG,CAAC,4BAA4BqB,WAAW,CAACwC,OAAO,WAAW,CAAC;;IAEvE;IACA,MAAMC,gBAAgB,GAAGC,kBAAkB,CAACC,mBAAmB,CAAC,CAAC;;IAEjE;IACA,IAAIJ,SAAS,EAAE;MACb7C,cAAc,CAACgD,kBAAkB,CAACvD,IAAI,CAAC;MACvCS,UAAU,CAAC8C,kBAAkB,CAAC/C,OAAO,CAAC;IACxC;;IAEA;IACA,MAAMiD,WAAW,GAAGF,kBAAkB,CAACG,SAAS,CAAC,CAAC1D,IAAI,EAAEQ,OAAO,KAAK;MAClE,IAAI4C,SAAS,EAAE;QACb7D,OAAO,CAACC,GAAG,CAAC,kBAAkBqB,WAAW,CAACwC,OAAO,mBAAmB,EAAE;UACpErD,IAAI,EAAE,CAAC,CAACA,IAAI;UACZQ;QACF,CAAC,CAAC;QACFD,cAAc,CAACP,IAAI,CAAC;QACpBS,UAAU,CAACD,OAAO,CAAC;MACrB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAAC8C,gBAAgB,IAAI,CAACC,kBAAkB,CAACvD,IAAI,EAAE;MACjDuD,kBAAkB,CAACI,SAAS,CAAC,CAAC,CAACC,KAAK,CAAE1D,KAAK,IAAK;QAC9C,IAAIkD,SAAS,EAAE;UACb7D,OAAO,CAACW,KAAK,CACX,iBAAiBW,WAAW,CAACwC,OAAO,eAAe,EACnDnD,KACF,CAAC;UACD;UACAO,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACXlB,OAAO,CAACC,GAAG,CAAC,4BAA4BqB,WAAW,CAACwC,OAAO,aAAa,CAAC;MACzED,SAAS,GAAG,KAAK,CAAC,CAAC;MACnBK,WAAW,CAAC,CAAC;IACf,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAtF,SAAS,CAAC,MAAM;IACd,MAAM0F,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCzD,iBAAiB,CAAE0D,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIjC,QAAQ,CAACkC,MAAM,CAAC;IAC3D,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,MAAMK,gBAAgB,GAAGA,CAAA,KAAM;IAC7BtD,QAAQ,CAAC,OAAO,CAAC;EACnB,CAAC;EAED,MAAMuD,eAAe,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC5B;IACA,CAAAA,qBAAA,GAAAhD,QAAQ,CAACiD,cAAc,CAAC,kBAAkB,CAAC,cAAAD,qBAAA,uBAA3CA,qBAAA,CAA6CE,cAAc,CAAC;MAC1DC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,oBACEtG,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3BtE,KAAA,CAAA+D,aAAA;IAASC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BtE,KAAA,CAAA+D,aAAA,CAACzD,SAAS;IAAA2D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRtE,KAAA,CAAA+D,aAAA,CAACxD,GAAG;IAACyD,SAAS,EAAC,+BAA+B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5CtE,KAAA,CAAA+D,aAAA,CAACvD,GAAG;IAAC+F,EAAE,EAAE,CAAE;IAACvC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BtE,KAAA,CAAA+D,aAAA,CAACpD,OAAO;IAACqD,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACjCtE,KAAA,CAAA+D,aAAA;IAAIC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAgB,CACvC,CAAC,eACNtE,KAAA,CAAA+D,aAAA;IAAGC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+BAAgC,CACpD,CAAC,eAENtE,KAAA,CAAA+D,aAAA;IAAIC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,wBAEzB,eAAAtE,KAAA,CAAA+D,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gBAAoB,CAC9C,CAAC,eAELtE,KAAA,CAAA+D,aAAA;IAAGC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6KAI7B,CAAC,eAEJtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BtE,KAAA,CAAA+D,aAAA,CAACtD,MAAM;IACL+F,IAAI,EAAC,IAAI;IACTxC,SAAS,EAAC,oBAAoB;IAC9ByC,OAAO,EAAER,gBAAiB;IAAAhC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE1BtE,KAAA,CAAA+D,aAAA,CAAClD,QAAQ;IAACmD,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAEvB,CAAC,eACTtE,KAAA,CAAA+D,aAAA,CAACtD,MAAM;IACL+F,IAAI,EAAC,IAAI;IACTE,OAAO,EAAC,eAAe;IACvB1C,SAAS,EAAC,sBAAsB;IAChCyC,OAAO,EAAEP,eAAgB;IAAAjC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1B,YAEO,CACL,CAAC,eAENtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBtE,KAAA,CAAA+D,aAAA,CAACnD,WAAW;IAACoD,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACtCtE,KAAA,CAAA+D,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,kBAAsB,CACzB,CAAC,eACNtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBtE,KAAA,CAAA+D,aAAA,CAACjD,OAAO;IAACkD,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAClCtE,KAAA,CAAA+D,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,eAAmB,CACtB,CAAC,eACNtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBtE,KAAA,CAAA+D,aAAA,CAAC9C,OAAO;IAAC+C,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAClCtE,KAAA,CAAA+D,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,kBAAsB,CACzB,CACF,CACF,CAAC,eAENtE,KAAA,CAAA+D,aAAA,CAACvD,GAAG;IAAC+F,EAAE,EAAE,CAAE;IAACvC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjCtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjCT,QAAQ,CAAC1B,cAAc,CAAC,CAAC2B,IAAI,eAC9B9D,KAAA,CAAA+D,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKT,QAAQ,CAAC1B,cAAc,CAAC,CAACoC,KAAU,CAAC,eACzCvE,KAAA,CAAA+D,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAIT,QAAQ,CAAC1B,cAAc,CAAC,CAACqC,WAAe,CACzC,CAAC,eAENxE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1BT,QAAQ,CAAC8C,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACrB7G,KAAA,CAAA+D,aAAA;IACE+C,GAAG,EAAED,KAAM;IACX7C,SAAS,EAAE,OACT6C,KAAK,KAAK1E,cAAc,GAAG,QAAQ,GAAG,EAAE,EACvC;IACHsE,OAAO,EAAEA,CAAA,KAAMrE,iBAAiB,CAACyE,KAAK,CAAE;IAAA5C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACzC,CACF,CACE,CACF,CACF,CACF,CACI,CACJ,CAAC,eAGVtE,KAAA,CAAA+D,aAAA;IAASC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCtE,KAAA,CAAA+D,aAAA,CAACzD,SAAS;IAAA2D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRtE,KAAA,CAAA+D,aAAA,CAACxD,GAAG;IAAA0D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACDM,KAAK,CAAC+B,GAAG,CAAC,CAACI,IAAI,EAAEF,KAAK,kBACrB7G,KAAA,CAAA+D,aAAA,CAACvD,GAAG;IAACwG,EAAE,EAAE,CAAE;IAACF,GAAG,EAAED,KAAM;IAAC7C,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7CtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBtE,KAAA,CAAA+D,aAAA;IAAIC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEyC,IAAI,CAACrC,MAAW,CAAC,eAC9C1E,KAAA,CAAA+D,aAAA;IAAGC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEyC,IAAI,CAACpC,KAAS,CACtC,CACF,CACN,CACE,CACI,CACJ,CAAC,eAGV3E,KAAA,CAAA+D,aAAA;IAASkD,EAAE,EAAC,kBAAkB;IAACjD,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzDtE,KAAA,CAAA+D,aAAA,CAACzD,SAAS;IAAA2D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRtE,KAAA,CAAA+D,aAAA,CAACxD,GAAG;IAAA0D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACFtE,KAAA,CAAA+D,aAAA,CAACvD,GAAG;IAAC+F,EAAE,EAAE,EAAG;IAACvC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvCtE,KAAA,CAAA+D,aAAA;IAAIC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,0BAA4B,CAAC,eAC3DtE,KAAA,CAAA+D,aAAA;IAAGC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yEAG7B,CACA,CACF,CAAC,eAENtE,KAAA,CAAA+D,aAAA,CAACxD,GAAG;IAAA0D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACDT,QAAQ,CAAC8C,GAAG,CAAC,CAACO,OAAO,EAAEL,KAAK,kBAC3B7G,KAAA,CAAA+D,aAAA,CAACvD,GAAG;IAACwG,EAAE,EAAE,CAAE;IAACF,GAAG,EAAED,KAAM;IAAC7C,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtCtE,KAAA,CAAA+D,aAAA,CAACrD,IAAI;IAACsD,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCtE,KAAA,CAAA+D,aAAA,CAACrD,IAAI,CAACyG,IAAI;IAACnD,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/B4C,OAAO,CAACpD,IAAI,eACb9D,KAAA,CAAA+D,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK4C,OAAO,CAAC3C,KAAU,CAAC,eACxBvE,KAAA,CAAA+D,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI4C,OAAO,CAAC1C,WAAe,CAClB,CACP,CACH,CACN,CACE,CACI,CACJ,CAAC,eAGVxE,KAAA,CAAA+D,aAAA;IAASC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BtE,KAAA,CAAA+D,aAAA,CAACzD,SAAS;IAAA2D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRtE,KAAA,CAAA+D,aAAA,CAACxD,GAAG;IAAA0D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACFtE,KAAA,CAAA+D,aAAA,CAACvD,GAAG;IAAC+F,EAAE,EAAE,EAAG;IAACvC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCtE,KAAA,CAAA+D,aAAA;IAAIqD,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ,CAAE;IAAApD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sCAE3B,CAAC,eACLtE,KAAA,CAAA+D,aAAA;IAAGqD,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ,CAAE;IAAApD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qEAG3B,CAAC,eACJtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BtE,KAAA,CAAA+D,aAAA,CAACtD,MAAM;IACL+F,IAAI,EAAC,IAAI;IACTxC,SAAS,EAAC,yBAAyB;IACnCyC,OAAO,EAAER,gBAAiB;IAAAhC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE1BtE,KAAA,CAAA+D,aAAA,CAAC7C,QAAQ;IAAC8C,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,oBAEvB,CAAC,eACTtE,KAAA,CAAA+D,aAAA,CAAC3D,IAAI;IACHkH,EAAE,EAAC,WAAW;IACdtD,SAAS,EAAC,yCAAyC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpD,cAEK,CACH,CACF,CACF,CACI,CACJ,CAAC,eAGVtE,KAAA,CAAA+D,aAAA;IAAQC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCtE,KAAA,CAAA+D,aAAA,CAACzD,SAAS;IAAA2D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRtE,KAAA,CAAA+D,aAAA,CAACxD,GAAG;IAAA0D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACFtE,KAAA,CAAA+D,aAAA,CAACvD,GAAG;IAAC+F,EAAE,EAAE,EAAG;IAACvC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BtE,KAAA,CAAA+D,aAAA,CAACpD,OAAO;IAACqD,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACxCtE,KAAA,CAAA+D,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,cAAkB,CACrB,CAAC,eACNtE,KAAA,CAAA+D,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,8CAAiD,CAAC,eACrDtE,KAAA,CAAA+D,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BtE,KAAA,CAAA+D,aAAA,CAAC3D,IAAI;IAACkH,EAAE,EAAC,OAAO;IAAArD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,MAAU,CAAC,eAC5BtE,KAAA,CAAA+D,aAAA,CAAC3D,IAAI;IAACkH,EAAE,EAAC,WAAW;IAAArD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UAAc,CAAC,eACpCtE,KAAA,CAAA+D,aAAA,CAAC3D,IAAI;IAACkH,EAAE,EAAC,QAAQ;IAAArD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OAAW,CAAC,eAC9BtE,KAAA,CAAA+D,aAAA,CAAC3D,IAAI;IAACkH,EAAE,EAAC,WAAW;IAAArD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UAAc,CAChC,CACF,CACF,CACI,CACL,CACL,CAAC;AAEV,CAAC;AAED,4BAAetE,KAAK,CAACuH,IAAI,CAACrF,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}