#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create admin user for the auction system
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append('backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from django.contrib.auth.models import User
from auction.models import UserProfile

def create_admin_user():
    """Create or update admin user"""
    print("🔧 Creating/Updating Admin User...")
    
    username = "aisha_admin"
    email = "<EMAIL>"
    password = "aisha2024!"
    
    try:
        # Check if user already exists
        try:
            user = User.objects.get(username=username)
            print(f"👤 User '{username}' already exists")
            print(f"   - Email: {user.email}")
            print(f"   - is_staff: {user.is_staff}")
            print(f"   - is_superuser: {user.is_superuser}")
            print(f"   - is_active: {user.is_active}")
            
            # Update user to ensure admin privileges
            user.is_staff = True
            user.is_superuser = True
            user.is_active = True
            user.email = email
            user.set_password(password)  # Update password
            user.save()
            print("✅ Updated existing user with admin privileges")
            
        except User.DoesNotExist:
            # Create new admin user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password
            )
            user.is_staff = True
            user.is_superuser = True
            user.is_active = True
            user.save()
            print(f"✅ Created new admin user: {username}")
        
        # Create or update user profile
        try:
            profile, created = UserProfile.objects.get_or_create(user=user)
            profile.user_role = 'admin'
            profile.save()
            
            if created:
                print("✅ Created user profile")
            else:
                print("✅ Updated user profile")
                
        except Exception as profile_error:
            print(f"⚠️ Profile creation/update warning: {profile_error}")
        
        # Verify the user can authenticate
        from django.contrib.auth import authenticate
        auth_user = authenticate(username=username, password=password)
        
        if auth_user:
            print("✅ Authentication test successful")
            print(f"   - Username: {auth_user.username}")
            print(f"   - Email: {auth_user.email}")
            print(f"   - is_staff: {auth_user.is_staff}")
            print(f"   - is_superuser: {auth_user.is_superuser}")
            print(f"   - is_active: {auth_user.is_active}")
        else:
            print("❌ Authentication test failed")
            
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        return False
    
    return True

def list_all_users():
    """List all users in the system"""
    print("\n👥 All Users in System:")
    print("-" * 50)
    
    users = User.objects.all()
    print(f"Total users: {users.count()}")
    
    for user in users:
        role = "Admin" if user.is_staff else "User"
        status = "Active" if user.is_active else "Inactive"
        print(f"   {user.username} ({user.email}) - {role} - {status}")
        
        # Check profile
        try:
            profile = user.profile
            print(f"      Profile role: {profile.user_role}")
        except:
            print(f"      No profile")

def test_password_validation():
    """Test if the password meets Django's validation requirements"""
    print("\n🔐 Testing Password Validation...")
    
    from django.contrib.auth.password_validation import validate_password
    from django.core.exceptions import ValidationError
    
    password = "aisha2024!"
    
    try:
        validate_password(password)
        print("✅ Password meets all validation requirements")
    except ValidationError as e:
        print("❌ Password validation failed:")
        for error in e.messages:
            print(f"   - {error}")

if __name__ == "__main__":
    print("🚀 Admin User Setup Script")
    print("=" * 40)
    
    test_password_validation()
    
    if create_admin_user():
        list_all_users()
        print("\n✅ Admin user setup completed successfully!")
        print("\nYou can now login with:")
        print("   Username: aisha_admin")
        print("   Password: aisha2024!")
    else:
        print("\n❌ Admin user setup failed!")
