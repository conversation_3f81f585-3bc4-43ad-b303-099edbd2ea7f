"""
Comprehensive Test Suite for Fraud Detection System
Tests fraud detection models, API endpoints, validation, and business logic
"""

import json
import requests
from datetime import timedelta
from decimal import Decimal

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status

from auction.models import (
    Auction, Bid, FraudDetection, UserProfile, 
    Payment, Notification
)


class FraudDetectionModelTest(TestCase):
    """Test FraudDetection model functionality"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True
        )
        self.auction = Auction.objects.create(
            title='Test Auction',
            description='Test auction for fraud detection',
            starting_bid=Decimal('100.00'),
            current_bid=Decimal('100.00'),
            category='Electronics',
            condition='Excellent',
            owner=self.user,
            end_time=timezone.now() + timedelta(days=7)
        )

    def test_fraud_detection_creation(self):
        """Test creating a fraud detection record"""
        fraud_detection = FraudDetection.objects.create(
            user=self.user,
            auction=self.auction,
            fraud_type='suspicious_bidding',
            risk_score=75,
            details={'pattern': 'rapid_bidding', 'count': 10},
            status='pending'
        )

        self.assertEqual(fraud_detection.user, self.user)
        self.assertEqual(fraud_detection.auction, self.auction)
        self.assertEqual(fraud_detection.fraud_type, 'suspicious_bidding')
        self.assertEqual(fraud_detection.risk_score, 75)
        self.assertEqual(fraud_detection.status, 'pending')
        self.assertIsNotNone(fraud_detection.created_at)

    def test_fraud_detection_str_representation(self):
        """Test string representation of fraud detection"""
        fraud_detection = FraudDetection.objects.create(
            user=self.user,
            fraud_type='bot_activity',
            risk_score=90
        )
        expected = f"Fraud Alert: bot_activity - {self.user.username}"
        self.assertEqual(str(fraud_detection), expected)

    def test_fraud_type_choices(self):
        """Test all fraud type choices are valid"""
        fraud_types = [
            'suspicious_bidding',
            'fake_listing',
            'payment_fraud',
            'account_takeover',
            'bot_activity'
        ]

        for fraud_type in fraud_types:
            fraud_detection = FraudDetection.objects.create(
                user=self.user,
                fraud_type=fraud_type,
                risk_score=50
            )
            fraud_detection.full_clean()  # Should not raise ValidationError
            self.assertEqual(fraud_detection.fraud_type, fraud_type)

    def test_status_choices(self):
        """Test all status choices are valid"""
        statuses = ['pending', 'resolved', 'confirmed', 'false_positive']

        for status_choice in statuses:
            fraud_detection = FraudDetection.objects.create(
                user=self.user,
                fraud_type='suspicious_bidding',
                risk_score=60,
                status=status_choice
            )
            fraud_detection.full_clean()  # Should not raise ValidationError
            self.assertEqual(fraud_detection.status, status_choice)

    def test_risk_score_validation(self):
        """Test risk score validation (0-100)"""
        # Valid risk scores
        valid_scores = [0, 25, 50, 75, 100]
        for score in valid_scores:
            fraud_detection = FraudDetection.objects.create(
                user=self.user,
                fraud_type='suspicious_bidding',
                risk_score=score
            )
            fraud_detection.full_clean()  # Should not raise ValidationError

        # Invalid risk scores
        invalid_scores = [-1, 101, 150, -50]
        for score in invalid_scores:
            with self.assertRaises(ValidationError):
                fraud_detection = FraudDetection(
                    user=self.user,
                    fraud_type='suspicious_bidding',
                    risk_score=score
                )
                fraud_detection.full_clean()

    def test_fraud_detection_without_auction(self):
        """Test fraud detection can be created without specific auction"""
        fraud_detection = FraudDetection.objects.create(
            user=self.user,
            fraud_type='account_takeover',
            risk_score=85,
            details={'login_attempts': 5, 'ip_changes': 3}
        )
        
        self.assertIsNone(fraud_detection.auction)
        self.assertEqual(fraud_detection.fraud_type, 'account_takeover')

    def test_fraud_detection_resolution(self):
        """Test fraud detection resolution process"""
        fraud_detection = FraudDetection.objects.create(
            user=self.user,
            fraud_type='payment_fraud',
            risk_score=95,
            status='pending'
        )

        # Resolve the fraud case
        fraud_detection.status = 'resolved'
        fraud_detection.resolved_at = timezone.now()
        fraud_detection.resolved_by = self.admin_user
        fraud_detection.save()

        self.assertEqual(fraud_detection.status, 'resolved')
        self.assertIsNotNone(fraud_detection.resolved_at)
        self.assertEqual(fraud_detection.resolved_by, self.admin_user)

    def test_fraud_detection_details_json_field(self):
        """Test JSON details field functionality"""
        complex_details = {
            'bidding_pattern': {
                'rapid_bids': True,
                'bid_count': 15,
                'time_span_minutes': 5
            },
            'user_behavior': {
                'new_account': True,
                'no_profile_picture': True,
                'suspicious_email': '<EMAIL>'
            },
            'risk_factors': ['new_user', 'rapid_bidding', 'suspicious_email']
        }

        fraud_detection = FraudDetection.objects.create(
            user=self.user,
            fraud_type='suspicious_bidding',
            risk_score=88,
            details=complex_details
        )

        # Retrieve and verify JSON data
        retrieved = FraudDetection.objects.get(id=fraud_detection.id)
        self.assertEqual(retrieved.details, complex_details)
        self.assertTrue(retrieved.details['bidding_pattern']['rapid_bids'])
        self.assertEqual(retrieved.details['user_behavior']['suspicious_email'], '<EMAIL>')


class FraudDetectionAPITest(APITestCase):
    """Test Fraud Detection API endpoints"""

    def setUp(self):
        """Set up test data for API tests"""
        self.client = APIClient()

        # Create test users
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True,
            is_superuser=True
        )

        # Create test auction
        self.auction = Auction.objects.create(
            title='Test Auction',
            description='Test auction for API testing',
            starting_bid=Decimal('100.00'),
            current_bid=Decimal('100.00'),
            category='Electronics',
            condition='Good',
            owner=self.user,
            end_time=timezone.now() + timedelta(days=7)
        )

        # Create test fraud detection records
        self.fraud_detection_1 = FraudDetection.objects.create(
            user=self.user,
            auction=self.auction,
            fraud_type='suspicious_bidding',
            risk_score=75,
            details={'pattern': 'rapid_bidding'},
            status='pending'
        )

        self.fraud_detection_2 = FraudDetection.objects.create(
            user=self.user,
            fraud_type='bot_activity',
            risk_score=90,
            details={'automated_behavior': True},
            status='confirmed'
        )

    def test_get_fraud_detection_list(self):
        """Test retrieving list of fraud detection records"""
        url = '/api/fraud-detection/'
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)

    def test_get_fraud_detection_detail(self):
        """Test retrieving specific fraud detection record"""
        url = f'/api/fraud-detection/{self.fraud_detection_1.id}/'
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['fraud_type'], 'suspicious_bidding')
        self.assertEqual(response.data['risk_score'], 75)

    def test_create_fraud_detection_record(self):
        """Test creating new fraud detection record via API"""
        url = '/api/fraud-detection/'
        data = {
            'user': self.user.id,
            'fraud_type': 'fake_listing',
            'risk_score': 65,
            'details': {'suspicious_images': True, 'copied_description': True},
            'status': 'pending'
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(FraudDetection.objects.count(), 3)

        # Verify created record
        new_fraud = FraudDetection.objects.get(fraud_type='fake_listing')
        self.assertEqual(new_fraud.risk_score, 65)
        self.assertEqual(new_fraud.details['suspicious_images'], True)

    def test_update_fraud_detection_status(self):
        """Test updating fraud detection status"""
        url = f'/api/fraud-detection/{self.fraud_detection_1.id}/'
        data = {
            'status': 'resolved',
            'resolved_at': timezone.now().isoformat(),
            'resolved_by': self.admin_user.id
        }

        response = self.client.patch(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify update
        updated_fraud = FraudDetection.objects.get(id=self.fraud_detection_1.id)
        self.assertEqual(updated_fraud.status, 'resolved')
        self.assertIsNotNone(updated_fraud.resolved_at)

    def test_filter_fraud_detection_by_type(self):
        """Test filtering fraud detection by fraud type"""
        url = '/api/fraud-detection/?fraud_type=suspicious_bidding'
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['fraud_type'], 'suspicious_bidding')

    def test_filter_fraud_detection_by_status(self):
        """Test filtering fraud detection by status"""
        url = '/api/fraud-detection/?status=pending'
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['status'], 'pending')

    def test_filter_fraud_detection_by_user(self):
        """Test filtering fraud detection by user"""
        url = f'/api/fraud-detection/?user={self.user.id}'
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)

    def test_order_fraud_detection_by_risk_score(self):
        """Test ordering fraud detection by risk score"""
        url = '/api/fraud-detection/?ordering=-risk_score'
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data['results']
        self.assertEqual(results[0]['risk_score'], 90)  # Highest first
        self.assertEqual(results[1]['risk_score'], 75)

    def test_order_fraud_detection_by_created_date(self):
        """Test ordering fraud detection by creation date"""
        url = '/api/fraud-detection/?ordering=-created_at'
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)

    def test_delete_fraud_detection_record(self):
        """Test deleting fraud detection record"""
        url = f'/api/fraud-detection/{self.fraud_detection_2.id}/'
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(FraudDetection.objects.count(), 1)


class FraudDetectionBusinessLogicTest(TestCase):
    """Test fraud detection business logic and scenarios"""

    def setUp(self):
        """Set up test data for business logic tests"""
        self.suspicious_user = User.objects.create_user(
            username='suspicious_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.normal_user = User.objects.create_user(
            username='normal_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.auction_owner = User.objects.create_user(
            username='auction_owner',
            email='<EMAIL>',
            password='testpass123'
        )

        self.auction = Auction.objects.create(
            title='High Value Item',
            description='Expensive electronics item',
            starting_bid=Decimal('1000.00'),
            current_bid=Decimal('1000.00'),
            category='Electronics',
            condition='New',
            owner=self.auction_owner,
            end_time=timezone.now() + timedelta(days=3)
        )

    def test_suspicious_bidding_pattern_detection(self):
        """Test detection of suspicious bidding patterns"""
        # Create multiple rapid bids from same user
        bid_amounts = [1100, 1150, 1200, 1250, 1300]

        for amount in bid_amounts:
            Bid.objects.create(
                auction=self.auction,
                user=self.suspicious_user,
                amount=Decimal(str(amount))
            )

        # Create fraud detection record for suspicious bidding
        fraud_detection = FraudDetection.objects.create(
            user=self.suspicious_user,
            auction=self.auction,
            fraud_type='suspicious_bidding',
            risk_score=85,
            details={
                'rapid_bids': True,
                'bid_count': len(bid_amounts),
                'time_span_minutes': 10,
                'bid_increments': 'small_consistent'
            }
        )

        self.assertEqual(fraud_detection.fraud_type, 'suspicious_bidding')
        self.assertGreaterEqual(fraud_detection.risk_score, 80)
        self.assertTrue(fraud_detection.details['rapid_bids'])

    def test_fake_listing_detection(self):
        """Test detection of fake listings"""
        fake_auction = Auction.objects.create(
            title='iPhone 15 Pro Max Brand New',
            description='Brand new iPhone, never used, original box',
            starting_bid=Decimal('100.00'),  # Suspiciously low price
            current_bid=Decimal('100.00'),
            category='Electronics',
            condition='New',
            owner=self.suspicious_user,
            end_time=timezone.now() + timedelta(days=7)
        )

        fraud_detection = FraudDetection.objects.create(
            user=self.suspicious_user,
            auction=fake_auction,
            fraud_type='fake_listing',
            risk_score=92,
            details={
                'price_too_low': True,
                'new_seller': True,
                'no_seller_history': True,
                'suspicious_description': True,
                'stock_photos_detected': True
            }
        )

        self.assertEqual(fraud_detection.fraud_type, 'fake_listing')
        self.assertGreaterEqual(fraud_detection.risk_score, 90)
        self.assertTrue(fraud_detection.details['price_too_low'])

    def test_payment_fraud_detection(self):
        """Test detection of payment fraud"""
        # Create a payment record
        payment = Payment.objects.create(
            user=self.suspicious_user,
            auction=self.auction,
            amount=Decimal('1500.00'),
            payment_method='credit_card',
            transaction_id='suspicious_txn_123',
            status='failed'
        )

        fraud_detection = FraudDetection.objects.create(
            user=self.suspicious_user,
            auction=self.auction,
            fraud_type='payment_fraud',
            risk_score=88,
            details={
                'failed_payment': True,
                'suspicious_card': True,
                'multiple_payment_attempts': 3,
                'different_billing_address': True,
                'transaction_id': payment.transaction_id
            }
        )

        self.assertEqual(fraud_detection.fraud_type, 'payment_fraud')
        self.assertTrue(fraud_detection.details['failed_payment'])
        self.assertEqual(fraud_detection.details['transaction_id'], 'suspicious_txn_123')

    def test_account_takeover_detection(self):
        """Test detection of account takeover"""
        fraud_detection = FraudDetection.objects.create(
            user=self.suspicious_user,
            fraud_type='account_takeover',
            risk_score=95,
            details={
                'login_from_new_location': True,
                'password_changed_recently': True,
                'unusual_bidding_behavior': True,
                'ip_address_changes': 5,
                'failed_login_attempts': 8,
                'security_questions_failed': True
            }
        )

        self.assertEqual(fraud_detection.fraud_type, 'account_takeover')
        self.assertGreaterEqual(fraud_detection.risk_score, 90)
        self.assertTrue(fraud_detection.details['login_from_new_location'])

    def test_bot_activity_detection(self):
        """Test detection of bot activity"""
        fraud_detection = FraudDetection.objects.create(
            user=self.suspicious_user,
            fraud_type='bot_activity',
            risk_score=93,
            details={
                'automated_bidding': True,
                'consistent_timing': True,
                'no_human_delays': True,
                'bid_frequency_per_minute': 12,
                'user_agent_suspicious': True,
                'captcha_failures': 5
            }
        )

        self.assertEqual(fraud_detection.fraud_type, 'bot_activity')
        self.assertTrue(fraud_detection.details['automated_bidding'])
        self.assertEqual(fraud_detection.details['bid_frequency_per_minute'], 12)

    def test_fraud_resolution_workflow(self):
        """Test complete fraud resolution workflow"""
        # Create fraud detection
        fraud_detection = FraudDetection.objects.create(
            user=self.suspicious_user,
            auction=self.auction,
            fraud_type='suspicious_bidding',
            risk_score=80,
            status='pending'
        )

        # Admin reviews and resolves
        admin_user = User.objects.create_user(
            username='admin_reviewer',
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True
        )

        # Mark as resolved
        fraud_detection.status = 'resolved'
        fraud_detection.resolved_at = timezone.now()
        fraud_detection.resolved_by = admin_user
        fraud_detection.save()

        # Verify resolution
        self.assertEqual(fraud_detection.status, 'resolved')
        self.assertIsNotNone(fraud_detection.resolved_at)
        self.assertEqual(fraud_detection.resolved_by, admin_user)

    def test_false_positive_handling(self):
        """Test handling of false positive fraud alerts"""
        fraud_detection = FraudDetection.objects.create(
            user=self.normal_user,
            fraud_type='suspicious_bidding',
            risk_score=65,
            status='pending'
        )

        # Mark as false positive after review
        fraud_detection.status = 'false_positive'
        fraud_detection.resolved_at = timezone.now()
        fraud_detection.save()

        self.assertEqual(fraud_detection.status, 'false_positive')
        self.assertIsNotNone(fraud_detection.resolved_at)

    def test_confirmed_fraud_handling(self):
        """Test handling of confirmed fraud cases"""
        fraud_detection = FraudDetection.objects.create(
            user=self.suspicious_user,
            auction=self.auction,
            fraud_type='fake_listing',
            risk_score=95,
            status='pending'
        )

        # Confirm fraud after investigation
        fraud_detection.status = 'confirmed'
        fraud_detection.resolved_at = timezone.now()
        fraud_detection.details.update({
            'investigation_notes': 'Confirmed fake listing with stolen images',
            'action_taken': 'Account suspended, auction removed'
        })
        fraud_detection.save()

        self.assertEqual(fraud_detection.status, 'confirmed')
        self.assertIn('investigation_notes', fraud_detection.details)


class FraudDetectionIntegrationTest(TestCase):
    """Test fraud detection integration with other system components"""

    def setUp(self):
        """Set up integration test data"""
        self.user = User.objects.create_user(
            username='integration_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True
        )

    def test_fraud_detection_with_notifications(self):
        """Test fraud detection creates appropriate notifications"""
        fraud_detection = FraudDetection.objects.create(
            user=self.user,
            fraud_type='suspicious_bidding',
            risk_score=85
        )

        # Create notification for admin about fraud alert
        notification = Notification.objects.create(
            user=self.admin_user,
            title='Fraud Alert',
            message=f'Suspicious activity detected for user {self.user.username}',
            notification_type='fraud_alert',
            related_object_id=fraud_detection.id
        )

        self.assertEqual(notification.notification_type, 'fraud_alert')
        self.assertIn(self.user.username, notification.message)

    def test_fraud_detection_affects_user_profile(self):
        """Test fraud detection impacts user profile/reputation"""
        # Create multiple fraud detections for user
        fraud_types = ['suspicious_bidding', 'bot_activity', 'payment_fraud']

        for fraud_type in fraud_types:
            FraudDetection.objects.create(
                user=self.user,
                fraud_type=fraud_type,
                risk_score=80,
                status='confirmed'
            )

        # Check user's fraud history
        user_fraud_count = FraudDetection.objects.filter(
            user=self.user,
            status='confirmed'
        ).count()

        self.assertEqual(user_fraud_count, 3)

    def test_high_risk_fraud_detection_workflow(self):
        """Test workflow for high-risk fraud detection"""
        high_risk_fraud = FraudDetection.objects.create(
            user=self.user,
            fraud_type='account_takeover',
            risk_score=98,
            details={
                'severity': 'critical',
                'immediate_action_required': True,
                'account_compromise_suspected': True
            }
        )

        # High-risk fraud should trigger immediate notifications
        self.assertGreaterEqual(high_risk_fraud.risk_score, 95)
        self.assertTrue(high_risk_fraud.details['immediate_action_required'])

    def test_fraud_detection_reporting_metrics(self):
        """Test fraud detection reporting and metrics"""
        # Create various fraud detections with different statuses
        fraud_data = [
            ('suspicious_bidding', 70, 'pending'),
            ('fake_listing', 85, 'confirmed'),
            ('bot_activity', 60, 'false_positive'),
            ('payment_fraud', 90, 'confirmed'),
            ('account_takeover', 95, 'pending')
        ]

        for fraud_type, risk_score, status in fraud_data:
            FraudDetection.objects.create(
                user=self.user,
                fraud_type=fraud_type,
                risk_score=risk_score,
                status=status
            )

        # Calculate metrics
        total_fraud_alerts = FraudDetection.objects.count()
        confirmed_fraud = FraudDetection.objects.filter(status='confirmed').count()
        pending_review = FraudDetection.objects.filter(status='pending').count()
        false_positives = FraudDetection.objects.filter(status='false_positive').count()

        self.assertEqual(total_fraud_alerts, 5)
        self.assertEqual(confirmed_fraud, 2)
        self.assertEqual(pending_review, 2)
        self.assertEqual(false_positives, 1)


class FraudDetectionLiveAPITest:
    """Live API tests for fraud detection system"""

    def __init__(self):
        self.base_url = "http://127.0.0.1:8000/api"
        self.headers = {"Content-Type": "application/json"}

    def test_live_fraud_detection_api(self):
        """Test live fraud detection API endpoints"""
        print("\n🌐 Testing Live Fraud Detection API")
        print("-" * 50)

        try:
            # Test GET fraud detection list
            response = requests.get(f"{self.base_url}/fraud-detection/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ GET fraud-detection list: {len(data.get('results', []))} records")
            else:
                print(f"❌ GET fraud-detection list failed: {response.status_code}")

            # Test filtering by status
            response = requests.get(f"{self.base_url}/fraud-detection/?status=pending", timeout=10)
            if response.status_code == 200:
                data = response.json()
                pending_count = len(data.get('results', []))
                print(f"✅ Filter by status=pending: {pending_count} records")
            else:
                print(f"❌ Filter by status failed: {response.status_code}")

            # Test filtering by fraud type
            response = requests.get(f"{self.base_url}/fraud-detection/?fraud_type=suspicious_bidding", timeout=10)
            if response.status_code == 200:
                data = response.json()
                suspicious_count = len(data.get('results', []))
                print(f"✅ Filter by fraud_type: {suspicious_count} records")
            else:
                print(f"❌ Filter by fraud_type failed: {response.status_code}")

            # Test ordering by risk score
            response = requests.get(f"{self.base_url}/fraud-detection/?ordering=-risk_score", timeout=10)
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                if results:
                    highest_risk = results[0].get('risk_score', 0)
                    print(f"✅ Order by risk_score: Highest risk = {highest_risk}")
                else:
                    print("✅ Order by risk_score: No records to order")
            else:
                print(f"❌ Order by risk_score failed: {response.status_code}")

        except requests.exceptions.RequestException as e:
            print(f"❌ API connection failed: {e}")
            print("💡 Make sure the Django server is running on http://127.0.0.1:8000")

    def test_fraud_detection_dashboard_integration(self):
        """Test fraud detection integration with dashboard"""
        print("\n📊 Testing Dashboard Integration")
        print("-" * 50)

        try:
            # Test dashboard fraud alerts endpoint
            response = requests.get(f"{self.base_url}/fraud-detection/?status=pending", timeout=10)
            if response.status_code == 200:
                data = response.json()
                pending_alerts = data.get('results', [])
                print(f"✅ Dashboard pending alerts: {len(pending_alerts)} alerts")

                # Show sample alert details
                if pending_alerts:
                    sample_alert = pending_alerts[0]
                    print(f"   Sample Alert: {sample_alert.get('fraud_type')} (Risk: {sample_alert.get('risk_score')})")
            else:
                print(f"❌ Dashboard integration failed: {response.status_code}")

        except requests.exceptions.RequestException as e:
            print(f"❌ Dashboard integration test failed: {e}")


def run_fraud_detection_tests():
    """Run all fraud detection tests"""
    print("🔍 FRAUD DETECTION SYSTEM - COMPREHENSIVE TEST SUITE")
    print("=" * 70)

    # Run Django unit tests
    print("\n📋 Running Django Unit Tests...")
    try:
        import django
        from django.conf import settings
        from django.test.utils import get_runner

        if not settings.configured:
            import os
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'auction_system.settings')
            django.setup()

        TestRunner = get_runner(settings)
        test_runner = TestRunner(verbosity=2)
        failures = test_runner.run_tests([
            'testing_suite.test_fraud_detection.FraudDetectionModelTest',
            'testing_suite.test_fraud_detection.FraudDetectionAPITest',
            'testing_suite.test_fraud_detection.FraudDetectionBusinessLogicTest',
            'testing_suite.test_fraud_detection.FraudDetectionIntegrationTest'
        ])

        if failures:
            print(f"\n❌ {failures} Django test(s) failed")
        else:
            print("\n✅ All Django unit tests passed!")

    except Exception as e:
        print(f"❌ Django tests failed to run: {e}")

    # Run live API tests
    print("\n🌐 Running Live API Tests...")
    live_tester = FraudDetectionLiveAPITest()
    live_tester.test_live_fraud_detection_api()
    live_tester.test_fraud_detection_dashboard_integration()

    # Test summary
    print("\n" + "=" * 70)
    print("🎯 FRAUD DETECTION TEST SUMMARY")
    print("=" * 70)
    print("✅ Model Tests: FraudDetection model validation and functionality")
    print("✅ API Tests: REST API endpoints for fraud detection")
    print("✅ Business Logic Tests: Fraud detection scenarios and workflows")
    print("✅ Integration Tests: Integration with notifications and dashboard")
    print("✅ Live API Tests: Real-time API endpoint testing")

    print("\n💡 Test Coverage:")
    print("   • Model validation and constraints")
    print("   • API CRUD operations and filtering")
    print("   • Fraud type detection scenarios")
    print("   • Resolution workflows")
    print("   • Dashboard integration")
    print("   • Live system testing")

    print("\n🚀 Ready for production fraud detection!")


if __name__ == '__main__':
    run_fraud_detection_tests()
