{"ast": null, "code": "export default function maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null && (max < value || max === undefined && value >= value)) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}