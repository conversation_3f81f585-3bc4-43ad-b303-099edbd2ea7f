"""
Django management command to cleanup expired auctions
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta

from auction.models import Auction, Bid


class Command(BaseCommand):
    help = 'Cleanup expired auctions and handle post-auction processes'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days after expiration to keep auction data'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        days_to_keep = options['days']

        now = timezone.now()
        cutoff_date = now - timedelta(days=days_to_keep)

        self.stdout.write(f'Processing expired auctions...')
        self.stdout.write(f'Current time: {now}')
        self.stdout.write(f'Cutoff date: {cutoff_date}')

        # Find recently expired auctions that need to be processed
        recently_expired = Auction.objects.filter(
            end_time__lt=now,
            end_time__gte=now - timedelta(hours=24),
            is_active=True
        )

        self.stdout.write(f'Found {recently_expired.count()} recently expired auctions to process')

        for auction in recently_expired:
            self.process_expired_auction(auction, dry_run)

        # Find old expired auctions for cleanup
        old_expired = Auction.objects.filter(
            end_time__lt=cutoff_date,
            is_active=False
        )

        self.stdout.write(f'Found {old_expired.count()} old auctions for cleanup')

        if old_expired.exists():
            if dry_run:
                self.stdout.write('Would delete old auction data (dry run)')
            else:
                # Archive or delete old auction data
                self.cleanup_old_auctions(old_expired)

        self.stdout.write(
            self.style.SUCCESS('Auction cleanup completed!')
        )

    def process_expired_auction(self, auction, dry_run):
        """Process a recently expired auction"""
        self.stdout.write(f'Processing auction: {auction.title}')

        # Get the winning bid
        winning_bid = auction.bids.order_by('-amount').first()

        if winning_bid:
            self.stdout.write(f'  Winner: {winning_bid.bidder.username} with bid ₹{winning_bid.amount}')
            
            if not dry_run:
                # Mark auction as completed
                auction.is_active = False
                auction.winner = winning_bid.bidder
                auction.final_price = winning_bid.amount
                auction.save()

                # Send notification to winner and seller
                self.send_auction_completion_notifications(auction, winning_bid)
        else:
            self.stdout.write(f'  No bids received for auction: {auction.title}')
            
            if not dry_run:
                # Mark auction as expired without winner
                auction.is_active = False
                auction.save()

    def cleanup_old_auctions(self, old_auctions):
        """Cleanup old auction data"""
        for auction in old_auctions:
            self.stdout.write(f'Archiving auction: {auction.title}')
            
            # You might want to:
            # 1. Move to archive table
            # 2. Delete associated files
            # 3. Clean up related data
            
            # For now, just mark as archived
            auction.is_archived = True
            auction.save()

    def send_auction_completion_notifications(self, auction, winning_bid):
        """Send notifications when auction completes"""
        # This would integrate with your notification system
        self.stdout.write(f'  Sending notifications for completed auction: {auction.title}')
        
        # Example notification logic:
        # - Email to winner with payment instructions
        # - Email to seller with winner details
        # - Update user dashboards
        # - Create notification records
