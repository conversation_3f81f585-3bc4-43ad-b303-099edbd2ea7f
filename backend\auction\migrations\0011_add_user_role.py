# Generated by Django 5.1.5 on 2025-06-02 15:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("auction", "0010_alter_payment_currency_alter_payment_payment_method"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="userprofile",
            name="user_role",
            field=models.CharField(
                choices=[
                    ("bidder", "Bidder"),
                    ("seller", "Seller"),
                    ("both", "Both (Seller & Bidder)"),
                ],
                default="bidder",
                max_length=10,
            ),
        ),
        migrations.AlterField(
            model_name="chatmessage",
            name="message_type",
            field=models.CharField(
                choices=[
                    ("text", "Text Message"),
                    ("system", "System Message"),
                    ("bid_alert", "Bid Alert"),
                    ("auction_update", "Auction Update"),
                    ("ai_response", "AI Response"),
                ],
                default="text",
                max_length=20,
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="chatmessage",
            name="sender",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="sent_messages",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
