@echo off
echo ========================================
echo    Stopping Online Auction System
echo ========================================
echo.

echo Stopping all services...

echo Stopping Django Backend...
taskkill /f /im python.exe >nul 2>&1
if not errorlevel 1 (
    echo SUCCESS: Djan<PERSON> Backend stopped
) else (
    echo INFO: Django Backend was not running
)

echo Stopping React Frontend...
taskkill /f /im node.exe >nul 2>&1
if not errorlevel 1 (
    echo SUCCESS: React Frontend stopped
) else (
    echo INFO: React Frontend was not running
)

echo Stopping Celery processes...
taskkill /f /im celery.exe >nul 2>&1
if not errorlevel 1 (
    echo SUCCESS: Celery processes stopped
) else (
    echo INFO: Celery processes were not running
)

echo Closing service windows...
taskkill /f /fi "WindowTitle eq Django Backend*" >nul 2>&1
taskkill /f /fi "WindowTitle eq React Frontend*" >nul 2>&1
taskkill /f /fi "WindowTitle eq Celery Worker*" >nul 2>&1
taskkill /f /fi "WindowTitle eq Celery Beat*" >nul 2>&1
taskkill /f /fi "WindowTitle eq Redis Server*" >nul 2>&1

echo.
echo ========================================
echo All services stopped successfully!
echo ========================================
echo.
echo NOTE: Redis and PostgreSQL services are still running
echo       (These can remain active for other applications)
echo.
echo Press any key to exit...
pause >nul
