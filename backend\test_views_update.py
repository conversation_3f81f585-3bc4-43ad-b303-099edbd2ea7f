#!/usr/bin/env python
"""
Test script to add some views to existing auctions and verify analytics update
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from auction.models import Auction
from auction.analytics_services import advanced_analytics_service
from django.db.models import F

def test_views_tracking():
    """Test that views are being tracked and analytics are updated"""
    
    print("🔍 Testing Views Tracking System...")
    print("=" * 50)
    
    # Get current analytics
    print("📊 Getting current analytics...")
    current_analytics = advanced_analytics_service.get_comprehensive_dashboard_data(use_cache=False)
    current_views = current_analytics['basic_metrics']['total_views']
    print(f"Current total views: {current_views}")
    
    # Get all auctions
    auctions = Auction.objects.all()
    print(f"Found {auctions.count()} auctions")
    
    if auctions.count() == 0:
        print("❌ No auctions found! Create some auctions first.")
        return
    
    # Add some views to auctions
    print("\n👀 Adding views to auctions...")
    views_added = 0
    
    for auction in auctions[:5]:  # Update first 5 auctions
        # Add 10-50 random views to each auction
        import random
        new_views = random.randint(10, 50)
        
        # Update views using F() expression (same as in the view)
        Auction.objects.filter(id=auction.id).update(
            views_count=F('views_count') + new_views
        )
        
        # Refresh to get updated count
        auction.refresh_from_db()
        views_added += new_views
        
        print(f"  ✅ {auction.title[:30]}... - Added {new_views} views (Total: {auction.views_count})")
    
    print(f"\n📈 Total views added: {views_added}")
    
    # Force analytics refresh
    print("\n🔄 Refreshing analytics...")
    advanced_analytics_service.invalidate_cache()
    updated_analytics = advanced_analytics_service.get_comprehensive_dashboard_data(use_cache=False)
    updated_views = updated_analytics['basic_metrics']['total_views']
    
    print(f"Updated total views: {updated_views}")
    print(f"Expected increase: {views_added}")
    print(f"Actual increase: {updated_views - current_views}")
    
    # Verify the update
    if updated_views > current_views:
        print("✅ SUCCESS: Views tracking is working!")
        print(f"   Views increased from {current_views} to {updated_views}")
    else:
        print("❌ ISSUE: Views not updating properly")
        print("   Check the analytics calculation logic")
    
    print("\n" + "=" * 50)
    print("🎯 Test completed!")
    
    # Show breakdown by auction
    print("\n📋 Current auction views breakdown:")
    for auction in auctions.order_by('-views_count')[:10]:
        print(f"  {auction.title[:40]:<40} - {auction.views_count:>6} views")

if __name__ == "__main__":
    test_views_tracking()
