.quick-admin-login {
  background: var(--gradient-primary);
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  color: white;
  box-shadow: var(--shadow-lg);
  text-align: center;
}

.admin-login-card {
  max-width: 400px;
  margin: 0 auto;
}

.admin-login-card h4 {
  margin-bottom: 10px;
  font-size: 1.5em;
  font-weight: 600;
}

.admin-login-card p {
  margin-bottom: 20px;
  opacity: 0.9;
}

.btn-admin {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  margin-bottom: 20px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-admin:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  color: white;
}

.btn-admin:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.admin-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
  text-align: left;
  backdrop-filter: blur(10px);
}

.admin-info small {
  line-height: 1.6;
  opacity: 0.9;
}

/* Admin Logged In State */
.quick-admin-login.admin-logged-in {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.admin-status h4 {
  margin-bottom: 10px;
  font-size: 1.4em;
}

.admin-status p {
  margin-bottom: 20px;
  font-size: 1.1em;
  opacity: 0.9;
}

.admin-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 300px;
  margin: 0 auto;
}

.admin-links .btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.admin-links .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.admin-links .btn-primary {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.admin-links .btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
}

.admin-links .btn-secondary {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.25);
  color: white;
}

.admin-links .btn-secondary:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  color: white;
}

.admin-links .btn-info {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.admin-links .btn-info:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.35);
  color: white;
  text-decoration: none;
}

.alert {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 6px;
  background: rgba(220, 53, 69, 0.2);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: white;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.15em;
}

/* Responsive Design */
@media (max-width: 768px) {
  .quick-admin-login {
    padding: 15px;
    margin: 15px 0;
  }

  .admin-login-card {
    max-width: 100%;
  }

  .admin-login-card h4 {
    font-size: 1.3em;
  }

  .btn-admin {
    padding: 10px 25px;
    font-size: 1em;
  }

  .admin-links {
    max-width: 100%;
  }

  .admin-links .btn {
    padding: 8px 16px;
    font-size: 0.9em;
  }
}
