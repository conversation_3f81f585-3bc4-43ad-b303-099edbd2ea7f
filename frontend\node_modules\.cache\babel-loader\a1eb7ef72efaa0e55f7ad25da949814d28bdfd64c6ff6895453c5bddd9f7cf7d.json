{"ast": null, "code": "export default function pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\nexport function pair(a, b) {\n  return [a, b];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}