# Testing Requirements for Online Auction System
# Install with: pip install -r tests/requirements.txt

# Core testing frameworks
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0
pytest-html==4.1.1
pytest-xdist==3.5.0

# Selenium and web testing
selenium==4.15.2
webdriver-manager==4.0.1
pytest-selenium==4.1.0

# API testing
requests==2.31.0
responses==0.24.1

# Test data and factories
factory-boy==3.3.0
faker==20.1.0

# Performance testing
locust==2.17.0

# Code quality and coverage
coverage==7.3.2
pytest-mock==3.12.0

# Database testing
pytest-postgresql==5.0.0

# Reporting and documentation
allure-pytest==2.13.2
pytest-json-report==1.5.0

# Load testing
artillery==1.7.9

# Browser automation utilities
pyautogui==0.9.54
pillow==10.1.0

# Async testing
pytest-asyncio==0.21.1

# Environment management
python-dotenv==1.0.0

# Date/time utilities for testing
freezegun==1.2.2

# Mock and stub utilities
responses==0.24.1
httpretty==1.1.4

# Security testing
bandit==1.7.5

# Performance profiling
py-spy==0.3.14
memory-profiler==0.61.0
