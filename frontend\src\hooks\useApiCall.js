/**
 * React Hook for making API calls with global management
 * Provides loading states, error handling, and automatic cleanup
 */

import { useState, useEffect, useCallback, useRef } from "react";
import globalApiManager from "../utils/globalApiManager";
import axiosInstance from "../api/axiosInstance";

export const useApiCall = (url, options = {}) => {
  const {
    method = "GET",
    params = {},
    data = null,
    autoFetch = true,
    cacheTime = 5 * 60 * 1000, // 5 minutes
    dependencies = [],
    onSuccess = null,
    onError = null,
    ...apiOptions
  } = options;

  const [state, setState] = useState({
    data: null,
    loading: false,
    error: null,
    lastFetch: null,
  });

  const isMountedRef = useRef(true);
  const requestKeyRef = useRef(null);

  // Generate request key
  const requestKey = globalApiManager.generateRequestKey(url, method, params);
  requestKeyRef.current = requestKey;

  // Update state safely (only if component is mounted)
  const updateState = useCallback((updates) => {
    if (isMountedRef.current) {
      setState((prev) => ({ ...prev, ...updates }));
    }
  }, []);

  // Execute API call
  const execute = useCallback(
    async (overrideOptions = {}) => {
      if (!isMountedRef.current) return;

      const finalOptions = {
        method,
        params,
        data,
        cacheTime,
        ...apiOptions,
        ...overrideOptions,
      };

      updateState({ loading: true, error: null });

      try {
        console.log(`🚀 Executing API call: ${method} ${url}`);

        const result = await globalApiManager.makeRequest(
          axiosInstance,
          url,
          finalOptions
        );

        if (isMountedRef.current) {
          updateState({
            data: result,
            loading: false,
            error: null,
            lastFetch: Date.now(),
          });

          if (onSuccess) {
            onSuccess(result);
          }
        }

        return result;
      } catch (error) {
        console.error(`❌ API call failed: ${method} ${url}`, error);

        if (isMountedRef.current) {
          updateState({
            loading: false,
            error: error.message || "An error occurred",
            lastFetch: Date.now(),
          });

          if (onError) {
            onError(error);
          }
        }

        throw error;
      }
    },
    [
      url,
      method,
      JSON.stringify(params),
      JSON.stringify(data),
      cacheTime,
      onSuccess,
      onError,
      updateState,
    ]
  );

  // Refresh function
  const refresh = useCallback(() => {
    return execute({ skipCache: true });
  }, [execute]);

  // Clear cache for this request
  const clearCache = useCallback(() => {
    globalApiManager.clearCache(requestKey);
  }, [requestKey]);

  // Auto-fetch on mount and dependency changes
  useEffect(() => {
    if (autoFetch && url) {
      execute();
    }
  }, [autoFetch, url, execute, ...dependencies]);

  // Subscribe to global API manager updates
  useEffect(() => {
    if (!url) return;

    const unsubscribe = globalApiManager.subscribe(
      requestKey,
      ({ data, isError, error }) => {
        if (!isMountedRef.current) return;

        if (isError) {
          updateState({
            loading: false,
            error: error?.message || "An error occurred",
          });
        } else if (data) {
          updateState({
            data,
            loading: false,
            error: null,
            lastFetch: Date.now(),
          });
        }
      }
    );

    return unsubscribe;
  }, [requestKey, updateState]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return {
    ...state,
    execute,
    refresh,
    clearCache,
    isLoading: state.loading,
    hasError: !!state.error,
    hasData: !!state.data,
  };
};

// Hook for multiple API calls
export const useMultipleApiCalls = (requests = []) => {
  const [state, setState] = useState({
    data: {},
    loading: {},
    errors: {},
    allLoading: false,
    hasErrors: false,
  });

  const isMountedRef = useRef(true);

  const updateState = useCallback((updates) => {
    if (isMountedRef.current) {
      setState((prev) => ({ ...prev, ...updates }));
    }
  }, []);

  const executeAll = useCallback(async () => {
    if (!requests.length) return;

    // Set all as loading
    const loadingState = {};
    requests.forEach((req) => {
      loadingState[req.key] = true;
    });

    updateState({
      loading: loadingState,
      allLoading: true,
      errors: {},
      hasErrors: false,
    });

    const promises = requests.map(async (request) => {
      try {
        const result = await globalApiManager.makeRequest(
          axiosInstance,
          request.url,
          request.options || {}
        );

        return { key: request.key, data: result, error: null };
      } catch (error) {
        console.error(`❌ Multi-API call failed for ${request.key}:`, error);
        return { key: request.key, data: null, error: error.message };
      }
    });

    try {
      const results = await Promise.allSettled(promises);

      if (!isMountedRef.current) return;

      const newData = {};
      const newLoading = {};
      const newErrors = {};
      let hasErrors = false;

      results.forEach((result, index) => {
        const request = requests[index];
        const key = request.key;

        newLoading[key] = false;

        if (result.status === "fulfilled") {
          const { data, error } = result.value;
          newData[key] = data;
          if (error) {
            newErrors[key] = error;
            hasErrors = true;
          }
        } else {
          newErrors[key] = result.reason?.message || "Unknown error";
          hasErrors = true;
        }
      });

      updateState({
        data: newData,
        loading: newLoading,
        errors: newErrors,
        allLoading: false,
        hasErrors,
      });
    } catch (error) {
      console.error("❌ Multi-API execution failed:", error);
      updateState({
        allLoading: false,
        hasErrors: true,
      });
    }
  }, [requests, updateState]);

  // Execute on mount
  useEffect(() => {
    if (requests.length > 0) {
      executeAll();
    }
  }, [executeAll]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return {
    ...state,
    executeAll,
    getDataFor: (key) => state.data[key],
    getErrorFor: (key) => state.errors[key],
    isLoadingFor: (key) => state.loading[key],
  };
};

// Hook for paginated API calls
export const usePaginatedApiCall = (url, options = {}) => {
  const { pageSize = 20, initialPage = 1, ...otherOptions } = options;

  const [pagination, setPagination] = useState({
    currentPage: initialPage,
    totalPages: 1,
    totalItems: 0,
    hasNext: false,
    hasPrevious: false,
  });

  const apiCall = useApiCall(url, {
    ...otherOptions,
    params: {
      ...otherOptions.params,
      page: pagination.currentPage,
      page_size: pageSize,
    },
    dependencies: [pagination.currentPage],
    onSuccess: (data) => {
      if (data.count !== undefined) {
        const totalPages = Math.ceil(data.count / pageSize);
        setPagination((prev) => ({
          ...prev,
          totalPages,
          totalItems: data.count,
          hasNext: prev.currentPage < totalPages,
          hasPrevious: prev.currentPage > 1,
        }));
      }
      if (otherOptions.onSuccess) {
        otherOptions.onSuccess(data);
      }
    },
  });

  const goToPage = useCallback((page) => {
    setPagination((prev) => ({
      ...prev,
      currentPage: Math.max(1, Math.min(page, prev.totalPages)),
    }));
  }, []);

  const nextPage = useCallback(() => {
    if (pagination.hasNext) {
      goToPage(pagination.currentPage + 1);
    }
  }, [pagination.hasNext, pagination.currentPage, goToPage]);

  const previousPage = useCallback(() => {
    if (pagination.hasPrevious) {
      goToPage(pagination.currentPage - 1);
    }
  }, [pagination.hasPrevious, pagination.currentPage, goToPage]);

  return {
    ...apiCall,
    pagination,
    goToPage,
    nextPage,
    previousPage,
  };
};

export default useApiCall;
