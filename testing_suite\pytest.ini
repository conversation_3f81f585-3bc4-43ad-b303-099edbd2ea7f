[tool:pytest]
# PyTest Configuration for Online Auction System

# Django settings
DJANGO_SETTINGS_MODULE = OnlineAuctionSystem.settings
python_files = tests.py test_*.py *_tests.py
python_classes = Test* *Tests
python_functions = test_*

# Test discovery
testpaths = 
    backend/tests
    tests

# Markers for test categorization
markers =
    unit: Unit tests
    integration: Integration tests
    api: API tests
    selenium: Selenium automation tests
    performance: Performance tests
    security: Security tests
    smoke: Smoke tests
    regression: Regression tests
    slow: Slow running tests
    fast: Fast running tests

# Output options
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=auction
    --cov-report=html:test_results/coverage_html
    --cov-report=xml:test_results/coverage.xml
    --cov-report=term-missing
    --html=test_results/report.html
    --self-contained-html
    --json-report
    --json-report-file=test_results/report.json

# Minimum coverage percentage
cov-fail-under = 80

# Parallel execution
# addopts = -n auto  # Uncomment for parallel execution

# Filtering
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# Test timeout (in seconds)
timeout = 300

# Django database settings for tests
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}
