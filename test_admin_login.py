#!/usr/bin/env python3
"""
Test script to debug admin login issues
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_admin_login():
    """Test admin login functionality"""
    print("🧪 Testing Admin Login...")
    
    # Test credentials from QuickAdminLogin component
    admin_credentials = {
        "username": "aisha_admin",
        "password": "aisha2024!"
    }
    
    print(f"📝 Testing with credentials: {admin_credentials['username']}")
    
    try:
        # Make login request
        response = requests.post(
            f"{BASE_URL}/login/",
            json=admin_credentials,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"🔑 Access Token: {data.get('access', 'N/A')[:50]}...")
            print(f"🔄 Refresh Token: {data.get('refresh', 'N/A')[:50]}...")
            print(f"👤 User Info: {json.dumps(data.get('user', {}), indent=2)}")
            
            # Test if user has admin privileges
            user_info = data.get('user', {})
            print(f"\n🔍 Admin Status Check:")
            print(f"   - is_staff: {user_info.get('is_staff', False)}")
            print(f"   - is_superuser: {user_info.get('is_superuser', False)}")
            print(f"   - role: {user_info.get('role', 'N/A')}")
            print(f"   - user_role: {user_info.get('user_role', 'N/A')}")
            
        else:
            print("❌ Login failed!")
            print(f"📄 Response Text: {response.text}")
            
            try:
                error_data = response.json()
                print(f"📄 Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print("📄 Could not parse error response as JSON")
                
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Could not connect to the server")
        print("   Make sure the Django server is running on http://127.0.0.1:8000")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_user_list():
    """Test if we can get user information"""
    print("\n🧪 Testing User List Access...")
    
    try:
        response = requests.get(f"{BASE_URL}/users/")
        print(f"📊 Users endpoint status: {response.status_code}")
        
        if response.status_code == 200:
            users = response.json()
            print(f"👥 Found {len(users.get('results', []))} users")
            
            # Look for admin users
            admin_users = []
            for user in users.get('results', []):
                if user.get('is_staff') or user.get('is_superuser'):
                    admin_users.append(user)
            
            print(f"👑 Admin users found: {len(admin_users)}")
            for admin in admin_users:
                print(f"   - {admin.get('username')} (staff: {admin.get('is_staff')}, super: {admin.get('is_superuser')})")
                
    except Exception as e:
        print(f"❌ Error checking users: {e}")

if __name__ == "__main__":
    test_admin_login()
    test_user_list()
