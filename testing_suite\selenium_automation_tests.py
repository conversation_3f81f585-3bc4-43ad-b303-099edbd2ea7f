"""
Selenium Automation Tests for PyCharm IDE
Comprehensive end-to-end testing for Online Auction System
"""

import unittest
import time
import os
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import Action<PERSON>hains
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException


class AuctionSystemAutomationTests(unittest.TestCase):
    """
    Comprehensive Selenium automation tests for the Online Auction System
    Designed to run in PyCharm with detailed logging and reporting
    """
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment and browser"""
        print("🚀 Setting up Selenium automation tests...")
        
        # Configure Chrome options for testing
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--start-maximized")
        
        # Enable logging
        chrome_options.add_argument("--enable-logging")
        chrome_options.add_argument("--v=1")
        
        # Uncomment for headless mode (useful for CI/CD)
        # chrome_options.add_argument("--headless")
        
        try:
            cls.driver = webdriver.Chrome(options=chrome_options)
            cls.driver.implicitly_wait(10)
            cls.wait = WebDriverWait(cls.driver, 15)
            
            # Test configuration
            cls.base_url = "http://localhost:3001"
            cls.api_url = "http://127.0.0.1:8000"
            
            # Test data
            cls.test_users = {
                'seller': {
                    'username': 'selenium_seller',
                    'email': '<EMAIL>',
                    'password': 'SeleniumTest123!',
                    'first_name': 'Selenium',
                    'last_name': 'Seller'
                },
                'bidder': {
                    'username': 'selenium_bidder',
                    'email': '<EMAIL>',
                    'password': 'SeleniumTest123!',
                    'first_name': 'Selenium',
                    'last_name': 'Bidder'
                }
            }
            
            # Create test results directory
            cls.results_dir = "test_results"
            os.makedirs(cls.results_dir, exist_ok=True)
            
            print("✅ Selenium setup completed successfully")
            
        except Exception as e:
            print(f"❌ Failed to set up Selenium: {e}")
            raise
    
    @classmethod
    def tearDownClass(cls):
        """Clean up after all tests"""
        print("🧹 Cleaning up Selenium tests...")
        if hasattr(cls, 'driver'):
            cls.driver.quit()
        print("✅ Cleanup completed")
    
    def setUp(self):
        """Set up before each test"""
        self.test_start_time = time.time()
        print(f"\n🧪 Starting test: {self._testMethodName}")
        
        # Navigate to home page
        self.driver.get(self.base_url)
        self.wait_for_page_load()
    
    def tearDown(self):
        """Clean up after each test"""
        test_duration = time.time() - self.test_start_time
        print(f"⏱️ Test completed in {test_duration:.2f} seconds")
        
        # Take screenshot if test failed
        if hasattr(self._outcome, 'errors') and self._outcome.errors:
            self.take_screenshot(f"failed_{self._testMethodName}")
    
    def wait_for_page_load(self, timeout=10):
        """Wait for page to fully load"""
        try:
            self.wait.until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            time.sleep(1)  # Additional buffer
        except TimeoutException:
            print("⚠️ Page load timeout")
    
    def take_screenshot(self, name):
        """Take screenshot for debugging"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.results_dir}/{name}_{timestamp}.png"
        self.driver.save_screenshot(filename)
        print(f"📸 Screenshot saved: {filename}")
    
    def register_user(self, user_data):
        """Register a new user"""
        try:
            print(f"👤 Registering user: {user_data['username']}")
            
            # Navigate to registration
            register_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Register') or contains(text(), 'Sign Up')]"))
            )
            register_btn.click()
            
            # Fill registration form
            self.wait.until(EC.presence_of_element_located((By.NAME, "username"))).send_keys(user_data['username'])
            self.driver.find_element(By.NAME, "email").send_keys(user_data['email'])
            self.driver.find_element(By.NAME, "first_name").send_keys(user_data['first_name'])
            self.driver.find_element(By.NAME, "last_name").send_keys(user_data['last_name'])
            self.driver.find_element(By.NAME, "password").send_keys(user_data['password'])
            self.driver.find_element(By.NAME, "password2").send_keys(user_data['password'])
            
            # Submit form
            submit_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            submit_btn.click()
            
            # Wait for success
            time.sleep(3)
            print(f"✅ User {user_data['username']} registered successfully")
            return True
            
        except Exception as e:
            print(f"❌ Registration failed: {e}")
            self.take_screenshot(f"registration_failed_{user_data['username']}")
            return False
    
    def login_user(self, username, password):
        """Log in a user"""
        try:
            print(f"🔐 Logging in user: {username}")
            
            # Navigate to login
            login_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Login') or contains(text(), 'Sign In')]"))
            )
            login_btn.click()
            
            # Fill login form
            self.wait.until(EC.presence_of_element_located((By.NAME, "username"))).send_keys(username)
            self.driver.find_element(By.NAME, "password").send_keys(password)
            
            # Submit form
            submit_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            submit_btn.click()
            
            # Wait for successful login
            self.wait.until(
                EC.any_of(
                    EC.presence_of_element_located((By.XPATH, "//a[contains(text(), 'Dashboard')]")),
                    EC.presence_of_element_located((By.XPATH, "//a[contains(text(), 'Profile')]")),
                    EC.presence_of_element_located((By.CLASS_NAME, "user-menu"))
                )
            )
            
            print(f"✅ User {username} logged in successfully")
            return True
            
        except Exception as e:
            print(f"❌ Login failed: {e}")
            self.take_screenshot(f"login_failed_{username}")
            return False
    
    def logout_user(self):
        """Log out current user"""
        try:
            logout_btn = self.driver.find_element(By.XPATH, "//a[contains(text(), 'Logout') or contains(text(), 'Sign Out')]")
            logout_btn.click()
            time.sleep(2)
            print("✅ User logged out successfully")
            return True
        except Exception as e:
            print(f"❌ Logout failed: {e}")
            return False
    
    def test_01_homepage_loads(self):
        """Test that homepage loads correctly"""
        print("🏠 Testing homepage load...")
        
        # Check page title
        self.assertIn("Auction", self.driver.title)
        
        # Check for key elements
        elements_to_check = [
            "//nav",  # Navigation
            "//main",  # Main content
            "//footer"  # Footer
        ]
        
        for element_xpath in elements_to_check:
            try:
                element = self.driver.find_element(By.XPATH, element_xpath)
                self.assertTrue(element.is_displayed(), f"Element {element_xpath} should be visible")
            except NoSuchElementException:
                print(f"⚠️ Element {element_xpath} not found")
        
        print("✅ Homepage loads correctly")
    
    def test_02_user_registration_flow(self):
        """Test complete user registration flow"""
        print("📝 Testing user registration flow...")
        
        user_data = self.test_users['seller']
        success = self.register_user(user_data)
        self.assertTrue(success, "User registration should succeed")
        
        # Verify we're redirected appropriately
        current_url = self.driver.current_url
        self.assertTrue(
            any(keyword in current_url.lower() for keyword in ['login', 'dashboard', 'home']),
            "Should redirect to appropriate page after registration"
        )
        
        print("✅ User registration flow completed")
    
    def test_03_user_login_flow(self):
        """Test user login flow"""
        print("🔑 Testing user login flow...")
        
        # First register the user
        user_data = self.test_users['bidder']
        self.register_user(user_data)
        time.sleep(2)
        
        # Then test login
        success = self.login_user(user_data['username'], user_data['password'])
        self.assertTrue(success, "User login should succeed")
        
        print("✅ User login flow completed")
    
    def test_04_auction_creation_flow(self):
        """Test auction creation flow"""
        print("🏺 Testing auction creation flow...")
        
        # Login as seller
        user_data = self.test_users['seller']
        self.login_user(user_data['username'], user_data['password'])
        
        try:
            # Navigate to create auction
            create_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Create') or contains(text(), 'Sell')]"))
            )
            create_btn.click()
            
            # Fill auction form
            auction_data = {
                'title': 'Selenium Test Auction',
                'description': 'This is a test auction created by Selenium automation',
                'starting_bid': '100',
                'category': 'Electronics'
            }
            
            self.wait.until(EC.presence_of_element_located((By.NAME, "title"))).send_keys(auction_data['title'])
            self.driver.find_element(By.NAME, "description").send_keys(auction_data['description'])
            self.driver.find_element(By.NAME, "starting_bid").send_keys(auction_data['starting_bid'])
            
            # Select category if dropdown exists
            try:
                category_select = Select(self.driver.find_element(By.NAME, "category"))
                category_select.select_by_visible_text(auction_data['category'])
            except NoSuchElementException:
                print("⚠️ Category dropdown not found")
            
            # Submit auction
            submit_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            submit_btn.click()
            
            # Wait for success
            time.sleep(3)
            
            print("✅ Auction creation flow completed")
            
        except Exception as e:
            print(f"❌ Auction creation failed: {e}")
            self.take_screenshot("auction_creation_failed")
            self.fail(f"Auction creation failed: {e}")
    
    def test_05_auction_browsing_flow(self):
        """Test auction browsing and search"""
        print("🔍 Testing auction browsing flow...")
        
        try:
            # Navigate to auctions page
            auctions_link = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Auctions') or contains(text(), 'Browse')]"))
            )
            auctions_link.click()
            
            self.wait_for_page_load()
            
            # Check for auction listings
            auction_cards = self.driver.find_elements(By.CLASS_NAME, "auction-card")
            print(f"Found {len(auction_cards)} auction cards")
            
            # Test search functionality
            try:
                search_box = self.driver.find_element(By.NAME, "search")
                search_box.send_keys("test")
                search_box.send_keys(Keys.RETURN)
                time.sleep(2)
                print("✅ Search functionality tested")
            except NoSuchElementException:
                print("⚠️ Search box not found")
            
            # Test clicking on an auction if available
            if auction_cards:
                auction_cards[0].click()
                self.wait_for_page_load()
                
                # Verify auction detail page
                auction_title = self.driver.find_element(By.TAG_NAME, "h1")
                self.assertTrue(auction_title.is_displayed(), "Auction title should be visible")
                print("✅ Auction detail page loads correctly")
            
        except Exception as e:
            print(f"❌ Auction browsing failed: {e}")
            self.take_screenshot("auction_browsing_failed")
    
    def test_06_bidding_flow(self):
        """Test bidding functionality"""
        print("💰 Testing bidding flow...")
        
        # Login as bidder
        user_data = self.test_users['bidder']
        self.login_user(user_data['username'], user_data['password'])
        
        try:
            # Navigate to auctions
            self.driver.get(f"{self.base_url}/auctions")
            self.wait_for_page_load()
            
            # Find an auction to bid on
            auction_cards = self.driver.find_elements(By.CLASS_NAME, "auction-card")
            
            if auction_cards:
                auction_cards[0].click()
                self.wait_for_page_load()
                
                # Try to place a bid
                try:
                    bid_amount_field = self.driver.find_element(By.NAME, "bid_amount")
                    bid_amount_field.clear()
                    bid_amount_field.send_keys("150")
                    
                    bid_btn = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Bid') or contains(text(), 'Place')]")
                    bid_btn.click()
                    
                    time.sleep(2)
                    print("✅ Bid placement attempted")
                    
                except NoSuchElementException:
                    print("⚠️ Bidding interface not found")
            else:
                print("⚠️ No auctions available for bidding")
                
        except Exception as e:
            print(f"❌ Bidding flow failed: {e}")
            self.take_screenshot("bidding_failed")
    
    def test_07_responsive_design(self):
        """Test responsive design on different screen sizes"""
        print("📱 Testing responsive design...")
        
        screen_sizes = [
            (1920, 1080, "Desktop"),
            (1024, 768, "Tablet"),
            (375, 667, "Mobile")
        ]
        
        for width, height, device in screen_sizes:
            print(f"Testing {device} view ({width}x{height})")
            
            self.driver.set_window_size(width, height)
            time.sleep(2)
            
            # Check if navigation is accessible
            try:
                nav = self.driver.find_element(By.TAG_NAME, "nav")
                self.assertTrue(nav.is_displayed(), f"Navigation should be visible on {device}")
            except NoSuchElementException:
                print(f"⚠️ Navigation not found on {device}")
            
            # For mobile, check for mobile menu
            if device == "Mobile":
                try:
                    mobile_menu = self.driver.find_elements(By.CLASS_NAME, "mobile-menu")
                    hamburger = self.driver.find_elements(By.CLASS_NAME, "hamburger")
                    
                    if mobile_menu or hamburger:
                        print("✅ Mobile navigation found")
                    else:
                        print("⚠️ Mobile navigation not found")
                except Exception as e:
                    print(f"⚠️ Mobile menu check failed: {e}")
        
        # Reset to desktop size
        self.driver.set_window_size(1920, 1080)
        print("✅ Responsive design testing completed")
    
    def test_08_pwa_functionality(self):
        """Test PWA functionality"""
        print("📱 Testing PWA functionality...")
        
        # Check for PWA manifest
        try:
            manifest_link = self.driver.find_element(By.XPATH, "//link[@rel='manifest']")
            self.assertTrue(manifest_link.get_attribute("href"), "PWA manifest should have href")
            print("✅ PWA manifest found")
        except NoSuchElementException:
            print("❌ PWA manifest not found")
        
        # Check for service worker registration
        sw_registered = self.driver.execute_script("""
            return 'serviceWorker' in navigator;
        """)
        self.assertTrue(sw_registered, "Service Worker should be supported")
        print("✅ Service Worker support confirmed")
        
        # Check for install prompt
        try:
            install_prompts = self.driver.find_elements(By.CLASS_NAME, "install-prompt")
            if install_prompts:
                print("✅ Install prompt found")
            else:
                print("ℹ️ Install prompt not currently shown")
        except Exception as e:
            print(f"⚠️ Install prompt check failed: {e}")
    
    def test_09_performance_check(self):
        """Test basic performance metrics"""
        print("⚡ Testing performance...")
        
        start_time = time.time()
        
        # Navigate to different pages and measure load times
        pages_to_test = [
            ("Home", self.base_url),
            ("Auctions", f"{self.base_url}/auctions"),
            ("Search", f"{self.base_url}/search")
        ]
        
        for page_name, url in pages_to_test:
            page_start = time.time()
            self.driver.get(url)
            self.wait_for_page_load()
            page_load_time = time.time() - page_start
            
            print(f"📊 {page_name} page load time: {page_load_time:.2f}s")
            self.assertLess(page_load_time, 10, f"{page_name} page should load within 10 seconds")
        
        total_time = time.time() - start_time
        print(f"✅ Performance testing completed in {total_time:.2f}s")
    
    def test_10_error_handling(self):
        """Test error handling and edge cases"""
        print("🚨 Testing error handling...")
        
        # Test 404 page
        self.driver.get(f"{self.base_url}/nonexistent-page")
        time.sleep(2)
        
        # Should show some kind of error or redirect
        page_source = self.driver.page_source.lower()
        error_indicators = ["404", "not found", "error", "page not found"]
        
        has_error_handling = any(indicator in page_source for indicator in error_indicators)
        if has_error_handling:
            print("✅ Error page handling found")
        else:
            print("⚠️ No clear error handling detected")
        
        # Test invalid login
        self.driver.get(f"{self.base_url}/login")
        try:
            self.driver.find_element(By.NAME, "username").send_keys("invalid_user")
            self.driver.find_element(By.NAME, "password").send_keys("invalid_password")
            self.driver.find_element(By.XPATH, "//button[@type='submit']").click()
            time.sleep(2)
            
            # Look for error message
            error_elements = self.driver.find_elements(By.CLASS_NAME, "error")
            if error_elements:
                print("✅ Login error handling works")
            else:
                print("⚠️ Login error handling not clearly visible")
                
        except Exception as e:
            print(f"⚠️ Error handling test failed: {e}")


def run_automation_tests():
    """Run all automation tests with detailed reporting"""
    print("🤖 STARTING SELENIUM AUTOMATION TESTS")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestLoader().loadTestsFromTestCase(AuctionSystemAutomationTests)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=open('test_results/automation_results.txt', 'w'))
    result = runner.run(test_suite)
    
    # Generate summary report
    generate_test_report(result)
    
    return result


def generate_test_report(result):
    """Generate detailed test report"""
    report = {
        'timestamp': datetime.now().isoformat(),
        'total_tests': result.testsRun,
        'failures': len(result.failures),
        'errors': len(result.errors),
        'success_rate': ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0,
        'details': {
            'failures': [{'test': str(test), 'error': error} for test, error in result.failures],
            'errors': [{'test': str(test), 'error': error} for test, error in result.errors]
        }
    }
    
    # Save JSON report
    with open('test_results/automation_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # Print summary
    print("\n" + "=" * 60)
    print("🤖 SELENIUM AUTOMATION TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {report['total_tests']}")
    print(f"Failures: {report['failures']}")
    print(f"Errors: {report['errors']}")
    print(f"Success rate: {report['success_rate']:.1f}%")
    print(f"Report saved: test_results/automation_report.json")


if __name__ == "__main__":
    run_automation_tests()
