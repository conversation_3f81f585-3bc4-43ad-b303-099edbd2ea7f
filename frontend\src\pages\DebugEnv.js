import React from "react";
import { Container, Card, Table, Badge, Alert } from "react-bootstrap";
import { useAuth } from "../context/AuthContext";

function DebugEnv() {
  const { user } = useAuth();

  // Only allow admin users to access debug environment
  if (!user || user.role !== "admin") {
    return (
      <Container className="mt-5">
        <Alert variant="danger" className="text-center">
          <h4>🔒 Access Denied</h4>
          <p>This debug page is only accessible to admin users.</p>
          <p>
            Please log in as an admin to view environment debug information.
          </p>
        </Alert>
      </Container>
    );
  }
  const envVars = {
    REACT_APP_WEBSOCKET_ENABLED: process.env.REACT_APP_WEBSOCKET_ENABLED,
    REACT_APP_POLLING_ENABLED: process.env.REACT_APP_POLLING_ENABLED,
    REACT_APP_DEBUG_ENV: process.env.REACT_APP_DEBUG_ENV,
    REACT_APP_API_URL: process.env.REACT_APP_API_URL,
    REACT_APP_WS_URL: process.env.REACT_APP_WS_URL,
    NODE_ENV: process.env.NODE_ENV,
  };

  // Test WebSocket configuration logic
  const isWebSocketEnabled = process.env.REACT_APP_WEBSOCKET_ENABLED === "true";
  const isPollingEnabled = process.env.REACT_APP_POLLING_ENABLED === "true";

  console.log("🔍 Debug Environment Variables:", envVars);
  console.log("🔧 Configuration Test:", {
    isWebSocketEnabled,
    isPollingEnabled,
    rawWebSocketValue: process.env.REACT_APP_WEBSOCKET_ENABLED,
    rawPollingValue: process.env.REACT_APP_POLLING_ENABLED,
  });

  const getBadgeVariant = (value) => {
    if (value === "true") return "success";
    if (value === "false") return "danger";
    if (value === undefined || value === null) return "warning";
    return "info";
  };

  return (
    <Container className="mt-4">
      <h2>🔍 Environment Variables Debug (Admin Only)</h2>

      <Card className="mb-4">
        <Card.Header>
          <h5>Raw Environment Variables</h5>
        </Card.Header>
        <Card.Body>
          <Table striped bordered hover>
            <thead>
              <tr>
                <th>Variable</th>
                <th>Value</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(envVars).map(([key, value]) => (
                <tr key={key}>
                  <td>
                    <code>{key}</code>
                  </td>
                  <td>
                    <code>{value || "undefined"}</code>
                  </td>
                  <td>
                    <Badge bg={getBadgeVariant(value)}>
                      {value === undefined
                        ? "UNDEFINED"
                        : value === "true"
                        ? "TRUE"
                        : value === "false"
                        ? "FALSE"
                        : "SET"}
                    </Badge>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      <Card className="mb-4">
        <Card.Header>
          <h5>Configuration Test Results</h5>
        </Card.Header>
        <Card.Body>
          <Table striped bordered hover>
            <thead>
              <tr>
                <th>Configuration</th>
                <th>Result</th>
                <th>Logic</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>WebSocket Enabled</td>
                <td>
                  <Badge bg={isWebSocketEnabled ? "success" : "danger"}>
                    {isWebSocketEnabled ? "TRUE" : "FALSE"}
                  </Badge>
                </td>
                <td>
                  <code>
                    process.env.REACT_APP_WEBSOCKET_ENABLED === "true"
                  </code>
                </td>
              </tr>
              <tr>
                <td>Polling Enabled</td>
                <td>
                  <Badge bg={isPollingEnabled ? "success" : "danger"}>
                    {isPollingEnabled ? "TRUE" : "FALSE"}
                  </Badge>
                </td>
                <td>
                  <code>process.env.REACT_APP_POLLING_ENABLED === "true"</code>
                </td>
              </tr>
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      <Card>
        <Card.Header>
          <h5>Expected Behavior</h5>
        </Card.Header>
        <Card.Body>
          {isWebSocketEnabled ? (
            <div className="alert alert-success">
              ✅ WebSocket should be enabled - real-time updates active
            </div>
          ) : isPollingEnabled ? (
            <div className="alert alert-warning">
              ⚠️ WebSocket disabled, polling enabled - fallback mode
            </div>
          ) : (
            <div className="alert alert-danger">
              ❌ Both WebSocket and polling disabled - manual refresh only
            </div>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
}

export default DebugEnv;
