import os

import django
from django.core.asgi import get_asgi_application

# Set Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "OnlineAuctionSystem.settings")

# Initialize Django ASGI application early to ensure the AppRegistry is populated
django_asgi_app = get_asgi_application()

from channels.auth import AuthMiddlewareStack
# Now import Django Channels components
from channels.routing import ProtocolTypeRouter, URLRouter

import auction.routing
from middleware.jwt_auth import JWTAuthMiddleware

application = ProtocolTypeRouter(
    {
        "http": django_asgi_app,
        "websocket": JWTAuthMiddleware(
            AuthMiddlewareStack(URLRouter(auction.routing.websocket_urlpatterns))
        ),
    }
)
