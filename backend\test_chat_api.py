#!/usr/bin/env python
"""
Test script to verify the chat API is working correctly
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:8000/api"
USERNAME = "aisha_admin"
PASSWORD = "aisha2024!"

def test_chat_api():
    """Test the chat API functionality"""
    print("🧪 Testing Chat API...")
    
    # Step 1: Login to get authentication token
    print("\n1️⃣ Logging in...")
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    try:
        login_response = requests.post(f"{BASE_URL}/login/", json=login_data)
        if login_response.status_code == 200:
            token = login_response.json().get("access")
            print(f"✅ Login successful! Token: {token[:20]}...")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Headers for authenticated requests
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Step 2: Get available chat rooms
    print("\n2️⃣ Getting chat rooms...")
    try:
        rooms_response = requests.get(f"{BASE_URL}/chat-rooms/", headers=headers)
        if rooms_response.status_code == 200:
            rooms_data = rooms_response.json()
            # Handle both list and paginated response formats
            if isinstance(rooms_data, dict) and 'results' in rooms_data:
                rooms = rooms_data['results']
            else:
                rooms = rooms_data

            print(f"✅ Found {len(rooms)} chat rooms")
            if rooms:
                first_room = rooms[0]
                print(f"🔍 Room data structure: {type(first_room)}")

                if isinstance(first_room, dict):
                    room_id = first_room["id"]
                    print(f"🏠 Using room ID: {room_id}")
                    auction_info = first_room.get('auction', {})
                    if isinstance(auction_info, dict):
                        print(f"📦 Room auction: {auction_info.get('title', 'Unknown')}")
                    else:
                        print(f"📦 Room auction ID: {auction_info}")
                else:
                    # If it's just an ID or different format
                    room_id = first_room
                    print(f"🏠 Using room ID: {room_id}")
            else:
                print("❌ No chat rooms found")
                return
        else:
            print(f"❌ Failed to get rooms: {rooms_response.status_code}")
            return
    except Exception as e:
        print(f"❌ Rooms error: {e}")
        return
    
    # Step 3: Test sending a message
    print("\n3️⃣ Sending test message...")
    test_message = {
        "room": room_id,
        "message": "Hello! This is a test message from the API test script.",
        "message_type": "text"
    }
    
    try:
        message_response = requests.post(f"{BASE_URL}/chat-messages/", json=test_message, headers=headers)
        if message_response.status_code == 201:
            message_data = message_response.json()
            print(f"✅ Message sent successfully!")
            print(f"📝 Message ID: {message_data.get('id')}")
            print(f"💬 Content: {message_data.get('message')}")
        else:
            print(f"❌ Failed to send message: {message_response.status_code}")
            print(f"Response: {message_response.text}")
            return
    except Exception as e:
        print(f"❌ Message sending error: {e}")
        return
    
    # Step 4: Test getting messages
    print("\n4️⃣ Getting messages...")
    try:
        messages_response = requests.get(f"{BASE_URL}/chat-messages/?room={room_id}", headers=headers)
        if messages_response.status_code == 200:
            messages_data = messages_response.json()
            # Handle both list and paginated response formats
            if isinstance(messages_data, dict) and 'results' in messages_data:
                messages = messages_data['results']
            else:
                messages = messages_data

            print(f"✅ Retrieved {len(messages)} messages")
            if messages:
                latest_message = messages[-1]
                print(f"📝 Latest message: {latest_message.get('message', '')[:50]}...")
        else:
            print(f"❌ Failed to get messages: {messages_response.status_code}")
            return
    except Exception as e:
        print(f"❌ Messages retrieval error: {e}")
        return
    
    # Step 5: Test AI response generation
    print("\n5️⃣ Testing AI response...")
    ai_test_data = {
        "message": "What's the current price?",
        "room_id": room_id
    }
    
    try:
        ai_response = requests.post(f"{BASE_URL}/ai/chat-response/", json=ai_test_data, headers=headers)
        if ai_response.status_code == 201:
            ai_data = ai_response.json()
            print(f"✅ AI response generated!")
            if ai_data.get("should_respond"):
                ai_message = ai_data.get("ai_message", {})
                print(f"🤖 AI said: {ai_message.get('message', '')[:100]}...")
        else:
            print(f"❌ AI response failed: {ai_response.status_code}")
            print(f"Response: {ai_response.text}")
    except Exception as e:
        print(f"❌ AI response error: {e}")
    
    # Step 6: Test clear messages (if user has permission)
    print("\n6️⃣ Testing clear messages...")
    try:
        clear_response = requests.delete(f"{BASE_URL}/chat-messages/clear-room/{room_id}/", headers=headers)
        if clear_response.status_code == 200:
            clear_data = clear_response.json()
            print(f"✅ Messages cleared successfully!")
            print(f"🗑️ Deleted {clear_data.get('deleted_count')} messages")
        elif clear_response.status_code == 403:
            print(f"⚠️ Permission denied for clearing messages (expected for non-owners)")
        else:
            print(f"❌ Clear failed: {clear_response.status_code}")
            print(f"Response: {clear_response.text}")
    except Exception as e:
        print(f"❌ Clear messages error: {e}")
    
    print("\n🎉 Chat API test completed!")

if __name__ == '__main__':
    test_chat_api()
