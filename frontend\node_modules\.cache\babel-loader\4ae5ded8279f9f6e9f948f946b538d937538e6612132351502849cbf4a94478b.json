{"ast": null, "code": "export default function (grouping, thousands) {\n  return function (value, width) {\n    var i = value.length,\n      t = [],\n      j = 0,\n      g = grouping[0],\n      length = 0;\n    while (i > 0 && g > 0) {\n      if (length + g + 1 > width) g = Math.max(1, width - length);\n      t.push(value.substring(i -= g, i + g));\n      if ((length += g + 1) > width) break;\n      g = grouping[j = (j + 1) % grouping.length];\n    }\n    return t.reverse().join(thousands);\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}