{"ast": null, "code": "import appearance from \"./appearance.js\";\nimport { sum } from \"./ascending.js\";\nexport default function (series) {\n  var n = series.length,\n    i,\n    j,\n    sums = series.map(sum),\n    order = appearance(series),\n    top = 0,\n    bottom = 0,\n    tops = [],\n    bottoms = [];\n  for (i = 0; i < n; ++i) {\n    j = order[i];\n    if (top < bottom) {\n      top += sums[j];\n      tops.push(j);\n    } else {\n      bottom += sums[j];\n      bottoms.push(j);\n    }\n  }\n  return bottoms.reverse().concat(tops);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}