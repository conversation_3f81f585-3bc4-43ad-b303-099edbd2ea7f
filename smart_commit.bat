@echo off
setlocal enabledelayedexpansion

echo ========================================
echo SMART FOLDER-BY-FOLDER COMMIT SCRIPT
echo ========================================
echo This script will:
echo 1. Check each folder for changes
echo 2. Only commit folders with actual modifications
echo 3. Skip unchanged files and folders
echo 4. Provide detailed feedback
echo ========================================
echo.

:: Initialize counters
set committed_folders=0
set skipped_folders=0
set total_files_committed=0

:: Function to safely commit a folder
:smart_commit_folder
set "folder_path=%~1"
set "commit_message=%~2"

echo [CHECKING] %folder_path%

:: Check if folder exists
if not exist "%folder_path%" (
    echo [SKIP] Folder does not exist: %folder_path%
    set /a skipped_folders+=1
    goto :eof
)

:: Add the folder to staging
git add "%folder_path%/" 2>nul

:: Check if there are any staged changes for this folder
for /f %%i in ('git diff --cached --name-only "%folder_path%/" 2^>nul ^| find /c /v ""') do set staged_files=%%i

if !staged_files! gtr 0 (
    echo [CHANGES] Found !staged_files! modified files in %folder_path%
    
    :: Show what files are being committed
    echo [FILES] The following files will be committed:
    git diff --cached --name-only "%folder_path%/"
    
    :: Commit the changes
    git commit -m "%commit_message%"
    
    if !errorlevel! equ 0 (
        echo [SUCCESS] Committed %folder_path% with !staged_files! files
        set /a committed_folders+=1
        set /a total_files_committed+=!staged_files!
    ) else (
        echo [ERROR] Failed to commit %folder_path%
        git reset HEAD "%folder_path%/" 2>nul
    )
) else (
    echo [SKIP] No changes found in %folder_path%
    git reset HEAD "%folder_path%/" 2>nul
    set /a skipped_folders+=1
)
echo.
goto :eof

:: Start the smart commit process
echo Starting smart commit process...
echo Current branch: 
git branch --show-current
echo.

:: Check overall repository status first
echo [INFO] Overall repository status:
git status --short
echo.

if exist "README.md" (
    call :smart_commit_folder "." "[root] Update root level configuration files"
)

:: Backend commits
call :smart_commit_folder "backend" "[backend] Django backend application updates"
call :smart_commit_folder "backend/OnlineAuctionSystem" "[backend/config] Django settings and configuration"
call :smart_commit_folder "backend/auction" "[backend/auction] Main auction app - models, views, services"
call :smart_commit_folder "backend/auctions" "[backend/auctions] Additional auction utilities and services"
call :smart_commit_folder "backend/templates" "[backend/templates] Email templates and HTML files"
call :smart_commit_folder "backend/static" "[backend/static] Static files and backend assets"
call :smart_commit_folder "backend/middleware" "[backend/middleware] Custom Django middleware"

:: Frontend commits
call :smart_commit_folder "frontend/src/components" "[frontend/components] React UI components and elements"
call :smart_commit_folder "frontend/src/pages" "[frontend/pages] React page components and routing"
call :smart_commit_folder "frontend/src/context" "[frontend/context] React context and state management"
call :smart_commit_folder "frontend/src/styles" "[frontend/styles] CSS styles and theme configurations"
call :smart_commit_folder "frontend/src/api" "[frontend/api] API services and axios configuration"
call :smart_commit_folder "frontend/src/utils" "[frontend/utils] Utility functions and helpers"
call :smart_commit_folder "frontend/public" "[frontend/public] Public assets and static files"
call :smart_commit_folder "frontend" "[frontend] React frontend application configuration"

:: Testing and utilities
call :smart_commit_folder "testing_suite" "[testing] Comprehensive testing suite and automation"
call :smart_commit_folder "scripts" "[scripts] Utility scripts and automation tools"

:: Final summary
echo ========================================
echo SMART COMMIT SUMMARY
echo ========================================
echo Folders committed: !committed_folders!
echo Folders skipped: !skipped_folders!
echo Total files committed: !total_files_committed!
echo ========================================
echo.

echo [FINAL STATUS] Repository status after commits:
git status --short
echo.

echo [RECENT COMMITS] Last 5 commits:
git log --oneline -5
echo.

if !committed_folders! gtr 0 (
    echo [SUCCESS] Smart commit completed! !committed_folders! folders were updated.
    echo.
    echo Would you like to push these changes to the remote repository?
    echo Type 'y' to push or any other key to skip:
    set /p push_choice=
    if /i "!push_choice!"=="y" (
        echo Pushing changes to remote repository...
        git push origin HEAD
        if !errorlevel! equ 0 (
            echo [SUCCESS] Changes pushed to remote repository!
        ) else (
            echo [ERROR] Failed to push changes. Please check your connection and try manually.
        )
    )
) else (
    echo [INFO] No changes were found to commit. Repository is up to date.
)

echo.
echo Script completed!
pause
