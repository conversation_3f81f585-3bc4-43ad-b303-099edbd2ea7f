#!/usr/bin/env python3
"""
Backend Integration Test
Verify that frontend is properly connected to backend and dummy data is working
"""

import requests
import json
import time
from datetime import datetime

class BackendIntegrationTest:
    """Test backend integration and data flow"""

    def __init__(self):
        self.base_url = "http://127.0.0.1:8000"
        self.frontend_url = "http://localhost:3001"
        self.test_results = []

    def log_test(self, test_name, success, message=""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })
        print(f"{status} {test_name}: {message}")

    def test_backend_health(self):
        """Test if Django backend is running"""
        try:
            response = requests.get(f"{self.base_url}/api/", timeout=5)
            if response.status_code == 200:
                self.log_test("Backend Health", True, "Django API is running")
                return True
            else:
                self.log_test("Backend Health", False, f"API returned {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_test("Backend Health", False, f"Connection failed: {e}")
            return False

    def test_frontend_health(self):
        """Test if React frontend is running"""
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                self.log_test("Frontend Health", True, "React app is running")
                return True
            else:
                self.log_test("Frontend Health", False, f"Frontend returned {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_test("Frontend Health", False, f"Connection failed: {e}")
            return False

    def test_auctions_api(self):
        """Test auctions API endpoint"""
        try:
            response = requests.get(f"{self.base_url}/api/auctions/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                auctions = data.get('results', data) if isinstance(data, dict) else data

                if isinstance(auctions, list) and len(auctions) > 0:
                    self.log_test("Auctions API", True, f"Found {len(auctions)} auctions")

                    # Test first auction structure
                    first_auction = auctions[0]
                    required_fields = ['id', 'title', 'description', 'starting_bid', 'current_bid']
                    missing_fields = [field for field in required_fields if field not in first_auction]

                    if not missing_fields:
                        self.log_test("Auction Data Structure", True, "All required fields present")
                    else:
                        self.log_test("Auction Data Structure", False, f"Missing fields: {missing_fields}")

                    return True
                else:
                    self.log_test("Auctions API", False, "No auctions found in response")
                    return False
            else:
                self.log_test("Auctions API", False, f"API returned {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_test("Auctions API", False, f"Request failed: {e}")
            return False

    def test_categories_api(self):
        """Test categories API endpoint"""
        try:
            response = requests.get(f"{self.base_url}/api/categories/", timeout=5)
            if response.status_code == 200:
                data = response.json()
                categories = data.get('results', data) if isinstance(data, dict) else data

                if isinstance(categories, list):
                    self.log_test("Categories API", True, f"Found {len(categories)} categories")
                    return True
                else:
                    self.log_test("Categories API", False, "Invalid categories response format")
                    return False
            else:
                self.log_test("Categories API", False, f"API returned {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_test("Categories API", False, f"Request failed: {e}")
            return False

    def test_search_api(self):
        """Test search API endpoint"""
        try:
            response = requests.get(f"{self.base_url}/api/search/advanced/?q=test", timeout=5)
            if response.status_code == 200:
                self.log_test("Search API", True, "Search endpoint is working")
                return True
            else:
                self.log_test("Search API", False, f"API returned {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_test("Search API", False, f"Request failed: {e}")
            return False

    def test_dummy_data_quality(self):
        """Test quality of dummy data"""
        try:
            response = requests.get(f"{self.base_url}/api/auctions/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                auctions = data.get('results', data) if isinstance(data, dict) else data

                if not isinstance(auctions, list) or len(auctions) == 0:
                    self.log_test("Dummy Data Quality", False, "No auctions found")
                    return False

                # Check for realistic data
                realistic_count = 0
                for auction in auctions[:5]:  # Check first 5 auctions
                    starting_bid = auction.get('starting_bid', 0)
                    # Convert to float if it's a string
                    if isinstance(starting_bid, str):
                        try:
                            starting_bid = float(starting_bid)
                        except (ValueError, TypeError):
                            starting_bid = 0

                    if (auction.get('title') and
                        auction.get('description') and
                        starting_bid > 0 and
                        auction.get('category')):
                        realistic_count += 1

                if realistic_count >= 3:
                    self.log_test("Dummy Data Quality", True, f"{realistic_count}/5 auctions have realistic data")
                    return True
                else:
                    self.log_test("Dummy Data Quality", False, f"Only {realistic_count}/5 auctions have realistic data")
                    return False
            else:
                self.log_test("Dummy Data Quality", False, "Could not fetch auctions for quality check")
                return False
        except Exception as e:
            self.log_test("Dummy Data Quality", False, f"Error checking data quality: {e}")
            return False

    def test_database_connection(self):
        """Test database connectivity through admin endpoint"""
        try:
            response = requests.get(f"{self.base_url}/admin/", timeout=5)
            # Admin should return 302 (redirect to login) or 200, not 500
            if response.status_code in [200, 302]:
                self.log_test("Database Connection", True, "Database is accessible")
                return True
            else:
                self.log_test("Database Connection", False, f"Admin returned {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_test("Database Connection", False, f"Connection failed: {e}")
            return False

    def run_all_tests(self):
        """Run all integration tests"""
        print("🚀 BACKEND INTEGRATION TEST SUITE")
        print("=" * 50)

        start_time = time.time()

        # Run tests in order
        tests = [
            self.test_backend_health,
            self.test_frontend_health,
            self.test_database_connection,
            self.test_auctions_api,
            self.test_categories_api,
            self.test_search_api,
            self.test_dummy_data_quality
        ]

        for test in tests:
            test()
            time.sleep(0.5)  # Brief pause between tests

        # Generate summary
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])

        duration = time.time() - start_time

        print("\n" + "=" * 50)
        print("📊 INTEGRATION TEST SUMMARY")
        print("=" * 50)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"Duration: {duration:.2f} seconds")

        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ Your backend integration is working perfectly!")
            print("✅ Dummy data is properly loaded!")
            print("✅ Frontend can connect to backend!")
        else:
            print(f"\n⚠️ {total_tests - passed_tests} tests failed.")
            print("Please check the failed tests above and ensure:")
            print("1. Django backend is running (python manage.py runserver)")
            print("2. React frontend is running (npm start)")
            print("3. Database is properly configured")
            print("4. Dummy data has been created")

        # Save detailed results
        with open('integration_test_results.json', 'w') as f:
            json.dump({
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': total_tests - passed_tests,
                    'success_rate': (passed_tests/total_tests)*100,
                    'duration': duration,
                    'timestamp': datetime.now().isoformat()
                },
                'test_results': self.test_results
            }, f, indent=2)

        print(f"\n📄 Detailed results saved to: integration_test_results.json")

        return passed_tests == total_tests


def main():
    """Main function"""
    tester = BackendIntegrationTest()
    success = tester.run_all_tests()

    if not success:
        exit(1)


if __name__ == "__main__":
    main()
