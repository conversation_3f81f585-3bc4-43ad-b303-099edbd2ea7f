import jwt
from channels.db import database_sync_to_async
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from jwt import decode as jwt_decode
from rest_framework_simplejwt.tokens import UntypedToken


@database_sync_to_async
def get_user(validated_token):
    try:
        user_id = validated_token.get("user_id")
        return get_user_model().objects.get(id=user_id)
    except:
        return AnonymousUser()


class JWTAuthMiddleware:
    def __init__(self, inner):
        self.inner = inner

    async def __call__(self, scope, receive, send):
        return await JWTAuthMiddlewareInstance(scope, self.inner)(receive, send)


class JWTAuthMiddlewareInstance:
    def __init__(self, scope, inner):
        self.inner = inner
        self.scope = dict(scope)

    async def __call__(self, receive, send):
        headers = dict(self.scope["headers"])
        token = None

        # Extract token from headers
        if b"authorization" in headers:
            auth_header = headers[b"authorization"].decode()
            if auth_header.startswith("Bearer "):
                token = auth_header.split(" ")[1]

        # For WebSocket connections, also check query parameters
        if not token and self.scope["type"] == "websocket":
            query_string = self.scope.get("query_string", b"").decode()
            print(f"WebSocket query string: {query_string}")
            if "token=" in query_string:
                # Parse query parameters
                from urllib.parse import parse_qs

                query_params = parse_qs(query_string)
                if "token" in query_params:
                    token = query_params["token"][0]
                    print(f"Extracted token from query: {token[:20]}...")
            else:
                print("No token found in query parameters")

        # Validate token and get user
        try:
            if token:
                validated_token = jwt_decode(
                    token, settings.SECRET_KEY, algorithms=["HS256"]
                )
                user = await get_user(validated_token)
                self.scope["user"] = user
                print(f"WebSocket authenticated user: {user}")
            else:
                self.scope["user"] = AnonymousUser()
                print("No token provided, setting AnonymousUser")
        except (jwt.ExpiredSignatureError, jwt.InvalidTokenError, Exception) as e:
            self.scope["user"] = AnonymousUser()
            print(f"Token validation failed: {e}")

        # Call the inner application
        return await self.inner(self.scope, receive, send)
