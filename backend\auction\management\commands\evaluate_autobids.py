from django.core.management.base import BaseCommand
from django.utils import timezone

from auction.models import Auction
from auction.utils.autobid import evaluate_auto_bids


class Command(BaseCommand):
    help = "Evaluate AutoBids on all active auctions"

    def handle(self, *args, **kwargs):
        active_auctions = Auction.objects.filter(end_time__gt=timezone.now())
        for auction in active_auctions:
            evaluate_auto_bids(auction)
        self.stdout.write(self.style.SUCCESS("AutoBid evaluation complete."))
