from django.core.management.base import BaseCommand
from django.db import transaction, connection
from auction.models import Auction, Bid, Watchlist, Notification, Payment, Review, AuditTrail, PricePrediction, PricePredictionHistory


class Command(BaseCommand):
    help = 'Reassign auction IDs to be sequential starting from 1'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm the operation without prompting',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Auction ID Reassignment'))
        self.stdout.write('=' * 50)
        
        if not options['confirm']:
            confirm = input("\n⚠️  This will reassign all auction IDs. Continue? (y/N): ")
            if confirm.lower() != 'y':
                self.stdout.write(self.style.ERROR('❌ Operation cancelled'))
                return
        
        try:
            self.reassign_auction_ids()
            self.stdout.write(self.style.SUCCESS('\n✅ Auction ID reassignment completed successfully!'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'\n❌ Error during reassignment: {str(e)}'))
            import traceback
            traceback.print_exc()

    def reassign_auction_ids(self):
        """Reassign auction IDs to be sequential starting from 1"""
        
        self.stdout.write('🔄 Starting auction ID reassignment...')
        
        with transaction.atomic():
            # Get all auctions ordered by creation date (to maintain chronological order)
            auctions = Auction.objects.all().order_by('created_at', 'id')
            
            if not auctions.exists():
                self.stdout.write(self.style.ERROR('❌ No auctions found to reassign'))
                return
            
            self.stdout.write(f'📊 Found {auctions.count()} auctions to reassign')
            
            # Create mapping of old ID to new ID
            id_mapping = {}
            new_id = 1
            
            for auction in auctions:
                id_mapping[auction.id] = new_id
                new_id += 1
            
            self.stdout.write('🗺️ ID Mapping created:')
            for old_id, new_id in list(id_mapping.items())[:10]:  # Show first 10
                self.stdout.write(f'  {old_id} -> {new_id}')
            if len(id_mapping) > 10:
                self.stdout.write(f'  ... and {len(id_mapping) - 10} more')
            
            # Store auction data before deletion
            auction_data = []
            for auction in auctions:
                auction_data.append({
                    'old_id': auction.id,
                    'new_id': id_mapping[auction.id],
                    'title': auction.title,
                    'description': auction.description,
                    'starting_bid': auction.starting_bid,
                    'current_bid': auction.current_bid,
                    'reserve_price': auction.reserve_price,
                    'buy_now_price': auction.buy_now_price,
                    'start_time': auction.start_time,
                    'end_time': auction.end_time,
                    'auction_type': auction.auction_type,
                    'category': auction.category,
                    'image': auction.image,
                    'additional_images': auction.additional_images,
                    'location': auction.location,
                    'condition': auction.condition,
                    'shipping_cost': auction.shipping_cost,
                    'owner': auction.owner,
                    'owner_rating': auction.owner_rating,
                    'created_at': auction.created_at,
                    'updated_at': auction.updated_at,
                    'is_closed': auction.is_closed,
                    'approved': auction.approved,
                    'featured': auction.featured,
                    'views_count': auction.views_count,
                })
            
            self.stdout.write('💾 Auction data stored')
            
            # Step 1: Update all related models with temporary negative IDs first
            self.stdout.write('\n🔄 Step 1: Updating related models with temporary IDs...')
            
            for old_id, new_id in id_mapping.items():
                temp_id = -new_id  # Use negative to avoid conflicts
                
                # Update all related models to temporary negative IDs
                Bid.objects.filter(auction_id=old_id).update(auction_id=temp_id)
                Watchlist.objects.filter(auction_id=old_id).update(auction_id=temp_id)
                Notification.objects.filter(auction_id=old_id).update(auction_id=temp_id)
                Payment.objects.filter(auction_id=old_id).update(auction_id=temp_id)
                Review.objects.filter(auction_id=old_id).update(auction_id=temp_id)
                AuditTrail.objects.filter(auction_id=old_id).update(auction_id=temp_id)
                PricePrediction.objects.filter(auction_id=old_id).update(auction_id=temp_id)
                PricePredictionHistory.objects.filter(auction_id=old_id).update(auction_id=temp_id)
            
            self.stdout.write('✅ Related models updated with temporary IDs')
            
            # Step 2: Delete all auctions and create new ones with sequential IDs
            self.stdout.write('\n🔄 Step 2: Reassigning auction IDs...')
            
            # Delete all existing auctions
            Auction.objects.all().delete()
            self.stdout.write('🗑️ Old auctions deleted')
            
            # Create new auctions with sequential IDs
            for data in auction_data:
                new_auction = Auction(
                    id=data['new_id'],
                    title=data['title'],
                    description=data['description'],
                    starting_bid=data['starting_bid'],
                    current_bid=data['current_bid'],
                    reserve_price=data['reserve_price'],
                    buy_now_price=data['buy_now_price'],
                    start_time=data['start_time'],
                    end_time=data['end_time'],
                    auction_type=data['auction_type'],
                    category=data['category'],
                    image=data['image'],
                    additional_images=data['additional_images'],
                    location=data['location'],
                    condition=data['condition'],
                    shipping_cost=data['shipping_cost'],
                    owner=data['owner'],
                    owner_rating=data['owner_rating'],
                    created_at=data['created_at'],
                    updated_at=data['updated_at'],
                    is_closed=data['is_closed'],
                    approved=data['approved'],
                    featured=data['featured'],
                    views_count=data['views_count'],
                )
                new_auction.save()
            
            self.stdout.write('✅ Auctions reassigned with sequential IDs')
            
            # Step 3: Update related models back to positive IDs
            self.stdout.write('\n🔄 Step 3: Updating related models with final IDs...')
            
            for old_id, new_id in id_mapping.items():
                temp_id = -new_id
                
                Bid.objects.filter(auction_id=temp_id).update(auction_id=new_id)
                Watchlist.objects.filter(auction_id=temp_id).update(auction_id=new_id)
                Notification.objects.filter(auction_id=temp_id).update(auction_id=new_id)
                Payment.objects.filter(auction_id=temp_id).update(auction_id=new_id)
                Review.objects.filter(auction_id=temp_id).update(auction_id=new_id)
                AuditTrail.objects.filter(auction_id=temp_id).update(auction_id=new_id)
                PricePrediction.objects.filter(auction_id=temp_id).update(auction_id=new_id)
                PricePredictionHistory.objects.filter(auction_id=temp_id).update(auction_id=new_id)
            
            self.stdout.write('✅ Related models updated with final IDs')
            
            # Step 4: Reset auto-increment counter
            self.stdout.write('\n🔄 Step 4: Resetting auto-increment counter...')
            
            max_id = max(id_mapping.values())
            next_id = max_id + 1
            
            with connection.cursor() as cursor:
                # Reset the auto-increment counter for the auction table
                cursor.execute(f"ALTER TABLE auction_auction AUTO_INCREMENT = {next_id}")
            
            self.stdout.write(f'✅ Auto-increment counter reset to {next_id}')
            
            # Verify the results
            self.stdout.write('\n🔍 Verification:')
            new_auctions = Auction.objects.all().order_by('id')
            new_ids = [auction.id for auction in new_auctions]
            expected_ids = list(range(1, len(new_ids) + 1))
            
            self.stdout.write(f'📊 Total auctions: {len(new_ids)}')
            self.stdout.write(f'🆔 New IDs: {new_ids}')
            self.stdout.write(f'✅ Sequential: {new_ids == expected_ids}')
            
            if new_ids == expected_ids:
                self.stdout.write('\n🎉 SUCCESS: Auction IDs have been successfully reassigned!')
                self.stdout.write(f'   Auctions now have sequential IDs from 1 to {len(new_ids)}')
                return True
            else:
                self.stdout.write('\n❌ ERROR: ID reassignment failed - IDs are not sequential')
                return False
