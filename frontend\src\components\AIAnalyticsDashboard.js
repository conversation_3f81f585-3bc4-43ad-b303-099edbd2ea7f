import React, { useState, useEffect } from "react";
import "./AIAnalyticsDashboard.css";
import { formatAuctionPrice } from "../utils/currency";

const AIAnalyticsDashboard = () => {
  const [analytics, setAnalytics] = useState(null);
  const [predictions, setPredictions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchAnalytics();
    fetchRecentPredictions();
  }, []);

  const fetchAnalytics = async () => {
    try {
      const response = await fetch(
        "http://127.0.0.1:8000/api/analytics-dashboard/",
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      if (!response.ok) throw new Error("Failed to fetch analytics");
      const data = await response.json();
      setAnalytics(data);
    } catch (err) {
      console.error("Analytics fetch error:", err);
      setError(err.message);
      // Set fallback data
      setAnalytics({
        accuracy_stats: { average_accuracy: 85, total_predictions: 150 },
        model_version: "v2.1",
        performance_metrics: { response_time: 120, uptime: 99.9 },
        recent_insights: [
          "AI model performing well",
          "High prediction accuracy",
        ],
      });
    }
  };

  const fetchRecentPredictions = async () => {
    try {
      const response = await fetch(
        "http://127.0.0.1:8000/api/price-predictions/?ordering=-created_at&limit=10"
      );
      if (!response.ok) throw new Error("Failed to fetch predictions");
      const data = await response.json();
      setPredictions(data.results || []);
    } catch (err) {
      console.error("Failed to fetch predictions:", err);
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 80) return "#4CAF50";
    if (confidence >= 60) return "#FF9800";
    return "#f44336";
  };

  const getAccuracyColor = (accuracy) => {
    if (accuracy >= 80) return "#4CAF50";
    if (accuracy >= 60) return "#FF9800";
    return "#f44336";
  };

  if (loading) {
    return (
      <div className="ai-analytics-dashboard loading">
        <div className="loading-spinner"></div>
        <p>Loading AI Analytics...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="ai-analytics-dashboard error">
        <h3>❌ Error Loading Analytics</h3>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>Retry</button>
      </div>
    );
  }

  return (
    <div className="ai-analytics-dashboard">
      <div className="dashboard-header">
        <h2>🤖 AI Analytics Dashboard</h2>
        <p>Monitor AI price prediction performance and insights</p>
      </div>

      {analytics && (
        <div className="analytics-overview">
          <div className="stats-grid">
            <div className="stat-card accuracy">
              <div className="stat-icon">🎯</div>
              <div className="stat-content">
                <h3>{analytics.accuracy_stats.average_accuracy}%</h3>
                <p>Average Accuracy</p>
                <small>
                  {analytics.accuracy_stats.total_predictions} predictions
                </small>
              </div>
            </div>

            <div className="stat-card model">
              <div className="stat-icon">🧠</div>
              <div className="stat-content">
                <h3>{analytics.model_version}</h3>
                <p>Model Version</p>
                <small>Latest AI model</small>
              </div>
            </div>

            <div className="stat-card high-confidence">
              <div className="stat-icon">⭐</div>
              <div className="stat-content">
                <h3>{analytics.confidence_distribution.high_confidence}</h3>
                <p>High Confidence</p>
                <small>≥80% confidence</small>
              </div>
            </div>

            <div className="stat-card medium-confidence">
              <div className="stat-icon">📊</div>
              <div className="stat-content">
                <h3>{analytics.confidence_distribution.medium_confidence}</h3>
                <p>Medium Confidence</p>
                <small>60-79% confidence</small>
              </div>
            </div>
          </div>

          <div className="confidence-chart">
            <h3>Confidence Distribution</h3>
            <div className="chart-container">
              <div className="confidence-bar-chart">
                <div className="bar-group">
                  <div
                    className="bar high"
                    style={{
                      height: `${
                        (analytics.confidence_distribution.high_confidence /
                          (analytics.confidence_distribution.high_confidence +
                            analytics.confidence_distribution
                              .medium_confidence +
                            analytics.confidence_distribution.low_confidence)) *
                        100
                      }%`,
                    }}
                  ></div>
                  <label>High (≥80%)</label>
                  <span>
                    {analytics.confidence_distribution.high_confidence}
                  </span>
                </div>
                <div className="bar-group">
                  <div
                    className="bar medium"
                    style={{
                      height: `${
                        (analytics.confidence_distribution.medium_confidence /
                          (analytics.confidence_distribution.high_confidence +
                            analytics.confidence_distribution
                              .medium_confidence +
                            analytics.confidence_distribution.low_confidence)) *
                        100
                      }%`,
                    }}
                  ></div>
                  <label>Medium (60-79%)</label>
                  <span>
                    {analytics.confidence_distribution.medium_confidence}
                  </span>
                </div>
                <div className="bar-group">
                  <div
                    className="bar low"
                    style={{
                      height: `${
                        (analytics.confidence_distribution.low_confidence /
                          (analytics.confidence_distribution.high_confidence +
                            analytics.confidence_distribution
                              .medium_confidence +
                            analytics.confidence_distribution.low_confidence)) *
                        100
                      }%`,
                    }}
                  ></div>
                  <label>Low (&lt;60%)</label>
                  <span>
                    {analytics.confidence_distribution.low_confidence}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="recent-predictions">
        <h3>Recent Price Predictions</h3>
        {predictions.length === 0 ? (
          <div className="no-predictions">
            <p>No predictions available yet.</p>
          </div>
        ) : (
          <div className="predictions-table">
            <div className="table-header">
              <span>Auction</span>
              <span>Predicted Price</span>
              <span>Confidence</span>
              <span>Date</span>
            </div>
            {predictions.map((prediction) => (
              <div key={prediction.id} className="table-row">
                <div className="auction-info">
                  <strong>{prediction.auction_title}</strong>
                  <small>{prediction.auction_category}</small>
                </div>
                <div className="predicted-price">
                  {formatAuctionPrice(prediction.predicted_price)}
                </div>
                <div className="confidence">
                  <div
                    className="confidence-badge"
                    style={{
                      backgroundColor: getConfidenceColor(
                        prediction.confidence_score * 100
                      ),
                      color: "white",
                    }}
                  >
                    {Math.round(prediction.confidence_score * 100)}%
                  </div>
                </div>
                <div className="prediction-date">
                  {new Date(prediction.created_at).toLocaleDateString()}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="dashboard-actions">
        <button
          className="refresh-btn"
          onClick={() => {
            fetchAnalytics();
            fetchRecentPredictions();
          }}
        >
          🔄 Refresh Data
        </button>
        <button
          className="export-btn"
          onClick={() => alert("Export functionality coming soon!")}
        >
          📊 Export Report
        </button>
      </div>
    </div>
  );
};

export default AIAnalyticsDashboard;
