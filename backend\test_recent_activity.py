#!/usr/bin/env python3
"""
Test Recent Activity in Admin Dashboard
Verifies that recent activity data is properly displayed
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

import requests
from django.contrib.auth.models import User
from auction.models import Auction, Bid, Category
from django.utils import timezone

class RecentActivityTester:
    def __init__(self):
        self.test_results = []
        
    def test_dashboard_data_structure(self):
        """Test that dashboard provides all required data"""
        print("🔍 TESTING DASHBOARD DATA STRUCTURE")
        print("="*40)
        
        try:
            response = requests.get('http://127.0.0.1:8000/api/ultra-fast-dashboard/')
            data = response.json()
            
            if not data.get('success'):
                print(f"   ❌ Dashboard API failed: {data.get('error')}")
                return False
            
            dashboard_data = data['data']
            required_fields = [
                'recent_auctions',
                'recent_bids', 
                'users',
                'basic_stats',
                'fraud_alerts'
            ]
            
            print("📋 Checking required fields:")
            for field in required_fields:
                if field in dashboard_data:
                    count = len(dashboard_data[field]) if isinstance(dashboard_data[field], list) else 'N/A'
                    print(f"   ✅ {field}: {count} items")
                else:
                    print(f"   ❌ {field}: Missing")
                    return False
            
            self.test_results.append({
                'test': 'Dashboard Data Structure',
                'status': 'SUCCESS',
                'details': f'All {len(required_fields)} required fields present'
            })
            return True
            
        except Exception as e:
            print(f"   ❌ Dashboard test failed: {e}")
            self.test_results.append({
                'test': 'Dashboard Data Structure',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def test_recent_auctions_data(self):
        """Test recent auctions data quality"""
        print(f"\n🏷️ TESTING RECENT AUCTIONS DATA")
        print("-" * 35)
        
        try:
            response = requests.get('http://127.0.0.1:8000/api/ultra-fast-dashboard/')
            data = response.json()['data']
            
            recent_auctions = data.get('recent_auctions', [])
            print(f"📊 Found {len(recent_auctions)} recent auctions")
            
            if not recent_auctions:
                print("   ⚠️ No recent auctions found")
                return True
            
            # Test first auction data structure
            first_auction = recent_auctions[0]
            required_auction_fields = ['id', 'title', 'current_bid', 'owner__username', 'created_at']
            
            print("🔍 Checking auction data structure:")
            for field in required_auction_fields:
                if field in first_auction:
                    print(f"   ✅ {field}: {first_auction[field]}")
                else:
                    print(f"   ❌ {field}: Missing")
                    return False
            
            # Test data quality
            print(f"\n📋 Sample auctions:")
            for auction in recent_auctions[:3]:
                print(f"   • {auction['title']} - ${auction['current_bid']} by {auction['owner__username']}")
                print(f"     Created: {auction['created_at'][:10]}")
            
            self.test_results.append({
                'test': 'Recent Auctions Data',
                'status': 'SUCCESS',
                'details': f'{len(recent_auctions)} auctions with complete data'
            })
            return True
            
        except Exception as e:
            print(f"   ❌ Recent auctions test failed: {e}")
            self.test_results.append({
                'test': 'Recent Auctions Data',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def test_recent_bids_data(self):
        """Test recent bids data quality"""
        print(f"\n💰 TESTING RECENT BIDS DATA")
        print("-" * 30)
        
        try:
            response = requests.get('http://127.0.0.1:8000/api/ultra-fast-dashboard/')
            data = response.json()['data']
            
            recent_bids = data.get('recent_bids', [])
            print(f"📊 Found {len(recent_bids)} recent bids")
            
            if not recent_bids:
                print("   ⚠️ No recent bids found")
                return True
            
            # Test first bid data structure
            first_bid = recent_bids[0]
            required_bid_fields = ['id', 'amount', 'user__username', 'auction__title', 'created_at']
            
            print("🔍 Checking bid data structure:")
            for field in required_bid_fields:
                if field in first_bid:
                    print(f"   ✅ {field}: {first_bid[field]}")
                else:
                    print(f"   ❌ {field}: Missing")
                    return False
            
            # Test data quality
            print(f"\n📋 Sample bids:")
            for bid in recent_bids[:3]:
                print(f"   • ${bid['amount']} by {bid['user__username']}")
                print(f"     On: {bid['auction__title']}")
                print(f"     Time: {bid['created_at'][:10]}")
            
            self.test_results.append({
                'test': 'Recent Bids Data',
                'status': 'SUCCESS',
                'details': f'{len(recent_bids)} bids with complete data'
            })
            return True
            
        except Exception as e:
            print(f"   ❌ Recent bids test failed: {e}")
            self.test_results.append({
                'test': 'Recent Bids Data',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def test_recent_users_data(self):
        """Test recent users data quality"""
        print(f"\n👥 TESTING RECENT USERS DATA")
        print("-" * 30)
        
        try:
            response = requests.get('http://127.0.0.1:8000/api/ultra-fast-dashboard/')
            data = response.json()['data']
            
            users = data.get('users', [])
            print(f"📊 Found {len(users)} users")
            
            if not users:
                print("   ⚠️ No users found")
                return True
            
            # Test first user data structure
            first_user = users[0]
            required_user_fields = ['id', 'username', 'email', 'is_active', 'date_joined']
            
            print("🔍 Checking user data structure:")
            for field in required_user_fields:
                if field in first_user:
                    value = first_user[field]
                    if field == 'email':
                        value = value[:20] + '...' if len(value) > 20 else value
                    print(f"   ✅ {field}: {value}")
                else:
                    print(f"   ❌ {field}: Missing")
                    return False
            
            # Test data quality
            print(f"\n📋 Sample users (recent 5):")
            for user in users[:5]:
                status = "Active" if user['is_active'] else "Inactive"
                print(f"   • {user['username']} ({user['email'][:20]}...) - {status}")
                print(f"     Joined: {user['date_joined'][:10]}")
            
            self.test_results.append({
                'test': 'Recent Users Data',
                'status': 'SUCCESS',
                'details': f'{len(users)} users with complete data'
            })
            return True
            
        except Exception as e:
            print(f"   ❌ Recent users test failed: {e}")
            self.test_results.append({
                'test': 'Recent Users Data',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def test_frontend_compatibility(self):
        """Test frontend data compatibility"""
        print(f"\n🔗 TESTING FRONTEND COMPATIBILITY")
        print("-" * 35)
        
        try:
            response = requests.get('http://127.0.0.1:8000/api/ultra-fast-dashboard/')
            data = response.json()['data']
            
            # Simulate frontend data processing
            recent_activity = {
                'recent_auctions': data.get('recent_auctions', []),
                'recent_bids': data.get('recent_bids', []),
                'recent_users': data.get('users', [])[:5],  # Frontend takes first 5
            }
            
            print("🔍 Frontend data structure simulation:")
            for key, value in recent_activity.items():
                print(f"   ✅ {key}: {len(value)} items")
                
                # Test that each item has required fields for frontend
                if value:
                    first_item = value[0]
                    if key == 'recent_auctions':
                        required = ['title', 'current_bid', 'owner__username', 'created_at']
                    elif key == 'recent_bids':
                        required = ['amount', 'user__username', 'auction__title', 'created_at']
                    else:  # recent_users
                        required = ['username', 'email', 'is_active', 'date_joined']
                    
                    missing_fields = [field for field in required if field not in first_item]
                    if missing_fields:
                        print(f"      ❌ Missing fields: {missing_fields}")
                        return False
                    else:
                        print(f"      ✅ All required fields present")
            
            self.test_results.append({
                'test': 'Frontend Compatibility',
                'status': 'SUCCESS',
                'details': 'All data structures compatible with frontend'
            })
            return True
            
        except Exception as e:
            print(f"   ❌ Frontend compatibility test failed: {e}")
            self.test_results.append({
                'test': 'Frontend Compatibility',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print(f"\n" + "="*60)
        print("📊 RECENT ACTIVITY TEST REPORT")
        print("="*60)
        
        print(f"\n🧪 TEST RESULTS:")
        success_count = len([r for r in self.test_results if r.get('status') == 'SUCCESS'])
        total_tests = len(self.test_results)
        
        print(f"   Total Tests: {total_tests}")
        print(f"   Successful: {success_count}")
        print(f"   Failed: {total_tests - success_count}")
        print(f"   Success Rate: {(success_count/total_tests*100):.1f}%" if total_tests > 0 else "   Success Rate: 0%")
        
        print(f"\n📋 DETAILED RESULTS:")
        for result in self.test_results:
            status_icon = "✅" if result.get('status') == 'SUCCESS' else "❌"
            print(f"   {status_icon} {result.get('test')}: {result.get('status')}")
            if result.get('details'):
                print(f"      Details: {result['details']}")
            if result.get('error'):
                print(f"      Error: {result['error']}")
        
        print(f"\n🎯 RECENT ACTIVITY STATUS:")
        if success_count >= total_tests * 0.8:  # 80% success rate
            print("   ✅ Recent activity is working perfectly!")
            print("   📊 All data sections are functional")
            print("   🚀 Ready for admin dashboard display")
        else:
            print("   ⚠️ Recent activity has some issues")
            print("   🔧 Requires attention before production")
            
        print(f"\n📱 ADMIN DASHBOARD URL: http://localhost:3001/admin-dashboard")
        print(f"🔗 API ENDPOINT: http://127.0.0.1:8000/api/ultra-fast-dashboard/")

def main():
    """Main execution function"""
    print("🚀 STARTING RECENT ACTIVITY TESTING")
    print("="*60)
    
    tester = RecentActivityTester()
    
    try:
        # Run all tests
        tester.test_dashboard_data_structure()
        tester.test_recent_auctions_data()
        tester.test_recent_bids_data()
        tester.test_recent_users_data()
        tester.test_frontend_compatibility()
        
        tester.generate_test_report()
        
    except Exception as e:
        print(f"❌ Error during recent activity testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
