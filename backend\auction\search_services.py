"""
Advanced Search Services with ML-powered recommendations
"""

import re
from decimal import Decimal

from django.contrib.auth.models import User
from django.db.models import Avg, Count, F, Q
from django.utils import timezone

from .models import Auction, Bid, Category, Watchlist


class AdvancedSearchService:
    """Advanced search with ML-powered features"""

    def __init__(self):
        self.search_weights = {
            "title": 3.0,
            "description": 2.0,
            "category": 2.5,
            "condition": 1.5,
            "location": 1.0,
        }

    def advanced_search(self, query_params):
        """Perform advanced search with multiple filters"""
        try:
            # Base queryset
            queryset = Auction.objects.filter(approved=True)

            # Text search
            search_query = query_params.get("q", "").strip()
            if search_query:
                queryset = self._apply_text_search(queryset, search_query)

            # Category filter - use exact match for precise filtering
            category = query_params.get("category")
            if category:
                queryset = queryset.filter(category__iexact=category)

            # Price range filter
            min_price = query_params.get("min_price")
            max_price = query_params.get("max_price")
            if min_price:
                queryset = queryset.filter(current_bid__gte=Decimal(min_price))
            if max_price:
                queryset = queryset.filter(current_bid__lte=Decimal(max_price))

            # Condition filter - use exact match for precise filtering
            condition = query_params.get("condition")
            if condition:
                queryset = queryset.filter(condition__iexact=condition)

            # Location filter
            location = query_params.get("location")
            if location:
                queryset = queryset.filter(
                    Q(owner__profile__location__icontains=location)
                    | Q(description__icontains=location)
                )

            # Status filter
            status = query_params.get("status")
            if status == "active":
                queryset = queryset.filter(end_time__gt=timezone.now())
            elif status == "ending_soon":
                soon = timezone.now() + timezone.timedelta(hours=24)
                queryset = queryset.filter(
                    end_time__gt=timezone.now(), end_time__lte=soon
                )
            elif status == "ended":
                queryset = queryset.filter(end_time__lte=timezone.now())

            # Seller rating filter
            min_rating = query_params.get("min_seller_rating")
            if min_rating:
                queryset = queryset.filter(
                    owner__profile__rating__gte=float(min_rating)
                )

            # Has bids filter
            has_bids = query_params.get("has_bids")
            if has_bids == "true":
                queryset = queryset.filter(bids__isnull=False).distinct()
            elif has_bids == "false":
                queryset = queryset.filter(bids__isnull=True)

            # Sorting
            sort_by = query_params.get("sort_by", "created_at")
            sort_order = query_params.get("sort_order", "desc")

            if sort_order == "desc":
                sort_by = f"-{sort_by}"

            # Apply sorting with fallbacks
            valid_sort_fields = [
                "created_at",
                "-created_at",
                "current_bid",
                "-current_bid",
                "end_time",
                "-end_time",
                "title",
                "-title",
            ]

            if sort_by in valid_sort_fields:
                queryset = queryset.order_by(sort_by)
            else:
                queryset = queryset.order_by("-created_at")

            return queryset.distinct()

        except Exception as e:
            print(f"Search error: {e}")
            return Auction.objects.filter(approved=True).order_by("-created_at")

    def _apply_text_search(self, queryset, search_query):
        """Apply intelligent text search"""
        try:
            # Clean and prepare search terms
            terms = self._extract_search_terms(search_query)

            if not terms:
                return queryset

            # Build search Q objects
            search_q = Q()

            for term in terms:
                term_q = (
                    Q(title__icontains=term)
                    | Q(description__icontains=term)
                    | Q(category__icontains=term)
                    | Q(condition__icontains=term)
                )
                search_q |= term_q

            return queryset.filter(search_q)

        except Exception as e:
            print(f"Text search error: {e}")
            return queryset

    def _extract_search_terms(self, query):
        """Extract and clean search terms"""
        try:
            # Remove special characters and split
            cleaned = re.sub(r"[^\w\s]", " ", query.lower())
            terms = [term.strip() for term in cleaned.split() if len(term.strip()) > 2]

            # Remove common stop words
            stop_words = {
                "the",
                "and",
                "for",
                "are",
                "but",
                "not",
                "you",
                "all",
                "can",
                "had",
                "her",
                "was",
                "one",
                "our",
                "out",
                "day",
                "get",
                "has",
                "him",
                "his",
                "how",
                "man",
                "new",
                "now",
                "old",
                "see",
                "two",
                "way",
                "who",
                "boy",
                "did",
                "its",
                "let",
                "put",
                "say",
                "she",
                "too",
                "use",
            }

            return [term for term in terms if term not in stop_words]

        except Exception as e:
            print(f"Term extraction error: {e}")
            return []

    def get_search_suggestions(self, partial_query, limit=10):
        """Get search suggestions based on partial query"""
        try:
            if len(partial_query) < 2:
                return []

            # Get suggestions from auction titles
            title_suggestions = Auction.objects.filter(
                title__icontains=partial_query, approved=True
            ).values_list("title", flat=True)[: limit // 2]

            # Get suggestions from categories
            category_suggestions = (
                Auction.objects.filter(category__icontains=partial_query, approved=True)
                .values_list("category", flat=True)
                .distinct()[: limit // 2]
            )

            # Combine and deduplicate
            suggestions = list(
                set(list(title_suggestions) + list(category_suggestions))
            )

            return suggestions[:limit]

        except Exception as e:
            print(f"Suggestions error: {e}")
            return []

    def get_popular_searches(self, limit=10):
        """Get popular search terms (simulated)"""
        try:
            # Get most common categories
            popular_categories = (
                Auction.objects.filter(approved=True)
                .values("category")
                .annotate(count=Count("id"))
                .order_by("-count")[:limit]
            )

            return [cat["category"] for cat in popular_categories if cat["category"]]

        except Exception as e:
            print(f"Popular searches error: {e}")
            return []

    def get_recommended_auctions(self, user_id=None, limit=10):
        """Get ML-powered auction recommendations"""
        try:
            if user_id:
                return self._get_personalized_recommendations(user_id, limit)
            else:
                return self._get_general_recommendations(limit)

        except Exception as e:
            print(f"Recommendations error: {e}")
            return []

    def _get_personalized_recommendations(self, user_id, limit):
        """Get personalized recommendations for logged-in user"""
        try:
            user = User.objects.get(id=user_id)

            # Get user's bidding history
            user_bids = Bid.objects.filter(user=user).values_list(
                "auction__category", flat=True
            )
            user_categories = list(set(user_bids))

            # Get user's watchlist
            watchlist_categories = Watchlist.objects.filter(user=user).values_list(
                "auction__category", flat=True
            )
            user_categories.extend(watchlist_categories)

            if user_categories:
                # Recommend auctions from similar categories
                recommendations = (
                    Auction.objects.filter(
                        category__in=user_categories,
                        approved=True,
                        end_time__gt=timezone.now(),
                    )
                    .exclude(owner=user)
                    .annotate(bid_count=Count("bids"))
                    .order_by("-bid_count", "-created_at")[:limit]
                )
            else:
                # Fallback to general recommendations
                recommendations = self._get_general_recommendations(limit)

            return recommendations

        except Exception as e:
            print(f"Personalized recommendations error: {e}")
            return self._get_general_recommendations(limit)

    def _get_general_recommendations(self, limit):
        """Get general recommendations for all users"""
        try:
            # Get trending auctions (most bids, recent)
            trending = (
                Auction.objects.filter(approved=True, end_time__gt=timezone.now())
                .annotate(bid_count=Count("bids"), avg_bid=Avg("bids__amount"))
                .order_by("-bid_count", "-avg_bid")[:limit]
            )

            return trending

        except Exception as e:
            print(f"General recommendations error: {e}")
            return Auction.objects.filter(
                approved=True, end_time__gt=timezone.now()
            ).order_by("-created_at")[:limit]

    def get_similar_auctions(self, auction_id, limit=5):
        """Get auctions similar to the given auction"""
        try:
            auction = Auction.objects.get(id=auction_id)

            # Find similar auctions by category and price range
            price_range = float(auction.current_bid) * 0.3  # 30% price range
            min_price = max(0, float(auction.current_bid) - price_range)
            max_price = float(auction.current_bid) + price_range

            similar = (
                Auction.objects.filter(
                    category=auction.category,
                    approved=True,
                    current_bid__gte=Decimal(str(min_price)),
                    current_bid__lte=Decimal(str(max_price)),
                )
                .exclude(id=auction_id)
                .order_by("-created_at")[:limit]
            )

            return similar

        except Exception as e:
            print(f"Similar auctions error: {e}")
            return []

    def get_search_filters(self):
        """Get available search filters"""
        try:
            # Get unique categories
            categories = (
                Auction.objects.filter(approved=True)
                .values_list("category", flat=True)
                .distinct()
            )

            # Get unique conditions
            conditions = (
                Auction.objects.filter(approved=True)
                .values_list("condition", flat=True)
                .distinct()
            )

            # Get price ranges
            price_stats = Auction.objects.filter(approved=True).aggregate(
                min_price=Avg("starting_bid"), max_price=Avg("current_bid")
            )

            return {
                "categories": [cat for cat in categories if cat],
                "conditions": [cond for cond in conditions if cond],
                "price_range": {
                    "min": float(price_stats["min_price"] or 0),
                    "max": float(price_stats["max_price"] or 1000),
                },
                "sort_options": [
                    {"value": "created_at", "label": "Newest First"},
                    {"value": "-created_at", "label": "Oldest First"},
                    {"value": "current_bid", "label": "Price: Low to High"},
                    {"value": "-current_bid", "label": "Price: High to Low"},
                    {"value": "end_time", "label": "Ending Soon"},
                    {"value": "title", "label": "Title A-Z"},
                ],
            }

        except Exception as e:
            print(f"Search filters error: {e}")
            return {
                "categories": [],
                "conditions": [],
                "price_range": {"min": 0, "max": 1000},
                "sort_options": [],
            }


# Global instance
advanced_search_service = AdvancedSearchService()
