/* Import Professional Theme */
@import './styles/professional-theme.css';
@import './styles/auction-enhancements.css';
@import './styles/navigation.css';

/* App Specific Styles */
.App {
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.container {
  padding-top: 20px;
  padding-bottom: 20px;
}

/* Animation for flip cards */
@keyframes flipCardFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) rotateX(-10deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotateX(0);
  }
}

/* Professional Navbar Styling */
.navbar {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.navbar-brand:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

.nav-link {
  font-weight: 500;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: #3498db !important;
}

/* Professional Hero Section */
.hero-section {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 30%, var(--accent-color) 70%, var(--secondary-color) 100%);
  color: white;
  padding: 120px 0;
  margin-bottom: 0;
  position: relative;
  overflow: hidden;
  min-height: 80vh;
  display: flex;
  align-items: center;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(217, 119, 6, 0.15) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(8, 145, 178, 0.15) 0%, transparent 60%),
    radial-gradient(circle at 40% 40%, rgba(5, 150, 105, 0.1) 0%, transparent 60%);
  pointer-events: none;
}

.hero-section::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(180deg, transparent 0%, var(--bg-secondary) 100%);
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.search-section {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  margin: var(--space-2xl) 0;
  box-shadow: var(--glass-shadow);
  position: relative;
}

.search-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: var(--radius-2xl);
  pointer-events: none;
}

.search-input {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-2xl);
  padding: var(--space-lg) var(--space-xl);
  font-size: 1.1rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-smooth);
  background: rgba(255, 255, 255, 0.9);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(15, 76, 117, 0.1), var(--shadow-md);
  transform: translateY(-2px);
  background: var(--white);
}

.search-btn {
  border-radius: var(--radius-2xl);
  padding: var(--space-lg) var(--space-2xl);
  background: var(--gradient-auction);
  border: none;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  transition: var(--transition-bounce);
  box-shadow: var(--shadow-md);
}

.search-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow-lg);
  background: var(--gradient-premium);
}

/* Enhanced Card Styles */
.card {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: none;
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.auction-card {
  position: relative;
  overflow: hidden;
}

.auction-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
  z-index: 1;
}

.auction-card:hover::before {
  left: 100%;
}

.card-img-top {
  transition: transform 0.4s ease;
  height: 250px;
  object-fit: contain;
}

.card:hover .card-img-top {
  transform: scale(1.1);
}

/* Enhanced Category Cards */
.category-card {
  border-radius: var(--radius-2xl);
  overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: var(--transition-smooth);
  height: 220px;
  border: 2px solid var(--border-light);
}

.category-card:hover {
  transform: translateY(-8px) scale(1.03);
  box-shadow: var(--shadow-xl);
  border-color: var(--secondary-color);
}

.category-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-premium);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.3rem;
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.category-card:hover .category-overlay {
  background: var(--gradient-hero);
}

.category-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: var(--transition-normal);
}

.category-card:hover .category-overlay::before {
  opacity: 1;
}

/* Enhanced Stats Section */
.stats-section {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-3xl);
  margin: var(--space-3xl) 0;
  box-shadow: var(--glass-shadow);
  position: relative;
}

.stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-elegant);
  border-radius: var(--radius-2xl);
  pointer-events: none;
  opacity: 0.6;
}

.stat-item {
  text-align: center;
  padding: var(--space-lg);
  position: relative;
  z-index: 1;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  background: var(--gradient-auction);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-sm);
  display: block;
}

.stat-label {
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Loading Skeleton */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-card {
  height: 300px;
  border-radius: 20px;
}

.skeleton-text {
  height: 20px;
  border-radius: 10px;
  margin: 10px 0;
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

/* Existing styles */
.bg-dark-custom {
  background-color: #121212 !important;
  color: #ffffff !important;
}

.dark-mode-bg {
  background: linear-gradient(135deg, #0f2027, #203a43, #2c5364);
  min-height: 100vh;
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-size: cover;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

.blinking-badge {
  animation: blink 1.5s infinite;
  box-shadow: 0 0 8px red;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

button[title="Back to top"] {
  animation: pulse 2s infinite;
}

/* Category Navigation Styles */
.category-breadcrumb {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 15px 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.category-breadcrumb .breadcrumb-item {
  font-weight: 500;
}

.category-breadcrumb .breadcrumb-item.active {
  color: var(--primary-color);
  font-weight: 600;
}

.category-breadcrumb .breadcrumb-item:not(.active) {
  color: #666;
  transition: color 0.3s ease;
}

.category-breadcrumb .breadcrumb-item:not(.active):hover {
  color: var(--primary-color);
}

.category-header {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.category-navigation-buttons {
  gap: 15px;
}

.category-navigation-buttons .btn {
  border-radius: 25px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.category-navigation-buttons .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.category-empty-state {
  background: rgba(248, 249, 250, 0.8);
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  border: 2px dashed #dee2e6;
}

.category-empty-state .fa-tags {
  color: #6c757d;
  margin-bottom: 20px;
}

.category-results-summary {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 15px 20px;
  margin-bottom: 20px;
  border-left: 4px solid var(--primary-color);
}
