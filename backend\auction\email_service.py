"""
Email Service for Online Auction System
"""

import logging

from celery import shared_task
from django.conf import settings
from django.core.mail import EmailMultiAlternatives, send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags

logger = logging.getLogger(__name__)


class EmailService:
    """Service for sending various types of emails"""

    @staticmethod
    def send_email(subject, template_name, context, recipient_email, from_email=None):
        """Send an email using a template"""
        try:
            if from_email is None:
                from_email = settings.DEFAULT_FROM_EMAIL

            # Render HTML content
            html_content = render_to_string(template_name, context)
            text_content = strip_tags(html_content)

            # Create email
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=from_email,
                to=[recipient_email],
            )
            email.attach_alternative(html_content, "text/html")

            # Send email
            result = email.send()
            logger.info(f"Email sent successfully to {recipient_email}: {subject}")
            return result

        except Exception as e:
            logger.error(f"Failed to send email to {recipient_email}: {str(e)}")
            return False

    @staticmethod
    def send_welcome_email(user):
        """Send welcome email to new user"""
        subject = "Welcome to Online Auction System! 🎉"
        template = "emails/welcome.html"
        context = {"user": user}

        return EmailService.send_email(
            subject=subject,
            template_name=template,
            context=context,
            recipient_email=user.email,
        )

    @staticmethod
    def send_bid_notification(auction, bid):
        """Send notification to auction owner about new bid"""
        subject = f"New Bid on '{auction.title}' - ₹{bid.amount}"
        template = "emails/bid_notification.html"
        context = {"auction": auction, "bid": bid}

        return EmailService.send_email(
            subject=subject,
            template_name=template,
            context=context,
            recipient_email=auction.owner.email,
        )

    @staticmethod
    def send_auction_ending_reminder(auction, user, user_bid=None):
        """Send reminder that auction is ending soon"""
        subject = f"⏰ '{auction.title}' ends soon!"
        template = "emails/auction_ending_reminder.html"
        context = {"auction": auction, "user": user, "user_bid": user_bid}

        return EmailService.send_email(
            subject=subject,
            template_name=template,
            context=context,
            recipient_email=user.email,
        )

    @staticmethod
    def send_auction_won_email(auction, winner):
        """Send congratulations email to auction winner"""
        subject = f"🎉 Congratulations! You won '{auction.title}'"
        template = "emails/auction_won.html"
        context = {"auction": auction, "winner": winner}

        return EmailService.send_email(
            subject=subject,
            template_name=template,
            context=context,
            recipient_email=winner.email,
        )

    @staticmethod
    def send_payment_confirmation(payment, user):
        """Send payment confirmation email"""
        subject = f"Payment Confirmed - ₹{payment.amount}"
        template = "emails/payment_confirmation.html"
        context = {"payment": payment, "user": user}

        return EmailService.send_email(
            subject=subject,
            template_name=template,
            context=context,
            recipient_email=user.email,
        )


# Celery tasks for async email sending
@shared_task
def send_welcome_email_task(user_id):
    """Async task to send welcome email"""
    try:
        from django.contrib.auth.models import User

        user = User.objects.get(id=user_id)
        return EmailService.send_welcome_email(user)
    except Exception as e:
        logger.error(f"Failed to send welcome email for user {user_id}: {str(e)}")
        return False


@shared_task
def send_bid_notification_task(auction_id, bid_id):
    """Async task to send bid notification"""
    try:
        from .models import Auction, Bid

        auction = Auction.objects.get(id=auction_id)
        bid = Bid.objects.get(id=bid_id)
        return EmailService.send_bid_notification(auction, bid)
    except Exception as e:
        logger.error(
            f"Failed to send bid notification for auction {auction_id}: {str(e)}"
        )
        return False


@shared_task
def send_auction_ending_reminder_task(auction_id, user_id):
    """Async task to send auction ending reminder"""
    try:
        from django.contrib.auth.models import User

        from .models import Auction, Bid

        auction = Auction.objects.get(id=auction_id)
        user = User.objects.get(id=user_id)

        # Get user's last bid on this auction
        user_bid = (
            Bid.objects.filter(auction=auction, user=user)
            .order_by("-created_at")
            .first()
        )

        return EmailService.send_auction_ending_reminder(auction, user, user_bid)
    except Exception as e:
        logger.error(
            f"Failed to send ending reminder for auction {auction_id}: {str(e)}"
        )
        return False


@shared_task
def test_email_configuration():
    """Test email configuration"""
    try:
        from django.core.mail import send_mail

        result = send_mail(
            subject="Test Email - Online Auction System",
            message="This is a test email to verify email configuration.",
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[settings.EMAIL_HOST_USER],
            fail_silently=False,
        )

        logger.info("Test email sent successfully")
        return result
    except Exception as e:
        logger.error(f"Test email failed: {str(e)}")
        return False
