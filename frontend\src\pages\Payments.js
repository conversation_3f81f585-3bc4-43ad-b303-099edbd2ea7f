import React, { useEffect, useState } from "react";
import { useAuth } from "../context/AuthContext";
import { useNavigate } from "react-router-dom";
import {
  Container,
  Row,
  Col,
  Card,
  Table,
  <PERSON><PERSON>,
  Badge,
  Al<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Spin<PERSON>,
} from "react-bootstrap";
import {
  FaCreditCard,
  FaClock,
  FaCheckCircle,
  FaExclamationTriangle,
  FaGavel,
  FaReceipt,
} from "react-icons/fa";
import { formatAuctionPrice } from "../utils/currency";

function Payments() {
  const { user, token } = useAuth();
  const navigate = useNavigate();
  const [payments, setPayments] = useState([]);
  const [wonAuctions, setWonAuctions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (!token || !user) return;
    fetchPaymentData();
  }, [token, user]);

  const fetchPaymentData = async () => {
    try {
      setLoading(true);

      // Fetch existing payments
      const paymentsResponse = await fetch(
        "http://127.0.0.1:8000/api/payments/",
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (paymentsResponse.ok) {
        const paymentsData = await paymentsResponse.json();
        setPayments(paymentsData.results || []);
      }

      // Fetch won auctions that need payment using the dedicated endpoint
      const wonAuctionsResponse = await fetch(
        "http://127.0.0.1:8000/api/auctions/won_auctions/",
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (wonAuctionsResponse.ok) {
        const wonAuctionsData = await wonAuctionsResponse.json();
        if (wonAuctionsData.success) {
          const allWonAuctions = wonAuctionsData.results || [];

          // Filter won auctions that need payment (no completed payment)
          const wonAuctionsNeedingPayment = allWonAuctions.filter(
            (auction) =>
              !payments.some(
                (p) =>
                  p.auction.id === auction.id &&
                  p.payment_status === "completed"
              )
          );

          setWonAuctions(wonAuctionsNeedingPayment);
        } else {
          console.error("Failed to fetch won auctions:", wonAuctionsData.error);
          setWonAuctions([]);
        }
      } else {
        console.error("Won auctions API call failed");
        setWonAuctions([]);
      }
    } catch (err) {
      console.error("Error fetching payment data:", err);
      setError("Failed to load payment information");
    } finally {
      setLoading(false);
    }
  };

  const getPaymentStatusBadge = (status) => {
    const statusConfig = {
      pending: { variant: "warning", icon: FaClock, text: "Pending" },
      processing: { variant: "info", icon: FaClock, text: "Processing" },
      completed: { variant: "success", icon: FaCheckCircle, text: "Completed" },
      failed: {
        variant: "danger",
        icon: FaExclamationTriangle,
        text: "Failed",
      },
      timeout: {
        variant: "danger",
        icon: FaExclamationTriangle,
        text: "Timeout",
      },
      cancelled: {
        variant: "secondary",
        icon: FaExclamationTriangle,
        text: "Cancelled",
      },
    };

    const config = statusConfig[status] || statusConfig.pending;
    const IconComponent = config.icon;

    return (
      <Badge bg={config.variant} className="d-flex align-items-center gap-1">
        <IconComponent size={12} />
        {config.text}
      </Badge>
    );
  };

  const handlePayNow = (auction) => {
    // Navigate to auction detail page where payment can be completed
    navigate(`/auction/${auction.id}`);
  };

  if (loading) {
    return (
      <Container className="mt-4 text-center">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-2">Loading payment information...</p>
      </Container>
    );
  }

  return (
    <Container className="mt-4">
      <Row>
        <Col>
          <h2 className="mb-4">
            <FaCreditCard className="me-2" />
            Payment Center
          </h2>

          {error && (
            <Alert variant="danger" dismissible onClose={() => setError("")}>
              {error}
            </Alert>
          )}

          <Tabs defaultActiveKey="pending" className="mb-4">
            {/* Won Auctions Needing Payment */}
            <Tab
              eventKey="pending"
              title={
                <>
                  <FaGavel className="me-2" />
                  Won Auctions ({wonAuctions.length})
                </>
              }
            >
              <Card>
                <Card.Body>
                  {wonAuctions.length === 0 ? (
                    <Alert variant="info">
                      <FaCheckCircle className="me-2" />
                      No pending payments for won auctions.
                    </Alert>
                  ) : (
                    <>
                      <Alert variant="warning">
                        <FaClock className="me-2" />
                        <strong>Payment Required:</strong> You have{" "}
                        {wonAuctions.length} won auction(s) requiring payment
                        within 24 hours.
                      </Alert>

                      <Table responsive hover>
                        <thead>
                          <tr>
                            <th>Auction</th>
                            <th>Winning Bid</th>
                            <th>End Date</th>
                            <th>Status</th>
                            <th>Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          {wonAuctions.map((auction) => (
                            <tr key={auction.id}>
                              <td>
                                <div>
                                  <strong>{auction.title}</strong>
                                  <br />
                                  <small className="text-muted">
                                    ID: #{auction.id}
                                  </small>
                                </div>
                              </td>
                              <td className="text-success fw-bold">
                                {formatAuctionPrice(auction.current_bid)}
                              </td>
                              <td>
                                {new Date(
                                  auction.end_time
                                ).toLocaleDateString()}
                                <br />
                                <small className="text-muted">
                                  {new Date(
                                    auction.end_time
                                  ).toLocaleTimeString()}
                                </small>
                              </td>
                              <td>
                                <Badge bg="warning">
                                  <FaClock className="me-1" />
                                  Payment Due
                                </Badge>
                              </td>
                              <td>
                                <Button
                                  variant="success"
                                  size="sm"
                                  onClick={() => handlePayNow(auction)}
                                >
                                  <FaCreditCard className="me-1" />
                                  Pay Now
                                </Button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    </>
                  )}
                </Card.Body>
              </Card>
            </Tab>

            {/* Payment History */}
            <Tab
              eventKey="history"
              title={
                <>
                  <FaReceipt className="me-2" />
                  Payment History ({payments.length})
                </>
              }
            >
              <Card>
                <Card.Body>
                  {payments.length === 0 ? (
                    <Alert variant="info">
                      <FaReceipt className="me-2" />
                      No payment history available.
                    </Alert>
                  ) : (
                    <Table responsive hover>
                      <thead>
                        <tr>
                          <th>Auction</th>
                          <th>Amount</th>
                          <th>Status</th>
                          <th>Payment Date</th>
                          <th>Transaction ID</th>
                        </tr>
                      </thead>
                      <tbody>
                        {payments.map((payment) => (
                          <tr key={payment.id}>
                            <td>
                              <div>
                                <strong>
                                  {payment.auction?.title || "Unknown Auction"}
                                </strong>
                                <br />
                                <small className="text-muted">
                                  ID: #{payment.auction?.id}
                                </small>
                              </div>
                            </td>
                            <td className="fw-bold">
                              {formatAuctionPrice(payment.amount)}
                            </td>
                            <td>
                              {getPaymentStatusBadge(payment.payment_status)}
                            </td>
                            <td>
                              {payment.payment_date ? (
                                <>
                                  {new Date(
                                    payment.payment_date
                                  ).toLocaleDateString()}
                                  <br />
                                  <small className="text-muted">
                                    {new Date(
                                      payment.payment_date
                                    ).toLocaleTimeString()}
                                  </small>
                                </>
                              ) : (
                                <span className="text-muted">-</span>
                              )}
                            </td>
                            <td>
                              <code>{payment.transaction_id || "N/A"}</code>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  )}
                </Card.Body>
              </Card>
            </Tab>
          </Tabs>
        </Col>
      </Row>
    </Container>
  );
}

export default Payments;
