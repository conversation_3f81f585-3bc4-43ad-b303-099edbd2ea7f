﻿import React from 'react';
function ManageUsers() {
  const [users, setUsers] = useState([]);
  useEffect(() => {
    const allUsers = JSON.parse(localStorage.getItem("users")) || [];
    setUsers(allUsers);
  }, []);

  return (
    <div className="container mt-5">
      <h3>All Registered Users</h3>
      <table className="table table-bordered">
        <thead>
          <tr><th>Username</th><th>Email</th><th>Role</th></tr>
        </thead>
        <tbody>
          {users.map((u, i) => (
            <tr key={i}>
              <td>{u.username}</td><td>{u.email}</td><td>{u.role}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
