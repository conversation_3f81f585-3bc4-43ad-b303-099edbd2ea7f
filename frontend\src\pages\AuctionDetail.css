/* Auction Detail Page Styles */

.auction-detail-navigation {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.back-button {
  transition: all 0.2s ease;
  border-radius: 6px;
  font-weight: 500;
}

.back-button:hover {
  transform: translateX(-2px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.auction-breadcrumb {
  background: transparent;
  margin: 0;
  padding: 0;
}

.auction-breadcrumb .breadcrumb-item {
  font-size: 0.9rem;
}

.auction-breadcrumb .breadcrumb-item a {
  color: #6c757d;
  text-decoration: none;
  transition: color 0.2s ease;
}

.auction-breadcrumb .breadcrumb-item a:hover {
  color: #007bff;
}

.auction-breadcrumb .breadcrumb-item.active {
  color: #495057;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .auction-detail-navigation {
    padding: 10px;
  }
  
  .d-flex.align-items-center.justify-content-between {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 10px;
  }
  
  .back-button {
    width: 100%;
    justify-content: center;
  }
  
  .auction-breadcrumb {
    width: 100%;
  }
  
  .auction-breadcrumb .breadcrumb-item {
    font-size: 0.8rem;
  }
}

/* Animation for smooth transitions */
.auction-detail-container {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Carousel Image Quality */
.carousel-image-container {
  position: relative;
  background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
              linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
              linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.carousel-image {
  /* High-quality image rendering */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
  image-rendering: high-quality;

  /* Prevent blurry images during transitions */
  backface-visibility: hidden;
  transform: translateZ(0);

  /* Smooth transitions */
  transition: opacity 0.3s ease-in-out;

  /* Ensure sharp edges */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Prevent image compression artifacts */
  image-orientation: from-image;

  /* Optimize for quality over speed */
  will-change: auto;
}

/* Carousel specific enhancements */
.carousel-item {
  /* Prevent blurring during slide transitions */
  backface-visibility: hidden;
  transform: translateZ(0);
}

.carousel-item.active {
  /* Ensure active images are crisp */
  transform: translateZ(0) translateX(0);
}

/* Remove any blur effects from Bootstrap carousel */
.carousel-item-next,
.carousel-item-prev,
.carousel-item.active.carousel-item-start,
.carousel-item.active.carousel-item-end {
  transform: translateZ(0);
  filter: none;
}

/* Enhanced carousel controls */
.carousel-control-prev,
.carousel-control-next {
  width: 5%;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
  opacity: 1;
}

/* Better carousel indicators */
.carousel-indicators {
  bottom: -40px;
}

.carousel-indicators [data-bs-target] {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin: 0 4px;
  background-color: #6c757d;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.carousel-indicators .active {
  background-color: #007bff;
}
