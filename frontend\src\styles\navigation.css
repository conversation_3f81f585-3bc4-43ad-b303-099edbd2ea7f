/* Professional Navigation Bar Styling */

/* Main Navigation Container */
.navbar {
  background: var(--gradient-nav) !important;
  box-shadow: var(--shadow-lg);
  border-bottom: 3px solid var(--secondary-color);
  padding: 0.75rem 0;
  transition: var(--transition-normal);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--secondary-color), transparent);
}

/* Brand Styling */
.navbar-brand {
  color: var(--nav-text) !important;
  font-weight: 700;
  font-size: 1.5rem;
  text-decoration: none;
  transition: var(--transition-normal);
  position: relative;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
}

.navbar-brand:hover {
  color: var(--secondary-color) !important;
  transform: translateY(-2px);
}

.navbar-brand::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--secondary-color);
  transition: var(--transition-normal);
  transform: translateX(-50%);
}

.navbar-brand:hover::after {
  width: 80%;
}

/* Navigation Links */
.navbar-nav .nav-link {
  color: var(--nav-text) !important;
  font-weight: 500;
  padding: 0.75rem 1rem !important;
  margin: 0 0.25rem;
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  position: relative;
  text-decoration: none;
}

.navbar-nav .nav-link:hover {
  color: var(--nav-hover) !important;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
  color: var(--nav-active) !important;
  background: rgba(217, 119, 6, 0.2);
  font-weight: 600;
}

/* Navigation Link Hover Effects */
.navbar-nav .nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-light), var(--accent-light));
  opacity: 0;
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  z-index: -1;
}

.navbar-nav .nav-link:hover::before {
  opacity: 0.1;
}

/* Welcome Message */
.navbar-nav .nav-link[class*="Welcome"] {
  color: var(--secondary-color) !important;
  font-weight: 600;
  background: rgba(217, 119, 6, 0.1);
  border: 1px solid rgba(217, 119, 6, 0.3);
}

/* Button Styling in Navbar */
.navbar .btn {
  border-radius: var(--radius-lg);
  font-weight: 600;
  padding: 0.5rem 1rem;
  transition: var(--transition-bounce);
  border: 2px solid transparent;
}

.navbar .btn-light {
  background: var(--white);
  color: var(--text-primary);
  border-color: var(--white);
}

.navbar .btn-light:hover {
  background: var(--secondary-color);
  color: var(--white);
  border-color: var(--secondary-color);
  transform: translateY(-2px) scale(1.05);
}

.navbar .btn-outline-light {
  background: transparent;
  color: var(--nav-text);
  border-color: rgba(255, 255, 255, 0.5);
}

.navbar .btn-outline-light:hover {
  background: var(--primary-light);
  color: var(--white);
  border-color: var(--primary-light);
  transform: translateY(-2px) scale(1.05);
}

/* Mobile Toggle Button */
.navbar-toggler {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-md);
  padding: 0.5rem;
  transition: var(--transition-normal);
}

.navbar-toggler:hover {
  border-color: var(--secondary-color);
  background: rgba(217, 119, 6, 0.1);
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Dark Mode Specific Styles */
.dark-mode .navbar {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%) !important;
  border-bottom-color: var(--secondary-color);
}

.dark-mode .navbar-brand,
.dark-mode .navbar-nav .nav-link {
  color: #ffffff !important;
}

.dark-mode .navbar-nav .nav-link:hover {
  color: var(--primary-light) !important;
  background: rgba(59, 130, 246, 0.1);
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .navbar-collapse {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    margin-top: 1rem;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .navbar-nav .nav-link {
    margin: 0.25rem 0;
    text-align: center;
  }
  
  .navbar .btn {
    width: 100%;
    margin: 0.25rem 0;
  }
}

@media (max-width: 576px) {
  .navbar-brand {
    font-size: 1.25rem;
  }
  
  .navbar {
    padding: 0.5rem 0;
  }
}

/* Animation for navbar items */
@keyframes navItemFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.navbar-nav .nav-item {
  animation: navItemFadeIn 0.3s ease-out;
}

.navbar-nav .nav-item:nth-child(1) { animation-delay: 0.1s; }
.navbar-nav .nav-item:nth-child(2) { animation-delay: 0.2s; }
.navbar-nav .nav-item:nth-child(3) { animation-delay: 0.3s; }
.navbar-nav .nav-item:nth-child(4) { animation-delay: 0.4s; }
.navbar-nav .nav-item:nth-child(5) { animation-delay: 0.5s; }

/* Professional Badge for Admin */
.admin-badge {
  background: var(--gradient-premium);
  color: var(--white);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: 0.5rem;
  box-shadow: var(--shadow-sm);
}

/* Notification Indicator */
.nav-notification {
  position: relative;
}

.nav-notification::after {
  content: '';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 8px;
  height: 8px;
  background: var(--error-color);
  border-radius: 50%;
  border: 2px solid var(--nav-bg);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Search in Navigation (if needed) */
.navbar-search {
  position: relative;
  max-width: 300px;
}

.navbar-search input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: var(--nav-text);
  border-radius: var(--radius-lg);
  padding: 0.5rem 1rem;
  width: 100%;
}

.navbar-search input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.navbar-search input:focus {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--secondary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(217, 119, 6, 0.3);
}
