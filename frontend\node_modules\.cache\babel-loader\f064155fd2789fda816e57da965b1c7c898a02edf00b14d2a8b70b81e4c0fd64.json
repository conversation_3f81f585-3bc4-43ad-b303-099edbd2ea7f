{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Online_Auction_System\\\\frontend\\\\src\\\\components\\\\FraudAlertDetails.js\";\nimport React from \"react\";\nimport { Card, Badge, Row, Col, Alert, ProgressBar } from \"react-bootstrap\";\nimport \"./FraudAlertDetails.css\";\nimport { FaExclamationTriangle, FaRobot, FaCreditCard, FaUserSecret, FaEye, FaShieldAlt, FaClock, FaChartLine, FaUser, FaGavel } from \"react-icons/fa\";\nconst FraudAlertDetails = ({\n  fraud\n}) => {\n  var _fraud$user, _fraud$details, _fraud$details2;\n  if (!fraud) return null;\n\n  // Get fraud type icon and color\n  const getFraudTypeInfo = type => {\n    const typeMap = {\n      suspicious_bidding: {\n        icon: /*#__PURE__*/React.createElement(FaGavel, {\n          className: \"me-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 15\n          }\n        }),\n        color: \"warning\",\n        title: \"Suspicious Bidding Pattern\",\n        description: \"Unusual bidding behavior detected\"\n      },\n      bot_activity: {\n        icon: /*#__PURE__*/React.createElement(FaRobot, {\n          className: \"me-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 15\n          }\n        }),\n        color: \"danger\",\n        title: \"Bot Activity Detected\",\n        description: \"Automated behavior patterns identified\"\n      },\n      payment_fraud: {\n        icon: /*#__PURE__*/React.createElement(FaCreditCard, {\n          className: \"me-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }\n        }),\n        color: \"danger\",\n        title: \"Payment Fraud\",\n        description: \"Suspicious payment activity detected\"\n      },\n      account_takeover: {\n        icon: /*#__PURE__*/React.createElement(FaUserSecret, {\n          className: \"me-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }\n        }),\n        color: \"danger\",\n        title: \"Account Takeover\",\n        description: \"Unauthorized account access suspected\"\n      },\n      fake_listing: {\n        icon: /*#__PURE__*/React.createElement(FaEye, {\n          className: \"me-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 15\n          }\n        }),\n        color: \"warning\",\n        title: \"Fake Listing\",\n        description: \"Potentially fraudulent auction listing\"\n      },\n      shill_bidding: {\n        icon: /*#__PURE__*/React.createElement(FaChartLine, {\n          className: \"me-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }\n        }),\n        color: \"warning\",\n        title: \"Shill Bidding\",\n        description: \"Coordinated bidding to inflate prices\"\n      }\n    };\n    return typeMap[type] || {\n      icon: /*#__PURE__*/React.createElement(FaExclamationTriangle, {\n        className: \"me-2\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 15\n        }\n      }),\n      color: \"secondary\",\n      title: type.replace(\"_\", \" \").toUpperCase(),\n      description: \"Security alert detected\"\n    };\n  };\n\n  // Get risk level info\n  const getRiskLevel = score => {\n    if (score >= 90) return {\n      level: \"CRITICAL\",\n      color: \"danger\",\n      variant: \"danger\"\n    };\n    if (score >= 80) return {\n      level: \"HIGH\",\n      color: \"warning\",\n      variant: \"warning\"\n    };\n    if (score >= 60) return {\n      level: \"MEDIUM\",\n      color: \"info\",\n      variant: \"info\"\n    };\n    return {\n      level: \"LOW\",\n      color: \"success\",\n      variant: \"success\"\n    };\n  };\n\n  // Format detection details in a user-friendly way\n  const formatDetails = (details, fraudType) => {\n    if (!details || typeof details !== \"object\") return null;\n    const formatters = {\n      suspicious_bidding: d => [d.rapid_consecutive_bids && {\n        icon: \"⚡\",\n        text: \"Multiple rapid bids detected\",\n        severity: \"high\"\n      }, d.unusual_timing_pattern && {\n        icon: \"⏰\",\n        text: \"Unusual bidding timing pattern\",\n        severity: \"medium\"\n      }, d.bid_count_in_last_hour && {\n        icon: \"📊\",\n        text: `${d.bid_count_in_last_hour} bids in last hour`,\n        severity: \"high\"\n      }, d.bid_increment_pattern === \"suspicious\" && {\n        icon: \"📈\",\n        text: \"Suspicious bid increment pattern\",\n        severity: \"medium\"\n      }, d.time_between_bids_seconds && {\n        icon: \"⏱️\",\n        text: `Consistent timing: ${d.time_between_bids_seconds.join(\", \")}s intervals`,\n        severity: \"high\"\n      }],\n      bot_activity: d => [d.automated_behavior && {\n        icon: \"🤖\",\n        text: \"Automated behavior detected\",\n        severity: \"critical\"\n      }, d.consistent_response_time && {\n        icon: \"⚡\",\n        text: \"Consistent response times (non-human)\",\n        severity: \"high\"\n      }, d.no_human_delays && {\n        icon: \"🚫\",\n        text: \"No natural human delays\",\n        severity: \"high\"\n      }, d.captcha_failures && {\n        icon: \"🔒\",\n        text: `${d.captcha_failures} CAPTCHA failures`,\n        severity: \"medium\"\n      }, d.user_agent_suspicious && {\n        icon: \"🌐\",\n        text: \"Suspicious browser/device signature\",\n        severity: \"medium\"\n      }, d.bid_frequency_per_minute && {\n        icon: \"📊\",\n        text: `${d.bid_frequency_per_minute} actions per minute`,\n        severity: \"critical\"\n      }],\n      payment_fraud: d => [d.stolen_card_indicators && {\n        icon: \"💳\",\n        text: \"Stolen card indicators detected\",\n        severity: \"critical\"\n      }, d.billing_address_mismatch && {\n        icon: \"📍\",\n        text: \"Billing address mismatch\",\n        severity: \"high\"\n      }, d.multiple_failed_payments && {\n        icon: \"❌\",\n        text: \"Multiple failed payment attempts\",\n        severity: \"high\"\n      }, d.velocity_check_failed && {\n        icon: \"🚨\",\n        text: \"Velocity check failed\",\n        severity: \"high\"\n      }, d.cvv_failures && {\n        icon: \"🔢\",\n        text: `${d.cvv_failures} CVV verification failures`,\n        severity: \"medium\"\n      }, d.high_risk_country && {\n        icon: \"🌍\",\n        text: \"Transaction from high-risk location\",\n        severity: \"medium\"\n      }],\n      account_takeover: d => [d.login_from_new_location && {\n        icon: \"📍\",\n        text: \"Login from new/unusual location\",\n        severity: \"high\"\n      }, d.unusual_bidding_behavior && {\n        icon: \"🎯\",\n        text: \"Unusual bidding behavior for this user\",\n        severity: \"medium\"\n      }, d.password_recently_changed && {\n        icon: \"🔑\",\n        text: \"Password recently changed\",\n        severity: \"medium\"\n      }, d.different_payment_method && {\n        icon: \"💳\",\n        text: \"New payment method added\",\n        severity: \"medium\"\n      }, d.suspicious_ip && {\n        icon: \"🌐\",\n        text: `Suspicious IP: ${d.suspicious_ip}`,\n        severity: \"high\"\n      }, d.login_time_unusual && {\n        icon: \"⏰\",\n        text: \"Login at unusual time\",\n        severity: \"low\"\n      }],\n      fake_listing: d => [d.stock_photos_detected && {\n        icon: \"📸\",\n        text: \"Stock photos detected\",\n        severity: \"high\"\n      }, d.price_too_low_for_item && {\n        icon: \"💰\",\n        text: \"Price significantly below market value\",\n        severity: \"high\"\n      }, d.vague_description && {\n        icon: \"📝\",\n        text: \"Vague or generic description\",\n        severity: \"medium\"\n      }, d.new_seller_account && {\n        icon: \"👤\",\n        text: \"New seller account\",\n        severity: \"medium\"\n      }, d.no_seller_history && {\n        icon: \"📊\",\n        text: \"No previous selling history\",\n        severity: \"medium\"\n      }, d.similar_listings_found && {\n        icon: \"🔍\",\n        text: `${d.similar_listings_found} similar listings found elsewhere`,\n        severity: \"high\"\n      }],\n      shill_bidding: d => [d.shill_bidding_pattern && {\n        icon: \"🎭\",\n        text: \"Shill bidding pattern detected\",\n        severity: \"high\"\n      }, d.bidding_on_same_seller_items && {\n        icon: \"🔄\",\n        text: \"Repeatedly bidding on same seller items\",\n        severity: \"high\"\n      }, d.artificial_price_inflation && {\n        icon: \"📈\",\n        text: \"Artificial price inflation detected\",\n        severity: \"high\"\n      }, d.coordinated_bidding && {\n        icon: \"🤝\",\n        text: \"Coordinated bidding activity\",\n        severity: \"critical\"\n      }, d.seller_connection_suspected && {\n        icon: \"🔗\",\n        text: \"Connection to seller suspected\",\n        severity: \"high\"\n      }, d.bid_timing_coordination && {\n        icon: \"⏰\",\n        text: \"Coordinated bid timing\",\n        severity: \"medium\"\n      }]\n    };\n    const formatter = formatters[fraudType];\n    if (!formatter) return null;\n    return formatter(details).filter(Boolean);\n  };\n  const typeInfo = getFraudTypeInfo(fraud.fraud_type);\n  const riskInfo = getRiskLevel(fraud.risk_score);\n  const detailItems = formatDetails(fraud.details, fraud.fraud_type);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"fraud-alert-details\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(Card, {\n    className: `border-${typeInfo.color} mb-3`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Card.Header, {\n    className: `bg-${typeInfo.color} text-white`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    className: \"align-items-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h5\", {\n    className: \"mb-0\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 15\n    }\n  }, typeInfo.icon, typeInfo.title), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 15\n    }\n  }, typeInfo.description)), /*#__PURE__*/React.createElement(Col, {\n    xs: \"auto\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(Badge, {\n    bg: riskInfo.variant,\n    className: \"fs-6\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 15\n    }\n  }, riskInfo.level, \" RISK\")))), /*#__PURE__*/React.createElement(Card.Body, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    className: \"mb-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    md: 6,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex align-items-center mb-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaShieldAlt, {\n    className: \"me-2 text-danger\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 17\n    }\n  }, \"Risk Score: \", fraud.risk_score, \"/100\")), /*#__PURE__*/React.createElement(ProgressBar, {\n    variant: riskInfo.variant,\n    now: fraud.risk_score,\n    label: `${fraud.risk_score}%`,\n    className: \"mb-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(Col, {\n    md: 6,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex align-items-center mb-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaUser, {\n    className: \"me-2 text-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 17\n    }\n  }, \"User: \", ((_fraud$user = fraud.user) === null || _fraud$user === void 0 ? void 0 : _fraud$user.username) || fraud.user)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex align-items-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaClock, {\n    className: \"me-2 text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    className: \"text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 17\n    }\n  }, \"Detected:\", \" \", new Date(fraud.detected_at || fraud.created_at).toLocaleString())))), detailItems && detailItems.length > 0 && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"h6\", {\n    className: \"mb-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaExclamationTriangle, {\n    className: \"me-2 text-warning\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 17\n    }\n  }), \"Detection Details\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"detection-details\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 15\n    }\n  }, detailItems.map((item, index) => /*#__PURE__*/React.createElement(Alert, {\n    key: index,\n    variant: item.severity === \"critical\" ? \"danger\" : item.severity === \"high\" ? \"warning\" : item.severity === \"medium\" ? \"info\" : \"light\",\n    className: \"py-2 mb-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 21\n    }\n  }, item.icon), item.text)))), ((_fraud$details = fraud.details) === null || _fraud$details === void 0 ? void 0 : _fraud$details.confidence_level) && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"mt-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"small\", {\n    className: \"text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 17\n    }\n  }, \"Detection Confidence:\"), \" \", fraud.details.confidence_level.replace(\"_\", \" \").toUpperCase())), ((_fraud$details2 = fraud.details) === null || _fraud$details2 === void 0 ? void 0 : _fraud$details2.detection_reason) && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"mt-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"small\", {\n    className: \"text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 17\n    }\n  }, \"Reason:\"), \" \", fraud.details.detection_reason)))));\n};\nexport default FraudAlertDetails;", "map": {"version": 3, "names": ["React", "Card", "Badge", "Row", "Col", "<PERSON><PERSON>", "ProgressBar", "FaExclamationTriangle", "FaRobot", "FaCreditCard", "FaUserSecret", "FaEye", "FaShieldAlt", "FaClock", "FaChartLine", "FaUser", "FaGavel", "FraudAlertDetails", "fraud", "_fraud$user", "_fraud$details", "_fraud$details2", "getFraudTypeInfo", "type", "typeMap", "suspicious_bidding", "icon", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "title", "description", "bot_activity", "payment_fraud", "account_takeover", "fake_listing", "shill_bidding", "replace", "toUpperCase", "getRiskLevel", "score", "level", "variant", "formatDetails", "details", "fraudType", "formatters", "d", "rapid_consecutive_bids", "text", "severity", "unusual_timing_pattern", "bid_count_in_last_hour", "bid_increment_pattern", "time_between_bids_seconds", "join", "automated_behavior", "consistent_response_time", "no_human_delays", "captcha_failures", "user_agent_suspicious", "bid_frequency_per_minute", "stolen_card_indicators", "billing_address_mismatch", "multiple_failed_payments", "velocity_check_failed", "cvv_failures", "high_risk_country", "login_from_new_location", "unusual_bidding_behavior", "password_recently_changed", "different_payment_method", "suspicious_ip", "login_time_unusual", "stock_photos_detected", "price_too_low_for_item", "vague_description", "new_seller_account", "no_seller_history", "similar_listings_found", "shill_bidding_pattern", "bidding_on_same_seller_items", "artificial_price_inflation", "coordinated_bidding", "seller_connection_suspected", "bid_timing_coordination", "formatter", "filter", "Boolean", "typeInfo", "fraud_type", "riskInfo", "risk_score", "detailItems", "Header", "xs", "bg", "Body", "md", "now", "label", "user", "username", "Date", "detected_at", "created_at", "toLocaleString", "length", "Fragment", "map", "item", "index", "key", "confidence_level", "detection_reason"], "sources": ["C:/Users/<USER>/Desktop/Online_Auction_System/frontend/src/components/FraudAlertDetails.js"], "sourcesContent": ["import React from \"react\";\nimport { <PERSON>, Badge, <PERSON>, <PERSON>, <PERSON><PERSON>, ProgressBar } from \"react-bootstrap\";\nimport \"./FraudAlertDetails.css\";\nimport {\n  FaExclamationTriangle,\n  FaRobot,\n  FaCreditCard,\n  FaUserSecret,\n  FaEye,\n  FaShieldAlt,\n  FaClock,\n  FaChartLine,\n  FaUser,\n  FaGavel,\n} from \"react-icons/fa\";\n\nconst FraudAlertDetails = ({ fraud }) => {\n  if (!fraud) return null;\n\n  // Get fraud type icon and color\n  const getFraudTypeInfo = (type) => {\n    const typeMap = {\n      suspicious_bidding: {\n        icon: <FaGavel className=\"me-2\" />,\n        color: \"warning\",\n        title: \"Suspicious Bidding Pattern\",\n        description: \"Unusual bidding behavior detected\",\n      },\n      bot_activity: {\n        icon: <FaRobot className=\"me-2\" />,\n        color: \"danger\",\n        title: \"Bot Activity Detected\",\n        description: \"Automated behavior patterns identified\",\n      },\n      payment_fraud: {\n        icon: <FaCreditCard className=\"me-2\" />,\n        color: \"danger\",\n        title: \"Payment Fraud\",\n        description: \"Suspicious payment activity detected\",\n      },\n      account_takeover: {\n        icon: <FaUserSecret className=\"me-2\" />,\n        color: \"danger\",\n        title: \"Account Takeover\",\n        description: \"Unauthorized account access suspected\",\n      },\n      fake_listing: {\n        icon: <FaEye className=\"me-2\" />,\n        color: \"warning\",\n        title: \"Fake Listing\",\n        description: \"Potentially fraudulent auction listing\",\n      },\n      shill_bidding: {\n        icon: <FaChartLine className=\"me-2\" />,\n        color: \"warning\",\n        title: \"Shill Bidding\",\n        description: \"Coordinated bidding to inflate prices\",\n      },\n    };\n    return (\n      typeMap[type] || {\n        icon: <FaExclamationTriangle className=\"me-2\" />,\n        color: \"secondary\",\n        title: type.replace(\"_\", \" \").toUpperCase(),\n        description: \"Security alert detected\",\n      }\n    );\n  };\n\n  // Get risk level info\n  const getRiskLevel = (score) => {\n    if (score >= 90)\n      return { level: \"CRITICAL\", color: \"danger\", variant: \"danger\" };\n    if (score >= 80)\n      return { level: \"HIGH\", color: \"warning\", variant: \"warning\" };\n    if (score >= 60) return { level: \"MEDIUM\", color: \"info\", variant: \"info\" };\n    return { level: \"LOW\", color: \"success\", variant: \"success\" };\n  };\n\n  // Format detection details in a user-friendly way\n  const formatDetails = (details, fraudType) => {\n    if (!details || typeof details !== \"object\") return null;\n\n    const formatters = {\n      suspicious_bidding: (d) => [\n        d.rapid_consecutive_bids && {\n          icon: \"⚡\",\n          text: \"Multiple rapid bids detected\",\n          severity: \"high\",\n        },\n        d.unusual_timing_pattern && {\n          icon: \"⏰\",\n          text: \"Unusual bidding timing pattern\",\n          severity: \"medium\",\n        },\n        d.bid_count_in_last_hour && {\n          icon: \"📊\",\n          text: `${d.bid_count_in_last_hour} bids in last hour`,\n          severity: \"high\",\n        },\n        d.bid_increment_pattern === \"suspicious\" && {\n          icon: \"📈\",\n          text: \"Suspicious bid increment pattern\",\n          severity: \"medium\",\n        },\n        d.time_between_bids_seconds && {\n          icon: \"⏱️\",\n          text: `Consistent timing: ${d.time_between_bids_seconds.join(\n            \", \"\n          )}s intervals`,\n          severity: \"high\",\n        },\n      ],\n      bot_activity: (d) => [\n        d.automated_behavior && {\n          icon: \"🤖\",\n          text: \"Automated behavior detected\",\n          severity: \"critical\",\n        },\n        d.consistent_response_time && {\n          icon: \"⚡\",\n          text: \"Consistent response times (non-human)\",\n          severity: \"high\",\n        },\n        d.no_human_delays && {\n          icon: \"🚫\",\n          text: \"No natural human delays\",\n          severity: \"high\",\n        },\n        d.captcha_failures && {\n          icon: \"🔒\",\n          text: `${d.captcha_failures} CAPTCHA failures`,\n          severity: \"medium\",\n        },\n        d.user_agent_suspicious && {\n          icon: \"🌐\",\n          text: \"Suspicious browser/device signature\",\n          severity: \"medium\",\n        },\n        d.bid_frequency_per_minute && {\n          icon: \"📊\",\n          text: `${d.bid_frequency_per_minute} actions per minute`,\n          severity: \"critical\",\n        },\n      ],\n      payment_fraud: (d) => [\n        d.stolen_card_indicators && {\n          icon: \"💳\",\n          text: \"Stolen card indicators detected\",\n          severity: \"critical\",\n        },\n        d.billing_address_mismatch && {\n          icon: \"📍\",\n          text: \"Billing address mismatch\",\n          severity: \"high\",\n        },\n        d.multiple_failed_payments && {\n          icon: \"❌\",\n          text: \"Multiple failed payment attempts\",\n          severity: \"high\",\n        },\n        d.velocity_check_failed && {\n          icon: \"🚨\",\n          text: \"Velocity check failed\",\n          severity: \"high\",\n        },\n        d.cvv_failures && {\n          icon: \"🔢\",\n          text: `${d.cvv_failures} CVV verification failures`,\n          severity: \"medium\",\n        },\n        d.high_risk_country && {\n          icon: \"🌍\",\n          text: \"Transaction from high-risk location\",\n          severity: \"medium\",\n        },\n      ],\n      account_takeover: (d) => [\n        d.login_from_new_location && {\n          icon: \"📍\",\n          text: \"Login from new/unusual location\",\n          severity: \"high\",\n        },\n        d.unusual_bidding_behavior && {\n          icon: \"🎯\",\n          text: \"Unusual bidding behavior for this user\",\n          severity: \"medium\",\n        },\n        d.password_recently_changed && {\n          icon: \"🔑\",\n          text: \"Password recently changed\",\n          severity: \"medium\",\n        },\n        d.different_payment_method && {\n          icon: \"💳\",\n          text: \"New payment method added\",\n          severity: \"medium\",\n        },\n        d.suspicious_ip && {\n          icon: \"🌐\",\n          text: `Suspicious IP: ${d.suspicious_ip}`,\n          severity: \"high\",\n        },\n        d.login_time_unusual && {\n          icon: \"⏰\",\n          text: \"Login at unusual time\",\n          severity: \"low\",\n        },\n      ],\n      fake_listing: (d) => [\n        d.stock_photos_detected && {\n          icon: \"📸\",\n          text: \"Stock photos detected\",\n          severity: \"high\",\n        },\n        d.price_too_low_for_item && {\n          icon: \"💰\",\n          text: \"Price significantly below market value\",\n          severity: \"high\",\n        },\n        d.vague_description && {\n          icon: \"📝\",\n          text: \"Vague or generic description\",\n          severity: \"medium\",\n        },\n        d.new_seller_account && {\n          icon: \"👤\",\n          text: \"New seller account\",\n          severity: \"medium\",\n        },\n        d.no_seller_history && {\n          icon: \"📊\",\n          text: \"No previous selling history\",\n          severity: \"medium\",\n        },\n        d.similar_listings_found && {\n          icon: \"🔍\",\n          text: `${d.similar_listings_found} similar listings found elsewhere`,\n          severity: \"high\",\n        },\n      ],\n      shill_bidding: (d) => [\n        d.shill_bidding_pattern && {\n          icon: \"🎭\",\n          text: \"Shill bidding pattern detected\",\n          severity: \"high\",\n        },\n        d.bidding_on_same_seller_items && {\n          icon: \"🔄\",\n          text: \"Repeatedly bidding on same seller items\",\n          severity: \"high\",\n        },\n        d.artificial_price_inflation && {\n          icon: \"📈\",\n          text: \"Artificial price inflation detected\",\n          severity: \"high\",\n        },\n        d.coordinated_bidding && {\n          icon: \"🤝\",\n          text: \"Coordinated bidding activity\",\n          severity: \"critical\",\n        },\n        d.seller_connection_suspected && {\n          icon: \"🔗\",\n          text: \"Connection to seller suspected\",\n          severity: \"high\",\n        },\n        d.bid_timing_coordination && {\n          icon: \"⏰\",\n          text: \"Coordinated bid timing\",\n          severity: \"medium\",\n        },\n      ],\n    };\n\n    const formatter = formatters[fraudType];\n    if (!formatter) return null;\n\n    return formatter(details).filter(Boolean);\n  };\n\n  const typeInfo = getFraudTypeInfo(fraud.fraud_type);\n  const riskInfo = getRiskLevel(fraud.risk_score);\n  const detailItems = formatDetails(fraud.details, fraud.fraud_type);\n\n  return (\n    <div className=\"fraud-alert-details\">\n      {/* Header */}\n      <Card className={`border-${typeInfo.color} mb-3`}>\n        <Card.Header className={`bg-${typeInfo.color} text-white`}>\n          <Row className=\"align-items-center\">\n            <Col>\n              <h5 className=\"mb-0\">\n                {typeInfo.icon}\n                {typeInfo.title}\n              </h5>\n              <small>{typeInfo.description}</small>\n            </Col>\n            <Col xs=\"auto\">\n              <Badge bg={riskInfo.variant} className=\"fs-6\">\n                {riskInfo.level} RISK\n              </Badge>\n            </Col>\n          </Row>\n        </Card.Header>\n\n        <Card.Body>\n          {/* Risk Score */}\n          <Row className=\"mb-3\">\n            <Col md={6}>\n              <div className=\"d-flex align-items-center mb-2\">\n                <FaShieldAlt className=\"me-2 text-danger\" />\n                <strong>Risk Score: {fraud.risk_score}/100</strong>\n              </div>\n              <ProgressBar\n                variant={riskInfo.variant}\n                now={fraud.risk_score}\n                label={`${fraud.risk_score}%`}\n                className=\"mb-2\"\n              />\n            </Col>\n            <Col md={6}>\n              <div className=\"d-flex align-items-center mb-2\">\n                <FaUser className=\"me-2 text-primary\" />\n                <strong>User: {fraud.user?.username || fraud.user}</strong>\n              </div>\n              <div className=\"d-flex align-items-center\">\n                <FaClock className=\"me-2 text-muted\" />\n                <small className=\"text-muted\">\n                  Detected:{\" \"}\n                  {new Date(\n                    fraud.detected_at || fraud.created_at\n                  ).toLocaleString()}\n                </small>\n              </div>\n            </Col>\n          </Row>\n\n          {/* Detection Details */}\n          {detailItems && detailItems.length > 0 && (\n            <>\n              <h6 className=\"mb-3\">\n                <FaExclamationTriangle className=\"me-2 text-warning\" />\n                Detection Details\n              </h6>\n              <div className=\"detection-details\">\n                {detailItems.map((item, index) => (\n                  <Alert\n                    key={index}\n                    variant={\n                      item.severity === \"critical\"\n                        ? \"danger\"\n                        : item.severity === \"high\"\n                        ? \"warning\"\n                        : item.severity === \"medium\"\n                        ? \"info\"\n                        : \"light\"\n                    }\n                    className=\"py-2 mb-2\"\n                  >\n                    <span className=\"me-2\">{item.icon}</span>\n                    {item.text}\n                  </Alert>\n                ))}\n              </div>\n            </>\n          )}\n\n          {/* Confidence Level */}\n          {fraud.details?.confidence_level && (\n            <div className=\"mt-3\">\n              <small className=\"text-muted\">\n                <strong>Detection Confidence:</strong>{\" \"}\n                {fraud.details.confidence_level.replace(\"_\", \" \").toUpperCase()}\n              </small>\n            </div>\n          )}\n\n          {/* Detection Reason */}\n          {fraud.details?.detection_reason && (\n            <div className=\"mt-2\">\n              <small className=\"text-muted\">\n                <strong>Reason:</strong> {fraud.details.detection_reason}\n              </small>\n            </div>\n          )}\n        </Card.Body>\n      </Card>\n    </div>\n  );\n};\n\nexport default FraudAlertDetails;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,WAAW,QAAQ,iBAAiB;AAC3E,OAAO,yBAAyB;AAChC,SACEC,qBAAqB,EACrBC,OAAO,EACPC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,WAAW,EACXC,OAAO,EACPC,WAAW,EACXC,MAAM,EACNC,OAAO,QACF,gBAAgB;AAEvB,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAA,IAAAC,WAAA,EAAAC,cAAA,EAAAC,eAAA;EACvC,IAAI,CAACH,KAAK,EAAE,OAAO,IAAI;;EAEvB;EACA,MAAMI,gBAAgB,GAAIC,IAAI,IAAK;IACjC,MAAMC,OAAO,GAAG;MACdC,kBAAkB,EAAE;QAClBC,IAAI,eAAE1B,KAAA,CAAA2B,aAAA,CAACX,OAAO;UAACY,SAAS,EAAC,MAAM;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QAClCC,KAAK,EAAE,SAAS;QAChBC,KAAK,EAAE,4BAA4B;QACnCC,WAAW,EAAE;MACf,CAAC;MACDC,YAAY,EAAE;QACZZ,IAAI,eAAE1B,KAAA,CAAA2B,aAAA,CAACnB,OAAO;UAACoB,SAAS,EAAC,MAAM;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QAClCC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,uBAAuB;QAC9BC,WAAW,EAAE;MACf,CAAC;MACDE,aAAa,EAAE;QACbb,IAAI,eAAE1B,KAAA,CAAA2B,aAAA,CAAClB,YAAY;UAACmB,SAAS,EAAC,MAAM;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACvCC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,eAAe;QACtBC,WAAW,EAAE;MACf,CAAC;MACDG,gBAAgB,EAAE;QAChBd,IAAI,eAAE1B,KAAA,CAAA2B,aAAA,CAACjB,YAAY;UAACkB,SAAS,EAAC,MAAM;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACvCC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,kBAAkB;QACzBC,WAAW,EAAE;MACf,CAAC;MACDI,YAAY,EAAE;QACZf,IAAI,eAAE1B,KAAA,CAAA2B,aAAA,CAAChB,KAAK;UAACiB,SAAS,EAAC,MAAM;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QAChCC,KAAK,EAAE,SAAS;QAChBC,KAAK,EAAE,cAAc;QACrBC,WAAW,EAAE;MACf,CAAC;MACDK,aAAa,EAAE;QACbhB,IAAI,eAAE1B,KAAA,CAAA2B,aAAA,CAACb,WAAW;UAACc,SAAS,EAAC,MAAM;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACtCC,KAAK,EAAE,SAAS;QAChBC,KAAK,EAAE,eAAe;QACtBC,WAAW,EAAE;MACf;IACF,CAAC;IACD,OACEb,OAAO,CAACD,IAAI,CAAC,IAAI;MACfG,IAAI,eAAE1B,KAAA,CAAA2B,aAAA,CAACpB,qBAAqB;QAACqB,SAAS,EAAC,MAAM;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;MAChDC,KAAK,EAAE,WAAW;MAClBC,KAAK,EAAEb,IAAI,CAACoB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC;MAC3CP,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMQ,YAAY,GAAIC,KAAK,IAAK;IAC9B,IAAIA,KAAK,IAAI,EAAE,EACb,OAAO;MAAEC,KAAK,EAAE,UAAU;MAAEZ,KAAK,EAAE,QAAQ;MAAEa,OAAO,EAAE;IAAS,CAAC;IAClE,IAAIF,KAAK,IAAI,EAAE,EACb,OAAO;MAAEC,KAAK,EAAE,MAAM;MAAEZ,KAAK,EAAE,SAAS;MAAEa,OAAO,EAAE;IAAU,CAAC;IAChE,IAAIF,KAAK,IAAI,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,QAAQ;MAAEZ,KAAK,EAAE,MAAM;MAAEa,OAAO,EAAE;IAAO,CAAC;IAC3E,OAAO;MAAED,KAAK,EAAE,KAAK;MAAEZ,KAAK,EAAE,SAAS;MAAEa,OAAO,EAAE;IAAU,CAAC;EAC/D,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACC,OAAO,EAAEC,SAAS,KAAK;IAC5C,IAAI,CAACD,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE,OAAO,IAAI;IAExD,MAAME,UAAU,GAAG;MACjB3B,kBAAkB,EAAG4B,CAAC,IAAK,CACzBA,CAAC,CAACC,sBAAsB,IAAI;QAC1B5B,IAAI,EAAE,GAAG;QACT6B,IAAI,EAAE,8BAA8B;QACpCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACI,sBAAsB,IAAI;QAC1B/B,IAAI,EAAE,GAAG;QACT6B,IAAI,EAAE,gCAAgC;QACtCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACK,sBAAsB,IAAI;QAC1BhC,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,GAAGF,CAAC,CAACK,sBAAsB,oBAAoB;QACrDF,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACM,qBAAqB,KAAK,YAAY,IAAI;QAC1CjC,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,kCAAkC;QACxCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACO,yBAAyB,IAAI;QAC7BlC,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,sBAAsBF,CAAC,CAACO,yBAAyB,CAACC,IAAI,CAC1D,IACF,CAAC,aAAa;QACdL,QAAQ,EAAE;MACZ,CAAC,CACF;MACDlB,YAAY,EAAGe,CAAC,IAAK,CACnBA,CAAC,CAACS,kBAAkB,IAAI;QACtBpC,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,6BAA6B;QACnCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACU,wBAAwB,IAAI;QAC5BrC,IAAI,EAAE,GAAG;QACT6B,IAAI,EAAE,uCAAuC;QAC7CC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACW,eAAe,IAAI;QACnBtC,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,yBAAyB;QAC/BC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACY,gBAAgB,IAAI;QACpBvC,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,GAAGF,CAAC,CAACY,gBAAgB,mBAAmB;QAC9CT,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACa,qBAAqB,IAAI;QACzBxC,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,qCAAqC;QAC3CC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACc,wBAAwB,IAAI;QAC5BzC,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,GAAGF,CAAC,CAACc,wBAAwB,qBAAqB;QACxDX,QAAQ,EAAE;MACZ,CAAC,CACF;MACDjB,aAAa,EAAGc,CAAC,IAAK,CACpBA,CAAC,CAACe,sBAAsB,IAAI;QAC1B1C,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,iCAAiC;QACvCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACgB,wBAAwB,IAAI;QAC5B3C,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,0BAA0B;QAChCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACiB,wBAAwB,IAAI;QAC5B5C,IAAI,EAAE,GAAG;QACT6B,IAAI,EAAE,kCAAkC;QACxCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACkB,qBAAqB,IAAI;QACzB7C,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,uBAAuB;QAC7BC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACmB,YAAY,IAAI;QAChB9C,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,GAAGF,CAAC,CAACmB,YAAY,4BAA4B;QACnDhB,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACoB,iBAAiB,IAAI;QACrB/C,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,qCAAqC;QAC3CC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDhB,gBAAgB,EAAGa,CAAC,IAAK,CACvBA,CAAC,CAACqB,uBAAuB,IAAI;QAC3BhD,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,iCAAiC;QACvCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACsB,wBAAwB,IAAI;QAC5BjD,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,wCAAwC;QAC9CC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACuB,yBAAyB,IAAI;QAC7BlD,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,2BAA2B;QACjCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACwB,wBAAwB,IAAI;QAC5BnD,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,0BAA0B;QAChCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACyB,aAAa,IAAI;QACjBpD,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,kBAAkBF,CAAC,CAACyB,aAAa,EAAE;QACzCtB,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAAC0B,kBAAkB,IAAI;QACtBrD,IAAI,EAAE,GAAG;QACT6B,IAAI,EAAE,uBAAuB;QAC7BC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDf,YAAY,EAAGY,CAAC,IAAK,CACnBA,CAAC,CAAC2B,qBAAqB,IAAI;QACzBtD,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,uBAAuB;QAC7BC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAAC4B,sBAAsB,IAAI;QAC1BvD,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,wCAAwC;QAC9CC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAAC6B,iBAAiB,IAAI;QACrBxD,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,8BAA8B;QACpCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAAC8B,kBAAkB,IAAI;QACtBzD,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,oBAAoB;QAC1BC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAAC+B,iBAAiB,IAAI;QACrB1D,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,6BAA6B;QACnCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACgC,sBAAsB,IAAI;QAC1B3D,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,GAAGF,CAAC,CAACgC,sBAAsB,mCAAmC;QACpE7B,QAAQ,EAAE;MACZ,CAAC,CACF;MACDd,aAAa,EAAGW,CAAC,IAAK,CACpBA,CAAC,CAACiC,qBAAqB,IAAI;QACzB5D,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,gCAAgC;QACtCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACkC,4BAA4B,IAAI;QAChC7D,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,yCAAyC;QAC/CC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACmC,0BAA0B,IAAI;QAC9B9D,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,qCAAqC;QAC3CC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACoC,mBAAmB,IAAI;QACvB/D,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,8BAA8B;QACpCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACqC,2BAA2B,IAAI;QAC/BhE,IAAI,EAAE,IAAI;QACV6B,IAAI,EAAE,gCAAgC;QACtCC,QAAQ,EAAE;MACZ,CAAC,EACDH,CAAC,CAACsC,uBAAuB,IAAI;QAC3BjE,IAAI,EAAE,GAAG;QACT6B,IAAI,EAAE,wBAAwB;QAC9BC,QAAQ,EAAE;MACZ,CAAC;IAEL,CAAC;IAED,MAAMoC,SAAS,GAAGxC,UAAU,CAACD,SAAS,CAAC;IACvC,IAAI,CAACyC,SAAS,EAAE,OAAO,IAAI;IAE3B,OAAOA,SAAS,CAAC1C,OAAO,CAAC,CAAC2C,MAAM,CAACC,OAAO,CAAC;EAC3C,CAAC;EAED,MAAMC,QAAQ,GAAGzE,gBAAgB,CAACJ,KAAK,CAAC8E,UAAU,CAAC;EACnD,MAAMC,QAAQ,GAAGpD,YAAY,CAAC3B,KAAK,CAACgF,UAAU,CAAC;EAC/C,MAAMC,WAAW,GAAGlD,aAAa,CAAC/B,KAAK,CAACgC,OAAO,EAAEhC,KAAK,CAAC8E,UAAU,CAAC;EAElE,oBACEhG,KAAA,CAAA2B,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElClC,KAAA,CAAA2B,aAAA,CAAC1B,IAAI;IAAC2B,SAAS,EAAE,UAAUmE,QAAQ,CAAC5D,KAAK,OAAQ;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/ClC,KAAA,CAAA2B,aAAA,CAAC1B,IAAI,CAACmG,MAAM;IAACxE,SAAS,EAAE,MAAMmE,QAAQ,CAAC5D,KAAK,aAAc;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxDlC,KAAA,CAAA2B,aAAA,CAACxB,GAAG;IAACyB,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjClC,KAAA,CAAA2B,aAAA,CAACvB,GAAG;IAAAyB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACFlC,KAAA,CAAA2B,aAAA;IAAIC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjB6D,QAAQ,CAACrE,IAAI,EACbqE,QAAQ,CAAC3D,KACR,CAAC,eACLpC,KAAA,CAAA2B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQ6D,QAAQ,CAAC1D,WAAmB,CACjC,CAAC,eACNrC,KAAA,CAAA2B,aAAA,CAACvB,GAAG;IAACiG,EAAE,EAAC,MAAM;IAAAxE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACZlC,KAAA,CAAA2B,aAAA,CAACzB,KAAK;IAACoG,EAAE,EAAEL,QAAQ,CAACjD,OAAQ;IAACpB,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1C+D,QAAQ,CAAClD,KAAK,EAAC,OACX,CACJ,CACF,CACM,CAAC,eAEd/C,KAAA,CAAA2B,aAAA,CAAC1B,IAAI,CAACsG,IAAI;IAAA1E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAERlC,KAAA,CAAA2B,aAAA,CAACxB,GAAG;IAACyB,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBlC,KAAA,CAAA2B,aAAA,CAACvB,GAAG;IAACoG,EAAE,EAAE,CAAE;IAAA3E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACTlC,KAAA,CAAA2B,aAAA;IAAKC,SAAS,EAAC,gCAAgC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7ClC,KAAA,CAAA2B,aAAA,CAACf,WAAW;IAACgB,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC5ClC,KAAA,CAAA2B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQ,cAAY,EAAChB,KAAK,CAACgF,UAAU,EAAC,MAAY,CAC/C,CAAC,eACNlG,KAAA,CAAA2B,aAAA,CAACrB,WAAW;IACV0C,OAAO,EAAEiD,QAAQ,CAACjD,OAAQ;IAC1ByD,GAAG,EAAEvF,KAAK,CAACgF,UAAW;IACtBQ,KAAK,EAAE,GAAGxF,KAAK,CAACgF,UAAU,GAAI;IAC9BtE,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACjB,CACE,CAAC,eACNlC,KAAA,CAAA2B,aAAA,CAACvB,GAAG;IAACoG,EAAE,EAAE,CAAE;IAAA3E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACTlC,KAAA,CAAA2B,aAAA;IAAKC,SAAS,EAAC,gCAAgC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7ClC,KAAA,CAAA2B,aAAA,CAACZ,MAAM;IAACa,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACxClC,KAAA,CAAA2B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQ,QAAM,EAAC,EAAAf,WAAA,GAAAD,KAAK,CAACyF,IAAI,cAAAxF,WAAA,uBAAVA,WAAA,CAAYyF,QAAQ,KAAI1F,KAAK,CAACyF,IAAa,CACvD,CAAC,eACN3G,KAAA,CAAA2B,aAAA;IAAKC,SAAS,EAAC,2BAA2B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxClC,KAAA,CAAA2B,aAAA,CAACd,OAAO;IAACe,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACvClC,KAAA,CAAA2B,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,WACnB,EAAC,GAAG,EACZ,IAAI2E,IAAI,CACP3F,KAAK,CAAC4F,WAAW,IAAI5F,KAAK,CAAC6F,UAC7B,CAAC,CAACC,cAAc,CAAC,CACZ,CACJ,CACF,CACF,CAAC,EAGLb,WAAW,IAAIA,WAAW,CAACc,MAAM,GAAG,CAAC,iBACpCjH,KAAA,CAAA2B,aAAA,CAAA3B,KAAA,CAAAkH,QAAA,qBACElH,KAAA,CAAA2B,aAAA;IAAIC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClBlC,KAAA,CAAA2B,aAAA,CAACpB,qBAAqB;IAACqB,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,qBAErD,CAAC,eACLlC,KAAA,CAAA2B,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/BiE,WAAW,CAACgB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3BrH,KAAA,CAAA2B,aAAA,CAACtB,KAAK;IACJiH,GAAG,EAAED,KAAM;IACXrE,OAAO,EACLoE,IAAI,CAAC5D,QAAQ,KAAK,UAAU,GACxB,QAAQ,GACR4D,IAAI,CAAC5D,QAAQ,KAAK,MAAM,GACxB,SAAS,GACT4D,IAAI,CAAC5D,QAAQ,KAAK,QAAQ,GAC1B,MAAM,GACN,OACL;IACD5B,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAErBlC,KAAA,CAAA2B,aAAA;IAAMC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEkF,IAAI,CAAC1F,IAAW,CAAC,EACxC0F,IAAI,CAAC7D,IACD,CACR,CACE,CACL,CACH,EAGA,EAAAnC,cAAA,GAAAF,KAAK,CAACgC,OAAO,cAAA9B,cAAA,uBAAbA,cAAA,CAAemG,gBAAgB,kBAC9BvH,KAAA,CAAA2B,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBlC,KAAA,CAAA2B,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BlC,KAAA,CAAA2B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQ,uBAA6B,CAAC,EAAC,GAAG,EACzChB,KAAK,CAACgC,OAAO,CAACqE,gBAAgB,CAAC5E,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CACzD,CACJ,CACN,EAGA,EAAAvB,eAAA,GAAAH,KAAK,CAACgC,OAAO,cAAA7B,eAAA,uBAAbA,eAAA,CAAemG,gBAAgB,kBAC9BxH,KAAA,CAAA2B,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBlC,KAAA,CAAA2B,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BlC,KAAA,CAAA2B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQ,SAAe,CAAC,KAAC,EAAChB,KAAK,CAACgC,OAAO,CAACsE,gBACnC,CACJ,CAEE,CACP,CACH,CAAC;AAEV,CAAC;AAED,eAAevG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}