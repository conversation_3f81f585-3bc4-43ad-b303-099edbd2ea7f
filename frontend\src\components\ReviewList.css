/* Review List Styles */

.review-list {
  margin-top: 1rem;
}

.review-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.review-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.stats-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stats-card .card-body {
  padding: 2rem;
}

/* Star ratings */
.stars {
  display: flex;
  gap: 2px;
}

.star-filled {
  color: #ffc107;
  font-size: 1rem;
}

.star-empty {
  color: #e9ecef;
  font-size: 1rem;
}

/* Rating breakdown */
.rating-breakdown {
  padding: 0 1rem;
}

.rating-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  gap: 0.75rem;
}

.rating-label {
  min-width: 50px;
  font-size: 0.875rem;
  color: #6c757d;
}

.rating-bar {
  flex: 1;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.rating-fill {
  height: 100%;
  background: linear-gradient(90deg, #ffc107 0%, #ffb300 100%);
  transition: width 0.3s ease;
}

.rating-count {
  min-width: 30px;
  text-align: right;
  font-size: 0.875rem;
  color: #6c757d;
}

/* Review content */
.review-comment {
  font-size: 1rem;
  line-height: 1.6;
  color: #495057;
  margin-bottom: 1rem;
}

.review-meta {
  border-top: 1px solid #f8f9fa;
  padding-top: 0.75rem;
}

.review-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;
}

/* Badges */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
}

.badge.bg-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.badge.bg-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.badge.bg-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
}

/* Buttons */
.btn-outline-primary {
  border-color: #007bff;
  color: #007bff;
  transition: all 0.3s ease;
}

.btn-outline-primary:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  transform: translateY(-1px);
}

.btn-outline-danger {
  border-color: #dc3545;
  color: #dc3545;
  transition: all 0.3s ease;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
  transform: translateY(-1px);
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 4px;
}

/* Statistics */
.stat-item {
  padding: 1rem;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-item strong {
  font-size: 1.5rem;
  color: #495057;
}

/* Empty state */
.review-list .card .fa-user {
  opacity: 0.3;
}

/* Loading state */
.spinner-border {
  color: #007bff;
}

/* Pagination */
.pagination {
  margin-bottom: 0;
}

.page-link {
  color: #007bff;
  border-color: #dee2e6;
  padding: 0.5rem 0.75rem;
  transition: all 0.3s ease;
}

.page-link:hover {
  color: #0056b3;
  background-color: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-1px);
}

.page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}

.page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
}

/* Responsive design */
@media (max-width: 768px) {
  .stats-card .card-body {
    padding: 1rem;
  }
  
  .rating-breakdown {
    padding: 0;
    margin-top: 1rem;
  }
  
  .review-actions {
    flex-direction: row;
    justify-content: flex-start;
    margin-top: 1rem;
  }
  
  .review-card .row {
    flex-direction: column;
  }
  
  .review-card .col-md-4 {
    text-align: left !important;
    margin-top: 1rem;
  }
  
  .stat-item {
    margin-bottom: 1rem;
  }
}

@media (max-width: 576px) {
  .review-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .btn-sm {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .pagination {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .page-item {
    margin: 0.125rem;
  }
}

/* Animations */
.review-card {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.rating-fill {
  animation: fillBar 1s ease-out;
}

@keyframes fillBar {
  from {
    width: 0;
  }
  to {
    width: var(--final-width);
  }
}

/* Hover effects */
.review-card .badge {
  transition: transform 0.2s ease;
}

.review-card:hover .badge {
  transform: scale(1.05);
}

/* Focus states for accessibility */
.btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.page-link:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Text styling */
.text-muted {
  color: #6c757d !important;
}

.review-meta .text-muted {
  font-size: 0.875rem;
}

/* Card spacing */
.review-card .card-body {
  padding: 1.5rem;
}

/* Icon styling */
.fa-check-circle,
.fa-thumbs-up,
.fa-flag {
  font-size: 0.875rem;
}

/* Verified badge styling */
.badge.bg-success .fa-check-circle {
  font-size: 0.75rem;
}

/* Review type specific styling */
.badge.bg-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.badge.bg-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.badge.bg-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
}
