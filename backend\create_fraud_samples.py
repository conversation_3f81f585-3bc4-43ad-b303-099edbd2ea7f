#!/usr/bin/env python3
"""
Create Sample Fraud Detection Data
Creates realistic fraud detection records for testing
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from django.contrib.auth.models import User
from auction.models import FraudDetection, Auction
from decimal import Decimal
from django.utils import timezone
from datetime import timedelta

def create_sample_fraud_data():
    """Create sample fraud detection records"""
    print("📝 Creating Sample Fraud Detection Data...")
    print("=" * 60)
    
    # Create test users
    users = []
    for i in range(3):
        user, created = User.objects.get_or_create(
            username=f'fraud_user_{i+1}',
            defaults={
                'email': f'fraud_user_{i+1}@example.com',
                'password': 'testpass123'
            }
        )
        users.append(user)
        if created:
            print(f"✅ Created test user: {user.username}")
    
    # Sample fraud detection data
    fraud_samples = [
        {
            'user': users[0],
            'fraud_type': 'suspicious_bidding',
            'risk_score': 85,
            'details': {
                'rapid_bids': True,
                'bid_count': 15,
                'time_span_minutes': 5,
                'pattern': 'rapid_consecutive',
                'suspicious_timing': True,
                'bid_increments': 'small_consistent'
            },
            'status': 'pending'
        },
        {
            'user': users[1],
            'fraud_type': 'bot_activity',
            'risk_score': 92,
            'details': {
                'automated_bidding': True,
                'consistent_timing': True,
                'no_human_delays': True,
                'bid_frequency_per_minute': 12,
                'user_agent_suspicious': True,
                'captcha_failures': 5,
                'ip_rotation': True
            },
            'status': 'pending'
        },
        {
            'user': users[2],
            'fraud_type': 'fake_listing',
            'risk_score': 78,
            'details': {
                'price_too_low': True,
                'suspicious_images': True,
                'copied_description': True,
                'new_seller': True,
                'no_seller_history': True,
                'stock_photos_detected': True
            },
            'status': 'confirmed'
        },
        {
            'user': users[0],
            'fraud_type': 'payment_fraud',
            'risk_score': 88,
            'details': {
                'failed_payment': True,
                'suspicious_card': True,
                'multiple_payment_attempts': 3,
                'different_billing_address': True,
                'chargeback_history': True
            },
            'status': 'pending'
        },
        {
            'user': users[1],
            'fraud_type': 'account_takeover',
            'risk_score': 95,
            'details': {
                'login_from_new_location': True,
                'password_changed_recently': True,
                'unusual_bidding_behavior': True,
                'ip_address_changes': 5,
                'failed_login_attempts': 8,
                'security_questions_failed': True,
                'device_fingerprint_mismatch': True
            },
            'status': 'pending'
        },
        {
            'user': users[2],
            'fraud_type': 'suspicious_bidding',
            'risk_score': 65,
            'details': {
                'bid_sniping': True,
                'last_second_bids': 8,
                'pattern': 'sniping',
                'automated_tools_suspected': False
            },
            'status': 'false_positive'
        }
    ]
    
    # Create fraud detection records
    created_count = 0
    for fraud_data in fraud_samples:
        try:
            fraud_detection = FraudDetection.objects.create(**fraud_data)
            created_count += 1
            print(f"✅ Created {fraud_detection.fraud_type} (Risk: {fraud_detection.risk_score}) - Status: {fraud_detection.status}")
        except Exception as e:
            print(f"❌ Failed to create fraud record: {e}")
    
    print(f"\n📊 Summary: Created {created_count} fraud detection records")
    
    # Show statistics
    print("\n📈 Fraud Detection Statistics:")
    print("-" * 40)
    
    total = FraudDetection.objects.count()
    pending = FraudDetection.objects.filter(status='pending').count()
    confirmed = FraudDetection.objects.filter(status='confirmed').count()
    resolved = FraudDetection.objects.filter(status='resolved').count()
    false_positive = FraudDetection.objects.filter(status='false_positive').count()
    
    print(f"Total fraud detections: {total}")
    print(f"Pending review: {pending}")
    print(f"Confirmed fraud: {confirmed}")
    print(f"Resolved cases: {resolved}")
    print(f"False positives: {false_positive}")
    
    # Show by fraud type
    print("\n🔍 By Fraud Type:")
    fraud_types = ['suspicious_bidding', 'fake_listing', 'payment_fraud', 'account_takeover', 'bot_activity']
    for fraud_type in fraud_types:
        count = FraudDetection.objects.filter(fraud_type=fraud_type).count()
        print(f"  {fraud_type}: {count}")
    
    # Show high-risk cases
    high_risk = FraudDetection.objects.filter(risk_score__gte=90).count()
    medium_risk = FraudDetection.objects.filter(risk_score__gte=70, risk_score__lt=90).count()
    low_risk = FraudDetection.objects.filter(risk_score__lt=70).count()
    
    print(f"\n⚠️  Risk Levels:")
    print(f"  High risk (90+): {high_risk}")
    print(f"  Medium risk (70-89): {medium_risk}")
    print(f"  Low risk (<70): {low_risk}")
    
    return created_count

def test_fraud_api_with_data():
    """Test fraud detection API with the created data"""
    print("\n🌐 Testing API with Sample Data...")
    print("-" * 40)
    
    import requests
    base_url = "http://127.0.0.1:8000/api"
    
    try:
        # Test GET all fraud detections
        response = requests.get(f"{base_url}/fraud-detection/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            count = len(data.get('results', []))
            print(f"✅ GET all fraud detections: {count} records")
            
            # Show sample record
            if data.get('results'):
                sample = data['results'][0]
                print(f"   Sample: {sample.get('fraud_type')} (Risk: {sample.get('risk_score')})")
        
        # Test filtering by high risk
        response = requests.get(f"{base_url}/fraud-detection/?risk_score__gte=90", timeout=10)
        if response.status_code == 200:
            data = response.json()
            count = len(data.get('results', []))
            print(f"✅ High-risk cases (90+): {count} records")
        
        # Test filtering by pending status
        response = requests.get(f"{base_url}/fraud-detection/?status=pending", timeout=10)
        if response.status_code == 200:
            data = response.json()
            count = len(data.get('results', []))
            print(f"✅ Pending cases: {count} records")
        
        # Test ordering by risk score
        response = requests.get(f"{base_url}/fraud-detection/?ordering=-risk_score", timeout=10)
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            if results:
                highest = results[0].get('risk_score', 0)
                lowest = results[-1].get('risk_score', 0)
                print(f"✅ Risk score range: {highest} (highest) to {lowest} (lowest)")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API test failed: {e}")
        print("💡 Make sure Django server is running")
        return False

def main():
    """Main function"""
    print("🔍 FRAUD DETECTION - SAMPLE DATA CREATOR")
    print("=" * 70)
    
    # Create sample data
    created_count = create_sample_fraud_data()
    
    # Test API with data
    api_ok = test_fraud_api_with_data()
    
    # Final summary
    print("\n" + "=" * 70)
    print("🎯 SAMPLE DATA CREATION COMPLETE")
    print("=" * 70)
    
    if created_count > 0 and api_ok:
        print("🎉 SUCCESS!")
        print(f"✅ Created {created_count} fraud detection records")
        print("✅ API endpoints working with sample data")
        print("\n💡 You can now:")
        print("   1. View fraud alerts in the admin dashboard")
        print("   2. Test fraud resolution workflows")
        print("   3. Filter and sort fraud detections")
        print("   4. Test high-risk fraud notifications")
        print("\n🚀 Fraud detection system ready for testing!")
    else:
        print("⚠️  Some issues occurred during setup")
        print("💡 Check the output above for details")

if __name__ == "__main__":
    main()
