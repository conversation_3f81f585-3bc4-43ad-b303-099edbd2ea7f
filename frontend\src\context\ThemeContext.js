import React, { createContext, useContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [darkMode, setDarkMode] = useState(false);

  // Load theme preference from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('darkMode');
    if (savedTheme !== null) {
      setDarkMode(JSON.parse(savedTheme));
    }
  }, []);

  // Apply theme to document body and save to localStorage
  useEffect(() => {
    if (darkMode) {
      document.body.className = "dark-mode-bg text-light";
      document.documentElement.setAttribute('data-theme', 'dark');
      
      // Apply dark mode to all cards
      const cards = document.querySelectorAll(".card");
      cards.forEach((card) => card.classList.add("dark-mode-card"));
      
      // Apply dark mode to other elements
      const containers = document.querySelectorAll(".container, .container-fluid");
      containers.forEach((container) => container.classList.add("dark-mode-container"));
      
    } else {
      document.body.className = "bg-light text-dark";
      document.documentElement.setAttribute('data-theme', 'light');
      
      // Remove dark mode classes
      const cards = document.querySelectorAll(".card");
      cards.forEach((card) => card.classList.remove("dark-mode-card"));
      
      const containers = document.querySelectorAll(".container, .container-fluid");
      containers.forEach((container) => container.classList.remove("dark-mode-container"));
    }
    
    // Save to localStorage
    localStorage.setItem('darkMode', JSON.stringify(darkMode));
  }, [darkMode]);

  const toggleDarkMode = () => {
    setDarkMode(prev => !prev);
  };

  // Theme colors object for easy access
  const theme = {
    colors: {
      // Light mode colors
      light: {
        primary: '#3498db',
        secondary: '#2c3e50',
        success: '#27ae60',
        danger: '#e74c3c',
        warning: '#f39c12',
        info: '#17a2b8',
        background: '#ffffff',
        surface: '#f8f9fa',
        text: '#212529',
        textSecondary: '#6c757d',
        textMuted: '#868e96',
        border: '#dee2e6',
        shadow: 'rgba(0, 0, 0, 0.1)',
      },
      // Dark mode colors
      dark: {
        primary: '#3498db',
        secondary: '#34495e',
        success: '#2ecc71',
        danger: '#e74c3c',
        warning: '#f39c12',
        info: '#3498db',
        background: '#0f172a',
        surface: '#1e293b',
        text: '#f8fafc',
        textSecondary: '#cbd5e1',
        textMuted: '#94a3b8',
        border: '#334155',
        shadow: 'rgba(0, 0, 0, 0.3)',
      }
    }
  };

  const currentTheme = darkMode ? theme.colors.dark : theme.colors.light;

  const value = {
    darkMode,
    toggleDarkMode,
    theme: currentTheme,
    colors: theme.colors,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext;
