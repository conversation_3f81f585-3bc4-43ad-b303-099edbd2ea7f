#!/usr/bin/env python3
"""
Test script for Admin API endpoints
Tests the admin dashboard and user management APIs
"""

import os
import sys
import django
import requests

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from django.contrib.auth.models import User
from auction.models import Auction, Bid
from rest_framework_simplejwt.tokens import RefreshToken

def test_admin_apis():
    """Test admin API endpoints"""
    
    print("🧪 Testing Admin API Endpoints")
    print("=" * 50)
    
    try:
        # Get or create admin user
        admin_user, created = User.objects.get_or_create(
            username='admin_test',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            print(f"✅ Created admin user: {admin_user.username}")
        else:
            print(f"✅ Using existing admin user: {admin_user.username}")
        
        # Get JWT token
        refresh = RefreshToken.for_user(admin_user)
        access_token = str(refresh.access_token)
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        base_url = "http://127.0.0.1:8000/api"
        
        # Test 1: Admin Users List
        print("\n1️⃣ Testing Admin Users List API...")
        try:
            response = requests.get(f"{base_url}/admin/users/", headers=headers)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Users found: {data.get('count', 0)}")
                print(f"   📄 Pages: {data.get('num_pages', 0)}")
                print(f"   👥 Users in response: {len(data.get('users', []))}")
                
                # Show first few users
                users = data.get('users', [])[:3]
                for user in users:
                    print(f"      - {user['username']} ({user['email']})")
            else:
                print(f"   ❌ Error: {response.text}")
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        # Test 2: Platform Stats
        print("\n2️⃣ Testing Platform Stats API...")
        try:
            response = requests.get(f"{base_url}/landing/stats/")
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                stats = data.get('stats', {})
                print(f"   ✅ Total Users: {stats.get('total_users', 0)}")
                print(f"   🏷️ Total Auctions: {stats.get('total_auctions', 0)}")
                print(f"   🔥 Active Auctions: {stats.get('active_auctions', 0)}")
                print(f"   💰 Total Bids: {stats.get('total_bids', 0)}")
            else:
                print(f"   ❌ Error: {response.text}")
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        # Test 3: Direct Database Count
        print("\n3️⃣ Testing Direct Database Counts...")
        total_users = User.objects.count()
        total_auctions = Auction.objects.count()
        total_bids = Bid.objects.count()
        
        print(f"   👥 Database Users: {total_users}")
        print(f"   🏷️ Database Auctions: {total_auctions}")
        print(f"   💰 Database Bids: {total_bids}")
        
        # Test 4: Admin Dashboard Stats (Extended)
        print("\n4️⃣ Testing Extended Admin Dashboard Stats...")
        try:
            response = requests.get(f"{base_url}/extended/admin/dashboard-stats/", headers=headers)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    stats = data.get('data', {})
                    overview = stats.get('overview', {})
                    print(f"   ✅ Extended Stats Success")
                    print(f"   👥 Total Users: {overview.get('total_users', 0)}")
                    print(f"   🏷️ Total Auctions: {overview.get('total_auctions', 0)}")
                    print(f"   🔥 Active Auctions: {overview.get('active_auctions', 0)}")
                    print(f"   💰 Total Revenue: {overview.get('total_revenue', 0)}")
                    print(f"   📊 Full data structure: {stats}")
                else:
                    print(f"   ❌ Extended Stats Failed: {data.get('error', 'Unknown error')}")
            else:
                print(f"   ❌ Error: {response.text}")
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        # Test 5: Check if admin user has proper permissions
        print("\n5️⃣ Testing Admin User Permissions...")
        print(f"   Username: {admin_user.username}")
        print(f"   Is Staff: {admin_user.is_staff}")
        print(f"   Is Superuser: {admin_user.is_superuser}")
        print(f"   Is Active: {admin_user.is_active}")
        
        print("\n🎉 Admin API test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_admin_apis()
