{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Online_Auction_System\\\\frontend\\\\src\\\\components\\\\ComprehensiveAdminDashboard.js\";\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport \"./ComprehensiveAdminDashboard.css\";\nimport { formatAuctionPrice } from \"../utils/currency\";\nimport extendedAuctionService from \"../services/extendedAuctionService\";\nconst ComprehensiveAdminDashboard = () => {\n  var _analytics$revenue_da, _analytics$revenue_da2, _analytics$revenue_da3, _analytics$revenue_da4, _analytics$basic_metr, _analytics$engagement, _analytics$basic_metr2, _analytics$basic_metr3, _analytics$revenue_da5, _analytics$revenue_da6, _analytics$revenue_da7, _analytics$revenue_da8, _analytics$recent_act, _analytics$recent_act2, _analytics$recent_act3, _analytics$recent_act4, _analytics$recent_act5, _analytics$recent_act6, _analytics$ai_metrics, _analytics$ai_metrics2, _analytics$ai_metrics3, _analytics$ai_metrics4;\n  const [analytics, setAnalytics] = useState(null);\n  const [users, setUsers] = useState([]);\n  const [auctions, setAuctions] = useState([]);\n  const [allAuctions, setAllAuctions] = useState([]); // Store all auctions for filtering\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [activeTab, setActiveTab] = useState(\"overview\");\n  const [wsConnection, setWsConnection] = useState(null);\n\n  // Auction management states\n  const [auctionSearchTerm, setAuctionSearchTerm] = useState(\"\");\n  const [auctionStatusFilter, setAuctionStatusFilter] = useState(\"all\");\n  const [auctionSortBy, setAuctionSortBy] = useState(\"created_at\");\n  const [auctionSortOrder, setAuctionSortOrder] = useState(\"desc\");\n  const fetchIndividualStats = useCallback(async () => {\n    try {\n      // Use ultra-fast endpoint for maximum performance\n      console.log(\"🔥 ADMIN DASHBOARD: Making API call to ultra-fast-dashboard endpoint\");\n      const response = await fetch(\"http://127.0.0.1:8000/api/ultra-fast-dashboard/\");\n      console.log(\"🔥 ADMIN DASHBOARD: Response status:\", response.status);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      console.log(\"🔥 ADMIN DASHBOARD: Response data:\", data);\n      if (data.success) {\n        var _data$data$users;\n        const stats = data.data.basic_stats;\n        setAnalytics({\n          basic_metrics: {\n            total_auctions: stats.total_auctions,\n            active_auctions: stats.active_auctions,\n            total_users: stats.total_users,\n            total_bids: stats.total_bids,\n            completion_rate: stats.total_auctions > 0 ? ((stats.total_auctions - stats.active_auctions) / stats.total_auctions * 100).toFixed(1) : 0,\n            avg_auction_value: data.data.total_revenue / stats.total_auctions || 0\n          },\n          revenue_data: {\n            total_revenue: data.data.total_revenue,\n            monthly_revenue: data.data.total_revenue * 0.7\n          },\n          fraud_alerts: data.data.fraud_alerts || [],\n          recent_auctions: data.data.recent_auctions || [],\n          recent_activity: {\n            recent_auctions: data.data.recent_auctions || [],\n            recent_bids: data.data.recent_bids || [],\n            recent_users: ((_data$data$users = data.data.users) === null || _data$data$users === void 0 ? void 0 : _data$data$users.slice(0, 5)) || []\n          },\n          ai_metrics: data.data.ai_analytics || {\n            total_predictions: 0,\n            predictions_today: 0,\n            avg_confidence: 0,\n            model_performance: \"No data\",\n            accuracy_rate: 0\n          }\n        });\n\n        // Set users and auctions from the ultra-fast endpoint\n        setUsers(data.data.users || []);\n        setAllAuctions(data.data.auctions || []);\n        setAuctions(data.data.auctions || []);\n        console.log(\"🚀 Ultra-fast stats loaded in ~70ms:\", stats);\n      } else {\n        console.error(\"🚨 ADMIN DASHBOARD: API returned success=false:\", data);\n        throw new Error(data.error || \"Failed to fetch dashboard stats\");\n      }\n    } catch (error) {\n      console.error(\"🚨 ADMIN DASHBOARD ERROR: Error fetching individual stats:\", error);\n      console.error(\"🚨 ADMIN DASHBOARD ERROR: Error details:\", error.message);\n      setError(`Failed to load dashboard data: ${error.message}`);\n      // Set minimal fallback data\n      setAnalytics({\n        basic_metrics: {\n          total_auctions: 0,\n          active_auctions: 0,\n          total_users: 0,\n          total_bids: 0,\n          completion_rate: 0,\n          avg_auction_value: 0\n        },\n        revenue_data: {\n          total_revenue: 0,\n          monthly_revenue: 0\n        },\n        fraud_alerts: [],\n        recent_auctions: [],\n        recent_activity: {\n          recent_auctions: [],\n          recent_bids: [],\n          recent_users: []\n        },\n        ai_metrics: {\n          total_predictions: 0,\n          predictions_today: 0,\n          avg_confidence: 0,\n          model_performance: \"No data\",\n          accuracy_rate: 0\n        }\n      });\n\n      // Set empty fallback data for users and auctions\n      setUsers([]);\n      setAllAuctions([]);\n      setAuctions([]);\n    }\n  }, []);\n\n  // Filter and sort auctions based on current filters\n  const filterAndSortAuctions = useCallback(() => {\n    let filtered = [...allAuctions];\n\n    // Apply search filter\n    if (auctionSearchTerm) {\n      filtered = filtered.filter(auction => auction.title.toLowerCase().includes(auctionSearchTerm.toLowerCase()) || auction.owner__username.toLowerCase().includes(auctionSearchTerm.toLowerCase()) || auction.category.toLowerCase().includes(auctionSearchTerm.toLowerCase()));\n    }\n\n    // Apply status filter\n    if (auctionStatusFilter !== \"all\") {\n      filtered = filtered.filter(auction => {\n        switch (auctionStatusFilter) {\n          case \"approved\":\n            return auction.approved === true;\n          case \"pending\":\n            return auction.approved === false;\n          case \"active\":\n            return auction.approved === true && new Date(auction.end_time) > new Date();\n          case \"ended\":\n            return new Date(auction.end_time) <= new Date();\n          default:\n            return true;\n        }\n      });\n    }\n\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let aValue, bValue;\n      switch (auctionSortBy) {\n        case \"title\":\n          aValue = a.title.toLowerCase();\n          bValue = b.title.toLowerCase();\n          break;\n        case \"current_bid\":\n          aValue = parseFloat(a.current_bid);\n          bValue = parseFloat(b.current_bid);\n          break;\n        case \"views_count\":\n          aValue = a.views_count || 0;\n          bValue = b.views_count || 0;\n          break;\n        case \"owner\":\n          aValue = a.owner__username.toLowerCase();\n          bValue = b.owner__username.toLowerCase();\n          break;\n        default:\n          // created_at\n          aValue = new Date(a.created_at);\n          bValue = new Date(b.created_at);\n      }\n      if (auctionSortOrder === \"asc\") {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setAuctions(filtered);\n  }, [allAuctions, auctionSearchTerm, auctionStatusFilter, auctionSortBy, auctionSortOrder]);\n\n  // Effect to filter auctions when filters change\n  useEffect(() => {\n    if (allAuctions.length > 0) {\n      filterAndSortAuctions();\n    }\n  }, [allAuctions, filterAndSortAuctions]);\n  const setupWebSocketConnection = useCallback(() => {\n    try {\n      const socket = extendedAuctionService.subscribeToAdminDashboard(data => {\n        console.log(\"Received comprehensive dashboard update:\", data);\n\n        // Update analytics in real-time\n        setAnalytics(prev => {\n          if (!prev) return null;\n          return {\n            ...prev,\n            basic_metrics: {\n              ...prev.basic_metrics,\n              ...data.basic_metrics\n            },\n            revenue_data: {\n              ...prev.revenue_data,\n              ...data.revenue_data\n            }\n          };\n        });\n        console.log(\"Comprehensive dashboard updated in real-time\");\n      });\n      setWsConnection(socket);\n    } catch (err) {\n      console.error(\"WebSocket connection error:\", err);\n    }\n  }, []);\n  const deleteUser = async (userId, username) => {\n    if (!window.confirm(`Are you sure you want to delete user \"${username}\"?`)) {\n      return;\n    }\n    try {\n      const response = await fetch(`http://127.0.0.1:8000/api/admin/delete-user/${userId}/`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`\n        }\n      });\n      if (response.ok) {\n        alert(`User \"${username}\" deleted successfully`);\n        retryLoadData(); // Refresh data\n      } else {\n        const error = await response.json();\n        alert(`Error: ${error.error}`);\n      }\n    } catch (err) {\n      alert(`Error deleting user: ${err.message}`);\n    }\n  };\n  const deleteAuction = async (auctionId, title) => {\n    if (!window.confirm(`Are you sure you want to delete auction \"${title}\"?`)) {\n      return;\n    }\n    try {\n      const response = await fetch(`http://127.0.0.1:8000/api/admin/delete-auction/${auctionId}/`, {\n        method: \"DELETE\"\n      });\n      if (response.ok) {\n        alert(`Auction \"${title}\" deleted successfully`);\n        retryLoadData(); // Refresh data\n      } else {\n        const error = await response.json();\n        alert(`Error: ${error.error}`);\n      }\n    } catch (err) {\n      alert(`Error deleting auction: ${err.message}`);\n    }\n  };\n\n  // Retry function for error handling\n  const retryLoadData = () => {\n    window.location.reload(); // Simple reload for now\n  };\n\n  // Main useEffect - moved after all function definitions to avoid hoisting issues\n  useEffect(() => {\n    console.log(\"🚀 ComprehensiveAdminDashboard: Initial load starting...\");\n\n    // Check user authentication\n    const user = JSON.parse(localStorage.getItem(\"auction_loggedin_user\") || \"{}\");\n    const token = localStorage.getItem(\"token\") || localStorage.getItem(\"access_token\");\n    console.log(\"🔐 ADMIN DASHBOARD: User:\", user);\n    console.log(\"🔐 ADMIN DASHBOARD: Token exists:\", !!token);\n    console.log(\"🔐 ADMIN DASHBOARD: User role:\", user.role);\n\n    // Call functions directly to avoid dependency issues\n    const loadData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        console.log(\"🚀 Using fast dashboard endpoint for optimal performance...\");\n\n        // Test basic connectivity first\n        console.log(\"🔍 ADMIN DASHBOARD: Testing basic connectivity...\");\n\n        // Test if backend is reachable\n        try {\n          const testResponse = await fetch(\"http://127.0.0.1:8000/api/test/\");\n          console.log(\"🔍 ADMIN DASHBOARD: Backend connectivity test:\", testResponse.status);\n        } catch (connectError) {\n          console.error(\"🚨 ADMIN DASHBOARD: Backend connectivity failed:\", connectError);\n          throw new Error(\"Cannot connect to backend server. Please ensure the server is running on port 8000.\");\n        }\n        await fetchIndividualStats();\n        console.log(\"✅ Dashboard loaded with ultra-fast endpoint - includes users, auctions, and AI analytics\");\n      } catch (err) {\n        console.error(\"🚨 ADMIN DASHBOARD: loadData error:\", err);\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadData();\n    setupWebSocketConnection();\n\n    // DISABLED: Auto-refresh functionality to prevent excessive API calls\n    // Use WebSocket for real-time updates instead\n    // const interval = setInterval(() => {\n    //   console.log(\"Auto-refreshing comprehensive dashboard data...\");\n    //   fetchDashboardData();\n    // }, 60000); // Refresh every minute\n\n    return () => {\n      if (wsConnection) {\n        wsConnection.close();\n      }\n      // clearInterval(interval);\n    };\n  }, []); // Empty dependency array - functions are memoized with useCallback\n\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"admin-dashboard loading\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }\n    }, \"Loading Admin Dashboard...\"));\n  }\n  if (error) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"admin-dashboard error\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(\"h3\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }\n    }, \"\\u274C Error Loading Dashboard\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }\n    }, error), /*#__PURE__*/React.createElement(\"button\", {\n      onClick: retryLoadData,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }\n    }, \"Retry\"));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"admin-dashboard\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"dashboard-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 9\n    }\n  }, \"\\uD83D\\uDEE1\\uFE0F Admin Dashboard\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 9\n    }\n  }, \"Comprehensive system management and analytics\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"dashboard-tabs\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: `tab ${activeTab === \"overview\" ? \"active\" : \"\"}`,\n    onClick: () => setActiveTab(\"overview\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 9\n    }\n  }, \"\\uD83D\\uDCCA Overview\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: `tab ${activeTab === \"users\" ? \"active\" : \"\"}`,\n    onClick: () => setActiveTab(\"users\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 9\n    }\n  }, \"\\uD83D\\uDC65 Users (\", users.length, \")\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: `tab ${activeTab === \"auctions\" ? \"active\" : \"\"}`,\n    onClick: () => setActiveTab(\"auctions\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 9\n    }\n  }, \"\\uD83C\\uDFE0 Auctions (\", auctions.length, \")\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: `tab ${activeTab === \"analytics\" ? \"active\" : \"\"}`,\n    onClick: () => setActiveTab(\"analytics\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 9\n    }\n  }, \"\\uD83E\\uDD16 AI Analytics\")), activeTab === \"overview\" && analytics && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"overview-tab\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metrics-grid\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-card revenue\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 15\n    }\n  }, \"\\uD83D\\uDCB0\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 454,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(((_analytics$revenue_da = analytics.revenue_data) === null || _analytics$revenue_da === void 0 ? void 0 : _analytics$revenue_da.total_revenue) || 0)), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 17\n    }\n  }, \"Total Revenue\"), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(((_analytics$revenue_da2 = analytics.revenue_data) === null || _analytics$revenue_da2 === void 0 ? void 0 : _analytics$revenue_da2.total_commission) || 0), \" \", \"commission earned\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-card growth\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 15\n    }\n  }, \"\\uD83D\\uDCC8\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    className: ((_analytics$revenue_da3 = analytics.revenue_data) === null || _analytics$revenue_da3 === void 0 ? void 0 : _analytics$revenue_da3.growth_rate) >= 0 ? \"positive\" : \"negative\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 17\n    }\n  }, ((_analytics$revenue_da4 = analytics.revenue_data) === null || _analytics$revenue_da4 === void 0 ? void 0 : _analytics$revenue_da4.growth_rate) || 0, \"%\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 17\n    }\n  }, \"Monthly Growth\"), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 17\n    }\n  }, \"Revenue growth rate\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-card users\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 15\n    }\n  }, \"\\uD83D\\uDC65\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 17\n    }\n  }, ((_analytics$basic_metr = analytics.basic_metrics) === null || _analytics$basic_metr === void 0 ? void 0 : _analytics$basic_metr.total_users) || 0), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 17\n    }\n  }, \"Total Users\"), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 17\n    }\n  }, ((_analytics$engagement = analytics.engagement_data) === null || _analytics$engagement === void 0 ? void 0 : _analytics$engagement.active_users) || 0, \" active this month\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-card auctions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 15\n    }\n  }, \"\\uD83C\\uDFE0\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"metric-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 17\n    }\n  }, ((_analytics$basic_metr2 = analytics.basic_metrics) === null || _analytics$basic_metr2 === void 0 ? void 0 : _analytics$basic_metr2.total_auctions) || 0), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 17\n    }\n  }, \"Total Auctions\"), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 17\n    }\n  }, ((_analytics$basic_metr3 = analytics.basic_metrics) === null || _analytics$basic_metr3 === void 0 ? void 0 : _analytics$basic_metr3.active_auctions) || 0, \" currently active\")))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"revenue-breakdown\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 513,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 13\n    }\n  }, \"\\uD83D\\uDCB0 Revenue Breakdown\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"revenue-cards\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"revenue-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 17\n    }\n  }, \"Today\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 518,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(((_analytics$revenue_da5 = analytics.revenue_data) === null || _analytics$revenue_da5 === void 0 ? void 0 : _analytics$revenue_da5.daily_revenue) || 0))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"revenue-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 17\n    }\n  }, \"This Week\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(((_analytics$revenue_da6 = analytics.revenue_data) === null || _analytics$revenue_da6 === void 0 ? void 0 : _analytics$revenue_da6.weekly_revenue) || 0))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"revenue-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 532,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 17\n    }\n  }, \"This Month\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(((_analytics$revenue_da7 = analytics.revenue_data) === null || _analytics$revenue_da7 === void 0 ? void 0 : _analytics$revenue_da7.monthly_revenue) || 0))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"revenue-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 541,\n      columnNumber: 17\n    }\n  }, \"Avg per Auction\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 17\n    }\n  }, formatAuctionPrice(((_analytics$revenue_da8 = analytics.revenue_data) === null || _analytics$revenue_da8 === void 0 ? void 0 : _analytics$revenue_da8.avg_auction_value) || 0))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"recent-activity\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 13\n    }\n  }, \"\\uD83D\\uDCCB Recent Activity\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-grid\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 17\n    }\n  }, \"\\uD83C\\uDFF7\\uFE0F Recent Auctions\"), ((_analytics$recent_act = analytics.recent_activity) === null || _analytics$recent_act === void 0 ? void 0 : (_analytics$recent_act2 = _analytics$recent_act.recent_auctions) === null || _analytics$recent_act2 === void 0 ? void 0 : _analytics$recent_act2.length) > 0 ? analytics.recent_activity.recent_auctions.slice(0, 5).map((auction, index) => /*#__PURE__*/React.createElement(\"div\", {\n    key: auction.id || index,\n    className: \"activity-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 23\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-main\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 27\n    }\n  }, auction.title), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-details\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 27\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"activity-amount\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 29\n    }\n  }, formatAuctionPrice(auction.current_bid)), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"activity-user\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 568,\n      columnNumber: 29\n    }\n  }, \"by \", auction.owner__username))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-time\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 25\n    }\n  }, new Date(auction.created_at).toLocaleDateString()))) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-activity\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 19\n    }\n  }, \"No recent auctions\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 17\n    }\n  }, \"\\uD83D\\uDCB0 Recent Bids\"), ((_analytics$recent_act3 = analytics.recent_activity) === null || _analytics$recent_act3 === void 0 ? void 0 : (_analytics$recent_act4 = _analytics$recent_act3.recent_bids) === null || _analytics$recent_act4 === void 0 ? void 0 : _analytics$recent_act4.length) > 0 ? analytics.recent_activity.recent_bids.slice(0, 5).map((bid, index) => /*#__PURE__*/React.createElement(\"div\", {\n    key: bid.id || index,\n    className: \"activity-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 589,\n      columnNumber: 23\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-main\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    className: \"bid-amount\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 27\n    }\n  }, formatAuctionPrice(bid.amount)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-details\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 594,\n      columnNumber: 27\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"activity-user\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 595,\n      columnNumber: 29\n    }\n  }, bid.user__username), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"activity-auction\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 598,\n      columnNumber: 29\n    }\n  }, \"on \", bid.auction__title))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-time\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 603,\n      columnNumber: 25\n    }\n  }, new Date(bid.created_at).toLocaleDateString()))) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-activity\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 19\n    }\n  }, \"No recent bids\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 613,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 17\n    }\n  }, \"\\uD83D\\uDC65 New Users\"), ((_analytics$recent_act5 = analytics.recent_activity) === null || _analytics$recent_act5 === void 0 ? void 0 : (_analytics$recent_act6 = _analytics$recent_act5.recent_users) === null || _analytics$recent_act6 === void 0 ? void 0 : _analytics$recent_act6.length) > 0 ? analytics.recent_activity.recent_users.slice(0, 5).map((user, index) => /*#__PURE__*/React.createElement(\"div\", {\n    key: user.id || index,\n    className: \"activity-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 619,\n      columnNumber: 23\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-main\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 621,\n      columnNumber: 27\n    }\n  }, user.username), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-details\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 27\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"activity-email\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 623,\n      columnNumber: 29\n    }\n  }, user.email), /*#__PURE__*/React.createElement(\"span\", {\n    className: `activity-status ${user.is_active ? \"active\" : \"inactive\"}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 624,\n      columnNumber: 29\n    }\n  }, user.is_active ? \"Active\" : \"Inactive\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"activity-time\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 25\n    }\n  }, new Date(user.date_joined).toLocaleDateString()))) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-activity\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 19\n    }\n  }, \"No new users\"))))), activeTab === \"users\" && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"users-tab\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 649,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"tab-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 650,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 13\n    }\n  }, \"\\uD83D\\uDC65 User Management\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 652,\n      columnNumber: 13\n    }\n  }, \"Manage all users in the system\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"users-table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 655,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 15\n    }\n  }, \"User\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 658,\n      columnNumber: 15\n    }\n  }, \"Email\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 659,\n      columnNumber: 15\n    }\n  }, \"Status\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 660,\n      columnNumber: 15\n    }\n  }, \"Auctions\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 661,\n      columnNumber: 15\n    }\n  }, \"Bids\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 662,\n      columnNumber: 15\n    }\n  }, \"Joined\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 663,\n      columnNumber: 15\n    }\n  }, \"Actions\")), users.map(user => /*#__PURE__*/React.createElement(\"div\", {\n    key: user.id,\n    className: \"table-row\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"user-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 669,\n      columnNumber: 19\n    }\n  }, user.username), user.is_staff && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"badge admin\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 670,\n      columnNumber: 37\n    }\n  }, \"Admin\")), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 672,\n      columnNumber: 17\n    }\n  }, user.email), /*#__PURE__*/React.createElement(\"span\", {\n    className: `status ${user.is_active ? \"active\" : \"inactive\"}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 673,\n      columnNumber: 17\n    }\n  }, user.is_active ? \"Active\" : \"Inactive\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 678,\n      columnNumber: 17\n    }\n  }, user.auction_count), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 679,\n      columnNumber: 17\n    }\n  }, user.bid_count), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 17\n    }\n  }, new Date(user.date_joined).toLocaleDateString()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 681,\n      columnNumber: 17\n    }\n  }, !user.is_staff && /*#__PURE__*/React.createElement(\"button\", {\n    className: \"delete-btn\",\n    onClick: () => deleteUser(user.id, user.username),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 683,\n      columnNumber: 21\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Delete\")))))), activeTab === \"auctions\" && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"auctions-tab\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 699,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"tab-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 700,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 701,\n      columnNumber: 13\n    }\n  }, \"\\uD83C\\uDFE0 Comprehensive Auction Management\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 702,\n      columnNumber: 13\n    }\n  }, \"Manage all \", allAuctions.length, \" auctions in the system with advanced filtering\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"auction-controls\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 709,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"control-row\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 710,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-control\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 711,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    placeholder: \"\\uD83D\\uDD0D Search auctions, owners, categories...\",\n    value: auctionSearchTerm,\n    onChange: e => setAuctionSearchTerm(e.target.value),\n    className: \"search-input\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 712,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"filter-control\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 721,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: auctionStatusFilter,\n    onChange: e => setAuctionStatusFilter(e.target.value),\n    className: \"filter-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 722,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 727,\n      columnNumber: 19\n    }\n  }, \"All Status\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"approved\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 728,\n      columnNumber: 19\n    }\n  }, \"\\u2705 Approved\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"pending\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 729,\n      columnNumber: 19\n    }\n  }, \"\\u23F3 Pending\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"active\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 730,\n      columnNumber: 19\n    }\n  }, \"\\uD83D\\uDD34 Active\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"ended\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 731,\n      columnNumber: 19\n    }\n  }, \"\\u26AB Ended\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"sort-control\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 735,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: auctionSortBy,\n    onChange: e => setAuctionSortBy(e.target.value),\n    className: \"sort-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"created_at\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 741,\n      columnNumber: 19\n    }\n  }, \"\\uD83D\\uDCC5 Date Created\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"title\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 742,\n      columnNumber: 19\n    }\n  }, \"\\uD83D\\uDCDD Title\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"current_bid\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 743,\n      columnNumber: 19\n    }\n  }, \"\\uD83D\\uDCB0 Current Bid\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"views_count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 744,\n      columnNumber: 19\n    }\n  }, \"\\uD83D\\uDC41\\uFE0F Views\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"owner\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 745,\n      columnNumber: 19\n    }\n  }, \"\\uD83D\\uDC64 Owner\")), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setAuctionSortOrder(auctionSortOrder === \"asc\" ? \"desc\" : \"asc\"),\n    className: \"sort-order-btn\",\n    title: `Sort ${auctionSortOrder === \"asc\" ? \"Descending\" : \"Ascending\"}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 748,\n      columnNumber: 17\n    }\n  }, auctionSortOrder === \"asc\" ? \"⬆️\" : \"⬇️\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"results-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 764,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"results-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 765,\n      columnNumber: 15\n    }\n  }, \"\\uD83D\\uDCCA Showing \", auctions.length, \" of \", allAuctions.length, \" auctions\"), auctionSearchTerm && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setAuctionSearchTerm(\"\"),\n    className: \"clear-search-btn\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 769,\n      columnNumber: 17\n    }\n  }, \"\\u274C Clear Search\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"auctions-table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 779,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 780,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 781,\n      columnNumber: 15\n    }\n  }, \"ID\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 782,\n      columnNumber: 15\n    }\n  }, \"Auction Details\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 783,\n      columnNumber: 15\n    }\n  }, \"Owner\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 784,\n      columnNumber: 15\n    }\n  }, \"Current Bid\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 785,\n      columnNumber: 15\n    }\n  }, \"Views\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 786,\n      columnNumber: 15\n    }\n  }, \"Status\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 787,\n      columnNumber: 15\n    }\n  }, \"Created\"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 788,\n      columnNumber: 15\n    }\n  }, \"Actions\")), auctions.length > 0 ? auctions.map(auction => {\n    var _auction$views_count;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      key: auction.id,\n      className: \"table-row\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 793,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"auction-id\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 794,\n        columnNumber: 19\n      }\n    }, \"#\", auction.id), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auction-info\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 795,\n        columnNumber: 19\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 21\n      }\n    }, auction.title), /*#__PURE__*/React.createElement(\"small\", {\n      className: \"auction-category\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 21\n      }\n    }, \"\\uD83D\\uDCC2 \", auction.category)), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"auction-owner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 801,\n        columnNumber: 19\n      }\n    }, \"\\uD83D\\uDC64 \", auction.owner__username), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"auction-bid\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 804,\n        columnNumber: 19\n      }\n    }, formatAuctionPrice(auction.current_bid)), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"views-count\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 807,\n        columnNumber: 19\n      }\n    }, \"\\uD83D\\uDC41\\uFE0F \", ((_auction$views_count = auction.views_count) === null || _auction$views_count === void 0 ? void 0 : _auction$views_count.toLocaleString()) || 0), /*#__PURE__*/React.createElement(\"span\", {\n      className: `status ${auction.approved ? \"approved\" : \"pending\"}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 19\n      }\n    }, auction.approved ? \"✅ Approved\" : \"⏳ Pending\"), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"auction-date\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 19\n      }\n    }, new Date(auction.created_at).toLocaleDateString()), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"actions\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 820,\n        columnNumber: 19\n      }\n    }, /*#__PURE__*/React.createElement(\"button\", {\n      className: \"delete-btn\",\n      onClick: () => deleteAuction(auction.id, auction.title),\n      title: \"Delete Auction\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 821,\n        columnNumber: 21\n      }\n    }, \"\\uD83D\\uDDD1\\uFE0F Delete\")));\n  }) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-results\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 832,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 833,\n      columnNumber: 17\n    }\n  }, \"\\uD83D\\uDD0D No auctions found matching your criteria\"), auctionSearchTerm && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setAuctionSearchTerm(\"\"),\n    className: \"clear-search-btn\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 835,\n      columnNumber: 19\n    }\n  }, \"Clear Search\")))), activeTab === \"analytics\" && analytics && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"analytics-tab\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 850,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"tab-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 851,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 852,\n      columnNumber: 13\n    }\n  }, \"\\uD83E\\uDD16 AI Analytics\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 853,\n      columnNumber: 13\n    }\n  }, \"Monitor AI performance and insights\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"ai-metrics\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 856,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"ai-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 857,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 858,\n      columnNumber: 15\n    }\n  }, \"AI Predictions\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 859,\n      columnNumber: 15\n    }\n  }, ((_analytics$ai_metrics = analytics.ai_metrics) === null || _analytics$ai_metrics === void 0 ? void 0 : _analytics$ai_metrics.total_predictions) || 0), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 860,\n      columnNumber: 15\n    }\n  }, \"Total predictions made\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"ai-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 863,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 864,\n      columnNumber: 15\n    }\n  }, \"Average Confidence\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 865,\n      columnNumber: 15\n    }\n  }, ((_analytics$ai_metrics2 = analytics.ai_metrics) === null || _analytics$ai_metrics2 === void 0 ? void 0 : _analytics$ai_metrics2.avg_confidence) || 0, \"%\"), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 866,\n      columnNumber: 15\n    }\n  }, \"Model confidence score\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"ai-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 869,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 870,\n      columnNumber: 15\n    }\n  }, \"Model Performance\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 871,\n      columnNumber: 15\n    }\n  }, ((_analytics$ai_metrics3 = analytics.ai_metrics) === null || _analytics$ai_metrics3 === void 0 ? void 0 : _analytics$ai_metrics3.model_performance) || \"N/A\"), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 872,\n      columnNumber: 15\n    }\n  }, \"Overall performance rating\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"ai-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 875,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 876,\n      columnNumber: 15\n    }\n  }, \"Predictions Today\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 877,\n      columnNumber: 15\n    }\n  }, ((_analytics$ai_metrics4 = analytics.ai_metrics) === null || _analytics$ai_metrics4 === void 0 ? void 0 : _analytics$ai_metrics4.predictions_today) || 0), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 878,\n      columnNumber: 15\n    }\n  }, \"Generated today\")))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"dashboard-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 885,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"refresh-btn\",\n    onClick: retryLoadData,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 886,\n      columnNumber: 9\n    }\n  }, \"\\uD83D\\uDD04 Refresh Data\")));\n};\nexport default ComprehensiveAdminDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "formatAuctionPrice", "extendedAuctionService", "ComprehensiveAdminDashboard", "_analytics$revenue_da", "_analytics$revenue_da2", "_analytics$revenue_da3", "_analytics$revenue_da4", "_analytics$basic_metr", "_analytics$engagement", "_analytics$basic_metr2", "_analytics$basic_metr3", "_analytics$revenue_da5", "_analytics$revenue_da6", "_analytics$revenue_da7", "_analytics$revenue_da8", "_analytics$recent_act", "_analytics$recent_act2", "_analytics$recent_act3", "_analytics$recent_act4", "_analytics$recent_act5", "_analytics$recent_act6", "_analytics$ai_metrics", "_analytics$ai_metrics2", "_analytics$ai_metrics3", "_analytics$ai_metrics4", "analytics", "setAnalytics", "users", "setUsers", "auctions", "setAuctions", "allAuctions", "setAllAuctions", "loading", "setLoading", "error", "setError", "activeTab", "setActiveTab", "wsConnection", "setWsConnection", "auctionSearchTerm", "setAuctionSearchTerm", "auctionStatusFilter", "setAuctionStatusFilter", "auctionSortBy", "setAuctionSortBy", "auctionSortOrder", "setAuctionSortOrder", "fetchIndividualStats", "console", "log", "response", "fetch", "status", "ok", "Error", "data", "json", "success", "_data$data$users", "stats", "basic_stats", "basic_metrics", "total_auctions", "active_auctions", "total_users", "total_bids", "completion_rate", "toFixed", "avg_auction_value", "total_revenue", "revenue_data", "monthly_revenue", "fraud_alerts", "recent_auctions", "recent_activity", "recent_bids", "recent_users", "slice", "ai_metrics", "ai_analytics", "total_predictions", "predictions_today", "avg_confidence", "model_performance", "accuracy_rate", "message", "filterAndSortAuctions", "filtered", "filter", "auction", "title", "toLowerCase", "includes", "owner__username", "category", "approved", "Date", "end_time", "sort", "a", "b", "aValue", "bValue", "parseFloat", "current_bid", "views_count", "created_at", "length", "setupWebSocketConnection", "socket", "subscribeToAdminDashboard", "prev", "err", "deleteUser", "userId", "username", "window", "confirm", "method", "headers", "Authorization", "localStorage", "getItem", "alert", "retryLoadData", "deleteAuction", "auctionId", "location", "reload", "user", "JSON", "parse", "token", "role", "loadData", "testResponse", "connectError", "close", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "total_commission", "growth_rate", "engagement_data", "active_users", "daily_revenue", "weekly_revenue", "map", "index", "key", "id", "toLocaleDateString", "bid", "amount", "user__username", "auction__title", "email", "is_active", "date_joined", "is_staff", "auction_count", "bid_count", "type", "placeholder", "value", "onChange", "e", "target", "_auction$views_count", "toLocaleString"], "sources": ["C:/Users/<USER>/Desktop/Online_Auction_System/frontend/src/components/ComprehensiveAdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\r\nimport \"./ComprehensiveAdminDashboard.css\";\r\nimport { formatAuctionPrice } from \"../utils/currency\";\r\nimport extendedAuctionService from \"../services/extendedAuctionService\";\r\n\r\nconst ComprehensiveAdminDashboard = () => {\r\n  const [analytics, setAnalytics] = useState(null);\r\n  const [users, setUsers] = useState([]);\r\n  const [auctions, setAuctions] = useState([]);\r\n  const [allAuctions, setAllAuctions] = useState([]); // Store all auctions for filtering\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [activeTab, setActiveTab] = useState(\"overview\");\r\n  const [wsConnection, setWsConnection] = useState(null);\r\n\r\n  // Auction management states\r\n  const [auctionSearchTerm, setAuctionSearchTerm] = useState(\"\");\r\n  const [auctionStatusFilter, setAuctionStatusFilter] = useState(\"all\");\r\n  const [auctionSortBy, setAuctionSortBy] = useState(\"created_at\");\r\n  const [auctionSortOrder, setAuctionSortOrder] = useState(\"desc\");\r\n\r\n  const fetchIndividualStats = useCallback(async () => {\r\n    try {\r\n      // Use ultra-fast endpoint for maximum performance\r\n      console.log(\r\n        \"🔥 ADMIN DASHBOARD: Making API call to ultra-fast-dashboard endpoint\"\r\n      );\r\n      const response = await fetch(\r\n        \"http://127.0.0.1:8000/api/ultra-fast-dashboard/\"\r\n      );\r\n\r\n      console.log(\"🔥 ADMIN DASHBOARD: Response status:\", response.status);\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n      console.log(\"🔥 ADMIN DASHBOARD: Response data:\", data);\r\n\r\n      if (data.success) {\r\n        const stats = data.data.basic_stats;\r\n\r\n        setAnalytics({\r\n          basic_metrics: {\r\n            total_auctions: stats.total_auctions,\r\n            active_auctions: stats.active_auctions,\r\n            total_users: stats.total_users,\r\n            total_bids: stats.total_bids,\r\n            completion_rate:\r\n              stats.total_auctions > 0\r\n                ? (\r\n                    ((stats.total_auctions - stats.active_auctions) /\r\n                      stats.total_auctions) *\r\n                    100\r\n                  ).toFixed(1)\r\n                : 0,\r\n            avg_auction_value:\r\n              data.data.total_revenue / stats.total_auctions || 0,\r\n          },\r\n          revenue_data: {\r\n            total_revenue: data.data.total_revenue,\r\n            monthly_revenue: data.data.total_revenue * 0.7,\r\n          },\r\n          fraud_alerts: data.data.fraud_alerts || [],\r\n          recent_auctions: data.data.recent_auctions || [],\r\n          recent_activity: {\r\n            recent_auctions: data.data.recent_auctions || [],\r\n            recent_bids: data.data.recent_bids || [],\r\n            recent_users: data.data.users?.slice(0, 5) || [],\r\n          },\r\n          ai_metrics: data.data.ai_analytics || {\r\n            total_predictions: 0,\r\n            predictions_today: 0,\r\n            avg_confidence: 0,\r\n            model_performance: \"No data\",\r\n            accuracy_rate: 0,\r\n          },\r\n        });\r\n\r\n        // Set users and auctions from the ultra-fast endpoint\r\n        setUsers(data.data.users || []);\r\n        setAllAuctions(data.data.auctions || []);\r\n        setAuctions(data.data.auctions || []);\r\n\r\n        console.log(\"🚀 Ultra-fast stats loaded in ~70ms:\", stats);\r\n      } else {\r\n        console.error(\"🚨 ADMIN DASHBOARD: API returned success=false:\", data);\r\n        throw new Error(data.error || \"Failed to fetch dashboard stats\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\r\n        \"🚨 ADMIN DASHBOARD ERROR: Error fetching individual stats:\",\r\n        error\r\n      );\r\n      console.error(\"🚨 ADMIN DASHBOARD ERROR: Error details:\", error.message);\r\n      setError(`Failed to load dashboard data: ${error.message}`);\r\n      // Set minimal fallback data\r\n      setAnalytics({\r\n        basic_metrics: {\r\n          total_auctions: 0,\r\n          active_auctions: 0,\r\n          total_users: 0,\r\n          total_bids: 0,\r\n          completion_rate: 0,\r\n          avg_auction_value: 0,\r\n        },\r\n        revenue_data: {\r\n          total_revenue: 0,\r\n          monthly_revenue: 0,\r\n        },\r\n        fraud_alerts: [],\r\n        recent_auctions: [],\r\n        recent_activity: {\r\n          recent_auctions: [],\r\n          recent_bids: [],\r\n          recent_users: [],\r\n        },\r\n        ai_metrics: {\r\n          total_predictions: 0,\r\n          predictions_today: 0,\r\n          avg_confidence: 0,\r\n          model_performance: \"No data\",\r\n          accuracy_rate: 0,\r\n        },\r\n      });\r\n\r\n      // Set empty fallback data for users and auctions\r\n      setUsers([]);\r\n      setAllAuctions([]);\r\n      setAuctions([]);\r\n    }\r\n  }, []);\r\n\r\n  // Filter and sort auctions based on current filters\r\n  const filterAndSortAuctions = useCallback(() => {\r\n    let filtered = [...allAuctions];\r\n\r\n    // Apply search filter\r\n    if (auctionSearchTerm) {\r\n      filtered = filtered.filter(\r\n        (auction) =>\r\n          auction.title\r\n            .toLowerCase()\r\n            .includes(auctionSearchTerm.toLowerCase()) ||\r\n          auction.owner__username\r\n            .toLowerCase()\r\n            .includes(auctionSearchTerm.toLowerCase()) ||\r\n          auction.category\r\n            .toLowerCase()\r\n            .includes(auctionSearchTerm.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // Apply status filter\r\n    if (auctionStatusFilter !== \"all\") {\r\n      filtered = filtered.filter((auction) => {\r\n        switch (auctionStatusFilter) {\r\n          case \"approved\":\r\n            return auction.approved === true;\r\n          case \"pending\":\r\n            return auction.approved === false;\r\n          case \"active\":\r\n            return (\r\n              auction.approved === true &&\r\n              new Date(auction.end_time) > new Date()\r\n            );\r\n          case \"ended\":\r\n            return new Date(auction.end_time) <= new Date();\r\n          default:\r\n            return true;\r\n        }\r\n      });\r\n    }\r\n\r\n    // Apply sorting\r\n    filtered.sort((a, b) => {\r\n      let aValue, bValue;\r\n\r\n      switch (auctionSortBy) {\r\n        case \"title\":\r\n          aValue = a.title.toLowerCase();\r\n          bValue = b.title.toLowerCase();\r\n          break;\r\n        case \"current_bid\":\r\n          aValue = parseFloat(a.current_bid);\r\n          bValue = parseFloat(b.current_bid);\r\n          break;\r\n        case \"views_count\":\r\n          aValue = a.views_count || 0;\r\n          bValue = b.views_count || 0;\r\n          break;\r\n        case \"owner\":\r\n          aValue = a.owner__username.toLowerCase();\r\n          bValue = b.owner__username.toLowerCase();\r\n          break;\r\n        default: // created_at\r\n          aValue = new Date(a.created_at);\r\n          bValue = new Date(b.created_at);\r\n      }\r\n\r\n      if (auctionSortOrder === \"asc\") {\r\n        return aValue > bValue ? 1 : -1;\r\n      } else {\r\n        return aValue < bValue ? 1 : -1;\r\n      }\r\n    });\r\n\r\n    setAuctions(filtered);\r\n  }, [\r\n    allAuctions,\r\n    auctionSearchTerm,\r\n    auctionStatusFilter,\r\n    auctionSortBy,\r\n    auctionSortOrder,\r\n  ]);\r\n\r\n  // Effect to filter auctions when filters change\r\n  useEffect(() => {\r\n    if (allAuctions.length > 0) {\r\n      filterAndSortAuctions();\r\n    }\r\n  }, [allAuctions, filterAndSortAuctions]);\r\n\r\n  const setupWebSocketConnection = useCallback(() => {\r\n    try {\r\n      const socket = extendedAuctionService.subscribeToAdminDashboard(\r\n        (data) => {\r\n          console.log(\"Received comprehensive dashboard update:\", data);\r\n\r\n          // Update analytics in real-time\r\n          setAnalytics((prev) => {\r\n            if (!prev) return null;\r\n\r\n            return {\r\n              ...prev,\r\n              basic_metrics: {\r\n                ...prev.basic_metrics,\r\n                ...data.basic_metrics,\r\n              },\r\n              revenue_data: {\r\n                ...prev.revenue_data,\r\n                ...data.revenue_data,\r\n              },\r\n            };\r\n          });\r\n\r\n          console.log(\"Comprehensive dashboard updated in real-time\");\r\n        }\r\n      );\r\n      setWsConnection(socket);\r\n    } catch (err) {\r\n      console.error(\"WebSocket connection error:\", err);\r\n    }\r\n  }, []);\r\n\r\n  const deleteUser = async (userId, username) => {\r\n    if (\r\n      !window.confirm(`Are you sure you want to delete user \"${username}\"?`)\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(\r\n        `http://127.0.0.1:8000/api/admin/delete-user/${userId}/`,\r\n        {\r\n          method: \"DELETE\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        alert(`User \"${username}\" deleted successfully`);\r\n        retryLoadData(); // Refresh data\r\n      } else {\r\n        const error = await response.json();\r\n        alert(`Error: ${error.error}`);\r\n      }\r\n    } catch (err) {\r\n      alert(`Error deleting user: ${err.message}`);\r\n    }\r\n  };\r\n\r\n  const deleteAuction = async (auctionId, title) => {\r\n    if (\r\n      !window.confirm(`Are you sure you want to delete auction \"${title}\"?`)\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(\r\n        `http://127.0.0.1:8000/api/admin/delete-auction/${auctionId}/`,\r\n        {\r\n          method: \"DELETE\",\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        alert(`Auction \"${title}\" deleted successfully`);\r\n        retryLoadData(); // Refresh data\r\n      } else {\r\n        const error = await response.json();\r\n        alert(`Error: ${error.error}`);\r\n      }\r\n    } catch (err) {\r\n      alert(`Error deleting auction: ${err.message}`);\r\n    }\r\n  };\r\n\r\n  // Retry function for error handling\r\n  const retryLoadData = () => {\r\n    window.location.reload(); // Simple reload for now\r\n  };\r\n\r\n  // Main useEffect - moved after all function definitions to avoid hoisting issues\r\n  useEffect(() => {\r\n    console.log(\"🚀 ComprehensiveAdminDashboard: Initial load starting...\");\r\n\r\n    // Check user authentication\r\n    const user = JSON.parse(\r\n      localStorage.getItem(\"auction_loggedin_user\") || \"{}\"\r\n    );\r\n    const token =\r\n      localStorage.getItem(\"token\") || localStorage.getItem(\"access_token\");\r\n    console.log(\"🔐 ADMIN DASHBOARD: User:\", user);\r\n    console.log(\"🔐 ADMIN DASHBOARD: Token exists:\", !!token);\r\n    console.log(\"🔐 ADMIN DASHBOARD: User role:\", user.role);\r\n\r\n    // Call functions directly to avoid dependency issues\r\n    const loadData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n        console.log(\r\n          \"🚀 Using fast dashboard endpoint for optimal performance...\"\r\n        );\r\n\r\n        // Test basic connectivity first\r\n        console.log(\"🔍 ADMIN DASHBOARD: Testing basic connectivity...\");\r\n\r\n        // Test if backend is reachable\r\n        try {\r\n          const testResponse = await fetch(\"http://127.0.0.1:8000/api/test/\");\r\n          console.log(\r\n            \"🔍 ADMIN DASHBOARD: Backend connectivity test:\",\r\n            testResponse.status\r\n          );\r\n        } catch (connectError) {\r\n          console.error(\r\n            \"🚨 ADMIN DASHBOARD: Backend connectivity failed:\",\r\n            connectError\r\n          );\r\n          throw new Error(\r\n            \"Cannot connect to backend server. Please ensure the server is running on port 8000.\"\r\n          );\r\n        }\r\n\r\n        await fetchIndividualStats();\r\n        console.log(\r\n          \"✅ Dashboard loaded with ultra-fast endpoint - includes users, auctions, and AI analytics\"\r\n        );\r\n      } catch (err) {\r\n        console.error(\"🚨 ADMIN DASHBOARD: loadData error:\", err);\r\n        setError(err.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadData();\r\n    setupWebSocketConnection();\r\n\r\n    // DISABLED: Auto-refresh functionality to prevent excessive API calls\r\n    // Use WebSocket for real-time updates instead\r\n    // const interval = setInterval(() => {\r\n    //   console.log(\"Auto-refreshing comprehensive dashboard data...\");\r\n    //   fetchDashboardData();\r\n    // }, 60000); // Refresh every minute\r\n\r\n    return () => {\r\n      if (wsConnection) {\r\n        wsConnection.close();\r\n      }\r\n      // clearInterval(interval);\r\n    };\r\n  }, []); // Empty dependency array - functions are memoized with useCallback\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"admin-dashboard loading\">\r\n        <div className=\"loading-spinner\"></div>\r\n        <p>Loading Admin Dashboard...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"admin-dashboard error\">\r\n        <h3>❌ Error Loading Dashboard</h3>\r\n        <p>{error}</p>\r\n        <button onClick={retryLoadData}>Retry</button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"admin-dashboard\">\r\n      <div className=\"dashboard-header\">\r\n        <h1>🛡️ Admin Dashboard</h1>\r\n        <p>Comprehensive system management and analytics</p>\r\n      </div>\r\n\r\n      {/* Navigation Tabs */}\r\n      <div className=\"dashboard-tabs\">\r\n        <button\r\n          className={`tab ${activeTab === \"overview\" ? \"active\" : \"\"}`}\r\n          onClick={() => setActiveTab(\"overview\")}\r\n        >\r\n          📊 Overview\r\n        </button>\r\n        <button\r\n          className={`tab ${activeTab === \"users\" ? \"active\" : \"\"}`}\r\n          onClick={() => setActiveTab(\"users\")}\r\n        >\r\n          👥 Users ({users.length})\r\n        </button>\r\n        <button\r\n          className={`tab ${activeTab === \"auctions\" ? \"active\" : \"\"}`}\r\n          onClick={() => setActiveTab(\"auctions\")}\r\n        >\r\n          🏠 Auctions ({auctions.length})\r\n        </button>\r\n        <button\r\n          className={`tab ${activeTab === \"analytics\" ? \"active\" : \"\"}`}\r\n          onClick={() => setActiveTab(\"analytics\")}\r\n        >\r\n          🤖 AI Analytics\r\n        </button>\r\n      </div>\r\n\r\n      {/* Overview Tab */}\r\n      {activeTab === \"overview\" && analytics && (\r\n        <div className=\"overview-tab\">\r\n          {/* Key Metrics */}\r\n          <div className=\"metrics-grid\">\r\n            <div className=\"metric-card revenue\">\r\n              <div className=\"metric-icon\">💰</div>\r\n              <div className=\"metric-content\">\r\n                <h3>\r\n                  {formatAuctionPrice(\r\n                    analytics.revenue_data?.total_revenue || 0\r\n                  )}\r\n                </h3>\r\n                <p>Total Revenue</p>\r\n                <small>\r\n                  {formatAuctionPrice(\r\n                    analytics.revenue_data?.total_commission || 0\r\n                  )}{\" \"}\r\n                  commission earned\r\n                </small>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"metric-card growth\">\r\n              <div className=\"metric-icon\">📈</div>\r\n              <div className=\"metric-content\">\r\n                <h3\r\n                  className={\r\n                    analytics.revenue_data?.growth_rate >= 0\r\n                      ? \"positive\"\r\n                      : \"negative\"\r\n                  }\r\n                >\r\n                  {analytics.revenue_data?.growth_rate || 0}%\r\n                </h3>\r\n                <p>Monthly Growth</p>\r\n                <small>Revenue growth rate</small>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"metric-card users\">\r\n              <div className=\"metric-icon\">👥</div>\r\n              <div className=\"metric-content\">\r\n                <h3>{analytics.basic_metrics?.total_users || 0}</h3>\r\n                <p>Total Users</p>\r\n                <small>\r\n                  {analytics.engagement_data?.active_users || 0} active this\r\n                  month\r\n                </small>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"metric-card auctions\">\r\n              <div className=\"metric-icon\">🏠</div>\r\n              <div className=\"metric-content\">\r\n                <h3>{analytics.basic_metrics?.total_auctions || 0}</h3>\r\n                <p>Total Auctions</p>\r\n                <small>\r\n                  {analytics.basic_metrics?.active_auctions || 0} currently\r\n                  active\r\n                </small>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Revenue Breakdown */}\r\n          <div className=\"revenue-breakdown\">\r\n            <h3>💰 Revenue Breakdown</h3>\r\n            <div className=\"revenue-cards\">\r\n              <div className=\"revenue-card\">\r\n                <h4>Today</h4>\r\n                <p>\r\n                  {formatAuctionPrice(\r\n                    analytics.revenue_data?.daily_revenue || 0\r\n                  )}\r\n                </p>\r\n              </div>\r\n              <div className=\"revenue-card\">\r\n                <h4>This Week</h4>\r\n                <p>\r\n                  {formatAuctionPrice(\r\n                    analytics.revenue_data?.weekly_revenue || 0\r\n                  )}\r\n                </p>\r\n              </div>\r\n              <div className=\"revenue-card\">\r\n                <h4>This Month</h4>\r\n                <p>\r\n                  {formatAuctionPrice(\r\n                    analytics.revenue_data?.monthly_revenue || 0\r\n                  )}\r\n                </p>\r\n              </div>\r\n              <div className=\"revenue-card\">\r\n                <h4>Avg per Auction</h4>\r\n                <p>\r\n                  {formatAuctionPrice(\r\n                    analytics.revenue_data?.avg_auction_value || 0\r\n                  )}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Recent Activity */}\r\n          <div className=\"recent-activity\">\r\n            <h3>📋 Recent Activity</h3>\r\n            <div className=\"activity-grid\">\r\n              <div className=\"activity-section\">\r\n                <h4>🏷️ Recent Auctions</h4>\r\n                {analytics.recent_activity?.recent_auctions?.length > 0 ? (\r\n                  analytics.recent_activity.recent_auctions\r\n                    .slice(0, 5)\r\n                    .map((auction, index) => (\r\n                      <div key={auction.id || index} className=\"activity-item\">\r\n                        <div className=\"activity-main\">\r\n                          <strong>{auction.title}</strong>\r\n                          <div className=\"activity-details\">\r\n                            <span className=\"activity-amount\">\r\n                              {formatAuctionPrice(auction.current_bid)}\r\n                            </span>\r\n                            <span className=\"activity-user\">\r\n                              by {auction.owner__username}\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"activity-time\">\r\n                          {new Date(auction.created_at).toLocaleDateString()}\r\n                        </div>\r\n                      </div>\r\n                    ))\r\n                ) : (\r\n                  <div className=\"no-activity\">No recent auctions</div>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"activity-section\">\r\n                <h4>💰 Recent Bids</h4>\r\n                {analytics.recent_activity?.recent_bids?.length > 0 ? (\r\n                  analytics.recent_activity.recent_bids\r\n                    .slice(0, 5)\r\n                    .map((bid, index) => (\r\n                      <div key={bid.id || index} className=\"activity-item\">\r\n                        <div className=\"activity-main\">\r\n                          <strong className=\"bid-amount\">\r\n                            {formatAuctionPrice(bid.amount)}\r\n                          </strong>\r\n                          <div className=\"activity-details\">\r\n                            <span className=\"activity-user\">\r\n                              {bid.user__username}\r\n                            </span>\r\n                            <span className=\"activity-auction\">\r\n                              on {bid.auction__title}\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"activity-time\">\r\n                          {new Date(bid.created_at).toLocaleDateString()}\r\n                        </div>\r\n                      </div>\r\n                    ))\r\n                ) : (\r\n                  <div className=\"no-activity\">No recent bids</div>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"activity-section\">\r\n                <h4>👥 New Users</h4>\r\n                {analytics.recent_activity?.recent_users?.length > 0 ? (\r\n                  analytics.recent_activity.recent_users\r\n                    .slice(0, 5)\r\n                    .map((user, index) => (\r\n                      <div key={user.id || index} className=\"activity-item\">\r\n                        <div className=\"activity-main\">\r\n                          <strong>{user.username}</strong>\r\n                          <div className=\"activity-details\">\r\n                            <span className=\"activity-email\">{user.email}</span>\r\n                            <span\r\n                              className={`activity-status ${\r\n                                user.is_active ? \"active\" : \"inactive\"\r\n                              }`}\r\n                            >\r\n                              {user.is_active ? \"Active\" : \"Inactive\"}\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"activity-time\">\r\n                          {new Date(user.date_joined).toLocaleDateString()}\r\n                        </div>\r\n                      </div>\r\n                    ))\r\n                ) : (\r\n                  <div className=\"no-activity\">No new users</div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Users Management Tab */}\r\n      {activeTab === \"users\" && (\r\n        <div className=\"users-tab\">\r\n          <div className=\"tab-header\">\r\n            <h3>👥 User Management</h3>\r\n            <p>Manage all users in the system</p>\r\n          </div>\r\n\r\n          <div className=\"users-table\">\r\n            <div className=\"table-header\">\r\n              <span>User</span>\r\n              <span>Email</span>\r\n              <span>Status</span>\r\n              <span>Auctions</span>\r\n              <span>Bids</span>\r\n              <span>Joined</span>\r\n              <span>Actions</span>\r\n            </div>\r\n\r\n            {users.map((user) => (\r\n              <div key={user.id} className=\"table-row\">\r\n                <div className=\"user-info\">\r\n                  <strong>{user.username}</strong>\r\n                  {user.is_staff && <span className=\"badge admin\">Admin</span>}\r\n                </div>\r\n                <span>{user.email}</span>\r\n                <span\r\n                  className={`status ${user.is_active ? \"active\" : \"inactive\"}`}\r\n                >\r\n                  {user.is_active ? \"Active\" : \"Inactive\"}\r\n                </span>\r\n                <span>{user.auction_count}</span>\r\n                <span>{user.bid_count}</span>\r\n                <span>{new Date(user.date_joined).toLocaleDateString()}</span>\r\n                <div className=\"actions\">\r\n                  {!user.is_staff && (\r\n                    <button\r\n                      className=\"delete-btn\"\r\n                      onClick={() => deleteUser(user.id, user.username)}\r\n                    >\r\n                      🗑️ Delete\r\n                    </button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Comprehensive Auctions Management Tab */}\r\n      {activeTab === \"auctions\" && (\r\n        <div className=\"auctions-tab\">\r\n          <div className=\"tab-header\">\r\n            <h3>🏠 Comprehensive Auction Management</h3>\r\n            <p>\r\n              Manage all {allAuctions.length} auctions in the system with\r\n              advanced filtering\r\n            </p>\r\n          </div>\r\n\r\n          {/* Auction Management Controls */}\r\n          <div className=\"auction-controls\">\r\n            <div className=\"control-row\">\r\n              <div className=\"search-control\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"🔍 Search auctions, owners, categories...\"\r\n                  value={auctionSearchTerm}\r\n                  onChange={(e) => setAuctionSearchTerm(e.target.value)}\r\n                  className=\"search-input\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"filter-control\">\r\n                <select\r\n                  value={auctionStatusFilter}\r\n                  onChange={(e) => setAuctionStatusFilter(e.target.value)}\r\n                  className=\"filter-select\"\r\n                >\r\n                  <option value=\"all\">All Status</option>\r\n                  <option value=\"approved\">✅ Approved</option>\r\n                  <option value=\"pending\">⏳ Pending</option>\r\n                  <option value=\"active\">🔴 Active</option>\r\n                  <option value=\"ended\">⚫ Ended</option>\r\n                </select>\r\n              </div>\r\n\r\n              <div className=\"sort-control\">\r\n                <select\r\n                  value={auctionSortBy}\r\n                  onChange={(e) => setAuctionSortBy(e.target.value)}\r\n                  className=\"sort-select\"\r\n                >\r\n                  <option value=\"created_at\">📅 Date Created</option>\r\n                  <option value=\"title\">📝 Title</option>\r\n                  <option value=\"current_bid\">💰 Current Bid</option>\r\n                  <option value=\"views_count\">👁️ Views</option>\r\n                  <option value=\"owner\">👤 Owner</option>\r\n                </select>\r\n\r\n                <button\r\n                  onClick={() =>\r\n                    setAuctionSortOrder(\r\n                      auctionSortOrder === \"asc\" ? \"desc\" : \"asc\"\r\n                    )\r\n                  }\r\n                  className=\"sort-order-btn\"\r\n                  title={`Sort ${\r\n                    auctionSortOrder === \"asc\" ? \"Descending\" : \"Ascending\"\r\n                  }`}\r\n                >\r\n                  {auctionSortOrder === \"asc\" ? \"⬆️\" : \"⬇️\"}\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"results-info\">\r\n              <span className=\"results-count\">\r\n                📊 Showing {auctions.length} of {allAuctions.length} auctions\r\n              </span>\r\n              {auctionSearchTerm && (\r\n                <button\r\n                  onClick={() => setAuctionSearchTerm(\"\")}\r\n                  className=\"clear-search-btn\"\r\n                >\r\n                  ❌ Clear Search\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"auctions-table\">\r\n            <div className=\"table-header\">\r\n              <span>ID</span>\r\n              <span>Auction Details</span>\r\n              <span>Owner</span>\r\n              <span>Current Bid</span>\r\n              <span>Views</span>\r\n              <span>Status</span>\r\n              <span>Created</span>\r\n              <span>Actions</span>\r\n            </div>\r\n\r\n            {auctions.length > 0 ? (\r\n              auctions.map((auction) => (\r\n                <div key={auction.id} className=\"table-row\">\r\n                  <span className=\"auction-id\">#{auction.id}</span>\r\n                  <div className=\"auction-info\">\r\n                    <strong>{auction.title}</strong>\r\n                    <small className=\"auction-category\">\r\n                      📂 {auction.category}\r\n                    </small>\r\n                  </div>\r\n                  <span className=\"auction-owner\">\r\n                    👤 {auction.owner__username}\r\n                  </span>\r\n                  <span className=\"auction-bid\">\r\n                    {formatAuctionPrice(auction.current_bid)}\r\n                  </span>\r\n                  <span className=\"views-count\">\r\n                    👁️ {auction.views_count?.toLocaleString() || 0}\r\n                  </span>\r\n                  <span\r\n                    className={`status ${\r\n                      auction.approved ? \"approved\" : \"pending\"\r\n                    }`}\r\n                  >\r\n                    {auction.approved ? \"✅ Approved\" : \"⏳ Pending\"}\r\n                  </span>\r\n                  <span className=\"auction-date\">\r\n                    {new Date(auction.created_at).toLocaleDateString()}\r\n                  </span>\r\n                  <div className=\"actions\">\r\n                    <button\r\n                      className=\"delete-btn\"\r\n                      onClick={() => deleteAuction(auction.id, auction.title)}\r\n                      title=\"Delete Auction\"\r\n                    >\r\n                      🗑️ Delete\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              ))\r\n            ) : (\r\n              <div className=\"no-results\">\r\n                <p>🔍 No auctions found matching your criteria</p>\r\n                {auctionSearchTerm && (\r\n                  <button\r\n                    onClick={() => setAuctionSearchTerm(\"\")}\r\n                    className=\"clear-search-btn\"\r\n                  >\r\n                    Clear Search\r\n                  </button>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* AI Analytics Tab */}\r\n      {activeTab === \"analytics\" && analytics && (\r\n        <div className=\"analytics-tab\">\r\n          <div className=\"tab-header\">\r\n            <h3>🤖 AI Analytics</h3>\r\n            <p>Monitor AI performance and insights</p>\r\n          </div>\r\n\r\n          <div className=\"ai-metrics\">\r\n            <div className=\"ai-card\">\r\n              <h4>AI Predictions</h4>\r\n              <p>{analytics.ai_metrics?.total_predictions || 0}</p>\r\n              <small>Total predictions made</small>\r\n            </div>\r\n\r\n            <div className=\"ai-card\">\r\n              <h4>Average Confidence</h4>\r\n              <p>{analytics.ai_metrics?.avg_confidence || 0}%</p>\r\n              <small>Model confidence score</small>\r\n            </div>\r\n\r\n            <div className=\"ai-card\">\r\n              <h4>Model Performance</h4>\r\n              <p>{analytics.ai_metrics?.model_performance || \"N/A\"}</p>\r\n              <small>Overall performance rating</small>\r\n            </div>\r\n\r\n            <div className=\"ai-card\">\r\n              <h4>Predictions Today</h4>\r\n              <p>{analytics.ai_metrics?.predictions_today || 0}</p>\r\n              <small>Generated today</small>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Refresh Button */}\r\n      <div className=\"dashboard-actions\">\r\n        <button className=\"refresh-btn\" onClick={retryLoadData}>\r\n          🔄 Refresh Data\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ComprehensiveAdminDashboard;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAO,mCAAmC;AAC1C,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,OAAOC,sBAAsB,MAAM,oCAAoC;AAEvE,MAAMC,2BAA2B,GAAGA,CAAA,KAAM;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACxC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACpD,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAM,CAAC4C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC,YAAY,CAAC;EAChE,MAAM,CAACkD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnD,QAAQ,CAAC,MAAM,CAAC;EAEhE,MAAMoD,oBAAoB,GAAGlD,WAAW,CAAC,YAAY;IACnD,IAAI;MACF;MACAmD,OAAO,CAACC,GAAG,CACT,sEACF,CAAC;MACD,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,iDACF,CAAC;MAEDH,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEC,QAAQ,CAACE,MAAM,CAAC;MAEpE,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBJ,QAAQ,CAACE,MAAM,EAAE,CAAC;MAC3D;MAEA,MAAMG,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClCR,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEM,IAAI,CAAC;MAEvD,IAAIA,IAAI,CAACE,OAAO,EAAE;QAAA,IAAAC,gBAAA;QAChB,MAAMC,KAAK,GAAGJ,IAAI,CAACA,IAAI,CAACK,WAAW;QAEnCpC,YAAY,CAAC;UACXqC,aAAa,EAAE;YACbC,cAAc,EAAEH,KAAK,CAACG,cAAc;YACpCC,eAAe,EAAEJ,KAAK,CAACI,eAAe;YACtCC,WAAW,EAAEL,KAAK,CAACK,WAAW;YAC9BC,UAAU,EAAEN,KAAK,CAACM,UAAU;YAC5BC,eAAe,EACbP,KAAK,CAACG,cAAc,GAAG,CAAC,GACpB,CACG,CAACH,KAAK,CAACG,cAAc,GAAGH,KAAK,CAACI,eAAe,IAC5CJ,KAAK,CAACG,cAAc,GACtB,GAAG,EACHK,OAAO,CAAC,CAAC,CAAC,GACZ,CAAC;YACPC,iBAAiB,EACfb,IAAI,CAACA,IAAI,CAACc,aAAa,GAAGV,KAAK,CAACG,cAAc,IAAI;UACtD,CAAC;UACDQ,YAAY,EAAE;YACZD,aAAa,EAAEd,IAAI,CAACA,IAAI,CAACc,aAAa;YACtCE,eAAe,EAAEhB,IAAI,CAACA,IAAI,CAACc,aAAa,GAAG;UAC7C,CAAC;UACDG,YAAY,EAAEjB,IAAI,CAACA,IAAI,CAACiB,YAAY,IAAI,EAAE;UAC1CC,eAAe,EAAElB,IAAI,CAACA,IAAI,CAACkB,eAAe,IAAI,EAAE;UAChDC,eAAe,EAAE;YACfD,eAAe,EAAElB,IAAI,CAACA,IAAI,CAACkB,eAAe,IAAI,EAAE;YAChDE,WAAW,EAAEpB,IAAI,CAACA,IAAI,CAACoB,WAAW,IAAI,EAAE;YACxCC,YAAY,EAAE,EAAAlB,gBAAA,GAAAH,IAAI,CAACA,IAAI,CAAC9B,KAAK,cAAAiC,gBAAA,uBAAfA,gBAAA,CAAiBmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI;UAChD,CAAC;UACDC,UAAU,EAAEvB,IAAI,CAACA,IAAI,CAACwB,YAAY,IAAI;YACpCC,iBAAiB,EAAE,CAAC;YACpBC,iBAAiB,EAAE,CAAC;YACpBC,cAAc,EAAE,CAAC;YACjBC,iBAAiB,EAAE,SAAS;YAC5BC,aAAa,EAAE;UACjB;QACF,CAAC,CAAC;;QAEF;QACA1D,QAAQ,CAAC6B,IAAI,CAACA,IAAI,CAAC9B,KAAK,IAAI,EAAE,CAAC;QAC/BK,cAAc,CAACyB,IAAI,CAACA,IAAI,CAAC5B,QAAQ,IAAI,EAAE,CAAC;QACxCC,WAAW,CAAC2B,IAAI,CAACA,IAAI,CAAC5B,QAAQ,IAAI,EAAE,CAAC;QAErCqB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEU,KAAK,CAAC;MAC5D,CAAC,MAAM;QACLX,OAAO,CAACf,KAAK,CAAC,iDAAiD,EAAEsB,IAAI,CAAC;QACtE,MAAM,IAAID,KAAK,CAACC,IAAI,CAACtB,KAAK,IAAI,iCAAiC,CAAC;MAClE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CACX,4DAA4D,EAC5DA,KACF,CAAC;MACDe,OAAO,CAACf,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAACoD,OAAO,CAAC;MACxEnD,QAAQ,CAAC,kCAAkCD,KAAK,CAACoD,OAAO,EAAE,CAAC;MAC3D;MACA7D,YAAY,CAAC;QACXqC,aAAa,EAAE;UACbC,cAAc,EAAE,CAAC;UACjBC,eAAe,EAAE,CAAC;UAClBC,WAAW,EAAE,CAAC;UACdC,UAAU,EAAE,CAAC;UACbC,eAAe,EAAE,CAAC;UAClBE,iBAAiB,EAAE;QACrB,CAAC;QACDE,YAAY,EAAE;UACZD,aAAa,EAAE,CAAC;UAChBE,eAAe,EAAE;QACnB,CAAC;QACDC,YAAY,EAAE,EAAE;QAChBC,eAAe,EAAE,EAAE;QACnBC,eAAe,EAAE;UACfD,eAAe,EAAE,EAAE;UACnBE,WAAW,EAAE,EAAE;UACfC,YAAY,EAAE;QAChB,CAAC;QACDE,UAAU,EAAE;UACVE,iBAAiB,EAAE,CAAC;UACpBC,iBAAiB,EAAE,CAAC;UACpBC,cAAc,EAAE,CAAC;UACjBC,iBAAiB,EAAE,SAAS;UAC5BC,aAAa,EAAE;QACjB;MACF,CAAC,CAAC;;MAEF;MACA1D,QAAQ,CAAC,EAAE,CAAC;MACZI,cAAc,CAAC,EAAE,CAAC;MAClBF,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0D,qBAAqB,GAAGzF,WAAW,CAAC,MAAM;IAC9C,IAAI0F,QAAQ,GAAG,CAAC,GAAG1D,WAAW,CAAC;;IAE/B;IACA,IAAIU,iBAAiB,EAAE;MACrBgD,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CACvBC,OAAO,IACNA,OAAO,CAACC,KAAK,CACVC,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACrD,iBAAiB,CAACoD,WAAW,CAAC,CAAC,CAAC,IAC5CF,OAAO,CAACI,eAAe,CACpBF,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACrD,iBAAiB,CAACoD,WAAW,CAAC,CAAC,CAAC,IAC5CF,OAAO,CAACK,QAAQ,CACbH,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACrD,iBAAiB,CAACoD,WAAW,CAAC,CAAC,CAC/C,CAAC;IACH;;IAEA;IACA,IAAIlD,mBAAmB,KAAK,KAAK,EAAE;MACjC8C,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAEC,OAAO,IAAK;QACtC,QAAQhD,mBAAmB;UACzB,KAAK,UAAU;YACb,OAAOgD,OAAO,CAACM,QAAQ,KAAK,IAAI;UAClC,KAAK,SAAS;YACZ,OAAON,OAAO,CAACM,QAAQ,KAAK,KAAK;UACnC,KAAK,QAAQ;YACX,OACEN,OAAO,CAACM,QAAQ,KAAK,IAAI,IACzB,IAAIC,IAAI,CAACP,OAAO,CAACQ,QAAQ,CAAC,GAAG,IAAID,IAAI,CAAC,CAAC;UAE3C,KAAK,OAAO;YACV,OAAO,IAAIA,IAAI,CAACP,OAAO,CAACQ,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,CAAC;UACjD;YACE,OAAO,IAAI;QACf;MACF,CAAC,CAAC;IACJ;;IAEA;IACAT,QAAQ,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAM,EAAEC,MAAM;MAElB,QAAQ3D,aAAa;QACnB,KAAK,OAAO;UACV0D,MAAM,GAAGF,CAAC,CAACT,KAAK,CAACC,WAAW,CAAC,CAAC;UAC9BW,MAAM,GAAGF,CAAC,CAACV,KAAK,CAACC,WAAW,CAAC,CAAC;UAC9B;QACF,KAAK,aAAa;UAChBU,MAAM,GAAGE,UAAU,CAACJ,CAAC,CAACK,WAAW,CAAC;UAClCF,MAAM,GAAGC,UAAU,CAACH,CAAC,CAACI,WAAW,CAAC;UAClC;QACF,KAAK,aAAa;UAChBH,MAAM,GAAGF,CAAC,CAACM,WAAW,IAAI,CAAC;UAC3BH,MAAM,GAAGF,CAAC,CAACK,WAAW,IAAI,CAAC;UAC3B;QACF,KAAK,OAAO;UACVJ,MAAM,GAAGF,CAAC,CAACN,eAAe,CAACF,WAAW,CAAC,CAAC;UACxCW,MAAM,GAAGF,CAAC,CAACP,eAAe,CAACF,WAAW,CAAC,CAAC;UACxC;QACF;UAAS;UACPU,MAAM,GAAG,IAAIL,IAAI,CAACG,CAAC,CAACO,UAAU,CAAC;UAC/BJ,MAAM,GAAG,IAAIN,IAAI,CAACI,CAAC,CAACM,UAAU,CAAC;MACnC;MAEA,IAAI7D,gBAAgB,KAAK,KAAK,EAAE;QAC9B,OAAOwD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEF1E,WAAW,CAAC2D,QAAQ,CAAC;EACvB,CAAC,EAAE,CACD1D,WAAW,EACXU,iBAAiB,EACjBE,mBAAmB,EACnBE,aAAa,EACbE,gBAAgB,CACjB,CAAC;;EAEF;EACAjD,SAAS,CAAC,MAAM;IACd,IAAIiC,WAAW,CAAC8E,MAAM,GAAG,CAAC,EAAE;MAC1BrB,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACzD,WAAW,EAAEyD,qBAAqB,CAAC,CAAC;EAExC,MAAMsB,wBAAwB,GAAG/G,WAAW,CAAC,MAAM;IACjD,IAAI;MACF,MAAMgH,MAAM,GAAG9G,sBAAsB,CAAC+G,yBAAyB,CAC5DvD,IAAI,IAAK;QACRP,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEM,IAAI,CAAC;;QAE7D;QACA/B,YAAY,CAAEuF,IAAI,IAAK;UACrB,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;UAEtB,OAAO;YACL,GAAGA,IAAI;YACPlD,aAAa,EAAE;cACb,GAAGkD,IAAI,CAAClD,aAAa;cACrB,GAAGN,IAAI,CAACM;YACV,CAAC;YACDS,YAAY,EAAE;cACZ,GAAGyC,IAAI,CAACzC,YAAY;cACpB,GAAGf,IAAI,CAACe;YACV;UACF,CAAC;QACH,CAAC,CAAC;QAEFtB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC7D,CACF,CAAC;MACDX,eAAe,CAACuE,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZhE,OAAO,CAACf,KAAK,CAAC,6BAA6B,EAAE+E,GAAG,CAAC;IACnD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,UAAU,GAAG,MAAAA,CAAOC,MAAM,EAAEC,QAAQ,KAAK;IAC7C,IACE,CAACC,MAAM,CAACC,OAAO,CAAC,yCAAyCF,QAAQ,IAAI,CAAC,EACtE;MACA;IACF;IAEA,IAAI;MACF,MAAMjE,QAAQ,GAAG,MAAMC,KAAK,CAC1B,+CAA+C+D,MAAM,GAAG,EACxD;QACEI,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QAC/D;MACF,CACF,CAAC;MAED,IAAIxE,QAAQ,CAACG,EAAE,EAAE;QACfsE,KAAK,CAAC,SAASR,QAAQ,wBAAwB,CAAC;QAChDS,aAAa,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,MAAM3F,KAAK,GAAG,MAAMiB,QAAQ,CAACM,IAAI,CAAC,CAAC;QACnCmE,KAAK,CAAC,UAAU1F,KAAK,CAACA,KAAK,EAAE,CAAC;MAChC;IACF,CAAC,CAAC,OAAO+E,GAAG,EAAE;MACZW,KAAK,CAAC,wBAAwBX,GAAG,CAAC3B,OAAO,EAAE,CAAC;IAC9C;EACF,CAAC;EAED,MAAMwC,aAAa,GAAG,MAAAA,CAAOC,SAAS,EAAEpC,KAAK,KAAK;IAChD,IACE,CAAC0B,MAAM,CAACC,OAAO,CAAC,4CAA4C3B,KAAK,IAAI,CAAC,EACtE;MACA;IACF;IAEA,IAAI;MACF,MAAMxC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kDAAkD2E,SAAS,GAAG,EAC9D;QACER,MAAM,EAAE;MACV,CACF,CAAC;MAED,IAAIpE,QAAQ,CAACG,EAAE,EAAE;QACfsE,KAAK,CAAC,YAAYjC,KAAK,wBAAwB,CAAC;QAChDkC,aAAa,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,MAAM3F,KAAK,GAAG,MAAMiB,QAAQ,CAACM,IAAI,CAAC,CAAC;QACnCmE,KAAK,CAAC,UAAU1F,KAAK,CAACA,KAAK,EAAE,CAAC;MAChC;IACF,CAAC,CAAC,OAAO+E,GAAG,EAAE;MACZW,KAAK,CAAC,2BAA2BX,GAAG,CAAC3B,OAAO,EAAE,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAMuC,aAAa,GAAGA,CAAA,KAAM;IAC1BR,MAAM,CAACW,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC;;EAED;EACApI,SAAS,CAAC,MAAM;IACdoD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;;IAEvE;IACA,MAAMgF,IAAI,GAAGC,IAAI,CAACC,KAAK,CACrBV,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,IAAI,IACnD,CAAC;IACD,MAAMU,KAAK,GACTX,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACvE1E,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgF,IAAI,CAAC;IAC9CjF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,CAAC,CAACmF,KAAK,CAAC;IACzDpF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEgF,IAAI,CAACI,IAAI,CAAC;;IAExD;IACA,MAAMC,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACFtG,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,IAAI,CAAC;QACdc,OAAO,CAACC,GAAG,CACT,6DACF,CAAC;;QAED;QACAD,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;;QAEhE;QACA,IAAI;UACF,MAAMsF,YAAY,GAAG,MAAMpF,KAAK,CAAC,iCAAiC,CAAC;UACnEH,OAAO,CAACC,GAAG,CACT,gDAAgD,EAChDsF,YAAY,CAACnF,MACf,CAAC;QACH,CAAC,CAAC,OAAOoF,YAAY,EAAE;UACrBxF,OAAO,CAACf,KAAK,CACX,kDAAkD,EAClDuG,YACF,CAAC;UACD,MAAM,IAAIlF,KAAK,CACb,qFACF,CAAC;QACH;QAEA,MAAMP,oBAAoB,CAAC,CAAC;QAC5BC,OAAO,CAACC,GAAG,CACT,0FACF,CAAC;MACH,CAAC,CAAC,OAAO+D,GAAG,EAAE;QACZhE,OAAO,CAACf,KAAK,CAAC,qCAAqC,EAAE+E,GAAG,CAAC;QACzD9E,QAAQ,CAAC8E,GAAG,CAAC3B,OAAO,CAAC;MACvB,CAAC,SAAS;QACRrD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDsG,QAAQ,CAAC,CAAC;IACV1B,wBAAwB,CAAC,CAAC;;IAE1B;IACA;IACA;IACA;IACA;IACA;;IAEA,OAAO,MAAM;MACX,IAAIvE,YAAY,EAAE;QAChBA,YAAY,CAACoG,KAAK,CAAC,CAAC;MACtB;MACA;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAI1G,OAAO,EAAE;IACX,oBACErC,KAAA,CAAAgJ,aAAA;MAAKC,SAAS,EAAC,yBAAyB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACtCvJ,KAAA,CAAAgJ,aAAA;MAAKC,SAAS,EAAC,iBAAiB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eACvCvJ,KAAA,CAAAgJ,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,4BAA6B,CAC7B,CAAC;EAEV;EAEA,IAAIhH,KAAK,EAAE;IACT,oBACEvC,KAAA,CAAAgJ,aAAA;MAAKC,SAAS,EAAC,uBAAuB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACpCvJ,KAAA,CAAAgJ,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,gCAA6B,CAAC,eAClCvJ,KAAA,CAAAgJ,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAIhH,KAAS,CAAC,eACdvC,KAAA,CAAAgJ,aAAA;MAAQQ,OAAO,EAAEtB,aAAc;MAAAgB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,OAAa,CAC1C,CAAC;EAEV;EAEA,oBACEvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,oCAAuB,CAAC,eAC5BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,+CAAgD,CAChD,CAAC,eAGNvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BvJ,KAAA,CAAAgJ,aAAA;IACEC,SAAS,EAAE,OAAOxG,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC7D+G,OAAO,EAAEA,CAAA,KAAM9G,YAAY,CAAC,UAAU,CAAE;IAAAwG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzC,uBAEO,CAAC,eACTvJ,KAAA,CAAAgJ,aAAA;IACEC,SAAS,EAAE,OAAOxG,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC1D+G,OAAO,EAAEA,CAAA,KAAM9G,YAAY,CAAC,OAAO,CAAE;IAAAwG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC,sBACW,EAACxH,KAAK,CAACkF,MAAM,EAAC,GAClB,CAAC,eACTjH,KAAA,CAAAgJ,aAAA;IACEC,SAAS,EAAE,OAAOxG,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC7D+G,OAAO,EAAEA,CAAA,KAAM9G,YAAY,CAAC,UAAU,CAAE;IAAAwG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzC,yBACc,EAACtH,QAAQ,CAACgF,MAAM,EAAC,GACxB,CAAC,eACTjH,KAAA,CAAAgJ,aAAA;IACEC,SAAS,EAAE,OAAOxG,SAAS,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC9D+G,OAAO,EAAEA,CAAA,KAAM9G,YAAY,CAAC,WAAW,CAAE;IAAAwG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1C,2BAEO,CACL,CAAC,EAGL9G,SAAS,KAAK,UAAU,IAAIZ,SAAS,iBACpC7B,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAO,CAAC,eACrCvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGnJ,kBAAkB,CACjB,EAAAG,qBAAA,GAAAsB,SAAS,CAAC+C,YAAY,cAAArE,qBAAA,uBAAtBA,qBAAA,CAAwBoE,aAAa,KAAI,CAC3C,CACE,CAAC,eACL3E,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,eAAgB,CAAC,eACpBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGnJ,kBAAkB,CACjB,EAAAI,sBAAA,GAAAqB,SAAS,CAAC+C,YAAY,cAAApE,sBAAA,uBAAtBA,sBAAA,CAAwBiJ,gBAAgB,KAAI,CAC9C,CAAC,EAAE,GAAG,EAAC,mBAEF,CACJ,CACF,CAAC,eAENzJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjCvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAO,CAAC,eACrCvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BvJ,KAAA,CAAAgJ,aAAA;IACEC,SAAS,EACP,EAAAxI,sBAAA,GAAAoB,SAAS,CAAC+C,YAAY,cAAAnE,sBAAA,uBAAtBA,sBAAA,CAAwBiJ,WAAW,KAAI,CAAC,GACpC,UAAU,GACV,UACL;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEA,EAAA7I,sBAAA,GAAAmB,SAAS,CAAC+C,YAAY,cAAAlE,sBAAA,uBAAtBA,sBAAA,CAAwBgJ,WAAW,KAAI,CAAC,EAAC,GACxC,CAAC,eACL1J,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,gBAAiB,CAAC,eACrBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,qBAA0B,CAC9B,CACF,CAAC,eAENvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAO,CAAC,eACrCvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,EAAA5I,qBAAA,GAAAkB,SAAS,CAACsC,aAAa,cAAAxD,qBAAA,uBAAvBA,qBAAA,CAAyB2D,WAAW,KAAI,CAAM,CAAC,eACpDtE,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,aAAc,CAAC,eAClBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG,EAAA3I,qBAAA,GAAAiB,SAAS,CAAC8H,eAAe,cAAA/I,qBAAA,uBAAzBA,qBAAA,CAA2BgJ,YAAY,KAAI,CAAC,EAAC,oBAEzC,CACJ,CACF,CAAC,eAEN5J,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnCvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAO,CAAC,eACrCvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,EAAA1I,sBAAA,GAAAgB,SAAS,CAACsC,aAAa,cAAAtD,sBAAA,uBAAvBA,sBAAA,CAAyBuD,cAAc,KAAI,CAAM,CAAC,eACvDpE,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,gBAAiB,CAAC,eACrBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG,EAAAzI,sBAAA,GAAAe,SAAS,CAACsC,aAAa,cAAArD,sBAAA,uBAAvBA,sBAAA,CAAyBuD,eAAe,KAAI,CAAC,EAAC,mBAE1C,CACJ,CACF,CACF,CAAC,eAGNrE,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,gCAAwB,CAAC,eAC7BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,OAAS,CAAC,eACdvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGnJ,kBAAkB,CACjB,EAAAW,sBAAA,GAAAc,SAAS,CAAC+C,YAAY,cAAA7D,sBAAA,uBAAtBA,sBAAA,CAAwB8I,aAAa,KAAI,CAC3C,CACC,CACA,CAAC,eACN7J,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,WAAa,CAAC,eAClBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGnJ,kBAAkB,CACjB,EAAAY,sBAAA,GAAAa,SAAS,CAAC+C,YAAY,cAAA5D,sBAAA,uBAAtBA,sBAAA,CAAwB8I,cAAc,KAAI,CAC5C,CACC,CACA,CAAC,eACN9J,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,YAAc,CAAC,eACnBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGnJ,kBAAkB,CACjB,EAAAa,sBAAA,GAAAY,SAAS,CAAC+C,YAAY,cAAA3D,sBAAA,uBAAtBA,sBAAA,CAAwB4D,eAAe,KAAI,CAC7C,CACC,CACA,CAAC,eACN7E,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iBAAmB,CAAC,eACxBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGnJ,kBAAkB,CACjB,EAAAc,sBAAA,GAAAW,SAAS,CAAC+C,YAAY,cAAA1D,sBAAA,uBAAtBA,sBAAA,CAAwBwD,iBAAiB,KAAI,CAC/C,CACC,CACA,CACF,CACF,CAAC,eAGN1E,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,8BAAsB,CAAC,eAC3BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,oCAAuB,CAAC,EAC3B,EAAApI,qBAAA,GAAAU,SAAS,CAACmD,eAAe,cAAA7D,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2B4D,eAAe,cAAA3D,sBAAA,uBAA1CA,sBAAA,CAA4C6F,MAAM,IAAG,CAAC,GACrDpF,SAAS,CAACmD,eAAe,CAACD,eAAe,CACtCI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACX4E,GAAG,CAAC,CAAChE,OAAO,EAAEiE,KAAK,kBAClBhK,KAAA,CAAAgJ,aAAA;IAAKiB,GAAG,EAAElE,OAAO,CAACmE,EAAE,IAAIF,KAAM;IAACf,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAASxD,OAAO,CAACC,KAAc,CAAC,eAChChG,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BvJ,KAAA,CAAAgJ,aAAA;IAAMC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BnJ,kBAAkB,CAAC2F,OAAO,CAACe,WAAW,CACnC,CAAC,eACP9G,KAAA,CAAAgJ,aAAA;IAAMC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,KAC3B,EAACxD,OAAO,CAACI,eACR,CACH,CACF,CAAC,eACNnG,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3B,IAAIjD,IAAI,CAACP,OAAO,CAACiB,UAAU,CAAC,CAACmD,kBAAkB,CAAC,CAC9C,CACF,CACN,CAAC,gBAEJnK,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oBAAuB,CAEnD,CAAC,eAENvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,0BAAkB,CAAC,EACtB,EAAAlI,sBAAA,GAAAQ,SAAS,CAACmD,eAAe,cAAA3D,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2B4D,WAAW,cAAA3D,sBAAA,uBAAtCA,sBAAA,CAAwC2F,MAAM,IAAG,CAAC,GACjDpF,SAAS,CAACmD,eAAe,CAACC,WAAW,CAClCE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACX4E,GAAG,CAAC,CAACK,GAAG,EAAEJ,KAAK,kBACdhK,KAAA,CAAAgJ,aAAA;IAAKiB,GAAG,EAAEG,GAAG,CAACF,EAAE,IAAIF,KAAM;IAACf,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClDvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BvJ,KAAA,CAAAgJ,aAAA;IAAQC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3BnJ,kBAAkB,CAACgK,GAAG,CAACC,MAAM,CACxB,CAAC,eACTrK,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BvJ,KAAA,CAAAgJ,aAAA;IAAMC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5Ba,GAAG,CAACE,cACD,CAAC,eACPtK,KAAA,CAAAgJ,aAAA;IAAMC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,KAC9B,EAACa,GAAG,CAACG,cACJ,CACH,CACF,CAAC,eACNvK,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3B,IAAIjD,IAAI,CAAC8D,GAAG,CAACpD,UAAU,CAAC,CAACmD,kBAAkB,CAAC,CAC1C,CACF,CACN,CAAC,gBAEJnK,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gBAAmB,CAE/C,CAAC,eAENvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,wBAAgB,CAAC,EACpB,EAAAhI,sBAAA,GAAAM,SAAS,CAACmD,eAAe,cAAAzD,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2B2D,YAAY,cAAA1D,sBAAA,uBAAvCA,sBAAA,CAAyCyF,MAAM,IAAG,CAAC,GAClDpF,SAAS,CAACmD,eAAe,CAACE,YAAY,CACnCC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACX4E,GAAG,CAAC,CAACxB,IAAI,EAAEyB,KAAK,kBACfhK,KAAA,CAAAgJ,aAAA;IAAKiB,GAAG,EAAE1B,IAAI,CAAC2B,EAAE,IAAIF,KAAM;IAACf,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnDvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAShB,IAAI,CAACd,QAAiB,CAAC,eAChCzH,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BvJ,KAAA,CAAAgJ,aAAA;IAAMC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEhB,IAAI,CAACiC,KAAY,CAAC,eACpDxK,KAAA,CAAAgJ,aAAA;IACEC,SAAS,EAAE,mBACTV,IAAI,CAACkC,SAAS,GAAG,QAAQ,GAAG,UAAU,EACrC;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEFhB,IAAI,CAACkC,SAAS,GAAG,QAAQ,GAAG,UACzB,CACH,CACF,CAAC,eACNzK,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3B,IAAIjD,IAAI,CAACiC,IAAI,CAACmC,WAAW,CAAC,CAACP,kBAAkB,CAAC,CAC5C,CACF,CACN,CAAC,gBAEJnK,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAiB,CAE7C,CACF,CACF,CACF,CACN,EAGA9G,SAAS,KAAK,OAAO,iBACpBzC,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,8BAAsB,CAAC,eAC3BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,gCAAiC,CACjC,CAAC,eAENvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,MAAU,CAAC,eACjBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,OAAW,CAAC,eAClBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,QAAY,CAAC,eACnBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,UAAc,CAAC,eACrBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,MAAU,CAAC,eACjBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,QAAY,CAAC,eACnBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,SAAa,CAChB,CAAC,EAELxH,KAAK,CAACgI,GAAG,CAAExB,IAAI,iBACdvI,KAAA,CAAAgJ,aAAA;IAAKiB,GAAG,EAAE1B,IAAI,CAAC2B,EAAG;IAACjB,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtCvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAShB,IAAI,CAACd,QAAiB,CAAC,EAC/Bc,IAAI,CAACoC,QAAQ,iBAAI3K,KAAA,CAAAgJ,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OAAW,CACxD,CAAC,eACNvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAOhB,IAAI,CAACiC,KAAY,CAAC,eACzBxK,KAAA,CAAAgJ,aAAA;IACEC,SAAS,EAAE,UAAUV,IAAI,CAACkC,SAAS,GAAG,QAAQ,GAAG,UAAU,EAAG;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAE7DhB,IAAI,CAACkC,SAAS,GAAG,QAAQ,GAAG,UACzB,CAAC,eACPzK,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAOhB,IAAI,CAACqC,aAAoB,CAAC,eACjC5K,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAOhB,IAAI,CAACsC,SAAgB,CAAC,eAC7B7K,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,IAAIjD,IAAI,CAACiC,IAAI,CAACmC,WAAW,CAAC,CAACP,kBAAkB,CAAC,CAAQ,CAAC,eAC9DnK,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACrB,CAAChB,IAAI,CAACoC,QAAQ,iBACb3K,KAAA,CAAAgJ,aAAA;IACEC,SAAS,EAAC,YAAY;IACtBO,OAAO,EAAEA,CAAA,KAAMjC,UAAU,CAACgB,IAAI,CAAC2B,EAAE,EAAE3B,IAAI,CAACd,QAAQ,CAAE;IAAAyB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACnD,2BAEO,CAEP,CACF,CACN,CACE,CACF,CACN,EAGA9G,SAAS,KAAK,UAAU,iBACvBzC,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,+CAAuC,CAAC,eAC5CvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,aACU,EAACpH,WAAW,CAAC8E,MAAM,EAAC,iDAE9B,CACA,CAAC,eAGNjH,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BvJ,KAAA,CAAAgJ,aAAA;IACE8B,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,qDAA2C;IACvDC,KAAK,EAAEnI,iBAAkB;IACzBoI,QAAQ,EAAGC,CAAC,IAAKpI,oBAAoB,CAACoI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;IACtD/B,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACzB,CACE,CAAC,eAENvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BvJ,KAAA,CAAAgJ,aAAA;IACEgC,KAAK,EAAEjI,mBAAoB;IAC3BkI,QAAQ,EAAGC,CAAC,IAAKlI,sBAAsB,CAACkI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;IACxD/B,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzBvJ,KAAA,CAAAgJ,aAAA;IAAQgC,KAAK,EAAC,KAAK;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,YAAkB,CAAC,eACvCvJ,KAAA,CAAAgJ,aAAA;IAAQgC,KAAK,EAAC,UAAU;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,iBAAkB,CAAC,eAC5CvJ,KAAA,CAAAgJ,aAAA;IAAQgC,KAAK,EAAC,SAAS;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gBAAiB,CAAC,eAC1CvJ,KAAA,CAAAgJ,aAAA;IAAQgC,KAAK,EAAC,QAAQ;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qBAAiB,CAAC,eACzCvJ,KAAA,CAAAgJ,aAAA;IAAQgC,KAAK,EAAC,OAAO;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAe,CAC/B,CACL,CAAC,eAENvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BvJ,KAAA,CAAAgJ,aAAA;IACEgC,KAAK,EAAE/H,aAAc;IACrBgI,QAAQ,EAAGC,CAAC,IAAKhI,gBAAgB,CAACgI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;IAClD/B,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvBvJ,KAAA,CAAAgJ,aAAA;IAAQgC,KAAK,EAAC,YAAY;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2BAAuB,CAAC,eACnDvJ,KAAA,CAAAgJ,aAAA;IAAQgC,KAAK,EAAC,OAAO;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oBAAgB,CAAC,eACvCvJ,KAAA,CAAAgJ,aAAA;IAAQgC,KAAK,EAAC,aAAa;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,0BAAsB,CAAC,eACnDvJ,KAAA,CAAAgJ,aAAA;IAAQgC,KAAK,EAAC,aAAa;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,0BAAiB,CAAC,eAC9CvJ,KAAA,CAAAgJ,aAAA;IAAQgC,KAAK,EAAC,OAAO;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oBAAgB,CAChC,CAAC,eAETvJ,KAAA,CAAAgJ,aAAA;IACEQ,OAAO,EAAEA,CAAA,KACPpG,mBAAmB,CACjBD,gBAAgB,KAAK,KAAK,GAAG,MAAM,GAAG,KACxC,CACD;IACD8F,SAAS,EAAC,gBAAgB;IAC1BjD,KAAK,EAAE,QACL7C,gBAAgB,KAAK,KAAK,GAAG,YAAY,GAAG,WAAW,EACtD;IAAA+F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEFpG,gBAAgB,KAAK,KAAK,GAAG,IAAI,GAAG,IAC/B,CACL,CACF,CAAC,eAENnD,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BvJ,KAAA,CAAAgJ,aAAA;IAAMC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uBACnB,EAACtH,QAAQ,CAACgF,MAAM,EAAC,MAAI,EAAC9E,WAAW,CAAC8E,MAAM,EAAC,WAChD,CAAC,EACNpE,iBAAiB,iBAChB7C,KAAA,CAAAgJ,aAAA;IACEQ,OAAO,EAAEA,CAAA,KAAM1G,oBAAoB,CAAC,EAAE,CAAE;IACxCmG,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7B,qBAEO,CAEP,CACF,CAAC,eAENvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,IAAQ,CAAC,eACfvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,iBAAqB,CAAC,eAC5BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,OAAW,CAAC,eAClBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,aAAiB,CAAC,eACxBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,OAAW,CAAC,eAClBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,QAAY,CAAC,eACnBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,SAAa,CAAC,eACpBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,SAAa,CAChB,CAAC,EAELtH,QAAQ,CAACgF,MAAM,GAAG,CAAC,GAClBhF,QAAQ,CAAC8H,GAAG,CAAEhE,OAAO;IAAA,IAAAqF,oBAAA;IAAA,oBACnBpL,KAAA,CAAAgJ,aAAA;MAAKiB,GAAG,EAAElE,OAAO,CAACmE,EAAG;MAACjB,SAAS,EAAC,WAAW;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzCvJ,KAAA,CAAAgJ,aAAA;MAAMC,SAAS,EAAC,YAAY;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,GAAC,EAACxD,OAAO,CAACmE,EAAS,CAAC,eACjDlK,KAAA,CAAAgJ,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC3BvJ,KAAA,CAAAgJ,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAASxD,OAAO,CAACC,KAAc,CAAC,eAChChG,KAAA,CAAAgJ,aAAA;MAAOC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,eAC/B,EAACxD,OAAO,CAACK,QACP,CACJ,CAAC,eACNpG,KAAA,CAAAgJ,aAAA;MAAMC,SAAS,EAAC,eAAe;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,eAC3B,EAACxD,OAAO,CAACI,eACR,CAAC,eACPnG,KAAA,CAAAgJ,aAAA;MAAMC,SAAS,EAAC,aAAa;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC1BnJ,kBAAkB,CAAC2F,OAAO,CAACe,WAAW,CACnC,CAAC,eACP9G,KAAA,CAAAgJ,aAAA;MAAMC,SAAS,EAAC,aAAa;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,qBACxB,EAAC,EAAA6B,oBAAA,GAAArF,OAAO,CAACgB,WAAW,cAAAqE,oBAAA,uBAAnBA,oBAAA,CAAqBC,cAAc,CAAC,CAAC,KAAI,CAC1C,CAAC,eACPrL,KAAA,CAAAgJ,aAAA;MACEC,SAAS,EAAE,UACTlD,OAAO,CAACM,QAAQ,GAAG,UAAU,GAAG,SAAS,EACxC;MAAA6C,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEFxD,OAAO,CAACM,QAAQ,GAAG,YAAY,GAAG,WAC/B,CAAC,eACPrG,KAAA,CAAAgJ,aAAA;MAAMC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC3B,IAAIjD,IAAI,CAACP,OAAO,CAACiB,UAAU,CAAC,CAACmD,kBAAkB,CAAC,CAC7C,CAAC,eACPnK,KAAA,CAAAgJ,aAAA;MAAKC,SAAS,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACtBvJ,KAAA,CAAAgJ,aAAA;MACEC,SAAS,EAAC,YAAY;MACtBO,OAAO,EAAEA,CAAA,KAAMrB,aAAa,CAACpC,OAAO,CAACmE,EAAE,EAAEnE,OAAO,CAACC,KAAK,CAAE;MACxDA,KAAK,EAAC,gBAAgB;MAAAkD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACvB,2BAEO,CACL,CACF,CAAC;EAAA,CACP,CAAC,gBAEFvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,uDAA8C,CAAC,EACjD1G,iBAAiB,iBAChB7C,KAAA,CAAAgJ,aAAA;IACEQ,OAAO,EAAEA,CAAA,KAAM1G,oBAAoB,CAAC,EAAE,CAAE;IACxCmG,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7B,cAEO,CAEP,CAEJ,CACF,CACN,EAGA9G,SAAS,KAAK,WAAW,IAAIZ,SAAS,iBACrC7B,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,2BAAmB,CAAC,eACxBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,qCAAsC,CACtC,CAAC,eAENvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,gBAAkB,CAAC,eACvBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,EAAA9H,qBAAA,GAAAI,SAAS,CAACuD,UAAU,cAAA3D,qBAAA,uBAApBA,qBAAA,CAAsB6D,iBAAiB,KAAI,CAAK,CAAC,eACrDtF,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,wBAA6B,CACjC,CAAC,eAENvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,oBAAsB,CAAC,eAC3BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,EAAA7H,sBAAA,GAAAG,SAAS,CAACuD,UAAU,cAAA1D,sBAAA,uBAApBA,sBAAA,CAAsB8D,cAAc,KAAI,CAAC,EAAC,GAAI,CAAC,eACnDxF,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,wBAA6B,CACjC,CAAC,eAENvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAqB,CAAC,eAC1BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,EAAA5H,sBAAA,GAAAE,SAAS,CAACuD,UAAU,cAAAzD,sBAAA,uBAApBA,sBAAA,CAAsB8D,iBAAiB,KAAI,KAAS,CAAC,eACzDzF,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,4BAAiC,CACrC,CAAC,eAENvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAqB,CAAC,eAC1BvJ,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,EAAA3H,sBAAA,GAAAC,SAAS,CAACuD,UAAU,cAAAxD,sBAAA,uBAApBA,sBAAA,CAAsB2D,iBAAiB,KAAI,CAAK,CAAC,eACrDvF,KAAA,CAAAgJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,iBAAsB,CAC1B,CACF,CACF,CACN,eAGDvJ,KAAA,CAAAgJ,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCvJ,KAAA,CAAAgJ,aAAA;IAAQC,SAAS,EAAC,aAAa;IAACO,OAAO,EAAEtB,aAAc;IAAAgB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2BAEhD,CACL,CACF,CAAC;AAEV,CAAC;AAED,eAAejJ,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}