import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";

const WebSocketContext = createContext(null);

export const WebSocketProvider = ({ auctionId, children }) => {
  const socket = useRef(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Check if WebSocket is enabled
    const isWebSocketEnabled =
      process.env.REACT_APP_WEBSOCKET_ENABLED === "true";

    if (!isWebSocketEnabled) {
      console.log("🔇 WebSocket disabled for auction", auctionId);
      return;
    }

    // Get authentication token
    const token =
      localStorage.getItem("token") || localStorage.getItem("access_token");

    console.log(
      "Token for auction WebSocket:",
      token ? `${token.substring(0, 20)}...` : "No token found"
    );

    // Add token as query parameter for WebSocket authentication
    const wsUrl = token
      ? `ws://127.0.0.1:8001/ws/auction/${auctionId}/?token=${token}`
      : `ws://127.0.0.1:8001/ws/auction/${auctionId}/`;

    console.log(
      "Connecting to auction WebSocket:",
      wsUrl.replace(token || "", "TOKEN_HIDDEN")
    );

    try {
      socket.current = new WebSocket(wsUrl);

      socket.current.onopen = () => {
        console.log("WebSocket connected");
        setIsConnected(true);
      };

      socket.current.onclose = () => {
        console.log("WebSocket disconnected");
        setIsConnected(false);
      };

      socket.current.onerror = (error) => {
        console.error("WebSocket error:", error);
        setIsConnected(false);
      };
    } catch (error) {
      console.error("Failed to create WebSocket:", error);
      setIsConnected(false);
    }

    return () => {
      if (socket.current && socket.current.readyState === WebSocket.OPEN) {
        socket.current.close();
      }
    };
  }, [auctionId]);

  return (
    <WebSocketContext.Provider value={{ socket: socket.current, isConnected }}>
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocket = () => useContext(WebSocketContext);
