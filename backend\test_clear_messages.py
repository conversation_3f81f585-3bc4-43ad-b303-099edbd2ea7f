#!/usr/bin/env python
"""
Test script to verify the clear messages functionality
"""

import os
import sys
import django
from datetime import datetime

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from auction.models import ChatRoom, ChatMessage

def test_clear_messages():
    """Test the clear messages functionality"""
    print("🧪 Testing clear messages functionality...")
    
    try:
        # Find a chat room with messages
        chat_room = ChatRoom.objects.filter(messages__isnull=False).first()
        
        if not chat_room:
            print("❌ No chat rooms with messages found.")
            print("💡 Run add_sample_chat_data.py first to create test data.")
            return
        
        # Count messages before clearing
        message_count_before = ChatMessage.objects.filter(room=chat_room).count()
        print(f"📊 Chat room {chat_room.id} has {message_count_before} messages")
        
        if message_count_before == 0:
            print("❌ No messages to clear in this room.")
            return
        
        # Show some sample messages
        sample_messages = ChatMessage.objects.filter(room=chat_room)[:3]
        print(f"\n📝 Sample messages:")
        for msg in sample_messages:
            sender = msg.sender.username if msg.sender else "AI"
            print(f"   {sender}: {msg.message[:50]}...")
        
        # Ask for confirmation
        confirm = input(f"\n🗑️ Clear all {message_count_before} messages from room {chat_room.id}? (y/N): ")
        
        if confirm.lower() != 'y':
            print("❌ Clear operation cancelled.")
            return
        
        # Clear messages
        deleted_count, _ = ChatMessage.objects.filter(room=chat_room).delete()
        
        # Verify clearing
        message_count_after = ChatMessage.objects.filter(room=chat_room).count()
        
        print(f"\n✅ Successfully cleared {deleted_count} messages!")
        print(f"📊 Messages before: {message_count_before}")
        print(f"📊 Messages after: {message_count_after}")
        print(f"🎯 Chat room ID: {chat_room.id}")
        
        if message_count_after == 0:
            print("✅ All messages cleared successfully!")
        else:
            print(f"⚠️ Warning: {message_count_after} messages still remain")
        
    except Exception as e:
        print(f"❌ Error testing clear messages: {str(e)}")
        import traceback
        traceback.print_exc()

def show_all_chat_rooms():
    """Show all available chat rooms"""
    print("\n📋 Available Chat Rooms:")
    print("=" * 50)
    
    chat_rooms = ChatRoom.objects.all()
    
    if not chat_rooms:
        print("❌ No chat rooms found.")
        return
    
    for room in chat_rooms:
        message_count = room.messages.count()
        print(f"🏠 Room {room.id}: {room.auction.title}")
        print(f"   📊 Messages: {message_count}")
        print(f"   📅 Created: {room.created_at.strftime('%Y-%m-%d %H:%M')}")
        print(f"   🔗 Auction ID: {room.auction.id}")
        print()

if __name__ == '__main__':
    print("🤖 Chat Message Clear Test")
    print("=" * 40)
    
    # Show available rooms first
    show_all_chat_rooms()
    
    # Test clear functionality
    test_clear_messages()
