import os
import warnings
from datetime import timedelta
from pathlib import Path

from decouple import config

# Suppress specific deprecation warnings
warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API.*")

BASE_DIR = Path(__file__).resolve().parent.parent
MEDIA_URL = "/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-nizb1qw&3o&ilb2n70ypk#@l_&@dt=s4w+gk587k6)f2r8@mhq"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = [
    "localhost",
    "127.0.0.1",
    "0.0.0.0",
    "*",  # Allow all hosts for development
]


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "channels",  # Add Django Channels for WebSocket support
    "django_redis",
    "django_filters",
    "rest_framework_simplejwt.token_blacklist",
    "corsheaders",
    "auction.apps.AuctionConfig",
    "auctions",  # Additional auction functionality
    "rest_framework.authtoken",
]

REST_FRAMEWORK = {
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.AllowAny",  # Keep API open for public browsing
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ],
    "DEFAULT_FILTER_BACKENDS": ["django_filters.rest_framework.DjangoFilterBackend"],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 10,
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=15),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "AUTH_HEADER_TYPES": ("Bearer",),
}

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",  # Standard Django CSRF middleware
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # Your React app URL (primary)
    "http://127.0.0.1:3000",  # Alternative localhost (primary)
    "http://localhost:3001",  # Your React app URL (fallback)
    "http://127.0.0.1:3001",  # Alternative localhost (fallback)
    "http://localhost:3002",  # Your React app URL (additional)
    "http://127.0.0.1:3002",  # Alternative localhost (additional)
    "http://localhost:8000",  # Django admin
    "http://127.0.0.1:8000",  # Django admin alternative
]

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = False  # Keep this False for security
CORS_ALLOWED_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
]

ROOT_URLCONF = "OnlineAuctionSystem.urls"

WSGI_APPLICATION = "OnlineAuctionSystem.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": config("DB_NAME", default="online_auction_db"),
        "USER": config("DB_USER", default="auction_user"),
        "PASSWORD": config("DB_PASSWORD", default="auction_password_2024"),
        "HOST": config("DB_HOST", default="localhost"),
        "PORT": config("DB_PORT", default="5432"),
        "OPTIONS": {
            "client_encoding": "UTF8",
        },
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
        "OPTIONS": {
            "min_length": 12,  # Increased minimum length
        },
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
    {
        "NAME": "auction.validators.StrongPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Asia/Kolkata"

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")

# Additional locations of static files
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
]

# Media files (user uploads)
MEDIA_URL = "/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Caching configuration
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/",  # Adjust the port and database number as needed
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}

ASGI_APPLICATION = "OnlineAuctionSystem.asgi.application"

# Channels configuration
# Using in-memory channel layer for Redis 3.2 compatibility
# Note: This doesn't persist across server restarts but works with older Redis
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer",
    },
}

# Redis channel layer (requires Redis 5.0+) - currently disabled due to Redis 3.2
# CHANNEL_LAYERS = {
#     'default': {
#         'BACKEND': 'channels_redis.core.RedisChannelLayer',
#         'CONFIG': {
#             "hosts": [('127.0.0.1', 6379)],
#             "symmetric_encryption_keys": [SECRET_KEY],
#             # Compatibility settings for older Redis versions
#             "capacity": 1500,
#             "expiry": 60,
#             # Use older Redis commands for compatibility
#             "group_expiry": 86400,
#         },
#     },
# }

EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = "smtp.gmail.com"
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = config("EMAIL_HOST_USER", default="")
EMAIL_HOST_PASSWORD = config("EMAIL_HOST_PASSWORD", default="")
DEFAULT_FROM_EMAIL = config(
    "DEFAULT_FROM_EMAIL", default="AuctionStore <<EMAIL>>"
)

# Celery Configuration
CELERY_BROKER_URL = "redis://127.0.0.1:6379/0"
CELERY_RESULT_BACKEND = "redis://127.0.0.1:6379/0"
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = TIME_ZONE

# Celery Beat Schedule
CELERY_BEAT_SCHEDULE = {
    "close-auctions-every-minute": {
        "task": "auction.tasks.close_auctions_task",
        "schedule": 60.0,  # Run every minute
    },
    "send-auction-ending-reminders": {
        "task": "auction.tasks.send_auction_ending_reminders",
        "schedule": 3600.0,  # Run every hour
    },
    "cleanup-old-notifications": {
        "task": "auction.tasks.cleanup_old_notifications",
        "schedule": 86400.0,  # Run daily
    },
    "process-pending-payments": {
        "task": "auction.tasks.process_pending_payments",
        "schedule": 3600.0,  # Run every hour
    },
}

# Stripe Payment Gateway Settings
STRIPE_PUBLISHABLE_KEY = config(
    "STRIPE_PUBLISHABLE_KEY", default="pk_test_your_stripe_publishable_key"
)
STRIPE_SECRET_KEY = config(
    "STRIPE_SECRET_KEY", default="sk_test_your_stripe_secret_key"
)
STRIPE_WEBHOOK_SECRET = config(
    "STRIPE_WEBHOOK_SECRET", default="whsec_your_webhook_secret"
)
STRIPE_CURRENCY = config("STRIPE_CURRENCY", default="usd")

# Stripe Configuration URLs
STRIPE_SUCCESS_URL = config(
    "STRIPE_SUCCESS_URL", default="http://127.0.0.1:3000/payment/success"
)
STRIPE_CANCEL_URL = config(
    "STRIPE_CANCEL_URL", default="http://127.0.0.1:3000/payment/cancel"
)

# Currency Settings
DEFAULT_CURRENCY = "USD"
CURRENCY_SYMBOL = "$"
SUPPORTED_CURRENCIES = ["USD", "EUR", "GBP", "CAD", "AUD"]

# Payment Settings
PAYMENT_SUCCESS_URL = config(
    "PAYMENT_SUCCESS_URL", default="http://localhost:3001/payment/success/"
)
PAYMENT_FAILURE_URL = config(
    "PAYMENT_FAILURE_URL", default="http://localhost:3001/payment/failure/"
)

# Email Configuration
EMAIL_BACKEND = config(
    "EMAIL_BACKEND", default="django.core.mail.backends.smtp.EmailBackend"
)
EMAIL_HOST = config("EMAIL_HOST", default="smtp.gmail.com")
EMAIL_PORT = config("EMAIL_PORT", default=587, cast=int)
EMAIL_USE_TLS = config("EMAIL_USE_TLS", default=True, cast=bool)
EMAIL_HOST_USER = config("EMAIL_HOST_USER", default="<EMAIL>")
EMAIL_HOST_PASSWORD = config("EMAIL_HOST_PASSWORD", default="")
DEFAULT_FROM_EMAIL = config(
    "DEFAULT_FROM_EMAIL", default="Online Auction System <<EMAIL>>"
)
SERVER_EMAIL = DEFAULT_FROM_EMAIL

# Email Templates
EMAIL_TEMPLATES = {
    "auction_ending_reminder": "emails/auction_ending_reminder.html",
    "bid_notification": "emails/bid_notification.html",
    "auction_won": "emails/auction_won.html",
    "auction_lost": "emails/auction_lost.html",
    "payment_confirmation": "emails/payment_confirmation.html",
    "welcome": "emails/welcome.html",
    "password_reset": "emails/password_reset.html",
    "review_notification": "emails/review_notification.html",
    "review_reminder": "emails/review_reminder.html",
    "admin_review_alert": "emails/admin_review_alert.html",
}

# Frontend URL for email links
FRONTEND_URL = "http://localhost:3001"
SITE_URL = config("SITE_URL", default="http://localhost:3000")

# Additional Email Settings for Reviews
ADMIN_EMAIL = config("ADMIN_EMAIL", default="<EMAIL>")
SUPPORT_EMAIL = config("SUPPORT_EMAIL", default="<EMAIL>")

# Email Settings
EMAIL_TIMEOUT = 30
EMAIL_USE_LOCALTIME = True

# Security Settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
# X-Frame-Options: Use SAMEORIGIN for development to reduce console warnings
# Change to 'DENY' in production for maximum security
X_FRAME_OPTIONS = "SAMEORIGIN" if DEBUG else "DENY"
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Session Security
SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_AGE = 3600  # 1 hour
SESSION_EXPIRE_AT_BROWSER_CLOSE = True

# CSRF Protection - Temporarily relaxed for development
CSRF_COOKIE_SECURE = False  # Set to True in production with HTTPS
CSRF_COOKIE_HTTPONLY = False  # Allow JavaScript access for admin
CSRF_USE_SESSIONS = False  # Use cookies instead of sessions
CSRF_COOKIE_SAMESITE = "Lax"  # Allow cross-site requests for admin
CSRF_FAILURE_VIEW = "django.views.csrf.csrf_failure"
CSRF_TRUSTED_ORIGINS = [
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:3001",
    "http://127.0.0.1:3001",
    "http://localhost:3002",
    "http://127.0.0.1:3002",
]

# Temporarily disable CSRF for development (REMOVE IN PRODUCTION!)
CSRF_COOKIE_NAME = "csrftoken"
CSRF_HEADER_NAME = "HTTP_X_CSRFTOKEN"

# Rate Limiting Settings
RATE_LIMIT_LOGIN_ATTEMPTS = 5
RATE_LIMIT_LOGIN_WINDOW = 300  # 5 minutes
RATE_LIMIT_API_CALLS = 100
RATE_LIMIT_API_WINDOW = 60  # 1 minute

# Content Security Policy
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = ("'self'", "'unsafe-inline'")
CSP_STYLE_SRC = ("'self'", "'unsafe-inline'")
CSP_IMG_SRC = ("'self'", "data:", "https:")
CSP_FONT_SRC = ("'self'", "https:")

# File Upload Security
FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
FILE_UPLOAD_PERMISSIONS = 0o644
