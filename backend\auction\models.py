import uuid
from decimal import Decimal

from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.utils import timezone


class Auction(models.Model):
    AUCTION_TYPES = [
        ("standard", "Standard Auction"),
        ("reserve", "Reserve Auction"),
        ("buy_now", "Buy It Now"),
        ("sealed_bid", "Sealed Bid"),
        ("reverse", "Reverse Auction"),
    ]

    CATEGORIES = [
        ("electronics", "Electronics"),
        ("fashion", "Fashion & Clothing"),
        ("home_garden", "Home & Garden"),
        ("sports", "Sports & Recreation"),
        ("collectibles", "Collectibles"),
        ("automotive", "Automotive"),
        ("books", "Books & Media"),
        ("art", "Art & Crafts"),
        ("jewelry", "Jewelry & Watches"),
        ("other", "Other"),
    ]

    title = models.CharField(max_length=200)
    description = models.TextField()
    starting_bid = models.DecimalField(
        max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal("0.01"))]
    )
    current_bid = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    reserve_price = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    buy_now_price = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    start_time = models.DateTimeField(default=timezone.now)
    end_time = models.DateTimeField()
    auction_type = models.CharField(
        max_length=20, choices=AUCTION_TYPES, default="standard"
    )
    category = models.CharField(max_length=100, choices=CATEGORIES, default="other")
    image = models.URLField(blank=True)
    additional_images = models.JSONField(
        default=list, blank=True
    )  # Store multiple image URLs
    location = models.CharField(max_length=200, blank=True)
    condition = models.CharField(max_length=50, blank=True)
    shipping_cost = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name="auctions")
    winner = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="won_auctions",
    )

    # Payment timeout management
    payment_deadline = models.DateTimeField(null=True, blank=True)
    payment_reminder_sent = models.BooleanField(default=False)
    payment_timeout_count = models.IntegerField(default=0)

    # Re-auction management
    original_auction = models.ForeignKey(
        "self", on_delete=models.SET_NULL, null=True, blank=True
    )
    re_auction_count = models.IntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_closed = models.BooleanField(default=False)
    approved = models.BooleanField(default=False)
    featured = models.BooleanField(default=False)
    views_count = models.PositiveIntegerField(default=0)
    watchers = models.ManyToManyField(User, related_name="watched_auctions", blank=True)

    def save(self, *args, **kwargs):
        if not self.current_bid:
            self.current_bid = self.starting_bid
        super().save(*args, **kwargs)

    @property
    def is_active(self):
        now = timezone.now()
        return self.start_time <= now <= self.end_time and not self.is_closed

    @property
    def time_remaining(self):
        if self.is_closed:
            return None
        now = timezone.now()
        if now > self.end_time:
            return None
        return self.end_time - now

    @property
    def total_bids(self):
        return self.bids.count()

    def get_auction_winner(self):
        """Get the winner of the auction (calculated from bids)"""
        if self.is_closed and self.bids.exists():
            return self.bids.order_by("-amount").first().user
        return None

    @property
    def reserve_met(self):
        if not self.reserve_price:
            return True
        return self.current_bid >= self.reserve_price

    def get_highest_bid(self):
        """Get the highest bid for this auction"""
        return self.bids.order_by("-amount").first()

    def get_winner(self):
        """Get the winner of the auction"""
        if self.is_closed and self.bids.exists():
            highest_bid = self.get_highest_bid()
            return highest_bid.user if highest_bid else None
        return None

    def set_payment_deadline(self):
        """Set payment deadline to 24 hours after auction ends"""
        from datetime import timedelta

        if self.is_closed and not self.payment_deadline:
            self.payment_deadline = timezone.now() + timedelta(hours=24)
            self.save()

    def is_payment_overdue(self):
        """Check if payment deadline has passed"""
        if self.payment_deadline:
            return timezone.now() > self.payment_deadline
        return False

    def handle_payment_timeout(self):
        """Handle payment timeout - re-list auction"""
        if self.is_payment_overdue() and self.winner:
            # Create re-auction
            self.create_re_auction()

            # Update current auction status
            self.payment_timeout_count += 1
            self.save()

            return True
        return False

    def create_re_auction(self):
        """Create a new auction when payment fails"""
        from datetime import timedelta

        re_auction = Auction.objects.create(
            title=f"{self.title} (Re-listed)",
            description=f"{self.description}\n\n[Re-listed due to payment timeout]",
            starting_bid=self.starting_bid,
            current_bid=self.starting_bid,
            reserve_price=self.reserve_price,
            buy_now_price=self.buy_now_price,
            category=self.category,
            condition=self.condition,
            auction_type=self.auction_type,
            owner=self.owner,
            end_time=timezone.now() + timedelta(days=7),  # 7 days from now
            original_auction=self.original_auction or self,
            re_auction_count=self.re_auction_count + 1,
            image=self.image,
            additional_images=self.additional_images,
            location=self.location,
            shipping_cost=self.shipping_cost,
            approved=True,
            featured=self.featured,
        )

        return re_auction

    def __str__(self):
        return self.title


class Bid(models.Model):
    auction = models.ForeignKey(Auction, related_name="bids", on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)

    def clean(self):
        if self.amount <= self.auction.current_bid:
            raise ValidationError(
                f"Bid amount must be greater than the current bid of {self.auction.current_bid}."
            )

    def save(self, *args, **kwargs):
        self.clean()  # Call the clean method to validate
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.username} bid {self.amount} on {self.auction.title}"


class Notification(models.Model):
    NOTIFICATION_TYPES = [
        ("bid", "New Bid"),
        ("auction_end", "Auction Ended"),
        ("payment", "Payment"),
        ("payment_reminder", "Payment Reminder"),
        ("payment_timeout", "Payment Timeout"),
        ("re_auction", "Re-auction"),
        ("general", "General"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    title = models.CharField(max_length=200, default="Notification")
    message = models.TextField()
    notification_type = models.CharField(
        max_length=20, choices=NOTIFICATION_TYPES, default="general"
    )
    auction = models.ForeignKey(
        Auction, on_delete=models.CASCADE, null=True, blank=True
    )
    status = models.CharField(
        max_length=10, choices=[("sent", "Sent"), ("read", "Read")], default="sent"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Notification for {self.user.username}: {self.message[:30]}..."


class Review(models.Model):
    RATING_CHOICES = [
        (1, '1 Star - Poor'),
        (2, '2 Stars - Fair'),
        (3, '3 Stars - Good'),
        (4, '4 Stars - Very Good'),
        (5, '5 Stars - Excellent'),
    ]

    REVIEW_TYPE_CHOICES = [
        ('seller', 'Seller Review'),
        ('buyer', 'Buyer Review'),
        ('general', 'General Review'),
    ]

    reviewer = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="given_reviews"
    )
    reviewee = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="received_reviews"
    )
    auction = models.ForeignKey("Auction", on_delete=models.CASCADE, related_name="reviews")
    rating = models.IntegerField(choices=RATING_CHOICES)
    comment = models.TextField(max_length=1000)
    review_type = models.CharField(max_length=10, choices=REVIEW_TYPE_CHOICES, default='general')
    is_verified = models.BooleanField(default=False)  # Verified purchase/sale
    helpful_count = models.PositiveIntegerField(default=0)
    reported_count = models.PositiveIntegerField(default=0)
    is_approved = models.BooleanField(default=True)
    admin_notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['reviewer', 'reviewee', 'auction']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.reviewer.username} → {self.reviewee.username}: {self.rating}⭐ ({self.review_type})"

    def save(self, *args, **kwargs):
        # Check if this is a verified review (reviewer was involved in the auction)
        if self.auction:
            # Check if reviewer was the winner or a bidder
            from .models import Bid
            reviewer_bids = Bid.objects.filter(auction=self.auction, user=self.reviewer).exists()
            auction_winner = self.auction.current_bid and hasattr(self.auction, 'winning_bid')

            if reviewer_bids or (auction_winner and self.auction.winning_bid.user == self.reviewer):
                self.is_verified = True

        super().save(*args, **kwargs)

        # Send email notification to reviewee
        self.send_review_notification()

    def send_review_notification(self):
        """Send email notification when a review is created"""
        from .email_services import send_review_notification_email
        try:
            send_review_notification_email(self)
        except Exception as e:
            print(f"Failed to send review notification email: {e}")


class AutoBid(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    auction = models.ForeignKey("Auction", on_delete=models.CASCADE)
    max_bid = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f"{self.user.username}'s auto-bid for {self.auction.title}"


class AuditTrail(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    action = models.CharField(max_length=100)
    details = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.timestamp} - {self.user}: {self.action}"


class Payment(models.Model):
    PAYMENT_STATUS_CHOICES = [
        ("pending", "Pending"),
        ("processing", "Processing"),
        ("completed", "Completed"),
        ("failed", "Failed"),
        ("refunded", "Refunded"),
        ("cancelled", "Cancelled"),
        ("timeout", "Payment Timeout"),
    ]

    PAYMENT_METHOD_CHOICES = [
        ("stripe", "Stripe"),
        ("card", "Credit/Debit Card"),
        ("manual", "Manual"),
    ]

    CURRENCY_CHOICES = [
        ("USD", "US Dollar"),
        ("EUR", "Euro"),
        ("GBP", "British Pound"),
        ("CAD", "Canadian Dollar"),
        ("AUD", "Australian Dollar"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    auction = models.ForeignKey(Auction, on_delete=models.CASCADE)
    amount = models.DecimalField(
        max_digits=12, decimal_places=2
    )  # Supports multiple currencies including USD
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default="USD")
    payment_status = models.CharField(
        max_length=20, choices=PAYMENT_STATUS_CHOICES, default="pending"
    )
    payment_method = models.CharField(
        max_length=20, choices=PAYMENT_METHOD_CHOICES, default="stripe"
    )
    transaction_id = models.CharField(max_length=255, blank=True)
    gateway_payment_id = models.CharField(
        max_length=255, blank=True
    )  # Gateway specific ID
    gateway_order_id = models.CharField(max_length=255, blank=True)  # Gateway order ID
    gateway_signature = models.CharField(max_length=500, blank=True)  # For verification
    payment_date = models.DateTimeField(null=True, blank=True)
    failure_reason = models.TextField(blank=True)

    # Payment timeout tracking
    payment_deadline = models.DateTimeField(null=True, blank=True)
    reminder_sent = models.BooleanField(default=False)
    timeout_notification_sent = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Payment by {self.user.username} for {self.auction.title} - ${self.amount} ({self.payment_status})"

    @property
    def formatted_amount(self):
        """Return formatted amount with currency symbol"""
        if self.currency == "USD":
            return f"${self.amount:,.2f}"
        elif self.currency == "EUR":
            return f"€{self.amount:,.2f}"
        elif self.currency == "GBP":
            return f"£{self.amount:,.2f}"
        elif self.currency == "CAD":
            return f"C${self.amount:,.2f}"
        elif self.currency == "AUD":
            return f"A${self.amount:,.2f}"
        else:
            return f"{self.amount:,.2f} {self.currency}"

    def is_overdue(self):
        """Check if payment is overdue"""
        if self.payment_deadline:
            return timezone.now() > self.payment_deadline
        return False

    def mark_as_timeout(self):
        """Mark payment as timeout"""
        self.payment_status = "timeout"
        self.timeout_notification_sent = True
        self.save()


class ContactMessage(models.Model):
    name = models.CharField(max_length=100)
    email = models.EmailField()
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return (
            f"{self.name} ({self.email}) - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
        )


# Enhanced Models for Advanced Features


class UserProfile(models.Model):
    """Extended user profile with additional information"""

    # User role choices
    ROLE_CHOICES = [
        ('bidder', 'Bidder'),
        ('seller', 'Seller'),
        ('both', 'Both (Seller & Bidder)'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="profile")
    user_role = models.CharField(max_length=10, choices=ROLE_CHOICES, default='bidder')
    phone_number = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    city = models.CharField(max_length=100, blank=True)
    country = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    avatar = models.URLField(blank=True)
    bio = models.TextField(blank=True, max_length=500)
    verified = models.BooleanField(default=False)
    rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    total_sales = models.PositiveIntegerField(default=0)
    total_purchases = models.PositiveIntegerField(default=0)
    preferred_language = models.CharField(max_length=10, default="en")
    preferred_currency = models.CharField(max_length=3, default="USD")
    email_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s Profile"

    def can_create_auctions(self):
        """Check if user can create auctions based on their role"""
        return self.user_role in ['seller', 'both']

    def can_bid_on_auctions(self):
        """Check if user can bid on auctions based on their role"""
        return self.user_role in ['bidder', 'both']


class Category(models.Model):
    """Enhanced category model with hierarchy support"""

    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)
    parent = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="subcategories",
    )
    image = models.URLField(blank=True)
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Categories"
        ordering = ["sort_order", "name"]

    def __str__(self):
        return self.name


class Watchlist(models.Model):
    """User's watchlist for tracking auctions"""

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    auction = models.ForeignKey(Auction, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ["user", "auction"]

    def __str__(self):
        return f"{self.user.username} watching {self.auction.title}"


class BidHistory(models.Model):
    """Detailed bid history for analytics"""

    auction = models.ForeignKey(
        Auction, on_delete=models.CASCADE, related_name="bid_history"
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    bid_amount = models.DecimalField(max_digits=10, decimal_places=2)
    previous_bid = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    bid_type = models.CharField(
        max_length=20,
        choices=[
            ("manual", "Manual Bid"),
            ("auto", "Auto Bid"),
            ("proxy", "Proxy Bid"),
        ],
        default="manual",
    )
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} bid ${self.bid_amount} on {self.auction.title}"


class FraudDetection(models.Model):
    """Fraud detection and security monitoring"""

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    auction = models.ForeignKey(
        Auction, on_delete=models.CASCADE, null=True, blank=True
    )
    fraud_type = models.CharField(
        max_length=50,
        choices=[
            ("suspicious_bidding", "Suspicious Bidding Pattern"),
            ("fake_listing", "Fake Listing"),
            ("payment_fraud", "Payment Fraud"),
            ("account_takeover", "Account Takeover"),
            ("bot_activity", "Bot Activity"),
        ],
    )
    risk_score = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    details = models.JSONField(default=dict)
    status = models.CharField(
        max_length=20,
        choices=[
            ("pending", "Pending Review"),
            ("resolved", "Resolved"),
            ("confirmed", "Confirmed Fraud"),
            ("false_positive", "False Positive"),
        ],
        default="pending",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="resolved_fraud_cases",
    )

    def __str__(self):
        return f"Fraud Alert: {self.fraud_type} - {self.user.username}"


class Analytics(models.Model):
    """System analytics and metrics"""

    metric_name = models.CharField(max_length=100)
    metric_value = models.DecimalField(max_digits=15, decimal_places=2)
    metric_type = models.CharField(
        max_length=50,
        choices=[
            ("user_engagement", "User Engagement"),
            ("auction_performance", "Auction Performance"),
            ("revenue", "Revenue"),
            ("system_health", "System Health"),
        ],
    )
    date = models.DateField()
    metadata = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ["metric_name", "date"]

    def __str__(self):
        return f"{self.metric_name}: {self.metric_value} ({self.date})"


# AI Price Prediction Models
class PricePrediction(models.Model):
    """AI-powered price predictions for auctions"""

    auction = models.OneToOneField(
        Auction, on_delete=models.CASCADE, related_name="price_prediction"
    )
    predicted_price = models.DecimalField(max_digits=10, decimal_places=2)
    confidence_score = models.FloatField()  # 0.0 to 1.0
    model_version = models.CharField(max_length=20, default="v1.0")
    features_used = models.JSONField(default=dict)  # Store features used for prediction
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"Price prediction for {self.auction.title}: ${self.predicted_price} (confidence: {self.confidence_score:.2f})"


class PricePredictionHistory(models.Model):
    """Historical price predictions for model improvement"""

    auction = models.ForeignKey(Auction, on_delete=models.CASCADE)
    predicted_price = models.DecimalField(max_digits=10, decimal_places=2)
    actual_price = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    confidence_score = models.FloatField()
    accuracy_score = models.FloatField(
        null=True, blank=True
    )  # Calculated after auction ends
    model_version = models.CharField(max_length=20)
    features_used = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)

    def calculate_accuracy(self):
        """Calculate prediction accuracy after auction ends"""
        if self.actual_price and self.predicted_price:
            error = abs(float(self.actual_price) - float(self.predicted_price))
            relative_error = error / float(self.actual_price)
            self.accuracy_score = max(0, 1 - relative_error)
            self.save()
            return self.accuracy_score
        return None

    def __str__(self):
        return f"Prediction history for {self.auction.title}: ${self.predicted_price} vs ${self.actual_price}"


# Real-time Chat System Models
class ChatRoom(models.Model):
    """Chat rooms for auction discussions"""

    auction = models.OneToOneField(
        Auction, on_delete=models.CASCADE, related_name="chat_room"
    )
    participants = models.ManyToManyField(User, related_name="chat_rooms", blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-updated_at"]

    def __str__(self):
        return f"Chat room for {self.auction.title}"

    def add_participant(self, user):
        """Add a user to the chat room"""
        self.participants.add(user)
        self.updated_at = timezone.now()
        self.save()

    def get_recent_messages(self, limit=50):
        """Get recent messages from this chat room"""
        return self.messages.order_by("-timestamp")[:limit]


class ChatMessage(models.Model):
    """Individual chat messages"""

    room = models.ForeignKey(
        ChatRoom, on_delete=models.CASCADE, related_name="messages"
    )
    sender = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="sent_messages", null=True, blank=True
    )
    message = models.TextField(max_length=1000)
    message_type = models.CharField(
        max_length=20,
        choices=[
            ("text", "Text Message"),
            ("system", "System Message"),
            ("bid_alert", "Bid Alert"),
            ("auction_update", "Auction Update"),
            ("ai_response", "AI Response"),
        ],
        default="text",
    )
    timestamp = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    edited_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ["timestamp"]

    def __str__(self):
        sender_name = self.sender.username if self.sender else "AI Assistant"
        return f"{sender_name}: {self.message[:50]}..."

    def mark_as_read(self):
        """Mark message as read"""
        self.is_read = True
        self.save()


class PrivateMessage(models.Model):
    """Private messages between users"""

    sender = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="sent_private_messages"
    )
    recipient = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="received_private_messages"
    )
    auction = models.ForeignKey(
        Auction, on_delete=models.CASCADE, null=True, blank=True
    )  # Optional auction context
    subject = models.CharField(max_length=200, blank=True)
    message = models.TextField(max_length=2000)
    timestamp = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    parent_message = models.ForeignKey(
        "self", on_delete=models.CASCADE, null=True, blank=True, related_name="replies"
    )

    class Meta:
        ordering = ["-timestamp"]

    def __str__(self):
        return f"From {self.sender.username} to {self.recipient.username}: {self.subject or 'No subject'}"

    def mark_as_read(self):
        """Mark private message as read"""
        self.is_read = True
        self.save()
