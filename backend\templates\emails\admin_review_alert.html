<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Review Alert - Admin Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 0 0 8px 8px;
        }
        .alert-box {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-left: 4px solid #dc3545;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .review-details {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            background: #dc3545;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 14px;
        }
        .rating {
            font-size: 20px;
            color: #ffc107;
        }
        .reported-count {
            background: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .stats-table th,
        .stats-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .stats-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚨 Review Alert</h1>
        <p>Admin attention required for reported review</p>
    </div>
    
    <div class="content">
        <div class="alert-box">
            <h3 style="margin-top: 0; color: #dc3545;">⚠️ Review Requires Attention</h3>
            <p>A review has been reported multiple times and needs administrative review.</p>
        </div>
        
        <div class="review-details">
            <h3>Review Details</h3>
            
            <table class="stats-table">
                <tr>
                    <th>Auction:</th>
                    <td>{{ auction_title }}</td>
                </tr>
                <tr>
                    <th>Reviewer:</th>
                    <td>{{ reviewer_name }}</td>
                </tr>
                <tr>
                    <th>Reviewee:</th>
                    <td>{{ reviewee_name }}</td>
                </tr>
                <tr>
                    <th>Rating:</th>
                    <td>
                        <span class="rating">{{ rating_stars }}</span>
                        ({{ rating }}/5)
                    </td>
                </tr>
                <tr>
                    <th>Review Type:</th>
                    <td>{{ review_type }}</td>
                </tr>
                <tr>
                    <th>Verified:</th>
                    <td>{{ is_verified|yesno:"Yes,No" }}</td>
                </tr>
                <tr>
                    <th>Reports:</th>
                    <td><span class="reported-count">{{ reported_count }} reports</span></td>
                </tr>
                <tr>
                    <th>Helpful Votes:</th>
                    <td>{{ helpful_count }}</td>
                </tr>
                <tr>
                    <th>Created:</th>
                    <td>{{ created_at|date:"M d, Y H:i" }}</td>
                </tr>
            </table>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0;">
                <strong>Review Comment:</strong><br>
                "{{ comment }}"
            </div>
        </div>
        
        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #856404;">📋 Recommended Actions:</h4>
            <ul style="color: #856404; margin-bottom: 0;">
                <li>Review the comment for inappropriate content</li>
                <li>Check the reviewer's history for patterns</li>
                <li>Consider contacting both parties if needed</li>
                <li>Take appropriate moderation action</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ site_url }}/admin/auction/review/{{ review_id }}/change/" class="btn">Review in Admin Panel</a>
            <a href="{{ site_url }}/admin/auction/review/" class="btn btn-secondary">All Reviews</a>
        </div>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
        
        <div style="background: #d1ecf1; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;">
            <h4 style="margin-top: 0; color: #0c5460;">ℹ️ System Information:</h4>
            <ul style="color: #0c5460; margin-bottom: 0;">
                <li><strong>Alert Trigger:</strong> {{ reported_count }} or more reports</li>
                <li><strong>Auto-generated:</strong> {{ timestamp|date:"M d, Y H:i" }}</li>
                <li><strong>Review ID:</strong> #{{ review_id }}</li>
                <li><strong>Auction ID:</strong> #{{ auction_id }}</li>
            </ul>
        </div>
    </div>
    
    <div class="footer">
        <p><strong>Online Auction System</strong><br>
        Automated Admin Alert System</p>
        
        <p style="font-size: 12px; color: #999;">
            This alert was automatically generated when a review received multiple reports.<br>
            Please take appropriate action to maintain community standards.
        </p>
    </div>
</body>
</html>
