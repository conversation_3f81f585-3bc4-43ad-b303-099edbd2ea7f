#!/usr/bin/env python3
"""
Simple Fraud Detection Test
Quick test to verify fraud detection system is working
"""

import os
import sys
import django
import requests
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from django.contrib.auth.models import User
from auction.models import FraudDetection, Auction
from decimal import Decimal
from django.utils import timezone
from datetime import timedelta

def test_fraud_detection_model():
    """Test FraudDetection model basic functionality"""
    print("🔍 Testing FraudDetection Model...")
    print("-" * 50)
    
    try:
        # Create a test user
        test_user, created = User.objects.get_or_create(
            username='fraud_test_user',
            defaults={
                'email': '<EMAIL>',
                'password': 'testpass123'
            }
        )
        
        # Create a fraud detection record
        fraud_detection = FraudDetection.objects.create(
            user=test_user,
            fraud_type='suspicious_bidding',
            risk_score=85,
            details={
                'test_data': True,
                'rapid_bids': True,
                'bid_count': 10
            },
            status='pending'
        )
        
        print(f"✅ Created fraud detection record: ID {fraud_detection.id}")
        print(f"   User: {fraud_detection.user.username}")
        print(f"   Type: {fraud_detection.fraud_type}")
        print(f"   Risk Score: {fraud_detection.risk_score}")
        print(f"   Status: {fraud_detection.status}")
        print(f"   Details: {fraud_detection.details}")
        
        # Test string representation
        print(f"   String repr: {str(fraud_detection)}")
        
        # Test fraud type choices
        fraud_types = ['suspicious_bidding', 'fake_listing', 'payment_fraud', 'account_takeover', 'bot_activity']
        print(f"✅ Available fraud types: {fraud_types}")
        
        # Test status choices
        statuses = ['pending', 'resolved', 'confirmed', 'false_positive']
        print(f"✅ Available statuses: {statuses}")
        
        # Clean up
        fraud_detection.delete()
        if created:
            test_user.delete()
            
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False

def test_fraud_detection_api():
    """Test FraudDetection API endpoints"""
    print("\n🌐 Testing FraudDetection API...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:8000/api"
    
    try:
        # Test GET fraud detection list
        response = requests.get(f"{base_url}/fraud-detection/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            count = len(data.get('results', []))
            print(f"✅ GET /fraud-detection/: {count} records found")
        else:
            print(f"❌ GET /fraud-detection/ failed: {response.status_code}")
            return False
        
        # Test filtering by status
        response = requests.get(f"{base_url}/fraud-detection/?status=pending", timeout=10)
        if response.status_code == 200:
            data = response.json()
            count = len(data.get('results', []))
            print(f"✅ Filter by status=pending: {count} records")
        else:
            print(f"❌ Filter by status failed: {response.status_code}")
        
        # Test filtering by fraud type
        response = requests.get(f"{base_url}/fraud-detection/?fraud_type=suspicious_bidding", timeout=10)
        if response.status_code == 200:
            data = response.json()
            count = len(data.get('results', []))
            print(f"✅ Filter by fraud_type: {count} records")
        else:
            print(f"❌ Filter by fraud_type failed: {response.status_code}")
        
        # Test ordering by risk score
        response = requests.get(f"{base_url}/fraud-detection/?ordering=-risk_score", timeout=10)
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            if results:
                highest_risk = results[0].get('risk_score', 0)
                print(f"✅ Order by risk_score: Highest risk = {highest_risk}")
            else:
                print("✅ Order by risk_score: No records to order")
        else:
            print(f"❌ Order by risk_score failed: {response.status_code}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API connection failed: {e}")
        print("💡 Make sure Django server is running: python manage.py runserver")
        return False

def test_fraud_detection_scenarios():
    """Test different fraud detection scenarios"""
    print("\n🎯 Testing Fraud Detection Scenarios...")
    print("-" * 50)
    
    try:
        # Create test user
        test_user, created = User.objects.get_or_create(
            username='scenario_test_user',
            defaults={
                'email': '<EMAIL>',
                'password': 'testpass123'
            }
        )
        
        # Scenario 1: Suspicious Bidding
        fraud1 = FraudDetection.objects.create(
            user=test_user,
            fraud_type='suspicious_bidding',
            risk_score=85,
            details={
                'rapid_bids': True,
                'bid_count': 15,
                'time_span_minutes': 5,
                'pattern': 'rapid_consecutive'
            }
        )
        print(f"✅ Scenario 1 - Suspicious Bidding: Risk {fraud1.risk_score}")
        
        # Scenario 2: Bot Activity
        fraud2 = FraudDetection.objects.create(
            user=test_user,
            fraud_type='bot_activity',
            risk_score=92,
            details={
                'automated_bidding': True,
                'consistent_timing': True,
                'captcha_failures': 5,
                'user_agent_suspicious': True
            }
        )
        print(f"✅ Scenario 2 - Bot Activity: Risk {fraud2.risk_score}")
        
        # Scenario 3: Fake Listing
        fraud3 = FraudDetection.objects.create(
            user=test_user,
            fraud_type='fake_listing',
            risk_score=78,
            details={
                'price_too_low': True,
                'suspicious_images': True,
                'copied_description': True,
                'new_seller': True
            }
        )
        print(f"✅ Scenario 3 - Fake Listing: Risk {fraud3.risk_score}")
        
        # Test resolution workflow
        fraud1.status = 'resolved'
        fraud1.resolved_at = timezone.now()
        fraud1.save()
        print(f"✅ Resolution workflow: {fraud1.fraud_type} marked as {fraud1.status}")
        
        # Clean up
        fraud1.delete()
        fraud2.delete()
        fraud3.delete()
        if created:
            test_user.delete()
        
        return True
        
    except Exception as e:
        print(f"❌ Scenario test failed: {e}")
        return False

def test_fraud_detection_integration():
    """Test fraud detection integration features"""
    print("\n🔗 Testing Integration Features...")
    print("-" * 50)
    
    try:
        # Test fraud detection count by status
        total_fraud = FraudDetection.objects.count()
        pending_fraud = FraudDetection.objects.filter(status='pending').count()
        confirmed_fraud = FraudDetection.objects.filter(status='confirmed').count()
        resolved_fraud = FraudDetection.objects.filter(status='resolved').count()
        
        print(f"✅ Total fraud detections: {total_fraud}")
        print(f"✅ Pending review: {pending_fraud}")
        print(f"✅ Confirmed fraud: {confirmed_fraud}")
        print(f"✅ Resolved cases: {resolved_fraud}")
        
        # Test fraud detection by type
        fraud_types = ['suspicious_bidding', 'fake_listing', 'payment_fraud', 'account_takeover', 'bot_activity']
        for fraud_type in fraud_types:
            count = FraudDetection.objects.filter(fraud_type=fraud_type).count()
            print(f"   {fraud_type}: {count} cases")
        
        # Test high-risk fraud detection
        high_risk_count = FraudDetection.objects.filter(risk_score__gte=90).count()
        print(f"✅ High-risk cases (90+): {high_risk_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Run all fraud detection tests"""
    print("🔍 FRAUD DETECTION SYSTEM - SIMPLE TEST SUITE")
    print("=" * 70)
    print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run tests
    model_ok = test_fraud_detection_model()
    api_ok = test_fraud_detection_api()
    scenarios_ok = test_fraud_detection_scenarios()
    integration_ok = test_fraud_detection_integration()
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 TEST SUMMARY")
    print("=" * 70)
    
    tests = [
        ("Model Tests", model_ok),
        ("API Tests", api_ok),
        ("Scenario Tests", scenarios_ok),
        ("Integration Tests", integration_ok)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} test categories passed")
    
    if passed == total:
        print("\n🎉 ALL FRAUD DETECTION TESTS PASSED!")
        print("✅ Fraud detection system is working correctly")
        print("✅ API endpoints are functional")
        print("✅ Model validation is working")
        print("✅ Business scenarios are supported")
        print("\n🚀 Fraud detection system is ready for use!")
    else:
        print("\n⚠️  SOME TESTS FAILED")
        print("💡 Check the output above for specific issues")
        print("💡 Ensure Django server is running for API tests")

if __name__ == "__main__":
    main()
