"""
URL configuration for auctions app
"""

from django.urls import path, include
from .views import auction_views, reporting_views

app_name = 'auctions'

# Auction-related URLs
auction_patterns = [
    path('trending/', auction_views.trending_auctions, name='trending'),
    path('ending-soon/', auction_views.ending_soon_auctions, name='ending_soon'),
    path('popular-categories/', auction_views.popular_categories, name='popular_categories'),
    path('user-stats/', auction_views.user_auction_stats, name='user_stats'),
    path('<int:auction_id>/extend/', auction_views.extend_auction, name='extend_auction'),
    path('<int:auction_id>/analytics/', auction_views.auction_analytics, name='auction_analytics'),
]

# Reporting and admin URLs
reporting_patterns = [
    path('dashboard-stats/', reporting_views.admin_dashboard_stats, name='dashboard_stats'),
    path('revenue-report/', reporting_views.revenue_report, name='revenue_report'),
    path('user-activity/', reporting_views.user_activity_report, name='user_activity'),
    path('export-data/', reporting_views.export_user_data, name='export_data'),
]

urlpatterns = [
    # Auction endpoints
    path('auctions/', include(auction_patterns)),
    
    # Admin/reporting endpoints
    path('admin/', include(reporting_patterns)),
    
    # User data export
    path('user/', include([
        path('export/', reporting_views.export_user_data, name='user_export'),
    ])),
]
