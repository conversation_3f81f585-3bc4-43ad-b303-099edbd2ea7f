"""
Enhanced Notification Services with Smart Delivery
"""

import json
from datetime import timed<PERSON><PERSON>

from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.html import strip_tags

from .models import Auction, Bid, Notification, User


class EnhancedNotificationService:
    """Enhanced notification service with smart delivery and templates"""

    def __init__(self):
        self.notification_types = {
            "bid_placed": {
                "title": "New Bid Placed",
                "priority": "medium",
                "email_template": "emails/bid_placed.html",
            },
            "outbid": {
                "title": "You've Been Outbid",
                "priority": "high",
                "email_template": "emails/outbid.html",
            },
            "auction_won": {
                "title": "Auction Won!",
                "priority": "high",
                "email_template": "emails/auction_won.html",
            },
            "auction_ending": {
                "title": "Auction Ending Soon",
                "priority": "medium",
                "email_template": "emails/auction_ending.html",
            },
            "payment_reminder": {
                "title": "Payment Reminder",
                "priority": "high",
                "email_template": "emails/payment_reminder.html",
            },
            "auction_approved": {
                "title": "Auction Approved",
                "priority": "medium",
                "email_template": "emails/auction_approved.html",
            },
            "auction_rejected": {
                "title": "Auction Rejected",
                "priority": "medium",
                "email_template": "emails/auction_rejected.html",
            },
            "new_message": {
                "title": "New Message",
                "priority": "low",
                "email_template": "emails/new_message.html",
            },
            "price_alert": {
                "title": "Price Alert",
                "priority": "medium",
                "email_template": "emails/price_alert.html",
            },
            "weekly_digest": {
                "title": "Weekly Auction Digest",
                "priority": "low",
                "email_template": "emails/weekly_digest.html",
            },
        }

    def create_notification(
        self,
        user,
        notification_type,
        title,
        message,
        auction=None,
        metadata=None,
        send_email=True,
    ):
        """Create a new notification"""
        try:
            # Create notification record
            notification = Notification.objects.create(
                user=user,
                title=title,
                message=message,
                notification_type=notification_type,
                auction=auction,
                metadata=metadata or {},
            )

            # Send email if enabled
            if send_email and self._should_send_email(user, notification_type):
                self._send_email_notification(notification)

            # Send push notification if enabled
            if self._should_send_push(user, notification_type):
                self._send_push_notification(notification)

            return notification

        except Exception as e:
            print(f"Notification creation error: {e}")
            return None

    def send_bid_notification(self, bid):
        """Send notification when a bid is placed"""
        try:
            auction = bid.auction
            bidder = bid.user

            # Notify auction owner
            if auction.owner != bidder:
                self.create_notification(
                    user=auction.owner,
                    notification_type="bid_placed",
                    title=f"New bid on {auction.title}",
                    message=f"{bidder.username} placed a bid of ₹{bid.amount}",
                    auction=auction,
                    metadata={"bid_amount": str(bid.amount), "bidder": bidder.username},
                )

            # Notify previous highest bidder (outbid notification)
            previous_bids = (
                Bid.objects.filter(auction=auction)
                .exclude(id=bid.id)
                .order_by("-amount")
            )

            if previous_bids.exists():
                previous_bidder = previous_bids.first().user
                if previous_bidder != bidder:
                    self.create_notification(
                        user=previous_bidder,
                        notification_type="outbid",
                        title=f"Outbid on {auction.title}",
                        message=f"Your bid has been exceeded. New highest bid: ₹{bid.amount}",
                        auction=auction,
                        metadata={"new_bid_amount": str(bid.amount)},
                    )

        except Exception as e:
            print(f"Bid notification error: {e}")

    def send_auction_ending_notifications(self):
        """Send notifications for auctions ending soon"""
        try:
            # Get auctions ending in next 24 hours
            tomorrow = timezone.now() + timedelta(hours=24)
            ending_auctions = Auction.objects.filter(
                end_time__lte=tomorrow, end_time__gt=timezone.now(), approved=True
            )

            for auction in ending_auctions:
                # Get all bidders
                bidders = User.objects.filter(bids__auction=auction).distinct()

                for bidder in bidders:
                    # Check if notification already sent
                    existing = Notification.objects.filter(
                        user=bidder,
                        auction=auction,
                        notification_type="auction_ending",
                        created_at__gte=timezone.now() - timedelta(hours=25),
                    ).exists()

                    if not existing:
                        time_left = auction.end_time - timezone.now()
                        hours_left = int(time_left.total_seconds() / 3600)

                        self.create_notification(
                            user=bidder,
                            notification_type="auction_ending",
                            title=f"Auction ending soon: {auction.title}",
                            message=f"Only {hours_left} hours left to bid!",
                            auction=auction,
                            metadata={"hours_left": hours_left},
                        )

        except Exception as e:
            print(f"Auction ending notifications error: {e}")

    def send_auction_won_notification(self, auction):
        """Send notification when auction ends"""
        try:
            if auction.is_closed and auction.bids.exists():
                winning_bid = auction.bids.order_by("-amount").first()
                winner = winning_bid.user

                self.create_notification(
                    user=winner,
                    notification_type="auction_won",
                    title=f"Congratulations! You won {auction.title}",
                    message=f"You won the auction with a bid of ₹{winning_bid.amount}. Please complete payment.",
                    auction=auction,
                    metadata={"winning_amount": str(winning_bid.amount)},
                )

                # Notify auction owner
                self.create_notification(
                    user=auction.owner,
                    notification_type="auction_won",
                    title=f"Your auction {auction.title} has ended",
                    message=f"Won by {winner.username} for ₹{winning_bid.amount}",
                    auction=auction,
                    metadata={
                        "winner": winner.username,
                        "amount": str(winning_bid.amount),
                    },
                )

        except Exception as e:
            print(f"Auction won notification error: {e}")

    def send_payment_reminder(self, auction, winner):
        """Send payment reminder notification"""
        try:
            self.create_notification(
                user=winner,
                notification_type="payment_reminder",
                title=f"Payment reminder for {auction.title}",
                message="Please complete your payment to finalize the purchase.",
                auction=auction,
                metadata={"reminder_count": 1},
            )

        except Exception as e:
            print(f"Payment reminder error: {e}")

    def send_auction_status_notification(self, auction, status):
        """Send notification when auction status changes"""
        try:
            if status == "approved":
                self.create_notification(
                    user=auction.owner,
                    notification_type="auction_approved",
                    title=f"Auction approved: {auction.title}",
                    message="Your auction has been approved and is now live!",
                    auction=auction,
                )
            elif status == "rejected":
                self.create_notification(
                    user=auction.owner,
                    notification_type="auction_rejected",
                    title=f"Auction rejected: {auction.title}",
                    message="Your auction was rejected. Please review and resubmit.",
                    auction=auction,
                )

        except Exception as e:
            print(f"Auction status notification error: {e}")

    def send_price_alert(self, user, auction, target_price):
        """Send price alert when auction reaches target price"""
        try:
            self.create_notification(
                user=user,
                notification_type="price_alert",
                title=f"Price alert: {auction.title}",
                message=f"The auction has reached your target price of ₹{target_price}",
                auction=auction,
                metadata={"target_price": str(target_price)},
            )

        except Exception as e:
            print(f"Price alert error: {e}")

    def send_weekly_digest(self, user):
        """Send weekly digest of auction activity"""
        try:
            # Get user's activity from past week
            week_ago = timezone.now() - timedelta(days=7)

            # User's bids
            user_bids = Bid.objects.filter(user=user, created_at__gte=week_ago).count()

            # Auctions user is watching
            watching_count = user.watchlist.filter(
                auction__end_time__gt=timezone.now()
            ).count()

            # New auctions in user's categories
            user_categories = list(
                set(
                    Bid.objects.filter(user=user).values_list(
                        "auction__category", flat=True
                    )
                )
            )

            new_auctions = Auction.objects.filter(
                category__in=user_categories, created_at__gte=week_ago, approved=True
            ).count()

            if user_bids > 0 or watching_count > 0 or new_auctions > 0:
                self.create_notification(
                    user=user,
                    notification_type="weekly_digest",
                    title="Your weekly auction digest",
                    message=f"You placed {user_bids} bids, watching {watching_count} auctions, {new_auctions} new auctions in your categories",
                    metadata={
                        "bids_count": user_bids,
                        "watching_count": watching_count,
                        "new_auctions": new_auctions,
                    },
                )

        except Exception as e:
            print(f"Weekly digest error: {e}")

    def _should_send_email(self, user, notification_type):
        """Check if email should be sent for this notification type"""
        try:
            # Check user preferences (if implemented)
            # For now, send emails for high priority notifications
            priority = self.notification_types.get(notification_type, {}).get(
                "priority", "low"
            )
            return priority in ["high", "medium"]

        except:
            return False

    def _should_send_push(self, user, notification_type):
        """Check if push notification should be sent"""
        try:
            # For now, send push for high priority notifications
            priority = self.notification_types.get(notification_type, {}).get(
                "priority", "low"
            )
            return priority == "high"

        except:
            return False

    def _send_email_notification(self, notification):
        """Send email notification"""
        try:
            notification_config = self.notification_types.get(
                notification.notification_type, {}
            )

            # Simple email for now (can be enhanced with templates)
            subject = notification.title
            message = notification.message

            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[notification.user.email],
                fail_silently=True,
            )

        except Exception as e:
            print(f"Email sending error: {e}")

    def _send_push_notification(self, notification):
        """Send push notification (placeholder for future implementation)"""
        try:
            # Placeholder for push notification implementation
            # Could integrate with Firebase, OneSignal, etc.
            print(
                f"Push notification: {notification.title} to {notification.user.username}"
            )

        except Exception as e:
            print(f"Push notification error: {e}")

    def get_user_notifications(self, user, limit=20, unread_only=False):
        """Get user's notifications"""
        try:
            queryset = Notification.objects.filter(user=user)

            if unread_only:
                queryset = queryset.filter(is_read=False)

            return queryset.order_by("-created_at")[:limit]

        except Exception as e:
            print(f"Get notifications error: {e}")
            return []

    def mark_notifications_read(self, user, notification_ids=None):
        """Mark notifications as read"""
        try:
            queryset = Notification.objects.filter(user=user, is_read=False)

            if notification_ids:
                queryset = queryset.filter(id__in=notification_ids)

            return queryset.update(is_read=True)

        except Exception as e:
            print(f"Mark read error: {e}")
            return 0


# Global instance
enhanced_notification_service = EnhancedNotificationService()
