import re

from django.core.management.base import BaseCommand

from auction.models import Auction


class Command(BaseCommand):
    help = "Clean up demo/test images from auction items"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be cleaned without making changes",
        )
        parser.add_argument(
            "--pattern",
            type=str,
            default="demo|test|placeholder|example|sample|unsplash|freepik|bing|pinimg",
            help="Regex pattern to match demo image URLs",
        )

    def handle(self, *args, **options):
        dry_run = options["dry_run"]
        pattern = options["pattern"]

        self.stdout.write(f"🔍 Searching for demo images with pattern: {pattern}")

        # Compile regex pattern
        regex = re.compile(pattern, re.IGNORECASE)

        # Find auctions with demo images
        auctions_to_clean = []

        for auction in Auction.objects.all():
            needs_cleaning = False
            changes = []

            # Check main image
            if auction.image and regex.search(auction.image):
                needs_cleaning = True
                changes.append(f"Main image: {auction.image}")

            # Check additional images
            if auction.additional_images:
                demo_additional = [
                    img for img in auction.additional_images if regex.search(img)
                ]
                if demo_additional:
                    needs_cleaning = True
                    changes.append(f"Additional images: {demo_additional}")

            if needs_cleaning:
                auctions_to_clean.append({"auction": auction, "changes": changes})

        if not auctions_to_clean:
            self.stdout.write(
                self.style.SUCCESS("✅ No demo images found! All auctions are clean.")
            )
            return

        self.stdout.write(
            f"🎯 Found {len(auctions_to_clean)} auctions with demo images:"
        )

        for item in auctions_to_clean:
            auction = item["auction"]
            changes = item["changes"]

            self.stdout.write(f"\n📦 Auction ID {auction.id}: {auction.title}")
            for change in changes:
                self.stdout.write(f"   - {change}")

        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    "\n🔍 DRY RUN: No changes made. Use without --dry-run to clean images."
                )
            )
            return

        # Confirm before making changes
        confirm = input(f"\n❓ Clean {len(auctions_to_clean)} auctions? (y/N): ")
        if confirm.lower() != "y":
            self.stdout.write("❌ Operation cancelled.")
            return

        # Clean the images
        cleaned_count = 0

        for item in auctions_to_clean:
            auction = item["auction"]

            # Clean main image
            if auction.image and regex.search(auction.image):
                auction.image = ""

            # Clean additional images
            if auction.additional_images:
                auction.additional_images = [
                    img for img in auction.additional_images if not regex.search(img)
                ]

            auction.save()
            cleaned_count += 1

            self.stdout.write(f"✅ Cleaned auction ID {auction.id}")

        self.stdout.write(
            self.style.SUCCESS(f"\n🎉 Successfully cleaned {cleaned_count} auctions!")
        )
        self.stdout.write(
            "💡 Tip: You can now use the admin dashboard to add proper images."
        )
