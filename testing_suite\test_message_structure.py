#!/usr/bin/env python3
"""
Test message structure to understand the data format
"""

import requests
import json

def test_message_structure():
    """Check the exact structure of messages returned by API"""
    
    base_url = "http://127.0.0.1:8000/api"
    
    print("🔍 Testing Message Structure")
    print("=" * 50)
    
    # Get messages directly without auth to see structure
    try:
        # Get auction and room
        auctions_response = requests.get(f"{base_url}/auctions/")
        auction_id = auctions_response.json()['results'][0]['id']
        
        chat_response = requests.get(f"{base_url}/chat-rooms/?auction={auction_id}")
        room_id = chat_response.json()['results'][0]['id']
        
        print(f"📋 Using room {room_id}")
        
        # Get messages
        messages_response = requests.get(
            f"{base_url}/chat-messages/?room={room_id}&ordering=timestamp"
        )
        
        if messages_response.status_code == 200:
            data = messages_response.json()
            messages = data.get('results', [])
            
            print(f"✅ Retrieved {len(messages)} messages")
            print(f"📊 Response structure: {list(data.keys())}")
            
            if messages:
                print(f"\n📋 First message structure:")
                first_message = messages[0]
                for key, value in first_message.items():
                    print(f"   {key}: {value} ({type(value).__name__})")
                
                print(f"\n📋 Last message structure:")
                last_message = messages[-1]
                for key, value in last_message.items():
                    print(f"   {key}: {value} ({type(value).__name__})")
                
                print(f"\n📋 All message previews:")
                for i, msg in enumerate(messages):
                    sender = msg.get('sender_username', 'Unknown')
                    text = msg.get('message', 'No message')[:50]
                    msg_type = msg.get('message_type', 'unknown')
                    msg_id = msg.get('id', 'no-id')
                    print(f"   {i+1}. ID:{msg_id} [{msg_type}] {sender}: {text}...")
                    
            else:
                print("❌ No messages found")
                
        else:
            print(f"❌ Failed to get messages: {messages_response.status_code}")
            print(f"   Response: {messages_response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_message_structure()
