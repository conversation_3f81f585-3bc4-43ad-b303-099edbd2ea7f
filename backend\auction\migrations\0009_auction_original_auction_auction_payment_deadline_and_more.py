# Generated by Django 5.1.5 on 2025-05-28 05:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("auction", "0008_chatroom_chatmessage_priceprediction_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="auction",
            name="original_auction",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="auction.auction",
            ),
        ),
        migrations.AddField(
            model_name="auction",
            name="payment_deadline",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="auction",
            name="payment_reminder_sent",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="auction",
            name="payment_timeout_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="auction",
            name="re_auction_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="notification",
            name="auction",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="auction.auction",
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="notification_type",
            field=models.CharField(
                choices=[
                    ("bid", "New Bid"),
                    ("auction_end", "Auction Ended"),
                    ("payment", "Payment"),
                    ("payment_reminder", "Payment Reminder"),
                    ("payment_timeout", "Payment Timeout"),
                    ("re_auction", "Re-auction"),
                    ("general", "General"),
                ],
                default="general",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="title",
            field=models.CharField(default="Notification", max_length=200),
        ),
        migrations.AddField(
            model_name="payment",
            name="payment_deadline",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="reminder_sent",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="payment",
            name="timeout_notification_sent",
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name="payment",
            name="payment_status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("processing", "Processing"),
                    ("completed", "Completed"),
                    ("failed", "Failed"),
                    ("refunded", "Refunded"),
                    ("cancelled", "Cancelled"),
                    ("timeout", "Payment Timeout"),
                ],
                default="pending",
                max_length=20,
            ),
        ),
    ]
