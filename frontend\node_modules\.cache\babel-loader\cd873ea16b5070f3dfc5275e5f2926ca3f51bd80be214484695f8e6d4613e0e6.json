{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Online_Auction_System\\\\frontend\\\\src\\\\pages\\\\LandingPage.js\";\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { Container, Row, Col, Button, Card } from \"react-bootstrap\";\nimport { FaGavel, FaShieldAlt, FaRocket, FaUsers, FaClock, FaChartLine, FaHeart, FaSearch } from \"react-icons/fa\";\nimport axiosInstance from \"../api/axiosInstance\";\nimport globalApiManager from \"../utils/globalApiManager\";\nimport \"./LandingPage.css\";\n\n// Global singleton to prevent multiple API calls\nclass LandingDataManager {\n  constructor() {\n    this.data = null;\n    this.loading = false;\n    this.lastFetch = 0;\n    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes\n    this.subscribers = new Set();\n    this.requestPromise = null;\n    this.isDestroyed = false; // Flag to prevent calls after destruction\n    this.maxRetries = 3;\n    this.retryCount = 0;\n    this.backoffDelay = 1000; // Start with 1 second\n  }\n  subscribe(callback) {\n    this.subscribers.add(callback);\n    return () => this.subscribers.delete(callback);\n  }\n  notify() {\n    this.subscribers.forEach(callback => callback(this.data, this.loading));\n  }\n  async fetchData() {\n    // Prevent calls if manager is destroyed\n    if (this.isDestroyed) {\n      console.log(\"🚫 LandingDataManager is destroyed - preventing API call\");\n      return this.data;\n    }\n    const now = Date.now();\n\n    // Return cached data if still valid\n    if (this.data && now - this.lastFetch < this.cacheExpiry) {\n      console.log(\"📦 Using cached landing data (singleton)\");\n      this.retryCount = 0; // Reset retry count on successful cache hit\n      return this.data;\n    }\n\n    // If already fetching, return the existing promise\n    if (this.requestPromise) {\n      console.log(\"🔄 Landing data request already in progress (singleton)\");\n      return this.requestPromise;\n    }\n\n    // Check retry limit\n    if (this.retryCount >= this.maxRetries) {\n      console.log(\"🚫 Max retries reached - using cached data or defaults\");\n      return this.data;\n    }\n\n    // Start new request\n    this.loading = true;\n    this.notify();\n    this.requestPromise = this.performFetch();\n    try {\n      const result = await this.requestPromise;\n      this.retryCount = 0; // Reset on success\n      return result;\n    } catch (error) {\n      this.retryCount++;\n      console.error(`❌ Fetch attempt ${this.retryCount}/${this.maxRetries} failed:`, error);\n      throw error;\n    } finally {\n      this.requestPromise = null;\n    }\n  }\n  async performFetch() {\n    try {\n      // Double-check if destroyed before making request\n      if (this.isDestroyed) {\n        console.log(\"🚫 Manager destroyed during fetch - aborting\");\n        return this.data;\n      }\n      console.log(\"🌐 Fetching fresh landing data (singleton)...\");\n\n      // Add timeout and abort controller for better control\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout\n\n      const response = await axiosInstance.get(\"/landing/data/\", {\n        signal: controller.signal\n      });\n      clearTimeout(timeoutId);\n      if (response.data.success) {\n        this.data = response.data.data;\n        this.lastFetch = Date.now();\n        localStorage.setItem(\"landingData\", JSON.stringify(this.data));\n        localStorage.setItem(\"landingData_time\", this.lastFetch.toString());\n        console.log(\"✅ Landing data fetched and cached successfully (singleton)\");\n      } else {\n        console.warn(\"⚠️ API returned success=false:\", response.data);\n      }\n      return this.data;\n    } catch (error) {\n      if (error.name === \"AbortError\") {\n        console.log(\"⏰ Landing data request timed out\");\n      } else {\n        console.error(\"❌ Failed to fetch landing page data (singleton):\", error);\n      }\n\n      // Implement exponential backoff for retries\n      if (this.retryCount < this.maxRetries) {\n        const delay = this.backoffDelay * Math.pow(2, this.retryCount);\n        console.log(`⏳ Retrying in ${delay}ms...`);\n        await new Promise(resolve => setTimeout(resolve, delay));\n      }\n      throw error;\n    } finally {\n      this.loading = false;\n      this.notify();\n    }\n  }\n\n  // Initialize with cached data\n  initializeFromCache() {\n    if (this.isDestroyed) {\n      console.log(\"🚫 Manager destroyed - skipping cache initialization\");\n      return false;\n    }\n    try {\n      const cachedData = localStorage.getItem(\"landingData\");\n      const cachedTime = localStorage.getItem(\"landingData_time\");\n      if (cachedData && cachedTime) {\n        const age = Date.now() - parseInt(cachedTime);\n        if (age < this.cacheExpiry) {\n          this.data = JSON.parse(cachedData);\n          this.lastFetch = parseInt(cachedTime);\n          console.log(\"📦 Initialized with cached landing data (singleton)\");\n          return true;\n        } else {\n          console.log(\"📦 Cached data expired, will fetch fresh data\");\n        }\n      }\n    } catch (error) {\n      console.error(\"❌ Error loading cached data:\", error);\n    }\n    return false;\n  }\n\n  // Method to destroy the manager and prevent further API calls\n  destroy() {\n    console.log(\"🗑️ Destroying LandingDataManager\");\n    this.isDestroyed = true;\n    this.subscribers.clear();\n    this.requestPromise = null;\n    this.data = null;\n  }\n\n  // Method to reset the manager (for testing or recovery)\n  reset() {\n    console.log(\"🔄 Resetting LandingDataManager\");\n    this.isDestroyed = false;\n    this.retryCount = 0;\n    this.requestPromise = null;\n    this.loading = false;\n  }\n}\n\n// Global instance\nconst landingDataManager = new LandingDataManager();\nconst LandingPage = () => {\n  const [currentFeature, setCurrentFeature] = useState(0);\n  const [landingData, setLandingData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  const navigate = useNavigate();\n  const componentId = useRef(`landing-${Date.now()}-${Math.random()}`); // Unique component ID for debugging\n\n  // Check for dark mode from body class\n  useEffect(() => {\n    const checkDarkMode = () => {\n      const isDark = document.body.classList.contains(\"dark-mode-bg\");\n      setIsDarkMode(isDark);\n    };\n\n    // Initial check\n    checkDarkMode();\n\n    // Listen for class changes on body\n    const observer = new MutationObserver(checkDarkMode);\n    observer.observe(document.body, {\n      attributes: true,\n      attributeFilter: [\"class\"]\n    });\n    return () => observer.disconnect();\n  }, []);\n  const features = [{\n    icon: /*#__PURE__*/React.createElement(FaGavel, {\n      className: \"feature-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 13\n      }\n    }),\n    title: \"Live Auctions\",\n    description: \"Participate in real-time bidding with instant updates and notifications\"\n  }, {\n    icon: /*#__PURE__*/React.createElement(FaShieldAlt, {\n      className: \"feature-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 13\n      }\n    }),\n    title: \"Secure Payments\",\n    description: \"Safe and secure transactions with Stripe payment integration\"\n  }, {\n    icon: /*#__PURE__*/React.createElement(FaRocket, {\n      className: \"feature-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 13\n      }\n    }),\n    title: \"AI Price Prediction\",\n    description: \"Get intelligent price predictions powered by advanced AI algorithms\"\n  }, {\n    icon: /*#__PURE__*/React.createElement(FaUsers, {\n      className: \"feature-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 13\n      }\n    }),\n    title: \"Community Driven\",\n    description: \"Join thousands of buyers and sellers in our trusted marketplace\"\n  }, {\n    icon: /*#__PURE__*/React.createElement(FaClock, {\n      className: \"feature-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 13\n      }\n    }),\n    title: \"24/7 Availability\",\n    description: \"Bid anytime, anywhere with our always-available platform\"\n  }, {\n    icon: /*#__PURE__*/React.createElement(FaChartLine, {\n      className: \"feature-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 13\n      }\n    }),\n    title: \"Market Analytics\",\n    description: \"Track trends and make informed decisions with detailed analytics\"\n  }];\n\n  // Default stats (will be replaced by API data)\n  const defaultStats = [{\n    number: \"10,000+\",\n    label: \"Active Users\"\n  }, {\n    number: \"50,000+\",\n    label: \"Auctions Completed\"\n  }, {\n    number: \"99.9%\",\n    label: \"Uptime\"\n  }, {\n    number: \"$2M+\",\n    label: \"Total Sales\"\n  }];\n\n  // Use API data if available, otherwise use defaults\n  const stats = landingData !== null && landingData !== void 0 && landingData.stats ? [{\n    number: `${landingData.stats.total_users.toLocaleString()}+`,\n    label: \"Active Users\"\n  }, {\n    number: `${landingData.stats.total_auctions.toLocaleString()}+`,\n    label: \"Auctions Completed\"\n  }, {\n    number: landingData.stats.uptime,\n    label: \"Uptime\"\n  }, {\n    number: `$${(landingData.stats.total_sales / 1000000).toFixed(1)}M+`,\n    label: \"Total Sales\"\n  }] : defaultStats;\n\n  // Initialize and subscribe to landing data manager\n  useEffect(() => {\n    let isMounted = true; // Flag to prevent state updates after unmount\n\n    console.log(`🔧 LandingPage component ${componentId.current} mounting`);\n\n    // Initialize from cache first\n    const cacheInitialized = landingDataManager.initializeFromCache();\n\n    // Set initial state only if component is still mounted\n    if (isMounted) {\n      setLandingData(landingDataManager.data);\n      setLoading(landingDataManager.loading);\n    }\n\n    // Subscribe to updates\n    const unsubscribe = landingDataManager.subscribe((data, loading) => {\n      if (isMounted) {\n        console.log(`📡 LandingPage ${componentId.current} received update:`, {\n          data: !!data,\n          loading\n        });\n        setLandingData(data);\n        setLoading(loading);\n      }\n    });\n\n    // Fetch fresh data if needed (only if cache wasn't sufficient)\n    if (!cacheInitialized || !landingDataManager.data) {\n      landingDataManager.fetchData().catch(error => {\n        if (isMounted) {\n          console.error(`❌ LandingPage ${componentId.current} fetch error:`, error);\n          // Set loading to false on error to prevent infinite loading state\n          setLoading(false);\n        }\n      });\n    }\n    return () => {\n      console.log(`🔧 LandingPage component ${componentId.current} unmounting`);\n      isMounted = false; // Prevent further state updates\n      unsubscribe();\n    };\n  }, []); // Empty dependency array - only run once on mount\n\n  // Auto-rotate features\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentFeature(prev => (prev + 1) % features.length);\n    }, 3000);\n    return () => clearInterval(interval);\n  }, []); // Remove features.length dependency since features array is constant\n\n  const handleGetStarted = () => {\n    navigate(\"/home\");\n  };\n  const handleLearnMore = () => {\n    var _document$getElementB;\n    // Scroll to features section\n    (_document$getElementB = document.getElementById(\"features-section\")) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"landing-page\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"section\", {\n    className: \"hero-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Container, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    className: \"align-items-center min-vh-100\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    lg: 6,\n    className: \"hero-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"logo-section mb-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"logo-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaGavel, {\n    className: \"logo-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(\"h1\", {\n    className: \"logo-text\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 19\n    }\n  }, \"AuctionStore\")), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"tagline\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 17\n    }\n  }, \"Where Every Bid Tells a Story\")), /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"hero-title\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 15\n    }\n  }, \"Discover, Bid, and Win\", /*#__PURE__*/React.createElement(\"span\", {\n    className: \"highlight\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 17\n    }\n  }, \" Amazing Items\")), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"hero-description\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 15\n    }\n  }, \"Join the world's most trusted online auction platform. From rare collectibles to everyday treasures, find exactly what you're looking for or discover something unexpected.\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"hero-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    size: \"lg\",\n    className: \"cta-button primary\",\n    onClick: handleGetStarted,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaRocket, {\n    className: \"me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 19\n    }\n  }), \"Get Started\"), /*#__PURE__*/React.createElement(Button, {\n    size: \"lg\",\n    variant: \"outline-light\",\n    className: \"cta-button secondary\",\n    onClick: handleLearnMore,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 17\n    }\n  }, \"Learn More\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"trust-indicators\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"trust-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaShieldAlt, {\n    className: \"trust-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 19\n    }\n  }, \"Secure & Trusted\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"trust-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaUsers, {\n    className: \"trust-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 19\n    }\n  }, \"10,000+ Users\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"trust-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaHeart, {\n    className: \"trust-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 19\n    }\n  }, \"99% Satisfaction\")))), /*#__PURE__*/React.createElement(Col, {\n    lg: 6,\n    className: \"hero-visual\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"feature-showcase\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"feature-card active\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 17\n    }\n  }, features[currentFeature].icon, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 19\n    }\n  }, features[currentFeature].title), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 19\n    }\n  }, features[currentFeature].description)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"feature-dots\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 17\n    }\n  }, features.map((_, index) => /*#__PURE__*/React.createElement(\"button\", {\n    key: index,\n    className: `dot ${index === currentFeature ? \"active\" : \"\"}`,\n    onClick: () => setCurrentFeature(index),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 21\n    }\n  })))))))), /*#__PURE__*/React.createElement(\"section\", {\n    className: \"stats-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Container, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 11\n    }\n  }, stats.map((stat, index) => /*#__PURE__*/React.createElement(Col, {\n    md: 3,\n    key: index,\n    className: \"text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 19\n    }\n  }, stat.number), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 19\n    }\n  }, stat.label))))))), /*#__PURE__*/React.createElement(\"section\", {\n    id: \"features-section\",\n    className: \"features-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Container, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    lg: 12,\n    className: \"text-center mb-5\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"section-title\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 15\n    }\n  }, \"Why Choose AuctionStore?\"), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"section-subtitle\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 15\n    }\n  }, \"Experience the future of online auctions with our cutting-edge features\"))), /*#__PURE__*/React.createElement(Row, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 11\n    }\n  }, features.map((feature, index) => /*#__PURE__*/React.createElement(Col, {\n    md: 4,\n    key: index,\n    className: \"mb-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Card, {\n    className: \"feature-card h-100\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(Card.Body, {\n    className: \"text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 19\n    }\n  }, feature.icon, /*#__PURE__*/React.createElement(\"h4\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 21\n    }\n  }, feature.title), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 21\n    }\n  }, feature.description)))))))), /*#__PURE__*/React.createElement(\"section\", {\n    className: \"cta-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Container, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    lg: 12,\n    className: \"text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: {\n      color: \"white\"\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 15\n    }\n  }, \"Ready to Start Your Auction Journey?\"), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      color: \"white\"\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 15\n    }\n  }, \"Join thousands of satisfied users and discover amazing deals today!\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"cta-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    size: \"lg\",\n    className: \"cta-button primary me-3\",\n    onClick: handleGetStarted,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaSearch, {\n    className: \"me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 19\n    }\n  }), \"Explore Auctions\"), /*#__PURE__*/React.createElement(Link, {\n    to: \"/register\",\n    className: \"btn btn-outline-light btn-lg cta-button\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 17\n    }\n  }, \"Sign Up Free\")))))), /*#__PURE__*/React.createElement(\"footer\", {\n    className: \"landing-footer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Container, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Row, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    lg: 12,\n    className: \"text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"footer-logo\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaGavel, {\n    className: \"footer-logo-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 17\n    }\n  }, \"AuctionStore\")), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 532,\n      columnNumber: 15\n    }\n  }, \"\\xA9 2024 AuctionStore. All rights reserved.\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"footer-links\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Link, {\n    to: \"/home\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 17\n    }\n  }, \"Home\"), /*#__PURE__*/React.createElement(Link, {\n    to: \"/auctions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 17\n    }\n  }, \"Auctions\"), /*#__PURE__*/React.createElement(Link, {\n    to: \"/login\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 17\n    }\n  }, \"Login\"), /*#__PURE__*/React.createElement(Link, {\n    to: \"/register\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 537,\n      columnNumber: 17\n    }\n  }, \"Register\")))))));\n};\nexport default /*#__PURE__*/React.memo(LandingPage);", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Link", "useNavigate", "Container", "Row", "Col", "<PERSON><PERSON>", "Card", "FaGavel", "FaShieldAlt", "FaRocket", "FaUsers", "FaClock", "FaChartLine", "FaHeart", "FaSearch", "axiosInstance", "globalApiManager", "LandingDataManager", "constructor", "data", "loading", "lastFetch", "cacheExpiry", "subscribers", "Set", "requestPromise", "isDestroyed", "maxRetries", "retryCount", "backoffDelay", "subscribe", "callback", "add", "delete", "notify", "for<PERSON>ach", "fetchData", "console", "log", "now", "Date", "performFetch", "result", "error", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "get", "signal", "clearTimeout", "success", "localStorage", "setItem", "JSON", "stringify", "toString", "warn", "name", "delay", "Math", "pow", "Promise", "resolve", "initializeFromCache", "cachedData", "getItem", "cachedTime", "age", "parseInt", "parse", "destroy", "clear", "reset", "landingDataManager", "LandingPage", "currentFeature", "setCurrentFeature", "landingData", "setLandingData", "setLoading", "isDarkMode", "setIsDarkMode", "navigate", "componentId", "random", "checkDarkMode", "isDark", "document", "body", "classList", "contains", "observer", "MutationObserver", "observe", "attributes", "attributeFilter", "disconnect", "features", "icon", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "defaultStats", "number", "label", "stats", "total_users", "toLocaleString", "total_auctions", "uptime", "total_sales", "toFixed", "isMounted", "current", "cacheInitialized", "unsubscribe", "catch", "interval", "setInterval", "prev", "length", "clearInterval", "handleGetStarted", "handleLearnMore", "_document$getElementB", "getElementById", "scrollIntoView", "behavior", "lg", "size", "onClick", "variant", "map", "_", "index", "key", "stat", "md", "id", "feature", "Body", "style", "color", "to", "memo"], "sources": ["C:/Users/<USER>/Desktop/Online_Auction_System/frontend/src/pages/LandingPage.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport { <PERSON>, useNavigate } from \"react-router-dom\";\r\nimport { Container, <PERSON>, <PERSON>, <PERSON><PERSON>, Card } from \"react-bootstrap\";\r\nimport {\r\n  FaGavel,\r\n  FaShieldAlt,\r\n  FaRocket,\r\n  FaUsers,\r\n  FaClock,\r\n  FaChartLine,\r\n  FaHeart,\r\n  FaSearch,\r\n} from \"react-icons/fa\";\r\nimport axiosInstance from \"../api/axiosInstance\";\r\nimport globalApiManager from \"../utils/globalApiManager\";\r\nimport \"./LandingPage.css\";\r\n\r\n// Global singleton to prevent multiple API calls\r\nclass LandingDataManager {\r\n  constructor() {\r\n    this.data = null;\r\n    this.loading = false;\r\n    this.lastFetch = 0;\r\n    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes\r\n    this.subscribers = new Set();\r\n    this.requestPromise = null;\r\n    this.isDestroyed = false; // Flag to prevent calls after destruction\r\n    this.maxRetries = 3;\r\n    this.retryCount = 0;\r\n    this.backoffDelay = 1000; // Start with 1 second\r\n  }\r\n\r\n  subscribe(callback) {\r\n    this.subscribers.add(callback);\r\n    return () => this.subscribers.delete(callback);\r\n  }\r\n\r\n  notify() {\r\n    this.subscribers.forEach((callback) => callback(this.data, this.loading));\r\n  }\r\n\r\n  async fetchData() {\r\n    // Prevent calls if manager is destroyed\r\n    if (this.isDestroyed) {\r\n      console.log(\"🚫 LandingDataManager is destroyed - preventing API call\");\r\n      return this.data;\r\n    }\r\n\r\n    const now = Date.now();\r\n\r\n    // Return cached data if still valid\r\n    if (this.data && now - this.lastFetch < this.cacheExpiry) {\r\n      console.log(\"📦 Using cached landing data (singleton)\");\r\n      this.retryCount = 0; // Reset retry count on successful cache hit\r\n      return this.data;\r\n    }\r\n\r\n    // If already fetching, return the existing promise\r\n    if (this.requestPromise) {\r\n      console.log(\"🔄 Landing data request already in progress (singleton)\");\r\n      return this.requestPromise;\r\n    }\r\n\r\n    // Check retry limit\r\n    if (this.retryCount >= this.maxRetries) {\r\n      console.log(\"🚫 Max retries reached - using cached data or defaults\");\r\n      return this.data;\r\n    }\r\n\r\n    // Start new request\r\n    this.loading = true;\r\n    this.notify();\r\n\r\n    this.requestPromise = this.performFetch();\r\n\r\n    try {\r\n      const result = await this.requestPromise;\r\n      this.retryCount = 0; // Reset on success\r\n      return result;\r\n    } catch (error) {\r\n      this.retryCount++;\r\n      console.error(\r\n        `❌ Fetch attempt ${this.retryCount}/${this.maxRetries} failed:`,\r\n        error\r\n      );\r\n      throw error;\r\n    } finally {\r\n      this.requestPromise = null;\r\n    }\r\n  }\r\n\r\n  async performFetch() {\r\n    try {\r\n      // Double-check if destroyed before making request\r\n      if (this.isDestroyed) {\r\n        console.log(\"🚫 Manager destroyed during fetch - aborting\");\r\n        return this.data;\r\n      }\r\n\r\n      console.log(\"🌐 Fetching fresh landing data (singleton)...\");\r\n\r\n      // Add timeout and abort controller for better control\r\n      const controller = new AbortController();\r\n      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout\r\n\r\n      const response = await axiosInstance.get(\"/landing/data/\", {\r\n        signal: controller.signal,\r\n      });\r\n\r\n      clearTimeout(timeoutId);\r\n\r\n      if (response.data.success) {\r\n        this.data = response.data.data;\r\n        this.lastFetch = Date.now();\r\n        localStorage.setItem(\"landingData\", JSON.stringify(this.data));\r\n        localStorage.setItem(\"landingData_time\", this.lastFetch.toString());\r\n        console.log(\r\n          \"✅ Landing data fetched and cached successfully (singleton)\"\r\n        );\r\n      } else {\r\n        console.warn(\"⚠️ API returned success=false:\", response.data);\r\n      }\r\n\r\n      return this.data;\r\n    } catch (error) {\r\n      if (error.name === \"AbortError\") {\r\n        console.log(\"⏰ Landing data request timed out\");\r\n      } else {\r\n        console.error(\r\n          \"❌ Failed to fetch landing page data (singleton):\",\r\n          error\r\n        );\r\n      }\r\n\r\n      // Implement exponential backoff for retries\r\n      if (this.retryCount < this.maxRetries) {\r\n        const delay = this.backoffDelay * Math.pow(2, this.retryCount);\r\n        console.log(`⏳ Retrying in ${delay}ms...`);\r\n        await new Promise((resolve) => setTimeout(resolve, delay));\r\n      }\r\n\r\n      throw error;\r\n    } finally {\r\n      this.loading = false;\r\n      this.notify();\r\n    }\r\n  }\r\n\r\n  // Initialize with cached data\r\n  initializeFromCache() {\r\n    if (this.isDestroyed) {\r\n      console.log(\"🚫 Manager destroyed - skipping cache initialization\");\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      const cachedData = localStorage.getItem(\"landingData\");\r\n      const cachedTime = localStorage.getItem(\"landingData_time\");\r\n\r\n      if (cachedData && cachedTime) {\r\n        const age = Date.now() - parseInt(cachedTime);\r\n        if (age < this.cacheExpiry) {\r\n          this.data = JSON.parse(cachedData);\r\n          this.lastFetch = parseInt(cachedTime);\r\n          console.log(\"📦 Initialized with cached landing data (singleton)\");\r\n          return true;\r\n        } else {\r\n          console.log(\"📦 Cached data expired, will fetch fresh data\");\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"❌ Error loading cached data:\", error);\r\n    }\r\n    return false;\r\n  }\r\n\r\n  // Method to destroy the manager and prevent further API calls\r\n  destroy() {\r\n    console.log(\"🗑️ Destroying LandingDataManager\");\r\n    this.isDestroyed = true;\r\n    this.subscribers.clear();\r\n    this.requestPromise = null;\r\n    this.data = null;\r\n  }\r\n\r\n  // Method to reset the manager (for testing or recovery)\r\n  reset() {\r\n    console.log(\"🔄 Resetting LandingDataManager\");\r\n    this.isDestroyed = false;\r\n    this.retryCount = 0;\r\n    this.requestPromise = null;\r\n    this.loading = false;\r\n  }\r\n}\r\n\r\n// Global instance\r\nconst landingDataManager = new LandingDataManager();\r\n\r\nconst LandingPage = () => {\r\n  const [currentFeature, setCurrentFeature] = useState(0);\r\n  const [landingData, setLandingData] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [isDarkMode, setIsDarkMode] = useState(false);\r\n  const navigate = useNavigate();\r\n  const componentId = useRef(`landing-${Date.now()}-${Math.random()}`); // Unique component ID for debugging\r\n\r\n  // Check for dark mode from body class\r\n  useEffect(() => {\r\n    const checkDarkMode = () => {\r\n      const isDark = document.body.classList.contains(\"dark-mode-bg\");\r\n      setIsDarkMode(isDark);\r\n    };\r\n\r\n    // Initial check\r\n    checkDarkMode();\r\n\r\n    // Listen for class changes on body\r\n    const observer = new MutationObserver(checkDarkMode);\r\n    observer.observe(document.body, {\r\n      attributes: true,\r\n      attributeFilter: [\"class\"],\r\n    });\r\n\r\n    return () => observer.disconnect();\r\n  }, []);\r\n\r\n  const features = [\r\n    {\r\n      icon: <FaGavel className=\"feature-icon\" />,\r\n      title: \"Live Auctions\",\r\n      description:\r\n        \"Participate in real-time bidding with instant updates and notifications\",\r\n    },\r\n    {\r\n      icon: <FaShieldAlt className=\"feature-icon\" />,\r\n      title: \"Secure Payments\",\r\n      description:\r\n        \"Safe and secure transactions with Stripe payment integration\",\r\n    },\r\n    {\r\n      icon: <FaRocket className=\"feature-icon\" />,\r\n      title: \"AI Price Prediction\",\r\n      description:\r\n        \"Get intelligent price predictions powered by advanced AI algorithms\",\r\n    },\r\n    {\r\n      icon: <FaUsers className=\"feature-icon\" />,\r\n      title: \"Community Driven\",\r\n      description:\r\n        \"Join thousands of buyers and sellers in our trusted marketplace\",\r\n    },\r\n    {\r\n      icon: <FaClock className=\"feature-icon\" />,\r\n      title: \"24/7 Availability\",\r\n      description: \"Bid anytime, anywhere with our always-available platform\",\r\n    },\r\n    {\r\n      icon: <FaChartLine className=\"feature-icon\" />,\r\n      title: \"Market Analytics\",\r\n      description:\r\n        \"Track trends and make informed decisions with detailed analytics\",\r\n    },\r\n  ];\r\n\r\n  // Default stats (will be replaced by API data)\r\n  const defaultStats = [\r\n    { number: \"10,000+\", label: \"Active Users\" },\r\n    { number: \"50,000+\", label: \"Auctions Completed\" },\r\n    { number: \"99.9%\", label: \"Uptime\" },\r\n    { number: \"$2M+\", label: \"Total Sales\" },\r\n  ];\r\n\r\n  // Use API data if available, otherwise use defaults\r\n  const stats = landingData?.stats\r\n    ? [\r\n        {\r\n          number: `${landingData.stats.total_users.toLocaleString()}+`,\r\n          label: \"Active Users\",\r\n        },\r\n        {\r\n          number: `${landingData.stats.total_auctions.toLocaleString()}+`,\r\n          label: \"Auctions Completed\",\r\n        },\r\n        { number: landingData.stats.uptime, label: \"Uptime\" },\r\n        {\r\n          number: `$${(landingData.stats.total_sales / 1000000).toFixed(1)}M+`,\r\n          label: \"Total Sales\",\r\n        },\r\n      ]\r\n    : defaultStats;\r\n\r\n  // Initialize and subscribe to landing data manager\r\n  useEffect(() => {\r\n    let isMounted = true; // Flag to prevent state updates after unmount\r\n\r\n    console.log(`🔧 LandingPage component ${componentId.current} mounting`);\r\n\r\n    // Initialize from cache first\r\n    const cacheInitialized = landingDataManager.initializeFromCache();\r\n\r\n    // Set initial state only if component is still mounted\r\n    if (isMounted) {\r\n      setLandingData(landingDataManager.data);\r\n      setLoading(landingDataManager.loading);\r\n    }\r\n\r\n    // Subscribe to updates\r\n    const unsubscribe = landingDataManager.subscribe((data, loading) => {\r\n      if (isMounted) {\r\n        console.log(`📡 LandingPage ${componentId.current} received update:`, {\r\n          data: !!data,\r\n          loading,\r\n        });\r\n        setLandingData(data);\r\n        setLoading(loading);\r\n      }\r\n    });\r\n\r\n    // Fetch fresh data if needed (only if cache wasn't sufficient)\r\n    if (!cacheInitialized || !landingDataManager.data) {\r\n      landingDataManager.fetchData().catch((error) => {\r\n        if (isMounted) {\r\n          console.error(\r\n            `❌ LandingPage ${componentId.current} fetch error:`,\r\n            error\r\n          );\r\n          // Set loading to false on error to prevent infinite loading state\r\n          setLoading(false);\r\n        }\r\n      });\r\n    }\r\n\r\n    return () => {\r\n      console.log(`🔧 LandingPage component ${componentId.current} unmounting`);\r\n      isMounted = false; // Prevent further state updates\r\n      unsubscribe();\r\n    };\r\n  }, []); // Empty dependency array - only run once on mount\r\n\r\n  // Auto-rotate features\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      setCurrentFeature((prev) => (prev + 1) % features.length);\r\n    }, 3000);\r\n    return () => clearInterval(interval);\r\n  }, []); // Remove features.length dependency since features array is constant\r\n\r\n  const handleGetStarted = () => {\r\n    navigate(\"/home\");\r\n  };\r\n\r\n  const handleLearnMore = () => {\r\n    // Scroll to features section\r\n    document.getElementById(\"features-section\")?.scrollIntoView({\r\n      behavior: \"smooth\",\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"landing-page\">\r\n      {/* Hero Section */}\r\n      <section className=\"hero-section\">\r\n        <Container>\r\n          <Row className=\"align-items-center min-vh-100\">\r\n            <Col lg={6} className=\"hero-content\">\r\n              <div className=\"logo-section mb-4\">\r\n                <div className=\"logo-container\">\r\n                  <FaGavel className=\"logo-icon\" />\r\n                  <h1 className=\"logo-text\">AuctionStore</h1>\r\n                </div>\r\n                <p className=\"tagline\">Where Every Bid Tells a Story</p>\r\n              </div>\r\n\r\n              <h2 className=\"hero-title\">\r\n                Discover, Bid, and Win\r\n                <span className=\"highlight\"> Amazing Items</span>\r\n              </h2>\r\n\r\n              <p className=\"hero-description\">\r\n                Join the world's most trusted online auction platform. From rare\r\n                collectibles to everyday treasures, find exactly what you're\r\n                looking for or discover something unexpected.\r\n              </p>\r\n\r\n              <div className=\"hero-buttons\">\r\n                <Button\r\n                  size=\"lg\"\r\n                  className=\"cta-button primary\"\r\n                  onClick={handleGetStarted}\r\n                >\r\n                  <FaRocket className=\"me-2\" />\r\n                  Get Started\r\n                </Button>\r\n                <Button\r\n                  size=\"lg\"\r\n                  variant=\"outline-light\"\r\n                  className=\"cta-button secondary\"\r\n                  onClick={handleLearnMore}\r\n                >\r\n                  Learn More\r\n                </Button>\r\n              </div>\r\n\r\n              <div className=\"trust-indicators\">\r\n                <div className=\"trust-item\">\r\n                  <FaShieldAlt className=\"trust-icon\" />\r\n                  <span>Secure & Trusted</span>\r\n                </div>\r\n                <div className=\"trust-item\">\r\n                  <FaUsers className=\"trust-icon\" />\r\n                  <span>10,000+ Users</span>\r\n                </div>\r\n                <div className=\"trust-item\">\r\n                  <FaHeart className=\"trust-icon\" />\r\n                  <span>99% Satisfaction</span>\r\n                </div>\r\n              </div>\r\n            </Col>\r\n\r\n            <Col lg={6} className=\"hero-visual\">\r\n              <div className=\"feature-showcase\">\r\n                <div className=\"feature-card active\">\r\n                  {features[currentFeature].icon}\r\n                  <h3>{features[currentFeature].title}</h3>\r\n                  <p>{features[currentFeature].description}</p>\r\n                </div>\r\n\r\n                <div className=\"feature-dots\">\r\n                  {features.map((_, index) => (\r\n                    <button\r\n                      key={index}\r\n                      className={`dot ${\r\n                        index === currentFeature ? \"active\" : \"\"\r\n                      }`}\r\n                      onClick={() => setCurrentFeature(index)}\r\n                    />\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </section>\r\n\r\n      {/* Stats Section */}\r\n      <section className=\"stats-section\">\r\n        <Container>\r\n          <Row>\r\n            {stats.map((stat, index) => (\r\n              <Col md={3} key={index} className=\"text-center\">\r\n                <div className=\"stat-item\">\r\n                  <h3 className=\"stat-number\">{stat.number}</h3>\r\n                  <p className=\"stat-label\">{stat.label}</p>\r\n                </div>\r\n              </Col>\r\n            ))}\r\n          </Row>\r\n        </Container>\r\n      </section>\r\n\r\n      {/* Features Section */}\r\n      <section id=\"features-section\" className=\"features-section\">\r\n        <Container>\r\n          <Row>\r\n            <Col lg={12} className=\"text-center mb-5\">\r\n              <h2 className=\"section-title\">Why Choose AuctionStore?</h2>\r\n              <p className=\"section-subtitle\">\r\n                Experience the future of online auctions with our cutting-edge\r\n                features\r\n              </p>\r\n            </Col>\r\n          </Row>\r\n\r\n          <Row>\r\n            {features.map((feature, index) => (\r\n              <Col md={4} key={index} className=\"mb-4\">\r\n                <Card className=\"feature-card h-100\">\r\n                  <Card.Body className=\"text-center\">\r\n                    {feature.icon}\r\n                    <h4>{feature.title}</h4>\r\n                    <p>{feature.description}</p>\r\n                  </Card.Body>\r\n                </Card>\r\n              </Col>\r\n            ))}\r\n          </Row>\r\n        </Container>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"cta-section\">\r\n        <Container>\r\n          <Row>\r\n            <Col lg={12} className=\"text-center\">\r\n              <h2 style={{ color: \"white\" }}>\r\n                Ready to Start Your Auction Journey?\r\n              </h2>\r\n              <p style={{ color: \"white\" }}>\r\n                Join thousands of satisfied users and discover amazing deals\r\n                today!\r\n              </p>\r\n              <div className=\"cta-buttons\">\r\n                <Button\r\n                  size=\"lg\"\r\n                  className=\"cta-button primary me-3\"\r\n                  onClick={handleGetStarted}\r\n                >\r\n                  <FaSearch className=\"me-2\" />\r\n                  Explore Auctions\r\n                </Button>\r\n                <Link\r\n                  to=\"/register\"\r\n                  className=\"btn btn-outline-light btn-lg cta-button\"\r\n                >\r\n                  Sign Up Free\r\n                </Link>\r\n              </div>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"landing-footer\">\r\n        <Container>\r\n          <Row>\r\n            <Col lg={12} className=\"text-center\">\r\n              <div className=\"footer-logo\">\r\n                <FaGavel className=\"footer-logo-icon\" />\r\n                <span>AuctionStore</span>\r\n              </div>\r\n              <p>&copy; 2024 AuctionStore. All rights reserved.</p>\r\n              <div className=\"footer-links\">\r\n                <Link to=\"/home\">Home</Link>\r\n                <Link to=\"/auctions\">Auctions</Link>\r\n                <Link to=\"/login\">Login</Link>\r\n                <Link to=\"/register\">Register</Link>\r\n              </div>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default React.memo(LandingPage);\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,QAAQ,iBAAiB;AACnE,SACEC,OAAO,EACPC,WAAW,EACXC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,WAAW,EACXC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAO,mBAAmB;;AAE1B;AACA,MAAMC,kBAAkB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAClC,IAAI,CAACC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,WAAW,GAAG,KAAK,CAAC,CAAC;IAC1B,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,YAAY,GAAG,IAAI,CAAC,CAAC;EAC5B;EAEAC,SAASA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACR,WAAW,CAACS,GAAG,CAACD,QAAQ,CAAC;IAC9B,OAAO,MAAM,IAAI,CAACR,WAAW,CAACU,MAAM,CAACF,QAAQ,CAAC;EAChD;EAEAG,MAAMA,CAAA,EAAG;IACP,IAAI,CAACX,WAAW,CAACY,OAAO,CAAEJ,QAAQ,IAAKA,QAAQ,CAAC,IAAI,CAACZ,IAAI,EAAE,IAAI,CAACC,OAAO,CAAC,CAAC;EAC3E;EAEA,MAAMgB,SAASA,CAAA,EAAG;IAChB;IACA,IAAI,IAAI,CAACV,WAAW,EAAE;MACpBW,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE,OAAO,IAAI,CAACnB,IAAI;IAClB;IAEA,MAAMoB,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAI,IAAI,CAACpB,IAAI,IAAIoB,GAAG,GAAG,IAAI,CAAClB,SAAS,GAAG,IAAI,CAACC,WAAW,EAAE;MACxDe,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,IAAI,CAACV,UAAU,GAAG,CAAC,CAAC,CAAC;MACrB,OAAO,IAAI,CAACT,IAAI;IAClB;;IAEA;IACA,IAAI,IAAI,CAACM,cAAc,EAAE;MACvBY,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtE,OAAO,IAAI,CAACb,cAAc;IAC5B;;IAEA;IACA,IAAI,IAAI,CAACG,UAAU,IAAI,IAAI,CAACD,UAAU,EAAE;MACtCU,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,OAAO,IAAI,CAACnB,IAAI;IAClB;;IAEA;IACA,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACc,MAAM,CAAC,CAAC;IAEb,IAAI,CAACT,cAAc,GAAG,IAAI,CAACgB,YAAY,CAAC,CAAC;IAEzC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACjB,cAAc;MACxC,IAAI,CAACG,UAAU,GAAG,CAAC,CAAC,CAAC;MACrB,OAAOc,MAAM;IACf,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,IAAI,CAACf,UAAU,EAAE;MACjBS,OAAO,CAACM,KAAK,CACX,mBAAmB,IAAI,CAACf,UAAU,IAAI,IAAI,CAACD,UAAU,UAAU,EAC/DgB,KACF,CAAC;MACD,MAAMA,KAAK;IACb,CAAC,SAAS;MACR,IAAI,CAAClB,cAAc,GAAG,IAAI;IAC5B;EACF;EAEA,MAAMgB,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF;MACA,IAAI,IAAI,CAACf,WAAW,EAAE;QACpBW,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC3D,OAAO,IAAI,CAACnB,IAAI;MAClB;MAEAkB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;;MAE5D;MACA,MAAMM,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;MAE/D,MAAMC,QAAQ,GAAG,MAAMlC,aAAa,CAACmC,GAAG,CAAC,gBAAgB,EAAE;QACzDC,MAAM,EAAEP,UAAU,CAACO;MACrB,CAAC,CAAC;MAEFC,YAAY,CAACN,SAAS,CAAC;MAEvB,IAAIG,QAAQ,CAAC9B,IAAI,CAACkC,OAAO,EAAE;QACzB,IAAI,CAAClC,IAAI,GAAG8B,QAAQ,CAAC9B,IAAI,CAACA,IAAI;QAC9B,IAAI,CAACE,SAAS,GAAGmB,IAAI,CAACD,GAAG,CAAC,CAAC;QAC3Be,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACtC,IAAI,CAAC,CAAC;QAC9DmC,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAClC,SAAS,CAACqC,QAAQ,CAAC,CAAC,CAAC;QACnErB,OAAO,CAACC,GAAG,CACT,4DACF,CAAC;MACH,CAAC,MAAM;QACLD,OAAO,CAACsB,IAAI,CAAC,gCAAgC,EAAEV,QAAQ,CAAC9B,IAAI,CAAC;MAC/D;MAEA,OAAO,IAAI,CAACA,IAAI;IAClB,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACd,IAAIA,KAAK,CAACiB,IAAI,KAAK,YAAY,EAAE;QAC/BvB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MACjD,CAAC,MAAM;QACLD,OAAO,CAACM,KAAK,CACX,kDAAkD,EAClDA,KACF,CAAC;MACH;;MAEA;MACA,IAAI,IAAI,CAACf,UAAU,GAAG,IAAI,CAACD,UAAU,EAAE;QACrC,MAAMkC,KAAK,GAAG,IAAI,CAAChC,YAAY,GAAGiC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACnC,UAAU,CAAC;QAC9DS,OAAO,CAACC,GAAG,CAAC,iBAAiBuB,KAAK,OAAO,CAAC;QAC1C,MAAM,IAAIG,OAAO,CAAEC,OAAO,IAAKlB,UAAU,CAACkB,OAAO,EAAEJ,KAAK,CAAC,CAAC;MAC5D;MAEA,MAAMlB,KAAK;IACb,CAAC,SAAS;MACR,IAAI,CAACvB,OAAO,GAAG,KAAK;MACpB,IAAI,CAACc,MAAM,CAAC,CAAC;IACf;EACF;;EAEA;EACAgC,mBAAmBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACxC,WAAW,EAAE;MACpBW,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;MACnE,OAAO,KAAK;IACd;IAEA,IAAI;MACF,MAAM6B,UAAU,GAAGb,YAAY,CAACc,OAAO,CAAC,aAAa,CAAC;MACtD,MAAMC,UAAU,GAAGf,YAAY,CAACc,OAAO,CAAC,kBAAkB,CAAC;MAE3D,IAAID,UAAU,IAAIE,UAAU,EAAE;QAC5B,MAAMC,GAAG,GAAG9B,IAAI,CAACD,GAAG,CAAC,CAAC,GAAGgC,QAAQ,CAACF,UAAU,CAAC;QAC7C,IAAIC,GAAG,GAAG,IAAI,CAAChD,WAAW,EAAE;UAC1B,IAAI,CAACH,IAAI,GAAGqC,IAAI,CAACgB,KAAK,CAACL,UAAU,CAAC;UAClC,IAAI,CAAC9C,SAAS,GAAGkD,QAAQ,CAACF,UAAU,CAAC;UACrChC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE,OAAO,IAAI;QACb,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC9D;MACF;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;IACA,OAAO,KAAK;EACd;;EAEA;EACA8B,OAAOA,CAAA,EAAG;IACRpC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChD,IAAI,CAACZ,WAAW,GAAG,IAAI;IACvB,IAAI,CAACH,WAAW,CAACmD,KAAK,CAAC,CAAC;IACxB,IAAI,CAACjD,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACN,IAAI,GAAG,IAAI;EAClB;;EAEA;EACAwD,KAAKA,CAAA,EAAG;IACNtC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAACZ,WAAW,GAAG,KAAK;IACxB,IAAI,CAACE,UAAU,GAAG,CAAC;IACnB,IAAI,CAACH,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACL,OAAO,GAAG,KAAK;EACtB;AACF;;AAEA;AACA,MAAMwD,kBAAkB,GAAG,IAAI3D,kBAAkB,CAAC,CAAC;AAEnD,MAAM4D,WAAW,GAAGA,CAAA,KAAM;EACxB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlF,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACmF,WAAW,EAAEC,cAAc,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACuB,OAAO,EAAE8D,UAAU,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsF,UAAU,EAAEC,aAAa,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMwF,QAAQ,GAAGpF,WAAW,CAAC,CAAC;EAC9B,MAAMqF,WAAW,GAAGvF,MAAM,CAAC,WAAWyC,IAAI,CAACD,GAAG,CAAC,CAAC,IAAIuB,IAAI,CAACyB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEtE;EACAzF,SAAS,CAAC,MAAM;IACd,MAAM0F,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,cAAc,CAAC;MAC/DT,aAAa,CAACK,MAAM,CAAC;IACvB,CAAC;;IAED;IACAD,aAAa,CAAC,CAAC;;IAEf;IACA,MAAMM,QAAQ,GAAG,IAAIC,gBAAgB,CAACP,aAAa,CAAC;IACpDM,QAAQ,CAACE,OAAO,CAACN,QAAQ,CAACC,IAAI,EAAE;MAC9BM,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,CAAC,OAAO;IAC3B,CAAC,CAAC;IAEF,OAAO,MAAMJ,QAAQ,CAACK,UAAU,CAAC,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,eAAEzG,KAAA,CAAA0G,aAAA,CAAC/F,OAAO;MAACgG,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC1CC,KAAK,EAAE,eAAe;IACtBC,WAAW,EACT;EACJ,CAAC,EACD;IACEV,IAAI,eAAEzG,KAAA,CAAA0G,aAAA,CAAC9F,WAAW;MAAC+F,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC9CC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EACT;EACJ,CAAC,EACD;IACEV,IAAI,eAAEzG,KAAA,CAAA0G,aAAA,CAAC7F,QAAQ;MAAC8F,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC3CC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EACT;EACJ,CAAC,EACD;IACEV,IAAI,eAAEzG,KAAA,CAAA0G,aAAA,CAAC5F,OAAO;MAAC6F,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC1CC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EACT;EACJ,CAAC,EACD;IACEV,IAAI,eAAEzG,KAAA,CAAA0G,aAAA,CAAC3F,OAAO;MAAC4F,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC1CC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEV,IAAI,eAAEzG,KAAA,CAAA0G,aAAA,CAAC1F,WAAW;MAAC2F,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC9CC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EACT;EACJ,CAAC,CACF;;EAED;EACA,MAAMC,YAAY,GAAG,CACnB;IAAEC,MAAM,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC5C;IAAED,MAAM,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAqB,CAAC,EAClD;IAAED,MAAM,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAc,CAAC,CACzC;;EAED;EACA,MAAMC,KAAK,GAAGnC,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEmC,KAAK,GAC5B,CACE;IACEF,MAAM,EAAE,GAAGjC,WAAW,CAACmC,KAAK,CAACC,WAAW,CAACC,cAAc,CAAC,CAAC,GAAG;IAC5DH,KAAK,EAAE;EACT,CAAC,EACD;IACED,MAAM,EAAE,GAAGjC,WAAW,CAACmC,KAAK,CAACG,cAAc,CAACD,cAAc,CAAC,CAAC,GAAG;IAC/DH,KAAK,EAAE;EACT,CAAC,EACD;IAAED,MAAM,EAAEjC,WAAW,CAACmC,KAAK,CAACI,MAAM;IAAEL,KAAK,EAAE;EAAS,CAAC,EACrD;IACED,MAAM,EAAE,IAAI,CAACjC,WAAW,CAACmC,KAAK,CAACK,WAAW,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;IACpEP,KAAK,EAAE;EACT,CAAC,CACF,GACDF,YAAY;;EAEhB;EACAlH,SAAS,CAAC,MAAM;IACd,IAAI4H,SAAS,GAAG,IAAI,CAAC,CAAC;;IAEtBrF,OAAO,CAACC,GAAG,CAAC,4BAA4BgD,WAAW,CAACqC,OAAO,WAAW,CAAC;;IAEvE;IACA,MAAMC,gBAAgB,GAAGhD,kBAAkB,CAACV,mBAAmB,CAAC,CAAC;;IAEjE;IACA,IAAIwD,SAAS,EAAE;MACbzC,cAAc,CAACL,kBAAkB,CAACzD,IAAI,CAAC;MACvC+D,UAAU,CAACN,kBAAkB,CAACxD,OAAO,CAAC;IACxC;;IAEA;IACA,MAAMyG,WAAW,GAAGjD,kBAAkB,CAAC9C,SAAS,CAAC,CAACX,IAAI,EAAEC,OAAO,KAAK;MAClE,IAAIsG,SAAS,EAAE;QACbrF,OAAO,CAACC,GAAG,CAAC,kBAAkBgD,WAAW,CAACqC,OAAO,mBAAmB,EAAE;UACpExG,IAAI,EAAE,CAAC,CAACA,IAAI;UACZC;QACF,CAAC,CAAC;QACF6D,cAAc,CAAC9D,IAAI,CAAC;QACpB+D,UAAU,CAAC9D,OAAO,CAAC;MACrB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACwG,gBAAgB,IAAI,CAAChD,kBAAkB,CAACzD,IAAI,EAAE;MACjDyD,kBAAkB,CAACxC,SAAS,CAAC,CAAC,CAAC0F,KAAK,CAAEnF,KAAK,IAAK;QAC9C,IAAI+E,SAAS,EAAE;UACbrF,OAAO,CAACM,KAAK,CACX,iBAAiB2C,WAAW,CAACqC,OAAO,eAAe,EACnDhF,KACF,CAAC;UACD;UACAuC,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACX7C,OAAO,CAACC,GAAG,CAAC,4BAA4BgD,WAAW,CAACqC,OAAO,aAAa,CAAC;MACzED,SAAS,GAAG,KAAK,CAAC,CAAC;MACnBG,WAAW,CAAC,CAAC;IACf,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA/H,SAAS,CAAC,MAAM;IACd,MAAMiI,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCjD,iBAAiB,CAAEkD,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAI7B,QAAQ,CAAC8B,MAAM,CAAC;IAC3D,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,MAAMK,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/C,QAAQ,CAAC,OAAO,CAAC;EACnB,CAAC;EAED,MAAMgD,eAAe,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC5B;IACA,CAAAA,qBAAA,GAAA5C,QAAQ,CAAC6C,cAAc,CAAC,kBAAkB,CAAC,cAAAD,qBAAA,uBAA3CA,qBAAA,CAA6CE,cAAc,CAAC;MAC1DC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,oBACE7I,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3BjH,KAAA,CAAA0G,aAAA;IAASC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BjH,KAAA,CAAA0G,aAAA,CAACpG,SAAS;IAAAsG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRjH,KAAA,CAAA0G,aAAA,CAACnG,GAAG;IAACoG,SAAS,EAAC,+BAA+B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5CjH,KAAA,CAAA0G,aAAA,CAAClG,GAAG;IAACsI,EAAE,EAAE,CAAE;IAACnC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BjH,KAAA,CAAA0G,aAAA,CAAC/F,OAAO;IAACgG,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACjCjH,KAAA,CAAA0G,aAAA;IAAIC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAgB,CACvC,CAAC,eACNjH,KAAA,CAAA0G,aAAA;IAAGC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+BAAgC,CACpD,CAAC,eAENjH,KAAA,CAAA0G,aAAA;IAAIC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,wBAEzB,eAAAjH,KAAA,CAAA0G,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gBAAoB,CAC9C,CAAC,eAELjH,KAAA,CAAA0G,aAAA;IAAGC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6KAI7B,CAAC,eAEJjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BjH,KAAA,CAAA0G,aAAA,CAACjG,MAAM;IACLsI,IAAI,EAAC,IAAI;IACTpC,SAAS,EAAC,oBAAoB;IAC9BqC,OAAO,EAAER,gBAAiB;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE1BjH,KAAA,CAAA0G,aAAA,CAAC7F,QAAQ;IAAC8F,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAEvB,CAAC,eACTjH,KAAA,CAAA0G,aAAA,CAACjG,MAAM;IACLsI,IAAI,EAAC,IAAI;IACTE,OAAO,EAAC,eAAe;IACvBtC,SAAS,EAAC,sBAAsB;IAChCqC,OAAO,EAAEP,eAAgB;IAAA7B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1B,YAEO,CACL,CAAC,eAENjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjH,KAAA,CAAA0G,aAAA,CAAC9F,WAAW;IAAC+F,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACtCjH,KAAA,CAAA0G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,kBAAsB,CACzB,CAAC,eACNjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjH,KAAA,CAAA0G,aAAA,CAAC5F,OAAO;IAAC6F,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAClCjH,KAAA,CAAA0G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,eAAmB,CACtB,CAAC,eACNjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjH,KAAA,CAAA0G,aAAA,CAACzF,OAAO;IAAC0F,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAClCjH,KAAA,CAAA0G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,kBAAsB,CACzB,CACF,CACF,CAAC,eAENjH,KAAA,CAAA0G,aAAA,CAAClG,GAAG;IAACsI,EAAE,EAAE,CAAE;IAACnC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjCjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjCT,QAAQ,CAACtB,cAAc,CAAC,CAACuB,IAAI,eAC9BzG,KAAA,CAAA0G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKT,QAAQ,CAACtB,cAAc,CAAC,CAACgC,KAAU,CAAC,eACzClH,KAAA,CAAA0G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAIT,QAAQ,CAACtB,cAAc,CAAC,CAACiC,WAAe,CACzC,CAAC,eAENnH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1BT,QAAQ,CAAC0C,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACrBpJ,KAAA,CAAA0G,aAAA;IACE2C,GAAG,EAAED,KAAM;IACXzC,SAAS,EAAE,OACTyC,KAAK,KAAKlE,cAAc,GAAG,QAAQ,GAAG,EAAE,EACvC;IACH8D,OAAO,EAAEA,CAAA,KAAM7D,iBAAiB,CAACiE,KAAK,CAAE;IAAAxC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACzC,CACF,CACE,CACF,CACF,CACF,CACI,CACJ,CAAC,eAGVjH,KAAA,CAAA0G,aAAA;IAASC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCjH,KAAA,CAAA0G,aAAA,CAACpG,SAAS;IAAAsG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRjH,KAAA,CAAA0G,aAAA,CAACnG,GAAG;IAAAqG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACDM,KAAK,CAAC2B,GAAG,CAAC,CAACI,IAAI,EAAEF,KAAK,kBACrBpJ,KAAA,CAAA0G,aAAA,CAAClG,GAAG;IAAC+I,EAAE,EAAE,CAAE;IAACF,GAAG,EAAED,KAAM;IAACzC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7CjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBjH,KAAA,CAAA0G,aAAA;IAAIC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEqC,IAAI,CAACjC,MAAW,CAAC,eAC9CrH,KAAA,CAAA0G,aAAA;IAAGC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEqC,IAAI,CAAChC,KAAS,CACtC,CACF,CACN,CACE,CACI,CACJ,CAAC,eAGVtH,KAAA,CAAA0G,aAAA;IAAS8C,EAAE,EAAC,kBAAkB;IAAC7C,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzDjH,KAAA,CAAA0G,aAAA,CAACpG,SAAS;IAAAsG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRjH,KAAA,CAAA0G,aAAA,CAACnG,GAAG;IAAAqG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACFjH,KAAA,CAAA0G,aAAA,CAAClG,GAAG;IAACsI,EAAE,EAAE,EAAG;IAACnC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvCjH,KAAA,CAAA0G,aAAA;IAAIC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,0BAA4B,CAAC,eAC3DjH,KAAA,CAAA0G,aAAA;IAAGC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yEAG7B,CACA,CACF,CAAC,eAENjH,KAAA,CAAA0G,aAAA,CAACnG,GAAG;IAAAqG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACDT,QAAQ,CAAC0C,GAAG,CAAC,CAACO,OAAO,EAAEL,KAAK,kBAC3BpJ,KAAA,CAAA0G,aAAA,CAAClG,GAAG;IAAC+I,EAAE,EAAE,CAAE;IAACF,GAAG,EAAED,KAAM;IAACzC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtCjH,KAAA,CAAA0G,aAAA,CAAChG,IAAI;IAACiG,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCjH,KAAA,CAAA0G,aAAA,CAAChG,IAAI,CAACgJ,IAAI;IAAC/C,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/BwC,OAAO,CAAChD,IAAI,eACbzG,KAAA,CAAA0G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKwC,OAAO,CAACvC,KAAU,CAAC,eACxBlH,KAAA,CAAA0G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAIwC,OAAO,CAACtC,WAAe,CAClB,CACP,CACH,CACN,CACE,CACI,CACJ,CAAC,eAGVnH,KAAA,CAAA0G,aAAA;IAASC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BjH,KAAA,CAAA0G,aAAA,CAACpG,SAAS;IAAAsG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRjH,KAAA,CAAA0G,aAAA,CAACnG,GAAG;IAAAqG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACFjH,KAAA,CAAA0G,aAAA,CAAClG,GAAG;IAACsI,EAAE,EAAE,EAAG;IAACnC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCjH,KAAA,CAAA0G,aAAA;IAAIiD,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sCAE3B,CAAC,eACLjH,KAAA,CAAA0G,aAAA;IAAGiD,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qEAG3B,CAAC,eACJjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BjH,KAAA,CAAA0G,aAAA,CAACjG,MAAM;IACLsI,IAAI,EAAC,IAAI;IACTpC,SAAS,EAAC,yBAAyB;IACnCqC,OAAO,EAAER,gBAAiB;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE1BjH,KAAA,CAAA0G,aAAA,CAACxF,QAAQ;IAACyF,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,oBAEvB,CAAC,eACTjH,KAAA,CAAA0G,aAAA,CAACtG,IAAI;IACHyJ,EAAE,EAAC,WAAW;IACdlD,SAAS,EAAC,yCAAyC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpD,cAEK,CACH,CACF,CACF,CACI,CACJ,CAAC,eAGVjH,KAAA,CAAA0G,aAAA;IAAQC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCjH,KAAA,CAAA0G,aAAA,CAACpG,SAAS;IAAAsG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRjH,KAAA,CAAA0G,aAAA,CAACnG,GAAG;IAAAqG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACFjH,KAAA,CAAA0G,aAAA,CAAClG,GAAG;IAACsI,EAAE,EAAE,EAAG;IAACnC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BjH,KAAA,CAAA0G,aAAA,CAAC/F,OAAO;IAACgG,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACxCjH,KAAA,CAAA0G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,cAAkB,CACrB,CAAC,eACNjH,KAAA,CAAA0G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,8CAAiD,CAAC,eACrDjH,KAAA,CAAA0G,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BjH,KAAA,CAAA0G,aAAA,CAACtG,IAAI;IAACyJ,EAAE,EAAC,OAAO;IAAAjD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,MAAU,CAAC,eAC5BjH,KAAA,CAAA0G,aAAA,CAACtG,IAAI;IAACyJ,EAAE,EAAC,WAAW;IAAAjD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UAAc,CAAC,eACpCjH,KAAA,CAAA0G,aAAA,CAACtG,IAAI;IAACyJ,EAAE,EAAC,QAAQ;IAAAjD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OAAW,CAAC,eAC9BjH,KAAA,CAAA0G,aAAA,CAACtG,IAAI;IAACyJ,EAAE,EAAC,WAAW;IAAAjD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UAAc,CAChC,CACF,CACF,CACI,CACL,CACL,CAAC;AAEV,CAAC;AAED,4BAAejH,KAAK,CAAC8J,IAAI,CAAC7E,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}