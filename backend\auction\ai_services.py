"""
AI Services for Price Prediction and Smart Features
"""

import logging
import re
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from django.db.models import Avg, Count, Q
from django.utils import timezone

from .models import Auction, Bid, PricePrediction, PricePredictionHistory

logger = logging.getLogger(__name__)


class PricePredictionService:
    """AI-powered price prediction service"""

    def __init__(self):
        self.model_version = "v1.0"
        self.feature_weights = {
            "category_avg": 0.25,
            "condition_factor": 0.20,
            "title_keywords": 0.15,
            "seller_rating": 0.15,
            "auction_duration": 0.10,
            "starting_bid_factor": 0.10,
            "market_trend": 0.05,
        }

    def predict_price(self, auction: Auction) -> Tuple[Decimal, float, Dict]:
        """
        Predict the final price for an auction
        Returns: (predicted_price, confidence_score, features_used)
        """
        try:
            features = self._extract_features(auction)
            predicted_price = self._calculate_prediction(features)
            confidence_score = self._calculate_confidence(features, auction)

            # Store prediction
            prediction, created = PricePrediction.objects.get_or_create(
                auction=auction,
                defaults={
                    "predicted_price": predicted_price,
                    "confidence_score": confidence_score,
                    "model_version": self.model_version,
                    "features_used": features,
                },
            )

            if not created:
                # Update existing prediction
                prediction.predicted_price = predicted_price
                prediction.confidence_score = confidence_score
                prediction.features_used = features
                prediction.save()

            # Store in history for model improvement
            PricePredictionHistory.objects.create(
                auction=auction,
                predicted_price=predicted_price,
                confidence_score=confidence_score,
                model_version=self.model_version,
                features_used=features,
            )

            return predicted_price, confidence_score, features

        except Exception as e:
            logger.error(f"Price prediction failed for auction {auction.id}: {str(e)}")
            # Return fallback prediction
            fallback_price = auction.starting_bid * Decimal("1.5")
            return fallback_price, 0.3, {"error": str(e)}

    def _extract_features(self, auction: Auction) -> Dict:
        """Extract features from auction for prediction"""
        features = {}

        # 1. Category-based features
        features["category"] = auction.category
        features["category_avg_price"] = self._get_category_average_price(
            auction.category
        )

        # 2. Title analysis
        features["title_length"] = len(auction.title)
        features["title_keywords"] = self._analyze_title_keywords(auction.title)
        features["brand_detected"] = self._detect_brand(
            auction.title, auction.description
        )

        # 3. Condition and quality indicators
        features["condition"] = auction.condition
        features["condition_score"] = self._get_condition_score(auction.condition)

        # 4. Seller reputation
        features["seller_rating"] = self._get_seller_rating(auction.owner)
        features["seller_auction_count"] = auction.owner.auctions.count()

        # 5. Auction characteristics
        features["starting_bid"] = float(auction.starting_bid)
        features["current_bid"] = float(auction.current_bid)
        features["has_reserve"] = auction.reserve_price is not None
        features["has_buy_now"] = auction.buy_now_price is not None
        features["auction_duration"] = self._get_auction_duration_hours(auction)

        # 6. Market trends
        features["similar_recent_avg"] = self._get_similar_recent_average(auction)
        features["market_activity"] = self._get_market_activity_score(auction.category)

        # 7. Image and description quality
        features["has_image"] = bool(auction.image)
        features["additional_images_count"] = len(auction.additional_images)
        features["description_length"] = len(auction.description)
        features["description_quality"] = self._analyze_description_quality(
            auction.description
        )

        return features

    def _calculate_prediction(self, features: Dict) -> Decimal:
        """Calculate price prediction based on features"""
        base_price = features.get("category_avg_price", 100.0)
        starting_bid = features.get("starting_bid", 100.0)
        current_bid = features.get("current_bid", starting_bid)

        # Use the higher of category average or current bid as base
        base_price = max(base_price, current_bid, starting_bid)

        # Apply feature-based adjustments
        multiplier = 1.0

        # Category and market trends
        multiplier *= 1 + features.get("market_activity", 0) * 0.1

        # Condition factor
        condition_score = features.get("condition_score", 0.5)
        multiplier *= 0.7 + condition_score * 0.6  # Range: 0.7 to 1.3

        # Seller reputation
        seller_rating = features.get("seller_rating", 0.5)
        multiplier *= 0.9 + seller_rating * 0.2  # Range: 0.9 to 1.1

        # Title keywords and brand
        keyword_score = features.get("title_keywords", 0)
        multiplier *= 1 + keyword_score * 0.15

        if features.get("brand_detected"):
            multiplier *= 1.1

        # Starting bid influence
        starting_bid = features.get("starting_bid", base_price)
        if starting_bid > base_price * 0.8:
            multiplier *= 1.05  # High starting bid suggests confidence

        # Quality indicators
        if features.get("has_image"):
            multiplier *= 1.02

        additional_images = features.get("additional_images_count", 0)
        multiplier *= 1 + min(additional_images * 0.01, 0.05)

        description_quality = features.get("description_quality", 0.5)
        multiplier *= 0.98 + description_quality * 0.04

        # Calculate final prediction
        predicted_price = base_price * multiplier

        # Ensure reasonable bounds - prediction should be above current bid
        min_price = max(
            float(current_bid) * 1.1, float(starting_bid) * 1.2
        )  # At least 10% above current bid
        max_price = max(
            float(base_price) * 3.0, float(current_bid) * 5.0
        )  # Allow for significant growth

        predicted_price = max(min_price, min(predicted_price, max_price))

        return Decimal(str(round(predicted_price, 2)))

    def _calculate_confidence(self, features: Dict, auction: Auction) -> float:
        """Calculate confidence score for the prediction"""
        confidence = 0.5  # Base confidence

        # More data = higher confidence
        if features.get("category_avg_price", 0) > 0:
            confidence += 0.1

        if features.get("similar_recent_avg", 0) > 0:
            confidence += 0.15

        # Seller reputation affects confidence
        seller_rating = features.get("seller_rating", 0.5)
        confidence += seller_rating * 0.1

        # Quality indicators
        if features.get("has_image"):
            confidence += 0.05

        if features.get("description_quality", 0) > 0.7:
            confidence += 0.05

        # Brand detection
        if features.get("brand_detected"):
            confidence += 0.1

        # Market activity
        market_activity = features.get("market_activity", 0)
        confidence += market_activity * 0.05

        return min(confidence, 0.95)  # Cap at 95%

    def _get_category_average_price(self, category: str) -> float:
        """Get average final price for category"""
        recent_auctions = Auction.objects.filter(
            category=category,
            is_closed=True,
            created_at__gte=timezone.now() - timedelta(days=90),
        )

        avg_price = recent_auctions.aggregate(avg_price=Avg("current_bid"))["avg_price"]
        return float(avg_price) if avg_price else 100.0

    def _analyze_title_keywords(self, title: str) -> float:
        """Analyze title for valuable keywords"""
        valuable_keywords = [
            "new",
            "mint",
            "rare",
            "vintage",
            "limited",
            "edition",
            "original",
            "authentic",
            "genuine",
            "premium",
            "professional",
            "deluxe",
        ]

        title_lower = title.lower()
        keyword_count = sum(
            1 for keyword in valuable_keywords if keyword in title_lower
        )

        return min(keyword_count * 0.1, 0.5)  # Cap at 0.5

    def _detect_brand(self, title: str, description: str) -> bool:
        """Detect if a known brand is mentioned"""
        brands = [
            "apple",
            "samsung",
            "sony",
            "nike",
            "adidas",
            "rolex",
            "omega",
            "canon",
            "nikon",
            "bmw",
            "mercedes",
            "audi",
            "gucci",
            "prada",
        ]

        text = (title + " " + description).lower()
        return any(brand in text for brand in brands)

    def _get_condition_score(self, condition: str) -> float:
        """Convert condition to numeric score"""
        condition_scores = {
            "new": 1.0,
            "like new": 0.9,
            "excellent": 0.8,
            "very good": 0.7,
            "good": 0.6,
            "fair": 0.4,
            "poor": 0.2,
        }

        return condition_scores.get(condition.lower(), 0.5)

    def _get_seller_rating(self, seller) -> float:
        """Get seller rating (0.0 to 1.0)"""
        if hasattr(seller, "profile") and seller.profile.rating:
            return min(float(seller.profile.rating) / 5.0, 1.0)
        return 0.5  # Default neutral rating

    def _get_auction_duration_hours(self, auction: Auction) -> float:
        """Get auction duration in hours"""
        duration = auction.end_time - auction.start_time
        return duration.total_seconds() / 3600

    def _get_similar_recent_average(self, auction: Auction) -> float:
        """Get average price of similar recent auctions"""
        # Find auctions with similar titles or in same category
        similar_auctions = Auction.objects.filter(
            Q(category=auction.category) | Q(title__icontains=auction.title.split()[0]),
            is_closed=True,
            created_at__gte=timezone.now() - timedelta(days=30),
        ).exclude(id=auction.id)

        avg_price = similar_auctions.aggregate(avg_price=Avg("current_bid"))[
            "avg_price"
        ]
        return float(avg_price) if avg_price else 0.0

    def _get_market_activity_score(self, category: str) -> float:
        """Get market activity score for category"""
        recent_count = Auction.objects.filter(
            category=category, created_at__gte=timezone.now() - timedelta(days=7)
        ).count()

        # Normalize to 0-1 scale (assuming 50+ auctions per week is high activity)
        return min(recent_count / 50.0, 1.0)

    def _analyze_description_quality(self, description: str) -> float:
        """Analyze description quality"""
        if not description:
            return 0.0

        score = 0.0

        # Length factor
        if len(description) > 100:
            score += 0.3
        if len(description) > 300:
            score += 0.2

        # Detailed information indicators
        detail_indicators = ["condition", "size", "color", "model", "year", "brand"]
        found_details = sum(
            1 for indicator in detail_indicators if indicator in description.lower()
        )
        score += min(found_details * 0.1, 0.3)

        # Professional language (no excessive caps, good grammar)
        caps_ratio = sum(1 for c in description if c.isupper()) / len(description)
        if caps_ratio < 0.1:  # Less than 10% caps
            score += 0.2

        return min(score, 1.0)


# Global instance
price_prediction_service = PricePredictionService()
