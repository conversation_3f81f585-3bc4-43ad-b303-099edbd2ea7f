{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport { ascendingDefined, compareDefined } from \"./sort.js\";\nexport default function rank(values, valueof = ascending) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = ascending;\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n  let k, r;\n  values = Uint32Array.from(V, (_, i) => i);\n  // Risky chaining due to Safari 14 https://github.com/d3/d3-array/issues/123\n  values.sort(valueof === ascending ? (i, j) => ascendingDefined(V[i], V[j]) : compareDefined(compareIndex));\n  values.forEach((j, i) => {\n    const c = compareIndex(j, k === undefined ? j : k);\n    if (c >= 0) {\n      if (k === undefined || c > 0) k = j, r = i;\n      R[j] = r;\n    } else {\n      R[j] = NaN;\n    }\n  });\n  return R;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}