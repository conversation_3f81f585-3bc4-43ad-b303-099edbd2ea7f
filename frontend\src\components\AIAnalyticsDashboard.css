.ai-analytics-dashboard {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.ai-analytics-dashboard.loading,
.ai-analytics-dashboard.error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
}

.dashboard-header h2 {
  color: #333;
  font-size: 2.2em;
  margin-bottom: 10px;
}

.dashboard-header p {
  color: #666;
  font-size: 1.1em;
}

.analytics-overview {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card.accuracy {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.stat-card.model {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

.stat-card.high-confidence {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
}

.stat-card.medium-confidence {
  background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
}

.stat-icon {
  font-size: 2.5em;
  opacity: 0.9;
}

.stat-content h3 {
  margin: 0;
  font-size: 2em;
  font-weight: 700;
}

.stat-content p {
  margin: 5px 0;
  font-size: 1.1em;
  font-weight: 500;
}

.stat-content small {
  opacity: 0.8;
  font-size: 0.85em;
}

.confidence-chart {
  margin-top: 30px;
}

.confidence-chart h3 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.chart-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.confidence-bar-chart {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 200px;
  gap: 20px;
}

.bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 120px;
}

.bar-group .bar {
  width: 60px;
  min-height: 20px;
  border-radius: 4px 4px 0 0;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.bar.high {
  background: linear-gradient(to top, #4CAF50, #66BB6A);
}

.bar.medium {
  background: linear-gradient(to top, #FF9800, #FFB74D);
}

.bar.low {
  background: linear-gradient(to top, #f44336, #EF5350);
}

.bar-group label {
  font-size: 0.85em;
  color: #666;
  margin-bottom: 5px;
  text-align: center;
}

.bar-group span {
  font-weight: 600;
  color: #333;
  font-size: 1.1em;
}

.recent-predictions {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.recent-predictions h3 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.no-predictions {
  text-align: center;
  color: #666;
  padding: 40px;
  font-style: italic;
}

.predictions-table {
  overflow-x: auto;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #eee;
  align-items: center;
  transition: background-color 0.3s ease;
}

.table-row:hover {
  background: #f8f9fa;
}

.auction-info strong {
  display: block;
  color: #333;
  margin-bottom: 2px;
}

.auction-info small {
  color: #666;
  text-transform: capitalize;
}

.predicted-price {
  font-weight: 600;
  color: #4CAF50;
  font-size: 1.1em;
}

.confidence-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.85em;
  font-weight: 600;
  text-align: center;
  min-width: 50px;
}

.prediction-date {
  color: #666;
  font-size: 0.9em;
}

.dashboard-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

.refresh-btn,
.export-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1em;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn:hover,
.export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.export-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.export-btn:hover {
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-analytics-dashboard {
    padding: 15px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .stat-card {
    padding: 15px;
  }
  
  .stat-icon {
    font-size: 2em;
  }
  
  .stat-content h3 {
    font-size: 1.5em;
  }
  
  .confidence-bar-chart {
    height: 150px;
    gap: 10px;
  }
  
  .bar-group .bar {
    width: 40px;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 10px;
    text-align: center;
  }
  
  .table-header {
    display: none;
  }
  
  .table-row {
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 10px;
    border: none;
  }
  
  .dashboard-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .refresh-btn,
  .export-btn {
    width: 200px;
    justify-content: center;
  }
}
