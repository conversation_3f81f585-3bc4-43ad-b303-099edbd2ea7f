import React, { useState } from "react";
import { Card, Badge, Button, Modal } from "react-bootstrap";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaReg<PERSON><PERSON>t, FaEye, FaClock, FaGavel } from "react-icons/fa";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { formatAuctionPrice } from "../utils/currency";
import { incrementAuctionViews } from "../api/auctions";

const AuctionCard = ({ auction, onWatchToggle, isWatched = false }) => {
  const { user } = useAuth();
  const location = useLocation();
  const [showModal, setShowModal] = useState(false);

  const formatTimeRemaining = (timeRemaining) => {
    if (!timeRemaining) return "Ended";
    const { days, hours, minutes } = timeRemaining;
    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const getAuctionTypeColor = (type) => {
    const colors = {
      standard: "primary",
      reserve: "warning",
      buy_now: "success",
      sealed_bid: "info",
      reverse: "danger",
    };
    return colors[type] || "secondary";
  };

  const handleWatchToggle = () => {
    if (user && onWatchToggle) {
      onWatchToggle(auction.id);
    }
  };

  // Create navigation state to help the detail page know where we came from
  const getNavigationState = () => {
    const currentPath = location.pathname;

    if (currentPath.includes("/auctions/category/")) {
      const category = currentPath.split("/auctions/category/")[1];
      return {
        from: {
          path: currentPath,
          label: `Back to ${
            category.charAt(0).toUpperCase() + category.slice(1)
          } Auctions`,
          icon: "FaTags",
        },
      };
    } else if (currentPath === "/auctions") {
      return {
        from: {
          path: "/auctions",
          label: "Back to All Auctions",
          icon: "FaGavel",
        },
      };
    } else if (currentPath === "/home" || currentPath === "/") {
      return {
        from: {
          path: "/home",
          label: "Back to Home",
          icon: "FaHome",
        },
      };
    }

    return null;
  };

  const handleQuickView = async () => {
    setShowModal(true);
    // Increment views count when quick view is opened
    await incrementAuctionViews(auction.id);
  };

  const handleViewDetails = async () => {
    // Increment views count when view details is clicked
    await incrementAuctionViews(auction.id);
  };

  return (
    <>
      <Card className="h-100 auction-card shadow-sm">
        <div className="position-relative">
          <Card.Img
            variant="top"
            src={
              auction.image && auction.image.trim() !== ""
                ? auction.image
                : "/placeholder-image.svg"
            }
            style={{ height: "200px", objectFit: "contain" }}
            onError={(e) => {
              e.target.src = "/placeholder-image.svg";
            }}
          />

          {/* Auction Type Badge */}
          <Badge
            bg={getAuctionTypeColor(auction.auction_type)}
            className="position-absolute top-0 start-0 m-2"
          >
            {auction.auction_type?.replace("_", " ").toUpperCase() ||
              "STANDARD"}
          </Badge>

          {/* Featured Badge */}
          {auction.featured && (
            <Badge bg="danger" className="position-absolute top-0 end-0 m-2">
              FEATURED
            </Badge>
          )}

          {/* Watch Button */}
          {user && (
            <Button
              variant="light"
              size="sm"
              className="position-absolute bottom-0 end-0 m-2 rounded-circle"
              onClick={handleWatchToggle}
            >
              {isWatched ? <FaHeart className="text-danger" /> : <FaRegHeart />}
            </Button>
          )}
        </div>

        <Card.Body className="d-flex flex-column">
          <Card.Title className="text-truncate" title={auction.title}>
            {auction.title}
          </Card.Title>

          <Card.Text className="text-muted small mb-2">
            by {auction.owner}
            {auction.owner_rating > 0 && (
              <span className="ms-1">⭐ {auction.owner_rating.toFixed(1)}</span>
            )}
          </Card.Text>

          <div className="mb-2">
            <div className="d-flex justify-content-between align-items-center">
              <span className="text-muted small">Current Bid:</span>
              <strong className="text-success">
                {formatAuctionPrice(auction.current_bid)}
              </strong>
            </div>

            {auction.reserve_price && !auction.reserve_met && (
              <div className="text-warning small">
                Reserve not met ({formatAuctionPrice(auction.reserve_price)})
              </div>
            )}

            {auction.buy_now_price && (
              <div className="d-flex justify-content-between align-items-center">
                <span className="text-muted small">Buy Now:</span>
                <strong className="text-primary">
                  {formatAuctionPrice(auction.buy_now_price)}
                </strong>
              </div>
            )}
          </div>

          <div className="mb-2">
            <div className="d-flex justify-content-between text-muted small">
              <span>
                <FaGavel className="me-1" />
                {auction.total_bids || 0} bids
              </span>
              <span>
                <FaEye className="me-1" />
                {auction.views_count || 0} views
              </span>
              <span>
                <FaHeart className="me-1" />
                {auction.watchers_count || 0} watching
              </span>
            </div>
          </div>

          <div className="mb-3">
            <div className="d-flex justify-content-between align-items-center">
              <span className="text-muted small">
                <FaClock className="me-1" />
                {auction.is_active ? "Ends in:" : "Ended"}
              </span>
              <span
                className={`small fw-bold ${
                  auction.is_active ? "text-danger" : "text-muted"
                }`}
              >
                {formatTimeRemaining(auction.time_remaining)}
              </span>
            </div>
          </div>

          <div className="mt-auto">
            <div className="d-grid gap-2">
              <Button
                variant="outline-primary"
                size="sm"
                onClick={handleQuickView}
              >
                Quick View
              </Button>

              <Link
                to={`/auction/${auction.id}`}
                state={getNavigationState()}
                className="btn btn-primary btn-sm"
                onClick={handleViewDetails}
              >
                View Details
              </Link>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Quick View Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{auction.title}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="row">
            <div className="col-md-6">
              <img
                src={
                  auction.image && auction.image.trim() !== ""
                    ? auction.image
                    : "/placeholder-image.svg"
                }
                alt={auction.title}
                className="img-fluid rounded"
                onError={(e) => {
                  e.target.src = "/placeholder-image.svg";
                }}
              />
            </div>
            <div className="col-md-6">
              <h6>Description</h6>
              <p className="text-muted">{auction.description}</p>

              <h6>Details</h6>
              <ul className="list-unstyled">
                <li>
                  <strong>Category:</strong> {auction.category}
                </li>
                <li>
                  <strong>Current Bid:</strong>{" "}
                  {formatAuctionPrice(auction.current_bid)}
                </li>
                <li>
                  <strong>Ends:</strong>{" "}
                  {new Date(auction.end_time).toLocaleString()}
                </li>
                <li>
                  <strong>Seller:</strong> {auction.owner}
                </li>
                <li className="d-flex align-items-center gap-3 mt-2">
                  <span>
                    <FaGavel className="me-1 text-muted" />
                    {auction.total_bids || 0} bids
                  </span>
                  <span>
                    <FaHeart className="me-1 text-muted" />
                    {auction.watchers_count || 0} watching
                  </span>
                  <span>
                    <FaEye className="me-1 text-muted" />
                    {auction.views_count || 0} views
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-between">
          <div>
            {/* Watchlist Button in Quick View */}
            {user && (
              <Button
                variant={isWatched ? "danger" : "outline-danger"}
                size="sm"
                onClick={handleWatchToggle}
                className="d-flex align-items-center"
                title={isWatched ? "Remove from watchlist" : "Add to watchlist"}
              >
                {isWatched ? (
                  <FaHeart className="me-1" />
                ) : (
                  <FaRegHeart className="me-1" />
                )}
                {isWatched ? "Remove from Watchlist" : "Add to Watchlist"}
              </Button>
            )}
          </div>
          <div className="d-flex gap-2">
            <Button variant="secondary" onClick={() => setShowModal(false)}>
              Close
            </Button>
            <Link
              to={`/auction/${auction.id}`}
              state={getNavigationState()}
              className="btn btn-primary"
              onClick={handleViewDetails}
            >
              View Full Details
            </Link>
          </div>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default AuctionCard;
