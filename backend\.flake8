[flake8]
max-line-length = 127
exclude = 
    venv,
    migrations,
    __pycache__,
    .git,
    .tox,
    .eggs,
    *.egg,
    build,
    dist,
    .venv
ignore = 
    E203,  # whitespace before ':'
    E501,  # line too long (handled by black)
    W503,  # line break before binary operator
    W504,  # line break after binary operator
per-file-ignores =
    __init__.py:F401
    settings.py:E501
    settings_test.py:E501
