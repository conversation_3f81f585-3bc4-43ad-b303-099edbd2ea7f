#!/usr/bin/env python3
"""
Test chat display functionality
"""

import requests
import json
import time

def test_chat_display():
    """Test sending a message and checking if it displays correctly"""
    
    base_url = "http://127.0.0.1:8000/api"
    
    print("🎨 Testing Chat Display")
    print("=" * 50)
    
    # Login
    login_data = {
        "username": "Arshitha_T",
        "password": "arshitha@_333"
    }
    
    try:
        login_response = requests.post(f"{base_url}/login/", json=login_data)
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ Login successful")
        else:
            print("❌ Login failed")
            return
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Get room
    try:
        auctions_response = requests.get(f"{base_url}/auctions/")
        auction_id = auctions_response.json()['results'][0]['id']
        
        chat_response = requests.get(f"{base_url}/chat-rooms/?auction={auction_id}")
        room_id = chat_response.json()['results'][0]['id']
        
        print(f"📋 Using room {room_id}")
    except Exception as e:
        print(f"❌ Setup error: {e}")
        return
    
    # Send a distinctive test message
    test_message = f"🎨 DISPLAY TEST {time.strftime('%H:%M:%S')} - This should appear in chat!"
    
    try:
        print(f"📤 Sending test message: {test_message}")
        
        message_response = requests.post(
            f"{base_url}/chat-messages/",
            json={
                "room": room_id,
                "message": test_message,
                "message_type": "text"
            },
            headers=headers
        )
        
        if message_response.status_code == 201:
            message_data = message_response.json()
            print(f"✅ Message sent successfully")
            print(f"   Message ID: {message_data.get('id')}")
            print(f"   Sender: {message_data.get('sender_username')}")
            print(f"   Content: {message_data.get('message')}")
            print(f"   Type: {message_data.get('message_type')}")
            print(f"   Time: {message_data.get('time_ago')}")
        else:
            print(f"❌ Message send failed: {message_response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ Send error: {e}")
        return
    
    print(f"\n🎯 Frontend Debugging Instructions:")
    print(f"1. Open the auction detail page in your browser")
    print(f"2. Open Developer Tools (F12)")
    print(f"3. Go to Console tab")
    print(f"4. Look for these logs:")
    print(f"   📥 Loading messages for room: {room_id}")
    print(f"   📥 Loaded [number] messages")
    print(f"   📋 Message data sample: [array of messages]")
    print(f"   🎨 Rendering messages: [number] messages")
    print(f"   👤 Current user: [username]")
    print(f"   🎨 Rendering message [index]: [message object]")
    print(f"")
    print(f"5. Your test message should appear as:")
    print(f"   Message: '{test_message}'")
    print(f"   Sender: 'Arshitha_T'")
    print(f"")
    print(f"6. If messages are loading but not displaying:")
    print(f"   - Check if 'messages.length' > 0 in console")
    print(f"   - Check if message objects have required fields")
    print(f"   - Look for any React rendering errors")
    print(f"")
    print(f"7. Try clicking the 🔄 refresh button in chat header")

if __name__ == "__main__":
    test_chat_display()
