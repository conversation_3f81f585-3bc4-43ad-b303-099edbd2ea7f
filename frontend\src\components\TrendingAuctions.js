import React, { useState, useEffect } from 'react';
import { extendedAuctionService } from '../services/extendedAuctionService';
import AuctionCard from './AuctionCard';
import './TrendingAuctions.css';

const TrendingAuctions = () => {
  const [trendingAuctions, setTrendingAuctions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [wsConnection, setWsConnection] = useState(null);

  useEffect(() => {
    fetchTrendingAuctions();
    setupWebSocketConnection();

    return () => {
      if (wsConnection) {
        wsConnection.close();
      }
    };
  }, []);

  const fetchTrendingAuctions = async () => {
    try {
      setLoading(true);
      const response = await extendedAuctionService.getTrendingAuctions();
      if (response.success) {
        setTrendingAuctions(response.data);
      } else {
        setError('Failed to fetch trending auctions');
      }
    } catch (err) {
      setError('Error loading trending auctions');
      console.error('Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const setupWebSocketConnection = () => {
    try {
      const socket = extendedAuctionService.subscribeToTrendingUpdates((data) => {
        // Update trending auction in real-time
        setTrendingAuctions(prev => {
          const updated = prev.map(auction => 
            auction.id === data.auction_id 
              ? { ...auction, current_bid: data.current_bid, bid_count: data.bid_count }
              : auction
          );
          return updated;
        });
      });
      setWsConnection(socket);
    } catch (err) {
      console.error('WebSocket connection error:', err);
    }
  };

  if (loading) {
    return (
      <div className="trending-auctions-loading">
        <div className="loading-spinner"></div>
        <p>Loading trending auctions...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="trending-auctions-error">
        <p>❌ {error}</p>
        <button onClick={fetchTrendingAuctions} className="retry-btn">
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="trending-auctions">
      <div className="trending-header">
        <h2>🔥 Trending Auctions</h2>
        <p>Most active auctions in the last 24 hours</p>
      </div>

      {trendingAuctions.length === 0 ? (
        <div className="no-trending">
          <p>No trending auctions at the moment</p>
        </div>
      ) : (
        <div className="trending-grid">
          {trendingAuctions.map((auction) => (
            <div key={auction.id} className="trending-item">
              <AuctionCard auction={auction} />
              <div className="trending-stats">
                <span className="bid-count">
                  🎯 {auction.bid_count || 0} bids
                </span>
                <span className="activity-indicator">
                  📈 Hot
                </span>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="trending-footer">
        <button onClick={fetchTrendingAuctions} className="refresh-btn">
          🔄 Refresh
        </button>
      </div>
    </div>
  );
};

export default TrendingAuctions;
