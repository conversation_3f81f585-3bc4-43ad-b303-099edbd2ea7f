#!/usr/bin/env python3
"""
Quick test for chat functionality
"""

import requests
import json

def test_chat_functionality():
    """Test chat room and message creation"""
    
    base_url = "http://127.0.0.1:8000/api"
    
    print("🔍 Testing Chat Functionality")
    print("=" * 50)
    
    # First, login to get a token
    login_data = {
        "username": "Arshitha_T",  # Use existing user
        "password": "arshitha@_333"
    }
    
    try:
        login_response = requests.post(f"{base_url}/login/", json=login_data)
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ Login successful")
        else:
            print("❌ Login failed, testing without auth")
            headers = {}
    except:
        print("❌ Login failed, testing without auth")
        headers = {}
    
    # Get an auction to test with
    try:
        auctions_response = requests.get(f"{base_url}/auctions/")
        if auctions_response.status_code == 200:
            auctions = auctions_response.json().get('results', [])
            if auctions:
                auction_id = auctions[0]['id']
                print(f"📋 Using auction ID: {auction_id}")
                
                # Test chat room creation/retrieval
                chat_room_data = {
                    "auction": auction_id,
                    "is_active": True
                }
                
                chat_response = requests.post(
                    f"{base_url}/chat-rooms/",
                    json=chat_room_data,
                    headers=headers
                )
                
                print(f"📊 Chat Room Status: {chat_response.status_code}")
                
                if chat_response.status_code in [200, 201]:
                    room_data = chat_response.json()
                    room_id = room_data.get('id')
                    print(f"✅ Chat room ready: {room_id}")
                    
                    # Test message creation
                    if headers:  # Only test if authenticated
                        message_data = {
                            "room": room_id,
                            "message": "Test message from API",
                            "message_type": "text"
                        }
                        
                        message_response = requests.post(
                            f"{base_url}/chat-messages/",
                            json=message_data,
                            headers=headers
                        )
                        
                        print(f"📊 Message Status: {message_response.status_code}")
                        
                        if message_response.status_code == 201:
                            print("✅ Message sent successfully!")
                            print(f"📥 Message: {message_response.json()}")
                        else:
                            print("❌ Message failed")
                            print(f"📥 Error: {message_response.text}")
                    else:
                        print("⚠️ Skipping message test (no auth)")
                        
                else:
                    print("❌ Chat room creation failed")
                    print(f"📥 Error: {chat_response.text}")
            else:
                print("❌ No auctions found")
        else:
            print("❌ Failed to get auctions")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_chat_functionality()
