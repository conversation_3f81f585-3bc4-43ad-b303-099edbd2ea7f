"""
Management command to fix auction winners for closed auctions
This command will set the winner field for auctions that are closed but don't have a winner set
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from auction.models import Auction, Payment


class Command(BaseCommand):
    help = 'Fix auction winners for closed auctions without winners set'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be fixed without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS('🔍 Checking for auctions that need winner fixes...')
        )
        
        # Find closed auctions without winners but with bids
        auctions_to_fix = Auction.objects.filter(
            is_closed=True,
            winner__isnull=True,
            bids__isnull=False
        ).distinct()
        
        if not auctions_to_fix.exists():
            self.stdout.write(
                self.style.SUCCESS('✅ No auctions need winner fixes!')
            )
            return
        
        self.stdout.write(
            self.style.WARNING(f'📊 Found {auctions_to_fix.count()} auctions that need winner fixes')
        )
        
        fixed_count = 0
        payment_created_count = 0
        
        for auction in auctions_to_fix:
            # Get the highest bid
            top_bid = auction.bids.order_by("-amount", "-created_at").first()
            
            if top_bid:
                self.stdout.write(
                    f'🎯 Auction: {auction.title}'
                )
                self.stdout.write(
                    f'   Winner: {top_bid.user.username} with bid ${top_bid.amount}'
                )
                
                if not dry_run:
                    # Set the winner
                    auction.winner = top_bid.user
                    auction.current_bid = top_bid.amount
                    auction.save()
                    
                    # Create payment record if it doesn't exist
                    payment, created = Payment.objects.get_or_create(
                        user=top_bid.user,
                        auction=auction,
                        defaults={
                            'amount': top_bid.amount,
                            'payment_status': "pending",
                        }
                    )
                    
                    if created:
                        payment_created_count += 1
                        self.stdout.write(
                            f'   💳 Created payment record for ${payment.amount}'
                        )
                    else:
                        self.stdout.write(
                            f'   💳 Payment record already exists'
                        )
                    
                    # Set payment deadline
                    auction.set_payment_deadline()
                    
                    fixed_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'   ✅ Fixed auction winner')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'   🔄 Would fix this auction (dry run)')
                    )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'🔄 DRY RUN: Would fix {auctions_to_fix.count()} auctions')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'✅ Fixed {fixed_count} auction winners')
            )
            self.stdout.write(
                self.style.SUCCESS(f'💳 Created {payment_created_count} payment records')
            )
        
        # Also check for auctions that ended but aren't marked as closed
        now = timezone.now()
        ended_auctions = Auction.objects.filter(
            end_time__lte=now,
            is_closed=False
        )
        
        if ended_auctions.exists():
            self.stdout.write(
                self.style.WARNING(f'⚠️  Found {ended_auctions.count()} auctions that ended but aren\'t closed')
            )
            
            if not dry_run:
                for auction in ended_auctions:
                    top_bid = auction.bids.order_by("-amount", "-created_at").first()
                    if top_bid:
                        auction.winner = top_bid.user
                        auction.current_bid = top_bid.amount
                    auction.is_closed = True
                    auction.save()
                    
                    if top_bid:
                        # Create payment record
                        Payment.objects.get_or_create(
                            user=top_bid.user,
                            auction=auction,
                            defaults={
                                'amount': top_bid.amount,
                                'payment_status': "pending",
                            }
                        )
                        auction.set_payment_deadline()
                
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Closed {ended_auctions.count()} ended auctions')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'🔄 Would close {ended_auctions.count()} ended auctions (dry run)')
                )
        
        self.stdout.write(
            self.style.SUCCESS('🎉 Auction winner fix completed!')
        )
