#!/usr/bin/env python3
"""
Debug script to check category data and routing issues
"""

import os
import sys
import django
import requests

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from auction.models import Auction, Category

def check_category_data():
    """Check what category data is being returned"""
    print("🔍 Checking Category Data")
    print("=" * 50)
    
    # Check database categories
    categories = Category.objects.all()
    print(f"📊 Database Categories: {categories.count()}")
    
    for cat in categories:
        print(f"   - {cat.name} (slug: {cat.slug})")
    
    # Check auction categories
    auction_categories = Auction.objects.values_list('category', flat=True).distinct()
    print(f"\n📦 Auction Categories in Database:")
    for cat in auction_categories:
        if cat:
            count = Auction.objects.filter(category=cat).count()
            print(f"   - {cat}: {count} auctions")

def test_categories_api():
    """Test the categories API endpoint"""
    print("\n🌐 Testing Categories API")
    print("=" * 50)
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/categories-list/", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response successful!")
            print(f"📄 Categories returned: {len(data)}")
            
            for i, cat in enumerate(data[:6]):  # Show first 6 like home page
                print(f"\n📂 Category {i+1}:")
                print(f"   Name: {cat.get('name')}")
                print(f"   Slug: {cat.get('slug')}")
                print(f"   Auction Count: {cat.get('auction_count', 0)}")
                print(f"   Image: {cat.get('image', 'No image')[:50]}...")
                
                # Check if slug is valid for routing
                slug = cat.get('slug')
                if slug:
                    print(f"   Route would be: /auctions/category/{slug}")
                else:
                    print(f"   ⚠️ No slug - would use: {cat.get('name', '').lower()}")
            
            return data
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ API request error: {e}")
        return None

def test_category_routing():
    """Test if category routes work"""
    print("\n🛣️ Testing Category Routes")
    print("=" * 50)
    
    test_categories = [
        'electronics',
        'fashion', 
        'art',
        'collectibles',
        'jewelry',
        'home_garden'
    ]
    
    for category in test_categories:
        try:
            url = f"http://127.0.0.1:8000/api/auctions/?category={category}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                count = data.get('count', 0)
                print(f"✅ {category}: {count} auctions found")
            else:
                print(f"❌ {category}: API error {response.status_code}")
                
        except Exception as e:
            print(f"❌ {category}: Request failed - {e}")

def check_frontend_routing():
    """Check if frontend routes are accessible"""
    print("\n🌐 Testing Frontend Routes")
    print("=" * 50)
    
    test_routes = [
        'http://127.0.0.1:3000/auctions',
        'http://127.0.0.1:3000/auctions/category/electronics',
        'http://127.0.0.1:3000/auctions/category/fashion',
        'http://127.0.0.1:3000/auctions/category/art'
    ]
    
    for route in test_routes:
        try:
            response = requests.get(route, timeout=5)
            if response.status_code == 200:
                print(f"✅ {route}: Accessible")
            else:
                print(f"❌ {route}: Status {response.status_code}")
        except Exception as e:
            print(f"❌ {route}: Failed - {e}")

def generate_fix_recommendations():
    """Generate recommendations to fix category click issues"""
    print("\n💡 Fix Recommendations")
    print("=" * 50)
    
    # Get API data
    api_data = test_categories_api()
    
    print("\n🎯 Potential Issues & Solutions:")
    
    print("\n1. **Category Slug Issues**")
    if api_data:
        for cat in api_data[:6]:
            slug = cat.get('slug')
            name = cat.get('name', '')
            if not slug:
                print(f"   ⚠️ {name}: Missing slug - will use '{name.lower()}'")
            else:
                print(f"   ✅ {name}: Has slug '{slug}'")
    
    print("\n2. **Navigation Issues**")
    print("   - Check if React Router is properly configured")
    print("   - Verify /auctions/category/:category route exists")
    print("   - Check if handleCategoryClick function is called")
    
    print("\n3. **API Endpoint Issues**")
    print("   - Verify categories-list API returns proper data")
    print("   - Check if auction filtering by category works")
    print("   - Ensure category parameter is properly handled")
    
    print("\n4. **Frontend Issues**")
    print("   - Check browser console for JavaScript errors")
    print("   - Verify CategoryFlipCard onClick is properly bound")
    print("   - Check if navigation state is preserved")
    
    print("\n✅ **Quick Fixes to Try:**")
    print("1. Add console.log in handleCategoryClick to debug")
    print("2. Check browser network tab for failed requests")
    print("3. Verify category slugs match between frontend and backend")
    print("4. Test direct navigation to /auctions/category/electronics")

if __name__ == "__main__":
    print("🔍 Category Click Debug Analysis")
    print("=" * 60)
    
    # Check database
    check_category_data()
    
    # Test API
    test_categories_api()
    
    # Test routing
    test_category_routing()
    
    # Test frontend
    check_frontend_routing()
    
    # Generate recommendations
    generate_fix_recommendations()
    
    print("\n" + "=" * 60)
    print("🎉 Debug analysis completed!")
