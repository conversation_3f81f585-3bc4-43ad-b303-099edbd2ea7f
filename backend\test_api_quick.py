#!/usr/bin/env python3
"""Quick API Test for Fraud Detection"""

import requests

def test_api():
    try:
        r = requests.get('http://127.0.0.1:8000/api/fraud-detection/')
        print('API Status:', r.status_code)
        
        if r.status_code == 200:
            data = r.json()
            print('Total Records:', len(data.get('results', [])))
            print('\nFraud Detection Records:')
            for item in data.get('results', []):
                print(f"- {item.get('fraud_type')} (Risk: {item.get('risk_score')}, Status: {item.get('status')})")
        else:
            print('API Error:', r.text)
            
    except Exception as e:
        print('Error:', e)

if __name__ == "__main__":
    test_api()
