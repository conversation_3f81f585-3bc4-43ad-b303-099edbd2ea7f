{"summary": {"total_tests": 14, "passed_tests": 12, "success_rate": 85.71428571428571, "timestamp": "2025-05-28T11:43:57.439297"}, "results": {"Backend Health Check": {"passed": true, "message": "", "timestamp": "2025-05-28T11:43:55.925599"}, "API Endpoint: API Root": {"passed": true, "message": "Status: 200", "timestamp": "2025-05-28T11:43:55.942765"}, "API Endpoint: Auctions List": {"passed": true, "message": "Status: 200", "timestamp": "2025-05-28T11:43:56.100835"}, "API Endpoint: Categories": {"passed": true, "message": "Status: 200", "timestamp": "2025-05-28T11:43:56.205300"}, "API Endpoint: Search Filters": {"passed": true, "message": "Status: 200", "timestamp": "2025-05-28T11:43:56.307535"}, "Auction Data Structure": {"passed": true, "message": "Found 15 auctions", "timestamp": "2025-05-28T11:43:56.457938"}, "Auction Field Validation": {"passed": true, "message": "All required fields present", "timestamp": "2025-05-28T11:43:56.458161"}, "Admin Auction Ownership": {"passed": true, "message": "10/10 auctions owned by admin", "timestamp": "2025-05-28T11:43:56.458357"}, "Search Functionality": {"passed": true, "message": "Search status: 200", "timestamp": "2025-05-28T11:43:56.577122"}, "Category Filtering": {"passed": false, "message": "Category filter status: 400", "timestamp": "2025-05-28T11:43:56.597751"}, "Registration Endpoint": {"passed": true, "message": "Status: 405", "timestamp": "2025-05-28T11:43:56.603723"}, "Login Endpoint": {"passed": true, "message": "Status: 405", "timestamp": "2025-05-28T11:43:56.625478"}, "Payment Timeout System": {"passed": false, "message": "Requested setting INSTALLED_APPS, but settings are not configured. You must either define the environment variable DJANGO_SETTINGS_MODULE or call settings.configure() before accessing settings.", "timestamp": "2025-05-28T11:43:57.274109"}, "Database Migrations": {"passed": true, "message": "Payment timeout fields accessible via API", "timestamp": "2025-05-28T11:43:57.436843"}}}