#!/usr/bin/env python3
"""
Watchlist API Test Script
Tests the watchlist functionality including add, remove, and list operations.
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:8000/api"
USERNAME = "aisha_admin"
PASSWORD = "aisha2024!"

def test_watchlist_api():
    print("🧪 Testing Watchlist API...")
    
    # Step 1: Login
    print("\n1️⃣ Logging in...")
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    try:
        login_response = requests.post(f"{BASE_URL}/login/", json=login_data)
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data["access"]
            print(f"✅ Login successful! Token: {access_token[:20]}...")
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Step 2: Get available auctions
    print("\n2️⃣ Getting available auctions...")
    try:
        auctions_response = requests.get(f"{BASE_URL}/auctions/", headers=headers)
        
        if auctions_response.status_code == 200:
            auctions_data = auctions_response.json()
            auctions = auctions_data.get('results', auctions_data) if isinstance(auctions_data, dict) else auctions_data
            
            if auctions:
                test_auction_id = auctions[0]['id']
                test_auction_title = auctions[0]['title']
                print(f"✅ Found {len(auctions)} auctions")
                print(f"🎯 Using auction: {test_auction_title} (ID: {test_auction_id})")
            else:
                print("❌ No auctions found")
                return
        else:
            print(f"❌ Failed to get auctions: {auctions_response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ Error getting auctions: {e}")
        return
    
    # Step 3: Get current watchlist
    print("\n3️⃣ Getting current watchlist...")
    try:
        watchlist_response = requests.get(f"{BASE_URL}/watchlist/", headers=headers)
        
        if watchlist_response.status_code == 200:
            watchlist_data = watchlist_response.json()
            watchlist = watchlist_data.get('results', watchlist_data) if isinstance(watchlist_data, dict) else watchlist_data
            print(f"✅ Current watchlist has {len(watchlist)} items")
            
            # Check if test auction is already in watchlist
            existing_item = None
            for item in watchlist:
                if item['auction'] == test_auction_id:
                    existing_item = item
                    break
                    
            if existing_item:
                print(f"📝 Test auction already in watchlist (ID: {existing_item['id']})")
            else:
                print("📝 Test auction not in watchlist")
                
        else:
            print(f"❌ Failed to get watchlist: {watchlist_response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ Error getting watchlist: {e}")
        return
    
    # Step 4: Add to watchlist (if not already there)
    if not existing_item:
        print("\n4️⃣ Adding auction to watchlist...")
        try:
            add_data = {
                "auction": test_auction_id
            }
            
            add_response = requests.post(f"{BASE_URL}/watchlist/", json=add_data, headers=headers)
            
            if add_response.status_code == 201:
                new_item = add_response.json()
                print(f"✅ Added to watchlist successfully!")
                print(f"📝 Watchlist item ID: {new_item['id']}")
                print(f"📝 Auction: {new_item.get('auction_title', 'Unknown')}")
                existing_item = new_item
            else:
                print(f"❌ Failed to add to watchlist: {add_response.status_code}")
                print(f"Response: {add_response.text}")
                return
                
        except Exception as e:
            print(f"❌ Error adding to watchlist: {e}")
            return
    else:
        print("\n4️⃣ Skipping add (already in watchlist)")
    
    # Step 5: Verify watchlist update
    print("\n5️⃣ Verifying watchlist update...")
    try:
        verify_response = requests.get(f"{BASE_URL}/watchlist/", headers=headers)
        
        if verify_response.status_code == 200:
            updated_watchlist_data = verify_response.json()
            updated_watchlist = updated_watchlist_data.get('results', updated_watchlist_data) if isinstance(updated_watchlist_data, dict) else updated_watchlist_data
            print(f"✅ Updated watchlist has {len(updated_watchlist)} items")
            
            # Find our test item
            found_item = None
            for item in updated_watchlist:
                if item['auction'] == test_auction_id:
                    found_item = item
                    break
                    
            if found_item:
                print(f"✅ Test auction found in watchlist")
                print(f"📝 Item details: {found_item.get('auction_title', 'Unknown')} - {found_item.get('auction_current_bid', 'N/A')}")
            else:
                print("❌ Test auction not found in updated watchlist")
                
        else:
            print(f"❌ Failed to verify watchlist: {verify_response.status_code}")
            
    except Exception as e:
        print(f"❌ Error verifying watchlist: {e}")
    
    # Step 6: Remove from watchlist
    print("\n6️⃣ Removing from watchlist...")
    try:
        if existing_item:
            remove_response = requests.delete(f"{BASE_URL}/watchlist/{existing_item['id']}/", headers=headers)
            
            if remove_response.status_code == 204:
                print(f"✅ Removed from watchlist successfully!")
            else:
                print(f"❌ Failed to remove from watchlist: {remove_response.status_code}")
                print(f"Response: {remove_response.text}")
        else:
            print("❌ No item to remove")
            
    except Exception as e:
        print(f"❌ Error removing from watchlist: {e}")
    
    # Step 7: Final verification
    print("\n7️⃣ Final verification...")
    try:
        final_response = requests.get(f"{BASE_URL}/watchlist/", headers=headers)
        
        if final_response.status_code == 200:
            final_watchlist_data = final_response.json()
            final_watchlist = final_watchlist_data.get('results', final_watchlist_data) if isinstance(final_watchlist_data, dict) else final_watchlist_data
            print(f"✅ Final watchlist has {len(final_watchlist)} items")
            
            # Check if test auction is still there
            still_there = any(item['auction'] == test_auction_id for item in final_watchlist)
            if not still_there:
                print("✅ Test auction successfully removed from watchlist")
            else:
                print("❌ Test auction still in watchlist")
                
        else:
            print(f"❌ Failed final verification: {final_response.status_code}")
            
    except Exception as e:
        print(f"❌ Error in final verification: {e}")
    
    print("\n🎉 Watchlist API test completed!")

if __name__ == "__main__":
    test_watchlist_api()
