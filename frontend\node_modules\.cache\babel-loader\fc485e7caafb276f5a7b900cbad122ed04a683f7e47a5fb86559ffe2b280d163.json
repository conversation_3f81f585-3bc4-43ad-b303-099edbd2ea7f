{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Online_Auction_System\\\\frontend\\\\src\\\\pages\\\\Home.js\";\nimport React, { useEffect, useState, useCallback } from \"react\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { fetchFeaturedAuctions, fetchCategories, fetchTrendingAuctions, incrementAuctionViews } from \"../api/auctions\";\nimport { useAuth } from \"../context/AuthContext\";\nimport { useMultipleApiCalls } from \"../hooks/useApiCall\";\nimport globalApiManager from \"../utils/globalApiManager\";\nimport NewAdvancedFilter from \"../components/NewAdvancedFilter\";\nimport CategoryFlipCard from \"../components/CategoryFlipCard\";\nimport { formatAuctionPrice } from \"../utils/currency\";\nfunction Home() {\n  const [featuredAuctions, setFeaturedAuctions] = useState([]);\n  const [trendingAuctions, setTrendingAuctions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [timers, setTimers] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    totalAuctions: 0,\n    activeAuctions: 0,\n    totalUsers: 0,\n    totalBids: 0\n  });\n  const [statsLoading, setStatsLoading] = useState(false);\n  const [lastUpdated, setLastUpdated] = useState(null);\n  // Circuit breaker state for featured auctions\n  const [featuredRetryCount, setFeaturedRetryCount] = useState(0);\n  const [featuredLastFetch, setFeaturedLastFetch] = useState(null);\n\n  // New state for filtered results\n  const [filteredResults, setFilteredResults] = useState([]);\n  const [isFiltered, setIsFiltered] = useState(false);\n  const [filterLoading, setFilterLoading] = useState(false);\n  const {\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n\n  // Fetch statistics with real data using global API manager\n  const fetchStats = useCallback(async () => {\n    try {\n      setStatsLoading(true);\n      console.log(\"🔄 Fetching statistics...\");\n\n      // Try platform stats API first\n      try {\n        const statsData = await globalApiManager.makeRequest(axiosInstance, \"landing/stats/\", {\n          cacheTime: 2 * 60 * 1000\n        } // Cache for 2 minutes\n        );\n        if (statsData.success && statsData.stats) {\n          const stats = statsData.stats;\n          const newStats = {\n            totalAuctions: stats.total_auctions || 0,\n            activeAuctions: stats.active_auctions || 0,\n            totalUsers: stats.total_users || 0,\n            totalBids: stats.total_bids || 0\n          };\n          console.log(\"✅ Using real platform stats:\", newStats);\n          setStats(newStats);\n          setLastUpdated(new Date());\n          return;\n        }\n      } catch (apiError) {\n        console.warn(\"⚠️ Platform stats API failed, falling back to individual endpoints:\", apiError);\n      }\n\n      // Fallback: Use multiple API calls with global manager\n      const requests = [{\n        key: \"auctions\",\n        url: \"auctions/\",\n        options: {\n          params: {\n            page_size: 1000\n          }\n        }\n      }, {\n        key: \"bids\",\n        url: \"bids/\",\n        options: {\n          params: {\n            page_size: 1000\n          }\n        }\n      }];\n      const results = await Promise.allSettled([globalApiManager.makeRequest(axiosInstance, \"auctions/\", {\n        params: {\n          page_size: 1000\n        }\n      }), globalApiManager.makeRequest(axiosInstance, \"bids/\", {\n        params: {\n          page_size: 1000\n        }\n      })]);\n      let totalAuctions = 0;\n      let activeAuctions = 0;\n      let totalUsers = 9; // Real user count from database\n      let totalBids = 0;\n\n      // Process results from global API manager\n      results.forEach((result, index) => {\n        if (result.status === \"fulfilled\") {\n          const data = result.value;\n          if (index === 0) {\n            var _data$results;\n            // auctions\n            console.log(\"📊 Auctions data:\", data);\n            totalAuctions = data.count || ((_data$results = data.results) === null || _data$results === void 0 ? void 0 : _data$results.length) || 0;\n            const auctions = data.results || [];\n            activeAuctions = auctions.filter(a => new Date(a.end_time) > new Date()).length;\n            console.log(`📈 Total auctions: ${totalAuctions}, Active: ${activeAuctions}`);\n          } else if (index === 1) {\n            var _data$results2;\n            // bids\n            console.log(\"💰 Bids data:\", data);\n            totalBids = data.count || ((_data$results2 = data.results) === null || _data$results2 === void 0 ? void 0 : _data$results2.length) || 0;\n            console.log(`💰 Total bids: ${totalBids}`);\n          }\n        } else {\n          console.warn(`❌ Failed to fetch ${requests[index].key}:`, result.reason);\n        }\n      });\n      console.log(\"👥 Using real user count:\", totalUsers);\n      const newStats = {\n        totalAuctions,\n        activeAuctions,\n        totalUsers,\n        totalBids\n      };\n      console.log(\"📊 Final stats:\", newStats);\n      setStats(newStats);\n      setLastUpdated(new Date());\n    } catch (error) {\n      console.error(\"❌ Error fetching stats:\", error);\n      // Set realistic fallback stats based on actual database counts\n      setStats({\n        totalAuctions: featuredAuctions.length || 30,\n        activeAuctions: featuredAuctions.filter(a => new Date(a.end_time) > new Date()).length || 27,\n        totalUsers: 9,\n        // Real user count from database\n        totalBids: 0 // Real bid count from database\n      });\n      setLastUpdated(new Date());\n    } finally {\n      setStatsLoading(false);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []); // FIXED: Remove dependencies to prevent infinite loop\n\n  // Default categories fallback\n  const defaultCategories = [{\n    name: \"Art\",\n    image: \"https://th.bing.com/th/id/OIP.XXwcURFvVIUMwbkxTazACQHaEz?rs=1&pid=ImgDetMain\",\n    slug: \"art\"\n  }, {\n    name: \"Electronics\",\n    image: \"https://img.freepik.com/premium-photo/8k-realistic-smartphone-accessories-white-canvas_893571-33631.jpg\",\n    slug: \"electronics\"\n  }, {\n    name: \"Antiques\",\n    image: \"https://img.freepik.com/psd-gratuit/vase-porcelaine-antique-fleurs-peintes-isolees-fond-transparent_191095-23323.jpg\",\n    slug: \"collectibles\"\n  }, {\n    name: \"Jewelry\",\n    image: \"https://i.pinimg.com/originals/1e/14/c5/1e14c5229b10f256d44aea92e47f57e5.jpg\",\n    slug: \"jewelry\"\n  }, {\n    name: \"Fashion\",\n    image: \"https://images.unsplash.com/photo-1445205170230-053b83016050?w=500\",\n    slug: \"fashion\"\n  }, {\n    name: \"Sports\",\n    image: \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500\",\n    slug: \"sports\"\n  }];\n\n  // Fetch all data on component mount using global API manager\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      try {\n        console.log(\"🏠 Loading home page data with global API manager...\");\n\n        // Use the existing API functions but with circuit breaker protection\n        const dataPromises = [];\n\n        // Featured auctions with circuit breaker\n        if (featuredRetryCount < 3) {\n          dataPromises.push(fetchFeaturedAuctions().then(data => ({\n            type: \"featured\",\n            data\n          })).catch(error => ({\n            type: \"featured\",\n            error\n          })));\n        }\n\n        // Trending auctions\n        dataPromises.push(fetchTrendingAuctions().then(data => ({\n          type: \"trending\",\n          data\n        })).catch(error => ({\n          type: \"trending\",\n          error\n        })));\n\n        // Categories\n        dataPromises.push(fetchCategories().then(data => ({\n          type: \"categories\",\n          data\n        })).catch(error => ({\n          type: \"categories\",\n          error\n        })));\n\n        // Execute all API calls with global manager protection\n        const results = await Promise.allSettled(dataPromises);\n\n        // Process results\n        results.forEach((result, index) => {\n          if (result.status === \"fulfilled\") {\n            const {\n              type,\n              data,\n              error\n            } = result.value;\n            if (error) {\n              console.error(`❌ Error loading ${type}:`, error);\n              if (type === \"featured\") {\n                setFeaturedRetryCount(prev => prev + 1);\n                setFeaturedAuctions([]);\n              } else if (type === \"categories\") {\n                setCategories(defaultCategories);\n              }\n            } else {\n              if (type === \"featured\") {\n                setFeaturedAuctions(data || []);\n                setFeaturedRetryCount(0);\n                setFeaturedLastFetch(Date.now());\n              } else if (type === \"trending\") {\n                setTrendingAuctions(data || []);\n              } else if (type === \"categories\") {\n                console.log(\"✅ Categories loaded:\", (data === null || data === void 0 ? void 0 : data.length) || 0, \"categories\");\n                setCategories((data === null || data === void 0 ? void 0 : data.length) > 0 ? data : defaultCategories);\n              }\n            }\n          }\n        });\n\n        // Fetch stats separately\n        await fetchStats();\n      } catch (error) {\n        console.error(\"❌ Error loading home page data:\", error);\n        setCategories(defaultCategories);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadData();\n\n    // DISABLED: Periodic refresh to prevent infinite API polling\n    // Set up periodic refresh only if polling is enabled\n    const isPollingEnabled = false; // FORCE DISABLED to stop infinite polling\n\n    if (isPollingEnabled) {\n      // Set up periodic refresh every 10 minutes for stats (reduced frequency)\n      const refreshInterval = setInterval(() => {\n        fetchStats();\n      }, 10 * 60 * 1000); // 10 minutes\n\n      return () => clearInterval(refreshInterval);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []); // FIXED: Remove fetchStats dependency to prevent infinite loop\n\n  // Refresh stats when user changes (login/logout)\n  useEffect(() => {\n    if (user) {\n      fetchStats(); // Refresh stats when user logs in\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [user]); // FIXED: Remove fetchStats dependency to prevent infinite loop\n\n  // Timer effect for countdown\n  useEffect(() => {\n    const interval = setInterval(() => {\n      const updatedTimers = {};\n      const allAuctions = isFiltered ? filteredResults : [...featuredAuctions, ...trendingAuctions];\n      allAuctions.forEach(auction => {\n        const now = new Date();\n        const end = new Date(auction.end_time || auction.endTime);\n        const diff = end - now;\n        if (diff <= 0) {\n          updatedTimers[auction.id] = \"Ended\";\n        } else {\n          const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n          const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n          const minutes = Math.floor(diff % (1000 * 60 * 60) / (1000 * 60));\n          if (days > 0) {\n            updatedTimers[auction.id] = `${days}d ${hours}h ${minutes}m`;\n          } else if (hours > 0) {\n            updatedTimers[auction.id] = `${hours}h ${minutes}m`;\n          } else {\n            updatedTimers[auction.id] = `${minutes}m`;\n          }\n        }\n      });\n      setTimers(updatedTimers);\n    }, 1000);\n    return () => clearInterval(interval);\n  }, [featuredAuctions, trendingAuctions, filteredResults, isFiltered]);\n  const handleCategoryClick = categorySlug => {\n    console.log(\"🔄 Category clicked:\", categorySlug);\n\n    // Handle slug normalization (convert hyphens to underscores for auction filtering)\n    const normalizedSlug = categorySlug.replace(/-/g, \"_\");\n    console.log(\"🔄 Normalized slug:\", normalizedSlug);\n    console.log(\"🔄 Navigating to:\", `/auctions/category/${normalizedSlug}`);\n\n    // Use route-based navigation for better SEO and user experience\n    navigate(`/auctions/category/${normalizedSlug}`);\n  };\n\n  // Handle search results from NewAdvancedFilter component\n  const handleSearchResults = searchResults => {\n    setFilterLoading(true);\n    if (searchResults && searchResults.length > 0) {\n      // Show filtered results on home page\n      setFilteredResults(searchResults);\n      setIsFiltered(true);\n    } else if (searchResults === null) {\n      // Clear search - show original content\n      setFilteredResults([]);\n      setIsFiltered(false);\n    } else {\n      // Empty results - show no results message\n      setFilteredResults([]);\n      setIsFiltered(true);\n    }\n    setFilterLoading(false);\n  };\n\n  // Handle clear results from NewAdvancedFilter component\n  const handleClearResults = () => {\n    // Clear filtered results and show original content\n    setFilteredResults([]);\n    setIsFiltered(false);\n    setFilterLoading(false);\n  };\n  const handleViewDetails = async auctionId => {\n    // Increment views count when view details is clicked\n    await incrementAuctionViews(auctionId);\n  };\n\n  // Manual refresh function\n  const handleRefresh = async () => {\n    setLoading(true);\n    try {\n      // Refresh all data\n      const [featured, trending, categoriesData] = await Promise.all([fetchFeaturedAuctions(), fetchTrendingAuctions(), fetchCategories()]);\n      setFeaturedAuctions(featured);\n      setTrendingAuctions(trending);\n      setCategories(categoriesData.length > 0 ? categoriesData : defaultCategories);\n\n      // Refresh stats\n      await fetchStats();\n    } catch (error) {\n      console.error(\"Error refreshing data:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Use the imported currency utility function\n  const formatCurrency = formatAuctionPrice;\n\n  // Loading skeleton component\n  const LoadingSkeleton = () => /*#__PURE__*/React.createElement(\"div\", {\n    className: \"col-md-4 mb-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card skeleton-card skeleton\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }\n  }));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"section\", {\n    className: \"hero-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 429,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"hero-content text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    className: \"display-3 fw-bold mb-4 fade-in-up\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 13\n    }\n  }, \"Welcome to \", /*#__PURE__*/React.createElement(\"span\", {\n    className: \"text-warning\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 26\n    }\n  }, \"AuctionStore\")), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"lead mb-5 fade-in-up\",\n    style: {\n      animationDelay: \"0.2s\",\n      color: \"white\"\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 13\n    }\n  }, \"Discover rare collectibles, bid for your dream items, and sell your treasures easily\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-section mx-auto\",\n    style: {\n      maxWidth: \"800px\"\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(NewAdvancedFilter, {\n    onFilterResults: handleSearchResults,\n    onClearResults: handleClearResults,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 15\n    }\n  }))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"section\", {\n    className: \"stats-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex justify-content-between align-items-center mb-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    className: \"mb-0\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-chart-bar me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 15\n    }\n  }), \"Live Statistics\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex align-items-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 13\n    }\n  }, lastUpdated && /*#__PURE__*/React.createElement(\"small\", {\n    className: \"text-muted me-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 17\n    }\n  }, \"Last updated: \", lastUpdated.toLocaleTimeString()), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-outline-primary btn-sm\",\n    onClick: handleRefresh,\n    disabled: loading || statsLoading,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: `fas fa-sync-alt me-2 ${loading || statsLoading ? \"fa-spin\" : \"\"}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 17\n    }\n  }), loading || statsLoading ? \"Refreshing...\" : \"Refresh\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"row\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"col-md-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 17\n    }\n  }, statsLoading ? /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-spinner fa-spin\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 21\n    }\n  }) : stats.totalAuctions), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 17\n    }\n  }, \"Total Auctions\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"col-md-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 17\n    }\n  }, statsLoading ? /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-spinner fa-spin\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 21\n    }\n  }) : stats.activeAuctions), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 17\n    }\n  }, \"Active Auctions\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"col-md-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 513,\n      columnNumber: 17\n    }\n  }, statsLoading ? /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-spinner fa-spin\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 21\n    }\n  }) : stats.totalUsers), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 17\n    }\n  }, \"Registered Users\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"col-md-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 17\n    }\n  }, statsLoading ? /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-spinner fa-spin\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 21\n    }\n  }) : stats.totalBids), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 532,\n      columnNumber: 17\n    }\n  }, \"Total Bids\"))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"text-center mb-5\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"d-flex justify-content-center align-items-center flex-wrap\",\n    style: {\n      gap: \"1rem\"\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Link, {\n    to: \"/auctions\",\n    className: \"btn btn-lg btn-primary px-5 py-3\",\n    style: {\n      background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n      border: \"none\",\n      borderRadius: \"50px\",\n      fontWeight: \"600\",\n      textTransform: \"uppercase\",\n      letterSpacing: \"1px\",\n      boxShadow: \"0 4px 15px rgba(102, 126, 234, 0.4)\",\n      transition: \"all 0.3s ease\"\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-gavel me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 15\n    }\n  }), \"Explore All Auctions\"), user && (user.can_create_auctions || user.is_staff) ? /*#__PURE__*/React.createElement(Link, {\n    to: \"/create-auction\",\n    className: \"btn btn-lg btn-outline-primary px-5 py-3\",\n    style: {\n      borderColor: \"#667eea\",\n      color: \"#667eea\",\n      borderRadius: \"50px\",\n      fontWeight: \"600\",\n      textTransform: \"uppercase\",\n      letterSpacing: \"1px\",\n      borderWidth: \"2px\",\n      transition: \"all 0.3s ease\"\n    },\n    onMouseEnter: e => {\n      e.target.style.background = \"#667eea\";\n      e.target.style.color = \"white\";\n      e.target.style.transform = \"translateY(-2px)\";\n      e.target.style.boxShadow = \"0 6px 20px rgba(102, 126, 234, 0.4)\";\n    },\n    onMouseLeave: e => {\n      e.target.style.background = \"transparent\";\n      e.target.style.color = \"#667eea\";\n      e.target.style.transform = \"translateY(0)\";\n      e.target.style.boxShadow = \"none\";\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-plus me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 588,\n      columnNumber: 17\n    }\n  }), \"Create Auction\") : user ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"btn btn-lg btn-outline-secondary px-5 py-3\",\n    style: {\n      borderColor: \"#6c757d\",\n      color: \"#6c757d\",\n      borderRadius: \"50px\",\n      fontWeight: \"600\",\n      textTransform: \"uppercase\",\n      letterSpacing: \"1px\",\n      borderWidth: \"2px\",\n      cursor: \"not-allowed\"\n    },\n    title: \"Your account type doesn't allow creating auctions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-lock me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 605,\n      columnNumber: 17\n    }\n  }), \"Create Auction (Restricted)\") : /*#__PURE__*/React.createElement(Link, {\n    to: \"/login\",\n    className: \"btn btn-lg btn-outline-primary px-5 py-3\",\n    style: {\n      borderColor: \"#667eea\",\n      color: \"#667eea\",\n      borderRadius: \"50px\",\n      fontWeight: \"600\",\n      textTransform: \"uppercase\",\n      letterSpacing: \"1px\",\n      borderWidth: \"2px\",\n      transition: \"all 0.3s ease\"\n    },\n    onMouseEnter: e => {\n      e.target.style.background = \"#667eea\";\n      e.target.style.color = \"white\";\n      e.target.style.transform = \"translateY(-2px)\";\n      e.target.style.boxShadow = \"0 6px 20px rgba(102, 126, 234, 0.4)\";\n    },\n    onMouseLeave: e => {\n      e.target.style.background = \"transparent\";\n      e.target.style.color = \"#667eea\";\n      e.target.style.transform = \"translateY(0)\";\n      e.target.style.boxShadow = \"none\";\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-sign-in-alt me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 635,\n      columnNumber: 17\n    }\n  }), \"Login to Create\"))), /*#__PURE__*/React.createElement(\"section\", {\n    className: \"mb-5\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 642,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"text-center mb-5 slide-in-left\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 643,\n      columnNumber: 11\n    }\n  }, isFiltered ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-search text-primary me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 646,\n      columnNumber: 17\n    }\n  }), \"Search Results\", /*#__PURE__*/React.createElement(\"span\", {\n    className: \"badge bg-primary ms-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 648,\n      columnNumber: 17\n    }\n  }, filteredResults.length, \" \", filteredResults.length === 1 ? \"result\" : \"results\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-outline-secondary btn-sm ms-3\",\n    onClick: handleClearResults,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 652,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-times me-1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 19\n    }\n  }), \"Clear Filters\")) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-star text-warning me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 661,\n      columnNumber: 17\n    }\n  }), \"Featured Auctions\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"row\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 11\n    }\n  }, loading || filterLoading ?\n  // Loading skeletons\n  Array.from({\n    length: 6\n  }).map((_, index) => /*#__PURE__*/React.createElement(LoadingSkeleton, {\n    key: index,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 17\n    }\n  })) : isFiltered ?\n  // Show filtered results\n  filteredResults.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"col-12 text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"alert alert-warning\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 677,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-search me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 678,\n      columnNumber: 21\n    }\n  }), \"No auctions found matching your search criteria.\", /*#__PURE__*/React.createElement(\"br\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 21\n    }\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary mt-2\",\n    onClick: handleClearResults,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 681,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-eye me-1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 685,\n      columnNumber: 23\n    }\n  }), \"View All Auctions\"))) : filteredResults.map((auction, index) => {\n    const isLive = timers[auction.id] !== \"Ended\";\n    const currentBid = auction.current_bid || auction.currentBid || auction.starting_bid || auction.startingBid;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      key: `filtered-${auction.id}-${index}`,\n      className: \"col-lg-4 col-md-6 mb-4 fade-in-up\",\n      style: {\n        animationDelay: `${index * 0.1}s`\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 699,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"card auction-card h-100\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 704,\n        columnNumber: 23\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"position-relative\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 705,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"img\", {\n      src: auction.image && auction.image.trim() !== \"\" ? auction.image : \"/placeholder-image.svg\",\n      className: \"card-img-top\",\n      alt: auction.title,\n      style: {\n        height: \"250px\",\n        objectFit: \"contain\"\n      },\n      onError: e => {\n        e.target.src = \"/placeholder-image.svg\";\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 706,\n        columnNumber: 27\n      }\n    }), auction.auction_type && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge bg-primary position-absolute top-0 start-0 m-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 29\n      }\n    }, auction.auction_type.replace(\"_\", \" \").toUpperCase()), isLive && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge bg-danger blinking-badge position-absolute top-0 end-0 m-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(\"i\", {\n      className: \"fas fa-circle me-1\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 732,\n        columnNumber: 31\n      }\n    }), \"LIVE\"), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge bg-info position-absolute\",\n      style: {\n        top: \"40px\",\n        right: \"8px\"\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 737,\n        columnNumber: 27\n      }\n    }, /*#__PURE__*/React.createElement(\"i\", {\n      className: \"fas fa-search me-1\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 741,\n        columnNumber: 29\n      }\n    }), \"RESULT\")), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"card-body d-flex flex-column\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 745,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"h5\", {\n      className: \"card-title text-truncate\",\n      title: auction.title,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 746,\n        columnNumber: 27\n      }\n    }, auction.title), /*#__PURE__*/React.createElement(\"p\", {\n      className: \"text-muted small mb-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 27\n      }\n    }, \"by \", auction.owner, auction.owner_rating > 0 && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"ms-1\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 756,\n        columnNumber: 31\n      }\n    }, /*#__PURE__*/React.createElement(\"i\", {\n      className: \"fas fa-star text-warning\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 757,\n        columnNumber: 33\n      }\n    }), \" \", auction.owner_rating.toFixed(1))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"mb-3\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 763,\n        columnNumber: 27\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 764,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"text-muted small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 765,\n        columnNumber: 31\n      }\n    }, \"Current Bid:\"), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"fw-bold text-success\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 768,\n        columnNumber: 31\n      }\n    }, formatCurrency(currentBid))), auction.reserve_price && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 774,\n        columnNumber: 31\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"text-muted small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 775,\n        columnNumber: 33\n      }\n    }, \"Reserve:\"), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 33\n      }\n    }, formatCurrency(auction.reserve_price))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"d-flex justify-content-between align-items-center\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 784,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"text-muted small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 785,\n        columnNumber: 31\n      }\n    }, \"Bids:\"), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge bg-secondary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 786,\n        columnNumber: 31\n      }\n    }, auction.total_bids || 0))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"mb-3\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 792,\n        columnNumber: 27\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"text-center p-2 bg-light rounded\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 793,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(\"small\", {\n      className: \"text-muted d-block\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 794,\n        columnNumber: 31\n      }\n    }, \"Time Remaining\"), /*#__PURE__*/React.createElement(\"span\", {\n      className: `fw-bold ${isLive ? \"text-danger\" : \"text-muted\"}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 31\n      }\n    }, timers[auction.id] || \"Loading...\"))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"mt-auto\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 807,\n        columnNumber: 27\n      }\n    }, /*#__PURE__*/React.createElement(Link, {\n      to: `/auction/${auction.id}`,\n      className: \"btn btn-primary w-100\",\n      onClick: () => handleViewDetails(auction.id),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 808,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(\"i\", {\n      className: \"fas fa-eye me-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 813,\n        columnNumber: 31\n      }\n    }), \"View Details\")))));\n  }) : featuredAuctions.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"col-12 text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 823,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"alert alert-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 824,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-info-circle me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 825,\n      columnNumber: 19\n    }\n  }), \"No featured auctions available at the moment.\")) : featuredAuctions.map((auction, index) => {\n    const isLive = timers[auction.id] !== \"Ended\";\n    const currentBid = auction.current_bid || auction.currentBid || auction.starting_bid || auction.startingBid;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      key: `featured-${auction.id}-${index}`,\n      className: \"col-lg-4 col-md-6 mb-4 fade-in-up\",\n      style: {\n        animationDelay: `${index * 0.1}s`\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 19\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"card auction-card h-100\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"position-relative\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 845,\n        columnNumber: 23\n      }\n    }, /*#__PURE__*/React.createElement(\"img\", {\n      src: auction.image && auction.image.trim() !== \"\" ? auction.image : \"/placeholder-image.svg\",\n      className: \"card-img-top\",\n      alt: auction.title,\n      style: {\n        height: \"250px\",\n        objectFit: \"contain\"\n      },\n      onError: e => {\n        e.target.src = \"/placeholder-image.svg\";\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 846,\n        columnNumber: 25\n      }\n    }), auction.auction_type && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge bg-primary position-absolute top-0 start-0 m-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 862,\n        columnNumber: 27\n      }\n    }, auction.auction_type.replace(\"_\", \" \").toUpperCase()), isLive && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge bg-danger blinking-badge position-absolute top-0 end-0 m-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 871,\n        columnNumber: 27\n      }\n    }, /*#__PURE__*/React.createElement(\"i\", {\n      className: \"fas fa-circle me-1\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 872,\n        columnNumber: 29\n      }\n    }), \"LIVE\"), auction.featured && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge bg-warning position-absolute\",\n      style: {\n        top: \"40px\",\n        right: \"8px\"\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 878,\n        columnNumber: 27\n      }\n    }, /*#__PURE__*/React.createElement(\"i\", {\n      className: \"fas fa-star me-1\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 882,\n        columnNumber: 29\n      }\n    }), \"FEATURED\")), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"card-body d-flex flex-column\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 887,\n        columnNumber: 23\n      }\n    }, /*#__PURE__*/React.createElement(\"h5\", {\n      className: \"card-title text-truncate\",\n      title: auction.title,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 888,\n        columnNumber: 25\n      }\n    }, auction.title), /*#__PURE__*/React.createElement(\"p\", {\n      className: \"text-muted small mb-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 895,\n        columnNumber: 25\n      }\n    }, \"by \", auction.owner, auction.owner_rating > 0 && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"ms-1\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 898,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(\"i\", {\n      className: \"fas fa-star text-warning\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 899,\n        columnNumber: 31\n      }\n    }), \" \", auction.owner_rating.toFixed(1))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"mb-3\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 905,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 906,\n        columnNumber: 27\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"text-muted small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 29\n      }\n    }, \"Current Bid:\"), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"fw-bold text-success\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 910,\n        columnNumber: 29\n      }\n    }, formatCurrency(currentBid))), auction.reserve_price && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 916,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"text-muted small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 917,\n        columnNumber: 31\n      }\n    }, \"Reserve:\"), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 918,\n        columnNumber: 31\n      }\n    }, formatCurrency(auction.reserve_price))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"d-flex justify-content-between align-items-center\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 924,\n        columnNumber: 27\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"text-muted small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 925,\n        columnNumber: 29\n      }\n    }, \"Bids:\"), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge bg-secondary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 926,\n        columnNumber: 29\n      }\n    }, auction.total_bids || 0))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"mb-3\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 932,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"text-center p-2 bg-light rounded\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 933,\n        columnNumber: 27\n      }\n    }, /*#__PURE__*/React.createElement(\"small\", {\n      className: \"text-muted d-block\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 934,\n        columnNumber: 29\n      }\n    }, \"Time Remaining\"), /*#__PURE__*/React.createElement(\"span\", {\n      className: `fw-bold ${isLive ? \"text-danger\" : \"text-muted\"}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 937,\n        columnNumber: 29\n      }\n    }, timers[auction.id] || \"Loading...\"))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"mt-auto\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 947,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(Link, {\n      to: `/auction/${auction.id}`,\n      className: \"btn btn-primary w-100\",\n      onClick: () => handleViewDetails(auction.id),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 948,\n        columnNumber: 27\n      }\n    }, /*#__PURE__*/React.createElement(\"i\", {\n      className: \"fas fa-eye me-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 953,\n        columnNumber: 29\n      }\n    }), \"View Details\")))));\n  }))), !isFiltered && /*#__PURE__*/React.createElement(\"section\", {\n    className: \"mb-5\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 967,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"text-center mb-5 slide-in-left\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 968,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-tags text-primary me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 969,\n      columnNumber: 15\n    }\n  }), \"Popular Categories\", /*#__PURE__*/React.createElement(\"small\", {\n    className: \"text-muted d-block mt-2\",\n    style: {\n      fontSize: \"0.6em\"\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 971,\n      columnNumber: 15\n    }\n  }, \"Hover over cards to see auction counts\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"row\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 979,\n      columnNumber: 13\n    }\n  }, categories.slice(0, 6).map((category, index) => /*#__PURE__*/React.createElement(\"div\", {\n    key: category.slug || index,\n    className: \"col-lg-2 col-md-4 col-sm-6 mb-4\",\n    style: {\n      animationDelay: `${index * 0.1}s`,\n      opacity: 0,\n      animation: `flipCardFadeIn 0.6s ease-out ${index * 0.1}s forwards`\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 981,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(CategoryFlipCard, {\n    category: category,\n    onClick: handleCategoryClick,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 992,\n      columnNumber: 19\n    }\n  }))))), !isFiltered && trendingAuctions.length > 0 && /*#__PURE__*/React.createElement(\"section\", {\n    className: \"mb-5\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1004,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"text-center mb-5 slide-in-left\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1005,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"fas fa-fire text-danger me-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1006,\n      columnNumber: 15\n    }\n  }), \"Trending Auctions\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"row\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1010,\n      columnNumber: 13\n    }\n  }, trendingAuctions.map((auction, index) => {\n    const isLive = timers[auction.id] !== \"Ended\";\n    const currentBid = auction.current_bid || auction.currentBid || auction.starting_bid || auction.startingBid;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      key: `trending-${auction.id}-${index}`,\n      className: \"col-lg-3 col-md-6 mb-4 fade-in-up\",\n      style: {\n        animationDelay: `${index * 0.1}s`\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1020,\n        columnNumber: 19\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"card auction-card h-100\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1025,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"position-relative\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1026,\n        columnNumber: 23\n      }\n    }, /*#__PURE__*/React.createElement(\"img\", {\n      src: auction.image && auction.image.trim() !== \"\" ? auction.image : \"/placeholder-image.svg\",\n      className: \"card-img-top\",\n      alt: auction.title,\n      style: {\n        height: \"200px\",\n        objectFit: \"contain\"\n      },\n      onError: e => {\n        e.target.src = \"/placeholder-image.svg\";\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1027,\n        columnNumber: 25\n      }\n    }), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge bg-danger position-absolute top-0 start-0 m-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1041,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"i\", {\n      className: \"fas fa-fire me-1\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1042,\n        columnNumber: 27\n      }\n    }), \"TRENDING\"), isLive && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge bg-success position-absolute top-0 end-0 m-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1046,\n        columnNumber: 27\n      }\n    }, /*#__PURE__*/React.createElement(\"i\", {\n      className: \"fas fa-circle me-1\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1047,\n        columnNumber: 29\n      }\n    }), \"LIVE\")), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"card-body\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1052,\n        columnNumber: 23\n      }\n    }, /*#__PURE__*/React.createElement(\"h6\", {\n      className: \"card-title text-truncate\",\n      title: auction.title,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1053,\n        columnNumber: 25\n      }\n    }, auction.title), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1060,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"text-muted small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1061,\n        columnNumber: 27\n      }\n    }, \"Current Bid:\"), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"fw-bold text-success small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1062,\n        columnNumber: 27\n      }\n    }, formatCurrency(currentBid))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-3\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1067,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"text-muted small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1068,\n        columnNumber: 27\n      }\n    }, \"Views:\"), /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge bg-info\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1069,\n        columnNumber: 27\n      }\n    }, auction.views_count || 0)), /*#__PURE__*/React.createElement(Link, {\n      to: `/auction/${auction.id}`,\n      className: \"btn btn-sm btn-outline-primary w-100\",\n      onClick: () => handleViewDetails(auction.id),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1074,\n        columnNumber: 25\n      }\n    }, \"View Details\"))));\n  })))));\n}\nexport default Home;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useCallback", "Link", "useNavigate", "fetchFeaturedAuctions", "fetchCategories", "fetchTrendingAuctions", "incrementAuctionViews", "useAuth", "useMultipleApiCalls", "globalApiManager", "NewAdvancedFilter", "CategoryFlipCard", "formatAuctionPrice", "Home", "featuredAuctions", "setFeaturedAuctions", "trendingAuctions", "setTrendingAuctions", "categories", "setCategories", "timers", "setTimers", "loading", "setLoading", "stats", "setStats", "totalAuctions", "activeAuctions", "totalUsers", "totalBids", "statsLoading", "setStatsLoading", "lastUpdated", "setLastUpdated", "featuredRetryCount", "setFeaturedRetryCount", "featuredLastFetch", "setFeaturedLastFetch", "filteredResults", "setFilteredResults", "isFiltered", "setIsFiltered", "filterLoading", "setFilterLoading", "user", "navigate", "fetchStats", "console", "log", "statsData", "makeRequest", "axiosInstance", "cacheTime", "success", "newStats", "total_auctions", "active_auctions", "total_users", "total_bids", "Date", "apiError", "warn", "requests", "key", "url", "options", "params", "page_size", "results", "Promise", "allSettled", "for<PERSON>ach", "result", "index", "status", "data", "value", "_data$results", "count", "length", "auctions", "filter", "a", "end_time", "_data$results2", "reason", "error", "defaultCategories", "name", "image", "slug", "loadData", "dataPromises", "push", "then", "type", "catch", "prev", "now", "isPollingEnabled", "refreshInterval", "setInterval", "clearInterval", "interval", "updatedTimers", "allAuctions", "auction", "end", "endTime", "diff", "id", "days", "Math", "floor", "hours", "minutes", "handleCategoryClick", "categorySlug", "normalizedSlug", "replace", "handleSearchResults", "searchResults", "handleClearResults", "handleViewDetails", "auctionId", "handleRefresh", "featured", "trending", "categoriesData", "all", "formatCurrency", "LoadingSkeleton", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "animationDelay", "color", "max<PERSON><PERSON><PERSON>", "onFilterResults", "onClearResults", "toLocaleTimeString", "onClick", "disabled", "gap", "to", "background", "border", "borderRadius", "fontWeight", "textTransform", "letterSpacing", "boxShadow", "transition", "can_create_auctions", "is_staff", "borderColor", "borderWidth", "onMouseEnter", "e", "target", "transform", "onMouseLeave", "cursor", "title", "Fragment", "Array", "from", "map", "_", "isLive", "currentBid", "current_bid", "starting_bid", "startingBid", "src", "trim", "alt", "height", "objectFit", "onError", "auction_type", "toUpperCase", "top", "right", "owner", "owner_rating", "toFixed", "reserve_price", "fontSize", "slice", "category", "opacity", "animation", "views_count"], "sources": ["C:/Users/<USER>/Desktop/Online_Auction_System/frontend/src/pages/Home.js"], "sourcesContent": ["import React, { useEffect, useState, useCallback } from \"react\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport {\r\n  fetchFeaturedAuctions,\r\n  fetchCategories,\r\n  fetchTrendingAuctions,\r\n  incrementAuctionViews,\r\n} from \"../api/auctions\";\r\nimport { useAuth } from \"../context/AuthContext\";\r\nimport { useMultipleApiCalls } from \"../hooks/useApiCall\";\r\nimport globalApiManager from \"../utils/globalApiManager\";\r\n\r\nimport NewAdvancedFilter from \"../components/NewAdvancedFilter\";\r\nimport CategoryFlipCard from \"../components/CategoryFlipCard\";\r\nimport { formatAuctionPrice } from \"../utils/currency\";\r\n\r\nfunction Home() {\r\n  const [featuredAuctions, setFeaturedAuctions] = useState([]);\r\n  const [trendingAuctions, setTrendingAuctions] = useState([]);\r\n  const [categories, setCategories] = useState([]);\r\n  const [timers, setTimers] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [stats, setStats] = useState({\r\n    totalAuctions: 0,\r\n    activeAuctions: 0,\r\n    totalUsers: 0,\r\n    totalBids: 0,\r\n  });\r\n  const [statsLoading, setStatsLoading] = useState(false);\r\n  const [lastUpdated, setLastUpdated] = useState(null);\r\n  // Circuit breaker state for featured auctions\r\n  const [featuredRetryCount, setFeaturedRetryCount] = useState(0);\r\n  const [featuredLastFetch, setFeaturedLastFetch] = useState(null);\r\n\r\n  // New state for filtered results\r\n  const [filteredResults, setFilteredResults] = useState([]);\r\n  const [isFiltered, setIsFiltered] = useState(false);\r\n  const [filterLoading, setFilterLoading] = useState(false);\r\n\r\n  const { user } = useAuth();\r\n  const navigate = useNavigate();\r\n\r\n  // Fetch statistics with real data using global API manager\r\n  const fetchStats = useCallback(async () => {\r\n    try {\r\n      setStatsLoading(true);\r\n      console.log(\"🔄 Fetching statistics...\");\r\n\r\n      // Try platform stats API first\r\n      try {\r\n        const statsData = await globalApiManager.makeRequest(\r\n          axiosInstance,\r\n          \"landing/stats/\",\r\n          { cacheTime: 2 * 60 * 1000 } // Cache for 2 minutes\r\n        );\r\n\r\n        if (statsData.success && statsData.stats) {\r\n          const stats = statsData.stats;\r\n          const newStats = {\r\n            totalAuctions: stats.total_auctions || 0,\r\n            activeAuctions: stats.active_auctions || 0,\r\n            totalUsers: stats.total_users || 0,\r\n            totalBids: stats.total_bids || 0,\r\n          };\r\n\r\n          console.log(\"✅ Using real platform stats:\", newStats);\r\n          setStats(newStats);\r\n          setLastUpdated(new Date());\r\n          return;\r\n        }\r\n      } catch (apiError) {\r\n        console.warn(\r\n          \"⚠️ Platform stats API failed, falling back to individual endpoints:\",\r\n          apiError\r\n        );\r\n      }\r\n\r\n      // Fallback: Use multiple API calls with global manager\r\n      const requests = [\r\n        {\r\n          key: \"auctions\",\r\n          url: \"auctions/\",\r\n          options: { params: { page_size: 1000 } },\r\n        },\r\n        { key: \"bids\", url: \"bids/\", options: { params: { page_size: 1000 } } },\r\n      ];\r\n\r\n      const results = await Promise.allSettled([\r\n        globalApiManager.makeRequest(axiosInstance, \"auctions/\", {\r\n          params: { page_size: 1000 },\r\n        }),\r\n        globalApiManager.makeRequest(axiosInstance, \"bids/\", {\r\n          params: { page_size: 1000 },\r\n        }),\r\n      ]);\r\n\r\n      let totalAuctions = 0;\r\n      let activeAuctions = 0;\r\n      let totalUsers = 9; // Real user count from database\r\n      let totalBids = 0;\r\n\r\n      // Process results from global API manager\r\n      results.forEach((result, index) => {\r\n        if (result.status === \"fulfilled\") {\r\n          const data = result.value;\r\n          if (index === 0) {\r\n            // auctions\r\n            console.log(\"📊 Auctions data:\", data);\r\n            totalAuctions = data.count || data.results?.length || 0;\r\n            const auctions = data.results || [];\r\n            activeAuctions = auctions.filter(\r\n              (a) => new Date(a.end_time) > new Date()\r\n            ).length;\r\n            console.log(\r\n              `📈 Total auctions: ${totalAuctions}, Active: ${activeAuctions}`\r\n            );\r\n          } else if (index === 1) {\r\n            // bids\r\n            console.log(\"💰 Bids data:\", data);\r\n            totalBids = data.count || data.results?.length || 0;\r\n            console.log(`💰 Total bids: ${totalBids}`);\r\n          }\r\n        } else {\r\n          console.warn(\r\n            `❌ Failed to fetch ${requests[index].key}:`,\r\n            result.reason\r\n          );\r\n        }\r\n      });\r\n\r\n      console.log(\"👥 Using real user count:\", totalUsers);\r\n\r\n      const newStats = {\r\n        totalAuctions,\r\n        activeAuctions,\r\n        totalUsers,\r\n        totalBids,\r\n      };\r\n\r\n      console.log(\"📊 Final stats:\", newStats);\r\n      setStats(newStats);\r\n      setLastUpdated(new Date());\r\n    } catch (error) {\r\n      console.error(\"❌ Error fetching stats:\", error);\r\n      // Set realistic fallback stats based on actual database counts\r\n      setStats({\r\n        totalAuctions: featuredAuctions.length || 30,\r\n        activeAuctions:\r\n          featuredAuctions.filter((a) => new Date(a.end_time) > new Date())\r\n            .length || 27,\r\n        totalUsers: 9, // Real user count from database\r\n        totalBids: 0, // Real bid count from database\r\n      });\r\n      setLastUpdated(new Date());\r\n    } finally {\r\n      setStatsLoading(false);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []); // FIXED: Remove dependencies to prevent infinite loop\r\n\r\n  // Default categories fallback\r\n  const defaultCategories = [\r\n    {\r\n      name: \"Art\",\r\n      image:\r\n        \"https://th.bing.com/th/id/OIP.XXwcURFvVIUMwbkxTazACQHaEz?rs=1&pid=ImgDetMain\",\r\n      slug: \"art\",\r\n    },\r\n    {\r\n      name: \"Electronics\",\r\n      image:\r\n        \"https://img.freepik.com/premium-photo/8k-realistic-smartphone-accessories-white-canvas_893571-33631.jpg\",\r\n      slug: \"electronics\",\r\n    },\r\n    {\r\n      name: \"Antiques\",\r\n      image:\r\n        \"https://img.freepik.com/psd-gratuit/vase-porcelaine-antique-fleurs-peintes-isolees-fond-transparent_191095-23323.jpg\",\r\n      slug: \"collectibles\",\r\n    },\r\n    {\r\n      name: \"Jewelry\",\r\n      image:\r\n        \"https://i.pinimg.com/originals/1e/14/c5/1e14c5229b10f256d44aea92e47f57e5.jpg\",\r\n      slug: \"jewelry\",\r\n    },\r\n    {\r\n      name: \"Fashion\",\r\n      image:\r\n        \"https://images.unsplash.com/photo-1445205170230-053b83016050?w=500\",\r\n      slug: \"fashion\",\r\n    },\r\n    {\r\n      name: \"Sports\",\r\n      image:\r\n        \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500\",\r\n      slug: \"sports\",\r\n    },\r\n  ];\r\n\r\n  // Fetch all data on component mount using global API manager\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      setLoading(true);\r\n      try {\r\n        console.log(\"🏠 Loading home page data with global API manager...\");\r\n\r\n        // Use the existing API functions but with circuit breaker protection\r\n        const dataPromises = [];\r\n\r\n        // Featured auctions with circuit breaker\r\n        if (featuredRetryCount < 3) {\r\n          dataPromises.push(\r\n            fetchFeaturedAuctions()\r\n              .then((data) => ({ type: \"featured\", data }))\r\n              .catch((error) => ({ type: \"featured\", error }))\r\n          );\r\n        }\r\n\r\n        // Trending auctions\r\n        dataPromises.push(\r\n          fetchTrendingAuctions()\r\n            .then((data) => ({ type: \"trending\", data }))\r\n            .catch((error) => ({ type: \"trending\", error }))\r\n        );\r\n\r\n        // Categories\r\n        dataPromises.push(\r\n          fetchCategories()\r\n            .then((data) => ({ type: \"categories\", data }))\r\n            .catch((error) => ({ type: \"categories\", error }))\r\n        );\r\n\r\n        // Execute all API calls with global manager protection\r\n        const results = await Promise.allSettled(dataPromises);\r\n\r\n        // Process results\r\n        results.forEach((result, index) => {\r\n          if (result.status === \"fulfilled\") {\r\n            const { type, data, error } = result.value;\r\n\r\n            if (error) {\r\n              console.error(`❌ Error loading ${type}:`, error);\r\n              if (type === \"featured\") {\r\n                setFeaturedRetryCount((prev) => prev + 1);\r\n                setFeaturedAuctions([]);\r\n              } else if (type === \"categories\") {\r\n                setCategories(defaultCategories);\r\n              }\r\n            } else {\r\n              if (type === \"featured\") {\r\n                setFeaturedAuctions(data || []);\r\n                setFeaturedRetryCount(0);\r\n                setFeaturedLastFetch(Date.now());\r\n              } else if (type === \"trending\") {\r\n                setTrendingAuctions(data || []);\r\n              } else if (type === \"categories\") {\r\n                console.log(\r\n                  \"✅ Categories loaded:\",\r\n                  data?.length || 0,\r\n                  \"categories\"\r\n                );\r\n                setCategories(data?.length > 0 ? data : defaultCategories);\r\n              }\r\n            }\r\n          }\r\n        });\r\n\r\n        // Fetch stats separately\r\n        await fetchStats();\r\n      } catch (error) {\r\n        console.error(\"❌ Error loading home page data:\", error);\r\n        setCategories(defaultCategories);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadData();\r\n\r\n    // DISABLED: Periodic refresh to prevent infinite API polling\r\n    // Set up periodic refresh only if polling is enabled\r\n    const isPollingEnabled = false; // FORCE DISABLED to stop infinite polling\r\n\r\n    if (isPollingEnabled) {\r\n      // Set up periodic refresh every 10 minutes for stats (reduced frequency)\r\n      const refreshInterval = setInterval(() => {\r\n        fetchStats();\r\n      }, 10 * 60 * 1000); // 10 minutes\r\n\r\n      return () => clearInterval(refreshInterval);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []); // FIXED: Remove fetchStats dependency to prevent infinite loop\r\n\r\n  // Refresh stats when user changes (login/logout)\r\n  useEffect(() => {\r\n    if (user) {\r\n      fetchStats(); // Refresh stats when user logs in\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [user]); // FIXED: Remove fetchStats dependency to prevent infinite loop\r\n\r\n  // Timer effect for countdown\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      const updatedTimers = {};\r\n      const allAuctions = isFiltered\r\n        ? filteredResults\r\n        : [...featuredAuctions, ...trendingAuctions];\r\n\r\n      allAuctions.forEach((auction) => {\r\n        const now = new Date();\r\n        const end = new Date(auction.end_time || auction.endTime);\r\n        const diff = end - now;\r\n\r\n        if (diff <= 0) {\r\n          updatedTimers[auction.id] = \"Ended\";\r\n        } else {\r\n          const days = Math.floor(diff / (1000 * 60 * 60 * 24));\r\n          const hours = Math.floor(\r\n            (diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)\r\n          );\r\n          const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\r\n\r\n          if (days > 0) {\r\n            updatedTimers[auction.id] = `${days}d ${hours}h ${minutes}m`;\r\n          } else if (hours > 0) {\r\n            updatedTimers[auction.id] = `${hours}h ${minutes}m`;\r\n          } else {\r\n            updatedTimers[auction.id] = `${minutes}m`;\r\n          }\r\n        }\r\n      });\r\n\r\n      setTimers(updatedTimers);\r\n    }, 1000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [featuredAuctions, trendingAuctions, filteredResults, isFiltered]);\r\n\r\n  const handleCategoryClick = (categorySlug) => {\r\n    console.log(\"🔄 Category clicked:\", categorySlug);\r\n\r\n    // Handle slug normalization (convert hyphens to underscores for auction filtering)\r\n    const normalizedSlug = categorySlug.replace(/-/g, \"_\");\r\n\r\n    console.log(\"🔄 Normalized slug:\", normalizedSlug);\r\n    console.log(\"🔄 Navigating to:\", `/auctions/category/${normalizedSlug}`);\r\n\r\n    // Use route-based navigation for better SEO and user experience\r\n    navigate(`/auctions/category/${normalizedSlug}`);\r\n  };\r\n\r\n  // Handle search results from NewAdvancedFilter component\r\n  const handleSearchResults = (searchResults) => {\r\n    setFilterLoading(true);\r\n\r\n    if (searchResults && searchResults.length > 0) {\r\n      // Show filtered results on home page\r\n      setFilteredResults(searchResults);\r\n      setIsFiltered(true);\r\n    } else if (searchResults === null) {\r\n      // Clear search - show original content\r\n      setFilteredResults([]);\r\n      setIsFiltered(false);\r\n    } else {\r\n      // Empty results - show no results message\r\n      setFilteredResults([]);\r\n      setIsFiltered(true);\r\n    }\r\n\r\n    setFilterLoading(false);\r\n  };\r\n\r\n  // Handle clear results from NewAdvancedFilter component\r\n  const handleClearResults = () => {\r\n    // Clear filtered results and show original content\r\n    setFilteredResults([]);\r\n    setIsFiltered(false);\r\n    setFilterLoading(false);\r\n  };\r\n\r\n  const handleViewDetails = async (auctionId) => {\r\n    // Increment views count when view details is clicked\r\n    await incrementAuctionViews(auctionId);\r\n  };\r\n\r\n  // Manual refresh function\r\n  const handleRefresh = async () => {\r\n    setLoading(true);\r\n    try {\r\n      // Refresh all data\r\n      const [featured, trending, categoriesData] = await Promise.all([\r\n        fetchFeaturedAuctions(),\r\n        fetchTrendingAuctions(),\r\n        fetchCategories(),\r\n      ]);\r\n\r\n      setFeaturedAuctions(featured);\r\n      setTrendingAuctions(trending);\r\n      setCategories(\r\n        categoriesData.length > 0 ? categoriesData : defaultCategories\r\n      );\r\n\r\n      // Refresh stats\r\n      await fetchStats();\r\n    } catch (error) {\r\n      console.error(\"Error refreshing data:\", error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Use the imported currency utility function\r\n  const formatCurrency = formatAuctionPrice;\r\n\r\n  // Loading skeleton component\r\n  const LoadingSkeleton = () => (\r\n    <div className=\"col-md-4 mb-4\">\r\n      <div className=\"card skeleton-card skeleton\"></div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div>\r\n      {/* Hero Section */}\r\n      <section className=\"hero-section\">\r\n        <div className=\"container\">\r\n          <div className=\"hero-content text-center\">\r\n            <h1 className=\"display-3 fw-bold mb-4 fade-in-up\">\r\n              Welcome to <span className=\"text-warning\">AuctionStore</span>\r\n            </h1>\r\n            <p\r\n              className=\"lead mb-5 fade-in-up\"\r\n              style={{\r\n                animationDelay: \"0.2s\",\r\n                color: \"white\",\r\n              }}\r\n            >\r\n              Discover rare collectibles, bid for your dream items, and sell\r\n              your treasures easily\r\n            </p>\r\n\r\n            {/* Advanced Search Section */}\r\n            <div\r\n              className=\"search-section mx-auto\"\r\n              style={{ maxWidth: \"800px\" }}\r\n            >\r\n              <NewAdvancedFilter\r\n                onFilterResults={handleSearchResults}\r\n                onClearResults={handleClearResults}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <div className=\"container\">\r\n        {/* Stats Section */}\r\n        <section className=\"stats-section\">\r\n          <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n            <h3 className=\"mb-0\">\r\n              <i className=\"fas fa-chart-bar me-2\"></i>Live Statistics\r\n            </h3>\r\n            <div className=\"d-flex align-items-center\">\r\n              {lastUpdated && (\r\n                <small className=\"text-muted me-3\">\r\n                  Last updated: {lastUpdated.toLocaleTimeString()}\r\n                </small>\r\n              )}\r\n              <button\r\n                className=\"btn btn-outline-primary btn-sm\"\r\n                onClick={handleRefresh}\r\n                disabled={loading || statsLoading}\r\n              >\r\n                <i\r\n                  className={`fas fa-sync-alt me-2 ${\r\n                    loading || statsLoading ? \"fa-spin\" : \"\"\r\n                  }`}\r\n                ></i>\r\n                {loading || statsLoading ? \"Refreshing...\" : \"Refresh\"}\r\n              </button>\r\n            </div>\r\n          </div>\r\n          <div className=\"row\">\r\n            <div className=\"col-md-3\">\r\n              <div className=\"stat-item\">\r\n                <div className=\"stat-number\">\r\n                  {statsLoading ? (\r\n                    <i className=\"fas fa-spinner fa-spin\"></i>\r\n                  ) : (\r\n                    stats.totalAuctions\r\n                  )}\r\n                </div>\r\n                <div className=\"stat-label\">Total Auctions</div>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-md-3\">\r\n              <div className=\"stat-item\">\r\n                <div className=\"stat-number\">\r\n                  {statsLoading ? (\r\n                    <i className=\"fas fa-spinner fa-spin\"></i>\r\n                  ) : (\r\n                    stats.activeAuctions\r\n                  )}\r\n                </div>\r\n                <div className=\"stat-label\">Active Auctions</div>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-md-3\">\r\n              <div className=\"stat-item\">\r\n                <div className=\"stat-number\">\r\n                  {statsLoading ? (\r\n                    <i className=\"fas fa-spinner fa-spin\"></i>\r\n                  ) : (\r\n                    stats.totalUsers\r\n                  )}\r\n                </div>\r\n                <div className=\"stat-label\">Registered Users</div>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-md-3\">\r\n              <div className=\"stat-item\">\r\n                <div className=\"stat-number\">\r\n                  {statsLoading ? (\r\n                    <i className=\"fas fa-spinner fa-spin\"></i>\r\n                  ) : (\r\n                    stats.totalBids\r\n                  )}\r\n                </div>\r\n                <div className=\"stat-label\">Total Bids</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </section>\r\n\r\n        {/* Quick Actions */}\r\n        <div className=\"text-center mb-5\">\r\n          <div\r\n            className=\"d-flex justify-content-center align-items-center flex-wrap\"\r\n            style={{ gap: \"1rem\" }}\r\n          >\r\n            <Link\r\n              to=\"/auctions\"\r\n              className=\"btn btn-lg btn-primary px-5 py-3\"\r\n              style={{\r\n                background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\r\n                border: \"none\",\r\n                borderRadius: \"50px\",\r\n                fontWeight: \"600\",\r\n                textTransform: \"uppercase\",\r\n                letterSpacing: \"1px\",\r\n                boxShadow: \"0 4px 15px rgba(102, 126, 234, 0.4)\",\r\n                transition: \"all 0.3s ease\",\r\n              }}\r\n            >\r\n              <i className=\"fas fa-gavel me-2\"></i>Explore All Auctions\r\n            </Link>\r\n            {user && (user.can_create_auctions || user.is_staff) ? (\r\n              <Link\r\n                to=\"/create-auction\"\r\n                className=\"btn btn-lg btn-outline-primary px-5 py-3\"\r\n                style={{\r\n                  borderColor: \"#667eea\",\r\n                  color: \"#667eea\",\r\n                  borderRadius: \"50px\",\r\n                  fontWeight: \"600\",\r\n                  textTransform: \"uppercase\",\r\n                  letterSpacing: \"1px\",\r\n                  borderWidth: \"2px\",\r\n                  transition: \"all 0.3s ease\",\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.target.style.background = \"#667eea\";\r\n                  e.target.style.color = \"white\";\r\n                  e.target.style.transform = \"translateY(-2px)\";\r\n                  e.target.style.boxShadow =\r\n                    \"0 6px 20px rgba(102, 126, 234, 0.4)\";\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.target.style.background = \"transparent\";\r\n                  e.target.style.color = \"#667eea\";\r\n                  e.target.style.transform = \"translateY(0)\";\r\n                  e.target.style.boxShadow = \"none\";\r\n                }}\r\n              >\r\n                <i className=\"fas fa-plus me-2\"></i>Create Auction\r\n              </Link>\r\n            ) : user ? (\r\n              <div\r\n                className=\"btn btn-lg btn-outline-secondary px-5 py-3\"\r\n                style={{\r\n                  borderColor: \"#6c757d\",\r\n                  color: \"#6c757d\",\r\n                  borderRadius: \"50px\",\r\n                  fontWeight: \"600\",\r\n                  textTransform: \"uppercase\",\r\n                  letterSpacing: \"1px\",\r\n                  borderWidth: \"2px\",\r\n                  cursor: \"not-allowed\",\r\n                }}\r\n                title=\"Your account type doesn't allow creating auctions\"\r\n              >\r\n                <i className=\"fas fa-lock me-2\"></i>Create Auction (Restricted)\r\n              </div>\r\n            ) : (\r\n              <Link\r\n                to=\"/login\"\r\n                className=\"btn btn-lg btn-outline-primary px-5 py-3\"\r\n                style={{\r\n                  borderColor: \"#667eea\",\r\n                  color: \"#667eea\",\r\n                  borderRadius: \"50px\",\r\n                  fontWeight: \"600\",\r\n                  textTransform: \"uppercase\",\r\n                  letterSpacing: \"1px\",\r\n                  borderWidth: \"2px\",\r\n                  transition: \"all 0.3s ease\",\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.target.style.background = \"#667eea\";\r\n                  e.target.style.color = \"white\";\r\n                  e.target.style.transform = \"translateY(-2px)\";\r\n                  e.target.style.boxShadow =\r\n                    \"0 6px 20px rgba(102, 126, 234, 0.4)\";\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.target.style.background = \"transparent\";\r\n                  e.target.style.color = \"#667eea\";\r\n                  e.target.style.transform = \"translateY(0)\";\r\n                  e.target.style.boxShadow = \"none\";\r\n                }}\r\n              >\r\n                <i className=\"fas fa-sign-in-alt me-2\"></i>Login to Create\r\n              </Link>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Featured Auctions or Filtered Results */}\r\n        <section className=\"mb-5\">\r\n          <h2 className=\"text-center mb-5 slide-in-left\">\r\n            {isFiltered ? (\r\n              <>\r\n                <i className=\"fas fa-search text-primary me-2\"></i>\r\n                Search Results\r\n                <span className=\"badge bg-primary ms-2\">\r\n                  {filteredResults.length}{\" \"}\r\n                  {filteredResults.length === 1 ? \"result\" : \"results\"}\r\n                </span>\r\n                <button\r\n                  className=\"btn btn-outline-secondary btn-sm ms-3\"\r\n                  onClick={handleClearResults}\r\n                >\r\n                  <i className=\"fas fa-times me-1\"></i>Clear Filters\r\n                </button>\r\n              </>\r\n            ) : (\r\n              <>\r\n                <i className=\"fas fa-star text-warning me-2\"></i>\r\n                Featured Auctions\r\n              </>\r\n            )}\r\n          </h2>\r\n\r\n          <div className=\"row\">\r\n            {loading || filterLoading ? (\r\n              // Loading skeletons\r\n              Array.from({ length: 6 }).map((_, index) => (\r\n                <LoadingSkeleton key={index} />\r\n              ))\r\n            ) : isFiltered ? (\r\n              // Show filtered results\r\n              filteredResults.length === 0 ? (\r\n                <div className=\"col-12 text-center\">\r\n                  <div className=\"alert alert-warning\">\r\n                    <i className=\"fas fa-search me-2\"></i>\r\n                    No auctions found matching your search criteria.\r\n                    <br />\r\n                    <button\r\n                      className=\"btn btn-primary mt-2\"\r\n                      onClick={handleClearResults}\r\n                    >\r\n                      <i className=\"fas fa-eye me-1\"></i>View All Auctions\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                filteredResults.map((auction, index) => {\r\n                  const isLive = timers[auction.id] !== \"Ended\";\r\n                  const currentBid =\r\n                    auction.current_bid ||\r\n                    auction.currentBid ||\r\n                    auction.starting_bid ||\r\n                    auction.startingBid;\r\n\r\n                  return (\r\n                    <div\r\n                      key={`filtered-${auction.id}-${index}`}\r\n                      className=\"col-lg-4 col-md-6 mb-4 fade-in-up\"\r\n                      style={{ animationDelay: `${index * 0.1}s` }}\r\n                    >\r\n                      <div className=\"card auction-card h-100\">\r\n                        <div className=\"position-relative\">\r\n                          <img\r\n                            src={\r\n                              auction.image && auction.image.trim() !== \"\"\r\n                                ? auction.image\r\n                                : \"/placeholder-image.svg\"\r\n                            }\r\n                            className=\"card-img-top\"\r\n                            alt={auction.title}\r\n                            style={{ height: \"250px\", objectFit: \"contain\" }}\r\n                            onError={(e) => {\r\n                              e.target.src = \"/placeholder-image.svg\";\r\n                            }}\r\n                          />\r\n\r\n                          {/* Auction Type Badge */}\r\n                          {auction.auction_type && (\r\n                            <span className=\"badge bg-primary position-absolute top-0 start-0 m-2\">\r\n                              {auction.auction_type\r\n                                .replace(\"_\", \" \")\r\n                                .toUpperCase()}\r\n                            </span>\r\n                          )}\r\n\r\n                          {/* Live Badge */}\r\n                          {isLive && (\r\n                            <span className=\"badge bg-danger blinking-badge position-absolute top-0 end-0 m-2\">\r\n                              <i className=\"fas fa-circle me-1\"></i>LIVE\r\n                            </span>\r\n                          )}\r\n\r\n                          {/* Search Result Badge */}\r\n                          <span\r\n                            className=\"badge bg-info position-absolute\"\r\n                            style={{ top: \"40px\", right: \"8px\" }}\r\n                          >\r\n                            <i className=\"fas fa-search me-1\"></i>RESULT\r\n                          </span>\r\n                        </div>\r\n\r\n                        <div className=\"card-body d-flex flex-column\">\r\n                          <h5\r\n                            className=\"card-title text-truncate\"\r\n                            title={auction.title}\r\n                          >\r\n                            {auction.title}\r\n                          </h5>\r\n\r\n                          <p className=\"text-muted small mb-2\">\r\n                            by {auction.owner}\r\n                            {auction.owner_rating > 0 && (\r\n                              <span className=\"ms-1\">\r\n                                <i className=\"fas fa-star text-warning\"></i>{\" \"}\r\n                                {auction.owner_rating.toFixed(1)}\r\n                              </span>\r\n                            )}\r\n                          </p>\r\n\r\n                          <div className=\"mb-3\">\r\n                            <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                              <span className=\"text-muted small\">\r\n                                Current Bid:\r\n                              </span>\r\n                              <span className=\"fw-bold text-success\">\r\n                                {formatCurrency(currentBid)}\r\n                              </span>\r\n                            </div>\r\n\r\n                            {auction.reserve_price && (\r\n                              <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                                <span className=\"text-muted small\">\r\n                                  Reserve:\r\n                                </span>\r\n                                <span className=\"small\">\r\n                                  {formatCurrency(auction.reserve_price)}\r\n                                </span>\r\n                              </div>\r\n                            )}\r\n\r\n                            <div className=\"d-flex justify-content-between align-items-center\">\r\n                              <span className=\"text-muted small\">Bids:</span>\r\n                              <span className=\"badge bg-secondary\">\r\n                                {auction.total_bids || 0}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n\r\n                          <div className=\"mb-3\">\r\n                            <div className=\"text-center p-2 bg-light rounded\">\r\n                              <small className=\"text-muted d-block\">\r\n                                Time Remaining\r\n                              </small>\r\n                              <span\r\n                                className={`fw-bold ${\r\n                                  isLive ? \"text-danger\" : \"text-muted\"\r\n                                }`}\r\n                              >\r\n                                {timers[auction.id] || \"Loading...\"}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n\r\n                          <div className=\"mt-auto\">\r\n                            <Link\r\n                              to={`/auction/${auction.id}`}\r\n                              className=\"btn btn-primary w-100\"\r\n                              onClick={() => handleViewDetails(auction.id)}\r\n                            >\r\n                              <i className=\"fas fa-eye me-2\"></i>View Details\r\n                            </Link>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                })\r\n              )\r\n            ) : featuredAuctions.length === 0 ? (\r\n              <div className=\"col-12 text-center\">\r\n                <div className=\"alert alert-info\">\r\n                  <i className=\"fas fa-info-circle me-2\"></i>\r\n                  No featured auctions available at the moment.\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              featuredAuctions.map((auction, index) => {\r\n                const isLive = timers[auction.id] !== \"Ended\";\r\n                const currentBid =\r\n                  auction.current_bid ||\r\n                  auction.currentBid ||\r\n                  auction.starting_bid ||\r\n                  auction.startingBid;\r\n\r\n                return (\r\n                  <div\r\n                    key={`featured-${auction.id}-${index}`}\r\n                    className=\"col-lg-4 col-md-6 mb-4 fade-in-up\"\r\n                    style={{ animationDelay: `${index * 0.1}s` }}\r\n                  >\r\n                    <div className=\"card auction-card h-100\">\r\n                      <div className=\"position-relative\">\r\n                        <img\r\n                          src={\r\n                            auction.image && auction.image.trim() !== \"\"\r\n                              ? auction.image\r\n                              : \"/placeholder-image.svg\"\r\n                          }\r\n                          className=\"card-img-top\"\r\n                          alt={auction.title}\r\n                          style={{ height: \"250px\", objectFit: \"contain\" }}\r\n                          onError={(e) => {\r\n                            e.target.src = \"/placeholder-image.svg\";\r\n                          }}\r\n                        />\r\n\r\n                        {/* Auction Type Badge */}\r\n                        {auction.auction_type && (\r\n                          <span className=\"badge bg-primary position-absolute top-0 start-0 m-2\">\r\n                            {auction.auction_type\r\n                              .replace(\"_\", \" \")\r\n                              .toUpperCase()}\r\n                          </span>\r\n                        )}\r\n\r\n                        {/* Live Badge */}\r\n                        {isLive && (\r\n                          <span className=\"badge bg-danger blinking-badge position-absolute top-0 end-0 m-2\">\r\n                            <i className=\"fas fa-circle me-1\"></i>LIVE\r\n                          </span>\r\n                        )}\r\n\r\n                        {/* Featured Badge */}\r\n                        {auction.featured && (\r\n                          <span\r\n                            className=\"badge bg-warning position-absolute\"\r\n                            style={{ top: \"40px\", right: \"8px\" }}\r\n                          >\r\n                            <i className=\"fas fa-star me-1\"></i>FEATURED\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"card-body d-flex flex-column\">\r\n                        <h5\r\n                          className=\"card-title text-truncate\"\r\n                          title={auction.title}\r\n                        >\r\n                          {auction.title}\r\n                        </h5>\r\n\r\n                        <p className=\"text-muted small mb-2\">\r\n                          by {auction.owner}\r\n                          {auction.owner_rating > 0 && (\r\n                            <span className=\"ms-1\">\r\n                              <i className=\"fas fa-star text-warning\"></i>{\" \"}\r\n                              {auction.owner_rating.toFixed(1)}\r\n                            </span>\r\n                          )}\r\n                        </p>\r\n\r\n                        <div className=\"mb-3\">\r\n                          <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                            <span className=\"text-muted small\">\r\n                              Current Bid:\r\n                            </span>\r\n                            <span className=\"fw-bold text-success\">\r\n                              {formatCurrency(currentBid)}\r\n                            </span>\r\n                          </div>\r\n\r\n                          {auction.reserve_price && (\r\n                            <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                              <span className=\"text-muted small\">Reserve:</span>\r\n                              <span className=\"small\">\r\n                                {formatCurrency(auction.reserve_price)}\r\n                              </span>\r\n                            </div>\r\n                          )}\r\n\r\n                          <div className=\"d-flex justify-content-between align-items-center\">\r\n                            <span className=\"text-muted small\">Bids:</span>\r\n                            <span className=\"badge bg-secondary\">\r\n                              {auction.total_bids || 0}\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"mb-3\">\r\n                          <div className=\"text-center p-2 bg-light rounded\">\r\n                            <small className=\"text-muted d-block\">\r\n                              Time Remaining\r\n                            </small>\r\n                            <span\r\n                              className={`fw-bold ${\r\n                                isLive ? \"text-danger\" : \"text-muted\"\r\n                              }`}\r\n                            >\r\n                              {timers[auction.id] || \"Loading...\"}\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"mt-auto\">\r\n                          <Link\r\n                            to={`/auction/${auction.id}`}\r\n                            className=\"btn btn-primary w-100\"\r\n                            onClick={() => handleViewDetails(auction.id)}\r\n                          >\r\n                            <i className=\"fas fa-eye me-2\"></i>View Details\r\n                          </Link>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })\r\n            )}\r\n          </div>\r\n        </section>\r\n\r\n        {/* Categories Section with Flip Cards - Hide when filtering */}\r\n        {!isFiltered && (\r\n          <section className=\"mb-5\">\r\n            <h2 className=\"text-center mb-5 slide-in-left\">\r\n              <i className=\"fas fa-tags text-primary me-2\"></i>\r\n              Popular Categories\r\n              <small\r\n                className=\"text-muted d-block mt-2\"\r\n                style={{ fontSize: \"0.6em\" }}\r\n              >\r\n                Hover over cards to see auction counts\r\n              </small>\r\n            </h2>\r\n\r\n            <div className=\"row\">\r\n              {categories.slice(0, 6).map((category, index) => (\r\n                <div\r\n                  key={category.slug || index}\r\n                  className=\"col-lg-2 col-md-4 col-sm-6 mb-4\"\r\n                  style={{\r\n                    animationDelay: `${index * 0.1}s`,\r\n                    opacity: 0,\r\n                    animation: `flipCardFadeIn 0.6s ease-out ${\r\n                      index * 0.1\r\n                    }s forwards`,\r\n                  }}\r\n                >\r\n                  <CategoryFlipCard\r\n                    category={category}\r\n                    onClick={handleCategoryClick}\r\n                  />\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </section>\r\n        )}\r\n\r\n        {/* Trending Auctions - Hide when filtering */}\r\n        {!isFiltered && trendingAuctions.length > 0 && (\r\n          <section className=\"mb-5\">\r\n            <h2 className=\"text-center mb-5 slide-in-left\">\r\n              <i className=\"fas fa-fire text-danger me-2\"></i>\r\n              Trending Auctions\r\n            </h2>\r\n\r\n            <div className=\"row\">\r\n              {trendingAuctions.map((auction, index) => {\r\n                const isLive = timers[auction.id] !== \"Ended\";\r\n                const currentBid =\r\n                  auction.current_bid ||\r\n                  auction.currentBid ||\r\n                  auction.starting_bid ||\r\n                  auction.startingBid;\r\n\r\n                return (\r\n                  <div\r\n                    key={`trending-${auction.id}-${index}`}\r\n                    className=\"col-lg-3 col-md-6 mb-4 fade-in-up\"\r\n                    style={{ animationDelay: `${index * 0.1}s` }}\r\n                  >\r\n                    <div className=\"card auction-card h-100\">\r\n                      <div className=\"position-relative\">\r\n                        <img\r\n                          src={\r\n                            auction.image && auction.image.trim() !== \"\"\r\n                              ? auction.image\r\n                              : \"/placeholder-image.svg\"\r\n                          }\r\n                          className=\"card-img-top\"\r\n                          alt={auction.title}\r\n                          style={{ height: \"200px\", objectFit: \"contain\" }}\r\n                          onError={(e) => {\r\n                            e.target.src = \"/placeholder-image.svg\";\r\n                          }}\r\n                        />\r\n\r\n                        <span className=\"badge bg-danger position-absolute top-0 start-0 m-2\">\r\n                          <i className=\"fas fa-fire me-1\"></i>TRENDING\r\n                        </span>\r\n\r\n                        {isLive && (\r\n                          <span className=\"badge bg-success position-absolute top-0 end-0 m-2\">\r\n                            <i className=\"fas fa-circle me-1\"></i>LIVE\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"card-body\">\r\n                        <h6\r\n                          className=\"card-title text-truncate\"\r\n                          title={auction.title}\r\n                        >\r\n                          {auction.title}\r\n                        </h6>\r\n\r\n                        <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                          <span className=\"text-muted small\">Current Bid:</span>\r\n                          <span className=\"fw-bold text-success small\">\r\n                            {formatCurrency(currentBid)}\r\n                          </span>\r\n                        </div>\r\n\r\n                        <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n                          <span className=\"text-muted small\">Views:</span>\r\n                          <span className=\"badge bg-info\">\r\n                            {auction.views_count || 0}\r\n                          </span>\r\n                        </div>\r\n\r\n                        <Link\r\n                          to={`/auction/${auction.id}`}\r\n                          className=\"btn btn-sm btn-outline-primary w-100\"\r\n                          onClick={() => handleViewDetails(auction.id)}\r\n                        >\r\n                          View Details\r\n                        </Link>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </section>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SACEC,qBAAqB,EACrBC,eAAe,EACfC,qBAAqB,EACrBC,qBAAqB,QAChB,iBAAiB;AACxB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,OAAOC,gBAAgB,MAAM,2BAA2B;AAExD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,SAASC,kBAAkB,QAAQ,mBAAmB;AAEtD,SAASC,IAAIA,CAAA,EAAG;EACd,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACiB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC;IACjC2B,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD;EACA,MAAM,CAACmC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACqC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2C,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAM;IAAE6C;EAAK,CAAC,GAAGrC,OAAO,CAAC,CAAC;EAC1B,MAAMsC,QAAQ,GAAG3C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM4C,UAAU,GAAG9C,WAAW,CAAC,YAAY;IACzC,IAAI;MACF+B,eAAe,CAAC,IAAI,CAAC;MACrBgB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,IAAI;QACF,MAAMC,SAAS,GAAG,MAAMxC,gBAAgB,CAACyC,WAAW,CAClDC,aAAa,EACb,gBAAgB,EAChB;UAAEC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG;QAAK,CAAC,CAAC;QAC/B,CAAC;QAED,IAAIH,SAAS,CAACI,OAAO,IAAIJ,SAAS,CAACzB,KAAK,EAAE;UACxC,MAAMA,KAAK,GAAGyB,SAAS,CAACzB,KAAK;UAC7B,MAAM8B,QAAQ,GAAG;YACf5B,aAAa,EAAEF,KAAK,CAAC+B,cAAc,IAAI,CAAC;YACxC5B,cAAc,EAAEH,KAAK,CAACgC,eAAe,IAAI,CAAC;YAC1C5B,UAAU,EAAEJ,KAAK,CAACiC,WAAW,IAAI,CAAC;YAClC5B,SAAS,EAAEL,KAAK,CAACkC,UAAU,IAAI;UACjC,CAAC;UAEDX,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEM,QAAQ,CAAC;UACrD7B,QAAQ,CAAC6B,QAAQ,CAAC;UAClBrB,cAAc,CAAC,IAAI0B,IAAI,CAAC,CAAC,CAAC;UAC1B;QACF;MACF,CAAC,CAAC,OAAOC,QAAQ,EAAE;QACjBb,OAAO,CAACc,IAAI,CACV,qEAAqE,EACrED,QACF,CAAC;MACH;;MAEA;MACA,MAAME,QAAQ,GAAG,CACf;QACEC,GAAG,EAAE,UAAU;QACfC,GAAG,EAAE,WAAW;QAChBC,OAAO,EAAE;UAAEC,MAAM,EAAE;YAAEC,SAAS,EAAE;UAAK;QAAE;MACzC,CAAC,EACD;QAAEJ,GAAG,EAAE,MAAM;QAAEC,GAAG,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEC,MAAM,EAAE;YAAEC,SAAS,EAAE;UAAK;QAAE;MAAE,CAAC,CACxE;MAED,MAAMC,OAAO,GAAG,MAAMC,OAAO,CAACC,UAAU,CAAC,CACvC7D,gBAAgB,CAACyC,WAAW,CAACC,aAAa,EAAE,WAAW,EAAE;QACvDe,MAAM,EAAE;UAAEC,SAAS,EAAE;QAAK;MAC5B,CAAC,CAAC,EACF1D,gBAAgB,CAACyC,WAAW,CAACC,aAAa,EAAE,OAAO,EAAE;QACnDe,MAAM,EAAE;UAAEC,SAAS,EAAE;QAAK;MAC5B,CAAC,CAAC,CACH,CAAC;MAEF,IAAIzC,aAAa,GAAG,CAAC;MACrB,IAAIC,cAAc,GAAG,CAAC;MACtB,IAAIC,UAAU,GAAG,CAAC,CAAC,CAAC;MACpB,IAAIC,SAAS,GAAG,CAAC;;MAEjB;MACAuC,OAAO,CAACG,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;QACjC,IAAID,MAAM,CAACE,MAAM,KAAK,WAAW,EAAE;UACjC,MAAMC,IAAI,GAAGH,MAAM,CAACI,KAAK;UACzB,IAAIH,KAAK,KAAK,CAAC,EAAE;YAAA,IAAAI,aAAA;YACf;YACA9B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2B,IAAI,CAAC;YACtCjD,aAAa,GAAGiD,IAAI,CAACG,KAAK,MAAAD,aAAA,GAAIF,IAAI,CAACP,OAAO,cAAAS,aAAA,uBAAZA,aAAA,CAAcE,MAAM,KAAI,CAAC;YACvD,MAAMC,QAAQ,GAAGL,IAAI,CAACP,OAAO,IAAI,EAAE;YACnCzC,cAAc,GAAGqD,QAAQ,CAACC,MAAM,CAC7BC,CAAC,IAAK,IAAIvB,IAAI,CAACuB,CAAC,CAACC,QAAQ,CAAC,GAAG,IAAIxB,IAAI,CAAC,CACzC,CAAC,CAACoB,MAAM;YACRhC,OAAO,CAACC,GAAG,CACT,sBAAsBtB,aAAa,aAAaC,cAAc,EAChE,CAAC;UACH,CAAC,MAAM,IAAI8C,KAAK,KAAK,CAAC,EAAE;YAAA,IAAAW,cAAA;YACtB;YACArC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE2B,IAAI,CAAC;YAClC9C,SAAS,GAAG8C,IAAI,CAACG,KAAK,MAAAM,cAAA,GAAIT,IAAI,CAACP,OAAO,cAAAgB,cAAA,uBAAZA,cAAA,CAAcL,MAAM,KAAI,CAAC;YACnDhC,OAAO,CAACC,GAAG,CAAC,kBAAkBnB,SAAS,EAAE,CAAC;UAC5C;QACF,CAAC,MAAM;UACLkB,OAAO,CAACc,IAAI,CACV,qBAAqBC,QAAQ,CAACW,KAAK,CAAC,CAACV,GAAG,GAAG,EAC3CS,MAAM,CAACa,MACT,CAAC;QACH;MACF,CAAC,CAAC;MAEFtC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEpB,UAAU,CAAC;MAEpD,MAAM0B,QAAQ,GAAG;QACf5B,aAAa;QACbC,cAAc;QACdC,UAAU;QACVC;MACF,CAAC;MAEDkB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEM,QAAQ,CAAC;MACxC7B,QAAQ,CAAC6B,QAAQ,CAAC;MAClBrB,cAAc,CAAC,IAAI0B,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdvC,OAAO,CAACuC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;MACA7D,QAAQ,CAAC;QACPC,aAAa,EAAEZ,gBAAgB,CAACiE,MAAM,IAAI,EAAE;QAC5CpD,cAAc,EACZb,gBAAgB,CAACmE,MAAM,CAAEC,CAAC,IAAK,IAAIvB,IAAI,CAACuB,CAAC,CAACC,QAAQ,CAAC,GAAG,IAAIxB,IAAI,CAAC,CAAC,CAAC,CAC9DoB,MAAM,IAAI,EAAE;QACjBnD,UAAU,EAAE,CAAC;QAAE;QACfC,SAAS,EAAE,CAAC,CAAE;MAChB,CAAC,CAAC;MACFI,cAAc,CAAC,IAAI0B,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,SAAS;MACR5B,eAAe,CAAC,KAAK,CAAC;IACxB;IACA;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMwD,iBAAiB,GAAG,CACxB;IACEC,IAAI,EAAE,KAAK;IACXC,KAAK,EACH,8EAA8E;IAChFC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,aAAa;IACnBC,KAAK,EACH,yGAAyG;IAC3GC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,UAAU;IAChBC,KAAK,EACH,sHAAsH;IACxHC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,SAAS;IACfC,KAAK,EACH,8EAA8E;IAChFC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,SAAS;IACfC,KAAK,EACH,oEAAoE;IACtEC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,QAAQ;IACdC,KAAK,EACH,oEAAoE;IACtEC,IAAI,EAAE;EACR,CAAC,CACF;;EAED;EACA5F,SAAS,CAAC,MAAM;IACd,MAAM6F,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3BpE,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACFwB,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;;QAEnE;QACA,MAAM4C,YAAY,GAAG,EAAE;;QAEvB;QACA,IAAI1D,kBAAkB,GAAG,CAAC,EAAE;UAC1B0D,YAAY,CAACC,IAAI,CACf1F,qBAAqB,CAAC,CAAC,CACpB2F,IAAI,CAAEnB,IAAI,KAAM;YAAEoB,IAAI,EAAE,UAAU;YAAEpB;UAAK,CAAC,CAAC,CAAC,CAC5CqB,KAAK,CAAEV,KAAK,KAAM;YAAES,IAAI,EAAE,UAAU;YAAET;UAAM,CAAC,CAAC,CACnD,CAAC;QACH;;QAEA;QACAM,YAAY,CAACC,IAAI,CACfxF,qBAAqB,CAAC,CAAC,CACpByF,IAAI,CAAEnB,IAAI,KAAM;UAAEoB,IAAI,EAAE,UAAU;UAAEpB;QAAK,CAAC,CAAC,CAAC,CAC5CqB,KAAK,CAAEV,KAAK,KAAM;UAAES,IAAI,EAAE,UAAU;UAAET;QAAM,CAAC,CAAC,CACnD,CAAC;;QAED;QACAM,YAAY,CAACC,IAAI,CACfzF,eAAe,CAAC,CAAC,CACd0F,IAAI,CAAEnB,IAAI,KAAM;UAAEoB,IAAI,EAAE,YAAY;UAAEpB;QAAK,CAAC,CAAC,CAAC,CAC9CqB,KAAK,CAAEV,KAAK,KAAM;UAAES,IAAI,EAAE,YAAY;UAAET;QAAM,CAAC,CAAC,CACrD,CAAC;;QAED;QACA,MAAMlB,OAAO,GAAG,MAAMC,OAAO,CAACC,UAAU,CAACsB,YAAY,CAAC;;QAEtD;QACAxB,OAAO,CAACG,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;UACjC,IAAID,MAAM,CAACE,MAAM,KAAK,WAAW,EAAE;YACjC,MAAM;cAAEqB,IAAI;cAAEpB,IAAI;cAAEW;YAAM,CAAC,GAAGd,MAAM,CAACI,KAAK;YAE1C,IAAIU,KAAK,EAAE;cACTvC,OAAO,CAACuC,KAAK,CAAC,mBAAmBS,IAAI,GAAG,EAAET,KAAK,CAAC;cAChD,IAAIS,IAAI,KAAK,UAAU,EAAE;gBACvB5D,qBAAqB,CAAE8D,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;gBACzClF,mBAAmB,CAAC,EAAE,CAAC;cACzB,CAAC,MAAM,IAAIgF,IAAI,KAAK,YAAY,EAAE;gBAChC5E,aAAa,CAACoE,iBAAiB,CAAC;cAClC;YACF,CAAC,MAAM;cACL,IAAIQ,IAAI,KAAK,UAAU,EAAE;gBACvBhF,mBAAmB,CAAC4D,IAAI,IAAI,EAAE,CAAC;gBAC/BxC,qBAAqB,CAAC,CAAC,CAAC;gBACxBE,oBAAoB,CAACsB,IAAI,CAACuC,GAAG,CAAC,CAAC,CAAC;cAClC,CAAC,MAAM,IAAIH,IAAI,KAAK,UAAU,EAAE;gBAC9B9E,mBAAmB,CAAC0D,IAAI,IAAI,EAAE,CAAC;cACjC,CAAC,MAAM,IAAIoB,IAAI,KAAK,YAAY,EAAE;gBAChChD,OAAO,CAACC,GAAG,CACT,sBAAsB,EACtB,CAAA2B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,MAAM,KAAI,CAAC,EACjB,YACF,CAAC;gBACD5D,aAAa,CAAC,CAAAwD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,MAAM,IAAG,CAAC,GAAGJ,IAAI,GAAGY,iBAAiB,CAAC;cAC5D;YACF;UACF;QACF,CAAC,CAAC;;QAEF;QACA,MAAMzC,UAAU,CAAC,CAAC;MACpB,CAAC,CAAC,OAAOwC,KAAK,EAAE;QACdvC,OAAO,CAACuC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvDnE,aAAa,CAACoE,iBAAiB,CAAC;MAClC,CAAC,SAAS;QACRhE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDoE,QAAQ,CAAC,CAAC;;IAEV;IACA;IACA,MAAMQ,gBAAgB,GAAG,KAAK,CAAC,CAAC;;IAEhC,IAAIA,gBAAgB,EAAE;MACpB;MACA,MAAMC,eAAe,GAAGC,WAAW,CAAC,MAAM;QACxCvD,UAAU,CAAC,CAAC;MACd,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;;MAEpB,OAAO,MAAMwD,aAAa,CAACF,eAAe,CAAC;IAC7C;IACA;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAtG,SAAS,CAAC,MAAM;IACd,IAAI8C,IAAI,EAAE;MACRE,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB;IACA;EACF,CAAC,EAAE,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZ;EACA9C,SAAS,CAAC,MAAM;IACd,MAAMyG,QAAQ,GAAGF,WAAW,CAAC,MAAM;MACjC,MAAMG,aAAa,GAAG,CAAC,CAAC;MACxB,MAAMC,WAAW,GAAGjE,UAAU,GAC1BF,eAAe,GACf,CAAC,GAAGxB,gBAAgB,EAAE,GAAGE,gBAAgB,CAAC;MAE9CyF,WAAW,CAAClC,OAAO,CAAEmC,OAAO,IAAK;QAC/B,MAAMR,GAAG,GAAG,IAAIvC,IAAI,CAAC,CAAC;QACtB,MAAMgD,GAAG,GAAG,IAAIhD,IAAI,CAAC+C,OAAO,CAACvB,QAAQ,IAAIuB,OAAO,CAACE,OAAO,CAAC;QACzD,MAAMC,IAAI,GAAGF,GAAG,GAAGT,GAAG;QAEtB,IAAIW,IAAI,IAAI,CAAC,EAAE;UACbL,aAAa,CAACE,OAAO,CAACI,EAAE,CAAC,GAAG,OAAO;QACrC,CAAC,MAAM;UACL,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;UACrD,MAAMK,KAAK,GAAGF,IAAI,CAACC,KAAK,CACrBJ,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAClD,CAAC;UACD,MAAMM,OAAO,GAAGH,IAAI,CAACC,KAAK,CAAEJ,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,CAAC,CAAC;UAEnE,IAAIE,IAAI,GAAG,CAAC,EAAE;YACZP,aAAa,CAACE,OAAO,CAACI,EAAE,CAAC,GAAG,GAAGC,IAAI,KAAKG,KAAK,KAAKC,OAAO,GAAG;UAC9D,CAAC,MAAM,IAAID,KAAK,GAAG,CAAC,EAAE;YACpBV,aAAa,CAACE,OAAO,CAACI,EAAE,CAAC,GAAG,GAAGI,KAAK,KAAKC,OAAO,GAAG;UACrD,CAAC,MAAM;YACLX,aAAa,CAACE,OAAO,CAACI,EAAE,CAAC,GAAG,GAAGK,OAAO,GAAG;UAC3C;QACF;MACF,CAAC,CAAC;MAEF9F,SAAS,CAACmF,aAAa,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMF,aAAa,CAACC,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACzF,gBAAgB,EAAEE,gBAAgB,EAAEsB,eAAe,EAAEE,UAAU,CAAC,CAAC;EAErE,MAAM4E,mBAAmB,GAAIC,YAAY,IAAK;IAC5CtE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEqE,YAAY,CAAC;;IAEjD;IACA,MAAMC,cAAc,GAAGD,YAAY,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAEtDxE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEsE,cAAc,CAAC;IAClDvE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,sBAAsBsE,cAAc,EAAE,CAAC;;IAExE;IACAzE,QAAQ,CAAC,sBAAsByE,cAAc,EAAE,CAAC;EAClD,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAIC,aAAa,IAAK;IAC7C9E,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI8E,aAAa,IAAIA,aAAa,CAAC1C,MAAM,GAAG,CAAC,EAAE;MAC7C;MACAxC,kBAAkB,CAACkF,aAAa,CAAC;MACjChF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIgF,aAAa,KAAK,IAAI,EAAE;MACjC;MACAlF,kBAAkB,CAAC,EAAE,CAAC;MACtBE,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,MAAM;MACL;MACAF,kBAAkB,CAAC,EAAE,CAAC;MACtBE,aAAa,CAAC,IAAI,CAAC;IACrB;IAEAE,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;;EAED;EACA,MAAM+E,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACAnF,kBAAkB,CAAC,EAAE,CAAC;IACtBE,aAAa,CAAC,KAAK,CAAC;IACpBE,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;EAED,MAAMgF,iBAAiB,GAAG,MAAOC,SAAS,IAAK;IAC7C;IACA,MAAMtH,qBAAqB,CAACsH,SAAS,CAAC;EACxC,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCtG,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAM,CAACuG,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,CAAC,GAAG,MAAM3D,OAAO,CAAC4D,GAAG,CAAC,CAC7D9H,qBAAqB,CAAC,CAAC,EACvBE,qBAAqB,CAAC,CAAC,EACvBD,eAAe,CAAC,CAAC,CAClB,CAAC;MAEFW,mBAAmB,CAAC+G,QAAQ,CAAC;MAC7B7G,mBAAmB,CAAC8G,QAAQ,CAAC;MAC7B5G,aAAa,CACX6G,cAAc,CAACjD,MAAM,GAAG,CAAC,GAAGiD,cAAc,GAAGzC,iBAC/C,CAAC;;MAED;MACA,MAAMzC,UAAU,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdvC,OAAO,CAACuC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACR/D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2G,cAAc,GAAGtH,kBAAkB;;EAEzC;EACA,MAAMuH,eAAe,GAAGA,CAAA,kBACtBtI,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5B9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,6BAA6B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAM,CAC/C,CACN;EAED,oBACE9I,KAAA,CAAAuI,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEE9I,KAAA,CAAAuI,aAAA;IAASC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/B9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,0BAA0B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvC9I,KAAA,CAAAuI,aAAA;IAAIC,SAAS,EAAC,mCAAmC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,aACrC,eAAA9I,KAAA,CAAAuI,aAAA;IAAMC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAkB,CAC1D,CAAC,eACL9I,KAAA,CAAAuI,aAAA;IACEC,SAAS,EAAC,sBAAsB;IAChCO,KAAK,EAAE;MACLC,cAAc,EAAE,MAAM;MACtBC,KAAK,EAAE;IACT,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACH,sFAGE,CAAC,eAGJ9I,KAAA,CAAAuI,aAAA;IACEC,SAAS,EAAC,wBAAwB;IAClCO,KAAK,EAAE;MAAEG,QAAQ,EAAE;IAAQ,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE7B9I,KAAA,CAAAuI,aAAA,CAAC1H,iBAAiB;IAChBsI,eAAe,EAAExB,mBAAoB;IACrCyB,cAAc,EAAEvB,kBAAmB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACpC,CACE,CACF,CACF,CACE,CAAC,eAEV9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExB9I,KAAA,CAAAuI,aAAA;IAASC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChC9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,wDAAwD;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrE9I,KAAA,CAAAuI,aAAA;IAAIC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClB9I,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,mBACvC,CAAC,eACL9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,2BAA2B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvC3G,WAAW,iBACVnC,KAAA,CAAAuI,aAAA;IAAOC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gBACnB,EAAC3G,WAAW,CAACkH,kBAAkB,CAAC,CACzC,CACR,eACDrJ,KAAA,CAAAuI,aAAA;IACEC,SAAS,EAAC,gCAAgC;IAC1Cc,OAAO,EAAEtB,aAAc;IACvBuB,QAAQ,EAAE9H,OAAO,IAAIQ,YAAa;IAAAwG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElC9I,KAAA,CAAAuI,aAAA;IACEC,SAAS,EAAE,wBACT/G,OAAO,IAAIQ,YAAY,GAAG,SAAS,GAAG,EAAE,EACvC;IAAAwG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACD,CAAC,EACJrH,OAAO,IAAIQ,YAAY,GAAG,eAAe,GAAG,SACvC,CACL,CACF,CAAC,eACNjC,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,KAAK;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClB9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzB7G,YAAY,gBACXjC,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,wBAAwB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,GAE1CnH,KAAK,CAACE,aAEL,CAAC,eACN7B,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gBAAmB,CAC5C,CACF,CAAC,eACN9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzB7G,YAAY,gBACXjC,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,wBAAwB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,GAE1CnH,KAAK,CAACG,cAEL,CAAC,eACN9B,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,iBAAoB,CAC7C,CACF,CAAC,eACN9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzB7G,YAAY,gBACXjC,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,wBAAwB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,GAE1CnH,KAAK,CAACI,UAEL,CAAC,eACN/B,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,kBAAqB,CAC9C,CACF,CAAC,eACN9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzB7G,YAAY,gBACXjC,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,wBAAwB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,GAE1CnH,KAAK,CAACK,SAEL,CAAC,eACNhC,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,YAAe,CACxC,CACF,CACF,CACE,CAAC,eAGV9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/B9I,KAAA,CAAAuI,aAAA;IACEC,SAAS,EAAC,4DAA4D;IACtEO,KAAK,EAAE;MAAES,GAAG,EAAE;IAAO,CAAE;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvB9I,KAAA,CAAAuI,aAAA,CAACnI,IAAI;IACHqJ,EAAE,EAAC,WAAW;IACdjB,SAAS,EAAC,kCAAkC;IAC5CO,KAAK,EAAE;MACLW,UAAU,EAAE,mDAAmD;MAC/DC,MAAM,EAAE,MAAM;MACdC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE,WAAW;MAC1BC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,qCAAqC;MAChDC,UAAU,EAAE;IACd,CAAE;IAAAxB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF9I,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,wBACjC,CAAC,EACN/F,IAAI,KAAKA,IAAI,CAACmH,mBAAmB,IAAInH,IAAI,CAACoH,QAAQ,CAAC,gBAClDnK,KAAA,CAAAuI,aAAA,CAACnI,IAAI;IACHqJ,EAAE,EAAC,iBAAiB;IACpBjB,SAAS,EAAC,0CAA0C;IACpDO,KAAK,EAAE;MACLqB,WAAW,EAAE,SAAS;MACtBnB,KAAK,EAAE,SAAS;MAChBW,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE,WAAW;MAC1BC,aAAa,EAAE,KAAK;MACpBM,WAAW,EAAE,KAAK;MAClBJ,UAAU,EAAE;IACd,CAAE;IACFK,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACW,UAAU,GAAG,SAAS;MACrCa,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACE,KAAK,GAAG,OAAO;MAC9BsB,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC0B,SAAS,GAAG,kBAAkB;MAC7CF,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACiB,SAAS,GACtB,qCAAqC;IACzC,CAAE;IACFU,YAAY,EAAGH,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACW,UAAU,GAAG,aAAa;MACzCa,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACE,KAAK,GAAG,SAAS;MAChCsB,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC0B,SAAS,GAAG,eAAe;MAC1CF,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACiB,SAAS,GAAG,MAAM;IACnC,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF9I,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,kBAChC,CAAC,GACL/F,IAAI,gBACN/C,KAAA,CAAAuI,aAAA;IACEC,SAAS,EAAC,4CAA4C;IACtDO,KAAK,EAAE;MACLqB,WAAW,EAAE,SAAS;MACtBnB,KAAK,EAAE,SAAS;MAChBW,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE,WAAW;MAC1BC,aAAa,EAAE,KAAK;MACpBM,WAAW,EAAE,KAAK;MAClBM,MAAM,EAAE;IACV,CAAE;IACFC,KAAK,EAAC,mDAAmD;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzD9I,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,+BACjC,CAAC,gBAEN9I,KAAA,CAAAuI,aAAA,CAACnI,IAAI;IACHqJ,EAAE,EAAC,QAAQ;IACXjB,SAAS,EAAC,0CAA0C;IACpDO,KAAK,EAAE;MACLqB,WAAW,EAAE,SAAS;MACtBnB,KAAK,EAAE,SAAS;MAChBW,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE,WAAW;MAC1BC,aAAa,EAAE,KAAK;MACpBM,WAAW,EAAE,KAAK;MAClBJ,UAAU,EAAE;IACd,CAAE;IACFK,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACW,UAAU,GAAG,SAAS;MACrCa,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACE,KAAK,GAAG,OAAO;MAC9BsB,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC0B,SAAS,GAAG,kBAAkB;MAC7CF,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACiB,SAAS,GACtB,qCAAqC;IACzC,CAAE;IACFU,YAAY,EAAGH,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACW,UAAU,GAAG,aAAa;MACzCa,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACE,KAAK,GAAG,SAAS;MAChCsB,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC0B,SAAS,GAAG,eAAe;MAC1CF,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACiB,SAAS,GAAG,MAAM;IACnC,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF9I,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,mBACvC,CAEL,CACF,CAAC,eAGN9I,KAAA,CAAAuI,aAAA;IAASC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB9I,KAAA,CAAAuI,aAAA;IAAIC,SAAS,EAAC,gCAAgC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3CnG,UAAU,gBACT3C,KAAA,CAAAuI,aAAA,CAAAvI,KAAA,CAAA6K,QAAA,qBACE7K,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,iCAAiC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,kBAEnD,eAAA9I,KAAA,CAAAuI,aAAA;IAAMC,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpCrG,eAAe,CAACyC,MAAM,EAAE,GAAG,EAC3BzC,eAAe,CAACyC,MAAM,KAAK,CAAC,GAAG,QAAQ,GAAG,SACvC,CAAC,eACPlF,KAAA,CAAAuI,aAAA;IACEC,SAAS,EAAC,uCAAuC;IACjDc,OAAO,EAAEzB,kBAAmB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE5B9I,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,iBAC/B,CACR,CAAC,gBAEH9I,KAAA,CAAAuI,aAAA,CAAAvI,KAAA,CAAA6K,QAAA,qBACE7K,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,+BAA+B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,qBAEjD,CAEF,CAAC,eAEL9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,KAAK;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjBrH,OAAO,IAAIoB,aAAa;EACvB;EACAiI,KAAK,CAACC,IAAI,CAAC;IAAE7F,MAAM,EAAE;EAAE,CAAC,CAAC,CAAC8F,GAAG,CAAC,CAACC,CAAC,EAAErG,KAAK,kBACrC5E,KAAA,CAAAuI,aAAA,CAACD,eAAe;IAACpE,GAAG,EAAEU,KAAM;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAC/B,CAAC,GACAnG,UAAU;EACZ;EACAF,eAAe,CAACyC,MAAM,KAAK,CAAC,gBAC1BlF,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjC9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClC9I,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,oDAEtC,eAAA9I,KAAA,CAAAuI,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,eACN9I,KAAA,CAAAuI,aAAA;IACEC,SAAS,EAAC,sBAAsB;IAChCc,OAAO,EAAEzB,kBAAmB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE5B9I,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,qBAC7B,CACL,CACF,CAAC,GAENrG,eAAe,CAACuI,GAAG,CAAC,CAACnE,OAAO,EAAEjC,KAAK,KAAK;IACtC,MAAMsG,MAAM,GAAG3J,MAAM,CAACsF,OAAO,CAACI,EAAE,CAAC,KAAK,OAAO;IAC7C,MAAMkE,UAAU,GACdtE,OAAO,CAACuE,WAAW,IACnBvE,OAAO,CAACsE,UAAU,IAClBtE,OAAO,CAACwE,YAAY,IACpBxE,OAAO,CAACyE,WAAW;IAErB,oBACEtL,KAAA,CAAAuI,aAAA;MACErE,GAAG,EAAE,YAAY2C,OAAO,CAACI,EAAE,IAAIrC,KAAK,EAAG;MACvC4D,SAAS,EAAC,mCAAmC;MAC7CO,KAAK,EAAE;QAAEC,cAAc,EAAE,GAAGpE,KAAK,GAAG,GAAG;MAAI,CAAE;MAAA6D,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAE7C9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,yBAAyB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACtC9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAChC9I,KAAA,CAAAuI,aAAA;MACEgD,GAAG,EACD1E,OAAO,CAACjB,KAAK,IAAIiB,OAAO,CAACjB,KAAK,CAAC4F,IAAI,CAAC,CAAC,KAAK,EAAE,GACxC3E,OAAO,CAACjB,KAAK,GACb,wBACL;MACD4C,SAAS,EAAC,cAAc;MACxBiD,GAAG,EAAE5E,OAAO,CAAC+D,KAAM;MACnB7B,KAAK,EAAE;QAAE2C,MAAM,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAU,CAAE;MACjDC,OAAO,EAAGrB,CAAC,IAAK;QACdA,CAAC,CAACC,MAAM,CAACe,GAAG,GAAG,wBAAwB;MACzC,CAAE;MAAA9C,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACH,CAAC,EAGDjC,OAAO,CAACgF,YAAY,iBACnB7L,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,sDAAsD;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACnEjC,OAAO,CAACgF,YAAY,CAClBnE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CACjBoE,WAAW,CAAC,CACX,CACP,EAGAZ,MAAM,iBACLlL,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,kEAAkE;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAChF9I,KAAA,CAAAuI,aAAA;MAAGC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAI,CAAC,QAClC,CACP,eAGD9I,KAAA,CAAAuI,aAAA;MACEC,SAAS,EAAC,iCAAiC;MAC3CO,KAAK,EAAE;QAAEgD,GAAG,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAM,CAAE;MAAAvD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAErC9I,KAAA,CAAAuI,aAAA;MAAGC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAI,CAAC,UAClC,CACH,CAAC,eAEN9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,8BAA8B;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC3C9I,KAAA,CAAAuI,aAAA;MACEC,SAAS,EAAC,0BAA0B;MACpCoC,KAAK,EAAE/D,OAAO,CAAC+D,KAAM;MAAAnC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEpBjC,OAAO,CAAC+D,KACP,CAAC,eAEL5K,KAAA,CAAAuI,aAAA;MAAGC,SAAS,EAAC,uBAAuB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,KAChC,EAACjC,OAAO,CAACoF,KAAK,EAChBpF,OAAO,CAACqF,YAAY,GAAG,CAAC,iBACvBlM,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,MAAM;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACpB9I,KAAA,CAAAuI,aAAA;MAAGC,SAAS,EAAC,0BAA0B;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAI,CAAC,EAAC,GAAG,EAC/CjC,OAAO,CAACqF,YAAY,CAACC,OAAO,CAAC,CAAC,CAC3B,CAEP,CAAC,eAEJnM,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,MAAM;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACnB9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,wDAAwD;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACrE9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAE7B,CAAC,eACP9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,sBAAsB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACnCT,cAAc,CAAC8C,UAAU,CACtB,CACH,CAAC,EAELtE,OAAO,CAACuF,aAAa,iBACpBpM,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,wDAAwD;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACrE9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,UAE7B,CAAC,eACP9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,OAAO;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACpBT,cAAc,CAACxB,OAAO,CAACuF,aAAa,CACjC,CACH,CACN,eAEDpM,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,mDAAmD;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAChE9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,OAAW,CAAC,eAC/C9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACjCjC,OAAO,CAAChD,UAAU,IAAI,CACnB,CACH,CACF,CAAC,eAEN7D,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,MAAM;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACnB9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,kCAAkC;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC/C9I,KAAA,CAAAuI,aAAA;MAAOC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,gBAE/B,CAAC,eACR9I,KAAA,CAAAuI,aAAA;MACEC,SAAS,EAAE,WACT0C,MAAM,GAAG,aAAa,GAAG,YAAY,EACpC;MAAAzC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEFvH,MAAM,CAACsF,OAAO,CAACI,EAAE,CAAC,IAAI,YACnB,CACH,CACF,CAAC,eAENjH,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACtB9I,KAAA,CAAAuI,aAAA,CAACnI,IAAI;MACHqJ,EAAE,EAAE,YAAY5C,OAAO,CAACI,EAAE,EAAG;MAC7BuB,SAAS,EAAC,uBAAuB;MACjCc,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAACjB,OAAO,CAACI,EAAE,CAAE;MAAAwB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAE7C9I,KAAA,CAAAuI,aAAA;MAAGC,SAAS,EAAC,iBAAiB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAI,CAAC,gBAC/B,CACH,CACF,CACF,CACF,CAAC;EAEV,CAAC,CACF,GACC7H,gBAAgB,CAACiE,MAAM,KAAK,CAAC,gBAC/BlF,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjC9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/B9I,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,iDAExC,CACF,CAAC,GAEN7H,gBAAgB,CAAC+J,GAAG,CAAC,CAACnE,OAAO,EAAEjC,KAAK,KAAK;IACvC,MAAMsG,MAAM,GAAG3J,MAAM,CAACsF,OAAO,CAACI,EAAE,CAAC,KAAK,OAAO;IAC7C,MAAMkE,UAAU,GACdtE,OAAO,CAACuE,WAAW,IACnBvE,OAAO,CAACsE,UAAU,IAClBtE,OAAO,CAACwE,YAAY,IACpBxE,OAAO,CAACyE,WAAW;IAErB,oBACEtL,KAAA,CAAAuI,aAAA;MACErE,GAAG,EAAE,YAAY2C,OAAO,CAACI,EAAE,IAAIrC,KAAK,EAAG;MACvC4D,SAAS,EAAC,mCAAmC;MAC7CO,KAAK,EAAE;QAAEC,cAAc,EAAE,GAAGpE,KAAK,GAAG,GAAG;MAAI,CAAE;MAAA6D,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAE7C9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,yBAAyB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACtC9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAChC9I,KAAA,CAAAuI,aAAA;MACEgD,GAAG,EACD1E,OAAO,CAACjB,KAAK,IAAIiB,OAAO,CAACjB,KAAK,CAAC4F,IAAI,CAAC,CAAC,KAAK,EAAE,GACxC3E,OAAO,CAACjB,KAAK,GACb,wBACL;MACD4C,SAAS,EAAC,cAAc;MACxBiD,GAAG,EAAE5E,OAAO,CAAC+D,KAAM;MACnB7B,KAAK,EAAE;QAAE2C,MAAM,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAU,CAAE;MACjDC,OAAO,EAAGrB,CAAC,IAAK;QACdA,CAAC,CAACC,MAAM,CAACe,GAAG,GAAG,wBAAwB;MACzC,CAAE;MAAA9C,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACH,CAAC,EAGDjC,OAAO,CAACgF,YAAY,iBACnB7L,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,sDAAsD;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACnEjC,OAAO,CAACgF,YAAY,CAClBnE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CACjBoE,WAAW,CAAC,CACX,CACP,EAGAZ,MAAM,iBACLlL,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,kEAAkE;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAChF9I,KAAA,CAAAuI,aAAA;MAAGC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAI,CAAC,QAClC,CACP,EAGAjC,OAAO,CAACoB,QAAQ,iBACfjI,KAAA,CAAAuI,aAAA;MACEC,SAAS,EAAC,oCAAoC;MAC9CO,KAAK,EAAE;QAAEgD,GAAG,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAM,CAAE;MAAAvD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAErC9I,KAAA,CAAAuI,aAAA;MAAGC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAI,CAAC,YAChC,CAEL,CAAC,eAEN9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,8BAA8B;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC3C9I,KAAA,CAAAuI,aAAA;MACEC,SAAS,EAAC,0BAA0B;MACpCoC,KAAK,EAAE/D,OAAO,CAAC+D,KAAM;MAAAnC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEpBjC,OAAO,CAAC+D,KACP,CAAC,eAEL5K,KAAA,CAAAuI,aAAA;MAAGC,SAAS,EAAC,uBAAuB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,KAChC,EAACjC,OAAO,CAACoF,KAAK,EAChBpF,OAAO,CAACqF,YAAY,GAAG,CAAC,iBACvBlM,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,MAAM;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACpB9I,KAAA,CAAAuI,aAAA;MAAGC,SAAS,EAAC,0BAA0B;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAI,CAAC,EAAC,GAAG,EAC/CjC,OAAO,CAACqF,YAAY,CAACC,OAAO,CAAC,CAAC,CAC3B,CAEP,CAAC,eAEJnM,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,MAAM;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACnB9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,wDAAwD;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACrE9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAE7B,CAAC,eACP9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,sBAAsB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACnCT,cAAc,CAAC8C,UAAU,CACtB,CACH,CAAC,EAELtE,OAAO,CAACuF,aAAa,iBACpBpM,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,wDAAwD;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACrE9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,UAAc,CAAC,eAClD9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,OAAO;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACpBT,cAAc,CAACxB,OAAO,CAACuF,aAAa,CACjC,CACH,CACN,eAEDpM,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,mDAAmD;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAChE9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,OAAW,CAAC,eAC/C9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACjCjC,OAAO,CAAChD,UAAU,IAAI,CACnB,CACH,CACF,CAAC,eAEN7D,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,MAAM;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACnB9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,kCAAkC;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC/C9I,KAAA,CAAAuI,aAAA;MAAOC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,gBAE/B,CAAC,eACR9I,KAAA,CAAAuI,aAAA;MACEC,SAAS,EAAE,WACT0C,MAAM,GAAG,aAAa,GAAG,YAAY,EACpC;MAAAzC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEFvH,MAAM,CAACsF,OAAO,CAACI,EAAE,CAAC,IAAI,YACnB,CACH,CACF,CAAC,eAENjH,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACtB9I,KAAA,CAAAuI,aAAA,CAACnI,IAAI;MACHqJ,EAAE,EAAE,YAAY5C,OAAO,CAACI,EAAE,EAAG;MAC7BuB,SAAS,EAAC,uBAAuB;MACjCc,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAACjB,OAAO,CAACI,EAAE,CAAE;MAAAwB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAE7C9I,KAAA,CAAAuI,aAAA;MAAGC,SAAS,EAAC,iBAAiB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAI,CAAC,gBAC/B,CACH,CACF,CACF,CACF,CAAC;EAEV,CAAC,CAEA,CACE,CAAC,EAGT,CAACnG,UAAU,iBACV3C,KAAA,CAAAuI,aAAA;IAASC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB9I,KAAA,CAAAuI,aAAA;IAAIC,SAAS,EAAC,gCAAgC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5C9I,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,+BAA+B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,sBAEjD,eAAA9I,KAAA,CAAAuI,aAAA;IACEC,SAAS,EAAC,yBAAyB;IACnCO,KAAK,EAAE;MAAEsD,QAAQ,EAAE;IAAQ,CAAE;IAAA5D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B,wCAEM,CACL,CAAC,eAEL9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,KAAK;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjBzH,UAAU,CAACiL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACtB,GAAG,CAAC,CAACuB,QAAQ,EAAE3H,KAAK,kBAC1C5E,KAAA,CAAAuI,aAAA;IACErE,GAAG,EAAEqI,QAAQ,CAAC1G,IAAI,IAAIjB,KAAM;IAC5B4D,SAAS,EAAC,iCAAiC;IAC3CO,KAAK,EAAE;MACLC,cAAc,EAAE,GAAGpE,KAAK,GAAG,GAAG,GAAG;MACjC4H,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,gCACT7H,KAAK,GAAG,GAAG;IAEf,CAAE;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF9I,KAAA,CAAAuI,aAAA,CAACzH,gBAAgB;IACfyL,QAAQ,EAAEA,QAAS;IACnBjD,OAAO,EAAE/B,mBAAoB;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC9B,CACE,CACN,CACE,CACE,CACV,EAGA,CAACnG,UAAU,IAAIxB,gBAAgB,CAAC+D,MAAM,GAAG,CAAC,iBACzClF,KAAA,CAAAuI,aAAA;IAASC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB9I,KAAA,CAAAuI,aAAA;IAAIC,SAAS,EAAC,gCAAgC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5C9I,KAAA,CAAAuI,aAAA;IAAGC,SAAS,EAAC,8BAA8B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAI,CAAC,qBAE9C,CAAC,eAEL9I,KAAA,CAAAuI,aAAA;IAAKC,SAAS,EAAC,KAAK;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjB3H,gBAAgB,CAAC6J,GAAG,CAAC,CAACnE,OAAO,EAAEjC,KAAK,KAAK;IACxC,MAAMsG,MAAM,GAAG3J,MAAM,CAACsF,OAAO,CAACI,EAAE,CAAC,KAAK,OAAO;IAC7C,MAAMkE,UAAU,GACdtE,OAAO,CAACuE,WAAW,IACnBvE,OAAO,CAACsE,UAAU,IAClBtE,OAAO,CAACwE,YAAY,IACpBxE,OAAO,CAACyE,WAAW;IAErB,oBACEtL,KAAA,CAAAuI,aAAA;MACErE,GAAG,EAAE,YAAY2C,OAAO,CAACI,EAAE,IAAIrC,KAAK,EAAG;MACvC4D,SAAS,EAAC,mCAAmC;MAC7CO,KAAK,EAAE;QAAEC,cAAc,EAAE,GAAGpE,KAAK,GAAG,GAAG;MAAI,CAAE;MAAA6D,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAE7C9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,yBAAyB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACtC9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAChC9I,KAAA,CAAAuI,aAAA;MACEgD,GAAG,EACD1E,OAAO,CAACjB,KAAK,IAAIiB,OAAO,CAACjB,KAAK,CAAC4F,IAAI,CAAC,CAAC,KAAK,EAAE,GACxC3E,OAAO,CAACjB,KAAK,GACb,wBACL;MACD4C,SAAS,EAAC,cAAc;MACxBiD,GAAG,EAAE5E,OAAO,CAAC+D,KAAM;MACnB7B,KAAK,EAAE;QAAE2C,MAAM,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAU,CAAE;MACjDC,OAAO,EAAGrB,CAAC,IAAK;QACdA,CAAC,CAACC,MAAM,CAACe,GAAG,GAAG,wBAAwB;MACzC,CAAE;MAAA9C,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACH,CAAC,eAEF9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,qDAAqD;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACnE9I,KAAA,CAAAuI,aAAA;MAAGC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAI,CAAC,YAChC,CAAC,EAENoC,MAAM,iBACLlL,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,oDAAoD;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAClE9I,KAAA,CAAAuI,aAAA;MAAGC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAI,CAAC,QAClC,CAEL,CAAC,eAEN9I,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,WAAW;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACxB9I,KAAA,CAAAuI,aAAA;MACEC,SAAS,EAAC,0BAA0B;MACpCoC,KAAK,EAAE/D,OAAO,CAAC+D,KAAM;MAAAnC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEpBjC,OAAO,CAAC+D,KACP,CAAC,eAEL5K,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,wDAAwD;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACrE9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAAkB,CAAC,eACtD9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,4BAA4B;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACzCT,cAAc,CAAC8C,UAAU,CACtB,CACH,CAAC,eAENnL,KAAA,CAAAuI,aAAA;MAAKC,SAAS,EAAC,wDAAwD;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACrE9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,QAAY,CAAC,eAChD9I,KAAA,CAAAuI,aAAA;MAAMC,SAAS,EAAC,eAAe;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC5BjC,OAAO,CAAC6F,WAAW,IAAI,CACpB,CACH,CAAC,eAEN1M,KAAA,CAAAuI,aAAA,CAACnI,IAAI;MACHqJ,EAAE,EAAE,YAAY5C,OAAO,CAACI,EAAE,EAAG;MAC7BuB,SAAS,EAAC,sCAAsC;MAChDc,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAACjB,OAAO,CAACI,EAAE,CAAE;MAAAwB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC9C,cAEK,CACH,CACF,CACF,CAAC;EAEV,CAAC,CACE,CACE,CAER,CACF,CAAC;AAEV;AAEA,eAAe9H,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}