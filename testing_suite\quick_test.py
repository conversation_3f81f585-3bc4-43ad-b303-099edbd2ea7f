#!/usr/bin/env python3
"""
Quick Test Runner for Online Auction System
Run basic tests to verify system functionality
"""

import os
import sys
import subprocess
import time

def run_django_tests():
    """Run basic Django unit tests"""
    print("🧪 Running Django Unit Tests...")
    
    os.chdir('backend')
    
    try:
        # Run a simple test to verify Django setup
        result = subprocess.run([
            sys.executable, 'manage.py', 'test', 
            'tests.test_models.UserProfileModelTest.test_user_profile_creation',
            '--verbosity=2'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Django tests passed")
            return True
        else:
            print("❌ Django tests failed")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error running Django tests: {e}")
        return False
    finally:
        os.chdir('..')

def test_api_endpoints():
    """Test basic API endpoints"""
    print("🔌 Testing API Endpoints...")
    
    try:
        import requests
        
        base_url = "http://127.0.0.1:8000"
        endpoints = [
            "/api/",
            "/api/auctions/",
            "/api/categories/"
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(base_url + endpoint, timeout=5)
                if response.status_code < 500:
                    print(f"✅ {endpoint}: OK ({response.status_code})")
                else:
                    print(f"⚠️ {endpoint}: Server Error ({response.status_code})")
            except requests.exceptions.RequestException as e:
                print(f"❌ {endpoint}: Connection failed")
        
        return True
        
    except ImportError:
        print("❌ Requests library not available")
        return False

def test_frontend():
    """Test frontend accessibility"""
    print("🌐 Testing Frontend...")
    
    try:
        import requests
        
        response = requests.get("http://localhost:3001", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend: Accessible")
            return True
        else:
            print(f"⚠️ Frontend: Unexpected status ({response.status_code})")
            return False
            
    except requests.exceptions.RequestException:
        print("❌ Frontend: Not accessible")
        return False
    except ImportError:
        print("❌ Requests library not available")
        return False

def check_database():
    """Check database connectivity"""
    print("🗄️ Checking Database...")
    
    os.chdir('backend')
    
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'check', '--database', 'default'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Database: Connected")
            return True
        else:
            print("❌ Database: Connection issues")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False
    finally:
        os.chdir('..')

def main():
    """Run quick tests"""
    print("🚀 QUICK SYSTEM TEST")
    print("=" * 40)
    
    start_time = time.time()
    
    tests = [
        ("Database Check", check_database),
        ("API Endpoints", test_api_endpoints),
        ("Frontend", test_frontend),
        ("Django Tests", run_django_tests)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 20)
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 QUICK TEST SUMMARY")
    print("=" * 40)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    duration = time.time() - start_time
    print(f"Duration: {duration:.2f} seconds")
    
    if passed == total:
        print("\n🎉 All quick tests passed! System is ready.")
    else:
        print("\n⚠️ Some tests failed. Check the output above.")
        print("💡 Make sure all services are running:")
        print("   - Django backend: python manage.py runserver")
        print("   - React frontend: npm start")
        print("   - PostgreSQL database")
        print("   - Redis server")

if __name__ == "__main__":
    main()
