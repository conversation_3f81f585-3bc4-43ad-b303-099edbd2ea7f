from django.conf import settings
from django.core.mail import send_mail

from ..models import Notification


def notify_user(user, message, email_subject=None):
    # In-App Notification
    Notification.objects.create(user=user, message=message)

    # Email Notification
    if user.email and email_subject:
        send_mail(
            email_subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [user.email],
            fail_silently=True,
        )
