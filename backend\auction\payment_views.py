"""
Payment Views for Stripe Payment Gateway
Handles Stripe payment processing, webhooks, and transactions
"""

import json
import logging

import stripe
from django.conf import settings
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response

from .models import Auction, Payment
from .payment_gateways import PaymentGatewayFactory, convert_currency
from .serializers import PaymentSerializer

logger = logging.getLogger(__name__)

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


@api_view(["POST"])
@permission_classes([AllowAny])  # Temporarily allow all access
def create_razorpay_order(request):
    """Create Razorpay order for auction payment"""
    try:
        auction_id = request.data.get("auction_id")
        amount = request.data.get("amount")
        currency = request.data.get("currency", "USD")

        if not auction_id or not amount:
            return Response(
                {"error": "auction_id and amount are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        auction = get_object_or_404(Auction, id=auction_id)

        # Convert amount to USD if needed
        if currency != "USD":
            amount = convert_currency(amount, currency, "USD")

        # Create payment record
        payment = Payment.objects.create(
            user=request.user if request.user.is_authenticated else None,
            auction=auction,
            amount=amount,
            currency="INR",
            payment_method="razorpay",
            payment_status="pending",
        )

        # Create Razorpay order
        gateway = PaymentGatewayFactory.get_gateway("razorpay")
        order_result = gateway.create_order(
            amount=amount,
            currency="INR",
            receipt=f"auction_{auction_id}_payment_{payment.id}",
        )

        if order_result["success"]:
            payment.gateway_order_id = order_result["order_id"]
            payment.save()

            return Response(
                {
                    "success": True,
                    "order_id": order_result["order_id"],
                    "amount": order_result["amount"],
                    "currency": order_result["currency"],
                    "payment_id": payment.id,
                    "key_id": settings.RAZORPAY_KEY_ID,
                    "auction_title": auction.title,
                }
            )
        else:
            payment.payment_status = "failed"
            payment.failure_reason = order_result.get("error", "Unknown error")
            payment.save()

            return Response(
                {"error": order_result.get("error", "Failed to create order")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    except Exception as e:
        logger.error(f"Razorpay order creation error: {str(e)}")
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])  # Temporarily allow all access
def verify_razorpay_payment(request):
    """Verify Razorpay payment"""
    try:
        payment_id = request.data.get("razorpay_payment_id")
        order_id = request.data.get("razorpay_order_id")
        signature = request.data.get("razorpay_signature")
        payment_db_id = request.data.get("payment_id")

        if not all([payment_id, order_id, signature, payment_db_id]):
            return Response(
                {"error": "Missing required payment verification data"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        payment = get_object_or_404(Payment, id=payment_db_id)

        # Verify signature
        gateway = PaymentGatewayFactory.get_gateway("razorpay")
        is_valid = gateway.verify_payment(payment_id, order_id, signature)

        if is_valid:
            # Update payment status
            payment.payment_status = "completed"
            payment.gateway_payment_id = payment_id
            payment.gateway_signature = signature
            payment.payment_date = timezone.now()
            payment.save()

            # Get payment details from Razorpay
            payment_details = gateway.get_payment_details(payment_id)

            return Response(
                {
                    "success": True,
                    "message": "Payment verified successfully",
                    "payment": PaymentSerializer(payment).data,
                    "payment_details": (
                        payment_details.get("payment", {})
                        if payment_details["success"]
                        else {}
                    ),
                }
            )
        else:
            payment.payment_status = "failed"
            payment.failure_reason = "Signature verification failed"
            payment.save()

            return Response(
                {"error": "Payment verification failed"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    except Exception as e:
        logger.error(f"Razorpay payment verification error: {str(e)}")
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])  # Temporarily allow all access
def create_upi_payment(request):
    """Create UPI payment link"""
    try:
        auction_id = request.data.get("auction_id")
        amount = request.data.get("amount")
        upi_id = request.data.get("upi_id", settings.DEFAULT_UPI_ID)

        if not auction_id or not amount:
            return Response(
                {"error": "auction_id and amount are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        auction = get_object_or_404(Auction, id=auction_id)

        # Create payment record
        payment = Payment.objects.create(
            user=request.user if request.user.is_authenticated else None,
            auction=auction,
            amount=amount,
            currency="USD",
            payment_method="upi",
            payment_status="pending",
        )

        # Generate UPI link
        gateway = PaymentGatewayFactory.get_gateway("upi")
        upi_result = gateway.generate_upi_link(
            vpa=upi_id,
            amount=amount,
            name=settings.UPI_MERCHANT_NAME,
            note=f"Payment for {auction.title}",
        )

        if upi_result["success"]:
            return Response(
                {
                    "success": True,
                    "upi_link": upi_result["upi_link"],
                    "payment_id": payment.id,
                    "qr_code_data": upi_result[
                        "upi_link"
                    ],  # Can be used to generate QR code
                    "instructions": "Scan QR code or click UPI link to pay",
                }
            )
        else:
            payment.payment_status = "failed"
            payment.failure_reason = upi_result.get(
                "error", "UPI link generation failed"
            )
            payment.save()

            return Response(
                {"error": upi_result.get("error", "Failed to create UPI payment")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    except Exception as e:
        logger.error(f"UPI payment creation error: {str(e)}")
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([AllowAny])  # Temporarily allow all access
def get_payment_methods(request):
    """Get available payment methods for India"""
    return Response(
        {
            "payment_methods": [
                {
                    "id": "razorpay",
                    "name": "Razorpay",
                    "description": "Cards, UPI, Net Banking, Wallets",
                    "logo": "/static/images/razorpay-logo.png",
                    "supported_methods": ["card", "upi", "netbanking", "wallet"],
                    "fees": "2% + GST",
                    "processing_time": "Instant",
                },
                {
                    "id": "upi",
                    "name": "UPI Direct",
                    "description": "Pay directly via UPI apps",
                    "logo": "/static/images/upi-logo.png",
                    "supported_methods": ["upi"],
                    "fees": "Free",
                    "processing_time": "Instant",
                },
                {
                    "id": "paytm",
                    "name": "Paytm",
                    "description": "Paytm Wallet & Payment Gateway",
                    "logo": "/static/images/paytm-logo.png",
                    "supported_methods": ["wallet", "card", "netbanking"],
                    "fees": "2-3%",
                    "processing_time": "Instant",
                },
                {
                    "id": "cashfree",
                    "name": "Cashfree",
                    "description": "Multiple payment options",
                    "logo": "/static/images/cashfree-logo.png",
                    "supported_methods": ["card", "upi", "netbanking"],
                    "fees": "1.75% + GST",
                    "processing_time": "Instant",
                },
            ],
            "currency": "USD",
            "currency_symbol": "$",
        }
    )


@api_view(["GET"])
@permission_classes([AllowAny])  # Temporarily allow all access
def get_exchange_rates(request):
    """Get current exchange rates for currency conversion"""
    return Response(
        {
            "base_currency": "USD",
            "rates": {
                "EUR": 0.85,
                "GBP": 0.73,
                "CAD": 1.25,
                "AUD": 1.35,
                "INR": 83.0,
            },
            "last_updated": timezone.now().isoformat(),
        }
    )


@csrf_exempt
@api_view(["POST"])
@permission_classes([AllowAny])
def razorpay_webhook(request):
    """Handle Razorpay webhooks"""
    try:
        # Verify webhook signature
        webhook_signature = request.META.get("HTTP_X_RAZORPAY_SIGNATURE")
        webhook_body = request.body

        # Process webhook event
        event_data = json.loads(webhook_body)
        event_type = event_data.get("event")

        if event_type == "payment.captured":
            payment_entity = event_data["payload"]["payment"]["entity"]
            payment_id = payment_entity["id"]
            order_id = payment_entity["order_id"]

            # Update payment status in database
            try:
                payment = Payment.objects.get(gateway_order_id=order_id)
                payment.payment_status = "completed"
                payment.gateway_payment_id = payment_id
                payment.payment_date = timezone.now()
                payment.save()

                logger.info(f"Payment {payment_id} marked as completed via webhook")
            except Payment.DoesNotExist:
                logger.warning(f"Payment not found for order_id: {order_id}")

        return JsonResponse({"status": "success"})

    except Exception as e:
        logger.error(f"Razorpay webhook error: {str(e)}")
        return JsonResponse({"status": "error"}, status=500)
