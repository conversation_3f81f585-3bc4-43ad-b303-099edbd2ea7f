@echo off
echo ============================================================
echo   PostgreSQL Database Setup for Online Auction System
echo ============================================================
echo.

echo Step 1: Creating database and user...
echo Please enter your PostgreSQL 'postgres' user password when prompted.
echo.

echo Creating database...
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost -c "CREATE DATABASE online_auction_db;"

echo Creating user...
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost -c "CREATE USER auction_user WITH PASSWORD 'auction_password_2024';"

echo Granting database privileges...
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost -c "GRANT ALL PRIVILEGES ON DATABASE online_auction_db TO auction_user;"

echo Setting up schema privileges...
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost -d online_auction_db -c "GRANT ALL ON SCHEMA public TO auction_user;"
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost -d online_auction_db -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO auction_user;"
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost -d online_auction_db -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO auction_user;"

echo.
echo ============================================================
echo Database setup completed!
echo ============================================================
echo.
echo Database Details:
echo   Host: localhost
echo   Port: 5432
echo   Database: online_auction_db
echo   User: auction_user
echo   Password: auction_password_2024
echo.
pause
