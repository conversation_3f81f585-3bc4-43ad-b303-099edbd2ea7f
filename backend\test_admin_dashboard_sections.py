#!/usr/bin/env python3
"""
Test all sections of the admin dashboard to ensure they're updating properly
"""

import requests
import json
import time

def test_admin_dashboard_sections():
    print("🔍 TESTING ADMIN DASHBOARD SECTIONS")
    print("="*50)
    
    # Test the ultra-fast dashboard endpoint
    print("\n1. Testing Ultra-Fast Dashboard Endpoint")
    print("-" * 40)
    
    start_time = time.time()
    response = requests.get('http://127.0.0.1:8000/api/ultra-fast-dashboard/')
    end_time = time.time()
    
    response_time = (end_time - start_time) * 1000
    print(f"⏱️  Response Time: {response_time:.0f}ms")
    print(f"📊 Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        
        if data.get('success'):
            dashboard_data = data['data']
            
            # Test Basic Stats Section
            print(f"\n✅ BASIC STATS SECTION:")
            basic_stats = dashboard_data.get('basic_stats', {})
            print(f"   👥 Total Users: {basic_stats.get('total_users', 0)}")
            print(f"   🏷️  Total Auctions: {basic_stats.get('total_auctions', 0)}")
            print(f"   🎯 Active Auctions: {basic_stats.get('active_auctions', 0)}")
            print(f"   💰 Total Bids: {basic_stats.get('total_bids', 0)}")
            print(f"   🚨 Pending Frauds: {basic_stats.get('pending_frauds', 0)}")
            
            # Test Revenue Section
            print(f"\n✅ REVENUE SECTION:")
            total_revenue = dashboard_data.get('total_revenue', 0)
            print(f"   💵 Total Revenue: ${total_revenue:,.2f}")
            print(f"   📈 Monthly Revenue: ${total_revenue * 0.7:,.2f}")
            
            # Test Users Section
            print(f"\n✅ USERS SECTION:")
            users = dashboard_data.get('users', [])
            print(f"   👥 Users Count: {len(users)}")
            if users:
                print(f"   📋 Sample Users:")
                for user in users[:3]:
                    status = "Active" if user.get('is_active') else "Inactive"
                    print(f"      - {user.get('username')} ({user.get('email')}) - {status}")
            
            # Test Auctions Section
            print(f"\n✅ AUCTIONS SECTION:")
            auctions = dashboard_data.get('auctions', [])
            print(f"   🏷️  Auctions Count: {len(auctions)}")
            if auctions:
                print(f"   📋 Sample Auctions:")
                for auction in auctions[:3]:
                    status = "Approved" if auction.get('approved') else "Pending"
                    print(f"      - {auction.get('title')} (${auction.get('current_bid')}) - {status}")
            
            # Test AI Analytics Section
            print(f"\n✅ AI ANALYTICS SECTION:")
            ai_analytics = dashboard_data.get('ai_analytics', {})
            print(f"   🤖 Total Predictions: {ai_analytics.get('total_predictions', 0)}")
            print(f"   📊 Predictions Today: {ai_analytics.get('predictions_today', 0)}")
            print(f"   🎯 Average Confidence: {ai_analytics.get('avg_confidence', 0)}%")
            print(f"   📈 Model Performance: {ai_analytics.get('model_performance', 'N/A')}")
            print(f"   ✅ Accuracy Rate: {ai_analytics.get('accuracy_rate', 0)}%")
            
            # Test Fraud Alerts Section
            print(f"\n✅ FRAUD ALERTS SECTION:")
            fraud_alerts = dashboard_data.get('fraud_alerts', [])
            print(f"   🚨 Fraud Alerts Count: {len(fraud_alerts)}")
            if fraud_alerts:
                print(f"   📋 Sample Fraud Alerts:")
                for alert in fraud_alerts[:3]:
                    fraud_type = alert.get('fraud_type', '').replace('_', ' ').title()
                    print(f"      - {fraud_type} (Risk: {alert.get('risk_score', 0)}/100) - User: {alert.get('user__username', 'N/A')}")
            
            # Test Recent Auctions Section
            print(f"\n✅ RECENT AUCTIONS SECTION:")
            recent_auctions = dashboard_data.get('recent_auctions', [])
            print(f"   🏷️  Recent Auctions Count: {len(recent_auctions)}")
            if recent_auctions:
                print(f"   📋 Sample Recent Auctions:")
                for auction in recent_auctions[:3]:
                    print(f"      - {auction.get('title')} (${auction.get('current_bid')}) - Owner: {auction.get('owner__username', 'N/A')}")
            
            # Overall Assessment
            print(f"\n🎯 DASHBOARD SECTIONS ASSESSMENT:")
            sections_working = 0
            total_sections = 6
            
            if basic_stats.get('total_users', 0) > 0:
                sections_working += 1
                print(f"   ✅ Basic Stats: Working ({basic_stats.get('total_users')} users)")
            else:
                print(f"   ❌ Basic Stats: No data")
            
            if total_revenue > 0:
                sections_working += 1
                print(f"   ✅ Revenue: Working (${total_revenue:,.2f})")
            else:
                print(f"   ❌ Revenue: No data")
            
            if len(users) > 0:
                sections_working += 1
                print(f"   ✅ Users: Working ({len(users)} users)")
            else:
                print(f"   ❌ Users: No data")
            
            if len(auctions) > 0:
                sections_working += 1
                print(f"   ✅ Auctions: Working ({len(auctions)} auctions)")
            else:
                print(f"   ❌ Auctions: No data")
            
            if ai_analytics.get('total_predictions', 0) >= 0:  # 0 is valid
                sections_working += 1
                print(f"   ✅ AI Analytics: Working ({ai_analytics.get('total_predictions')} predictions)")
            else:
                print(f"   ❌ AI Analytics: No data")
            
            if len(fraud_alerts) >= 0:  # 0 is valid (no fraud is good)
                sections_working += 1
                print(f"   ✅ Fraud Alerts: Working ({len(fraud_alerts)} alerts)")
            else:
                print(f"   ❌ Fraud Alerts: No data")
            
            success_rate = (sections_working / total_sections) * 100
            print(f"\n📊 SUCCESS RATE: {success_rate:.1f}% ({sections_working}/{total_sections} sections working)")
            
            if success_rate >= 100:
                print(f"🎉 EXCELLENT: All dashboard sections are working perfectly!")
            elif success_rate >= 80:
                print(f"✅ GOOD: Most dashboard sections are working well!")
            elif success_rate >= 60:
                print(f"⚠️  MODERATE: Some dashboard sections need attention!")
            else:
                print(f"❌ POOR: Dashboard sections need significant fixes!")
                
        else:
            print(f"❌ API Error: {data.get('error', 'Unknown error')}")
    else:
        print(f"❌ HTTP Error: {response.status_code}")
        print(f"Response: {response.text[:200]}")

def test_frontend_compatibility():
    print(f"\n🔗 FRONTEND COMPATIBILITY TEST")
    print("-" * 40)
    
    response = requests.get('http://127.0.0.1:8000/api/ultra-fast-dashboard/')
    if response.status_code == 200:
        data = response.json()
        dashboard_data = data['data']
        
        # Check if data structure matches frontend expectations
        expected_fields = {
            'basic_stats': ['total_users', 'total_auctions', 'active_auctions', 'total_bids'],
            'users': ['id', 'username', 'email', 'is_active'],
            'auctions': ['id', 'title', 'current_bid', 'approved'],
            'ai_analytics': ['total_predictions', 'avg_confidence', 'model_performance'],
            'fraud_alerts': ['id', 'fraud_type', 'risk_score'],
        }
        
        compatibility_score = 0
        total_checks = len(expected_fields)
        
        for section, fields in expected_fields.items():
            if section in dashboard_data:
                section_data = dashboard_data[section]
                if isinstance(section_data, list) and section_data:
                    # Check first item for required fields
                    first_item = section_data[0]
                    has_required_fields = all(field in first_item for field in fields)
                elif isinstance(section_data, dict):
                    # Check dict for required fields
                    has_required_fields = all(field in section_data for field in fields)
                else:
                    has_required_fields = True  # Empty list is valid
                
                if has_required_fields:
                    compatibility_score += 1
                    print(f"   ✅ {section}: Compatible")
                else:
                    print(f"   ❌ {section}: Missing required fields")
            else:
                print(f"   ❌ {section}: Section missing")
        
        compatibility_rate = (compatibility_score / total_checks) * 100
        print(f"\n📊 FRONTEND COMPATIBILITY: {compatibility_rate:.1f}%")
        
        if compatibility_rate >= 100:
            print(f"🎉 PERFECT: Frontend will work seamlessly!")
        elif compatibility_rate >= 80:
            print(f"✅ GOOD: Frontend should work well!")
        else:
            print(f"⚠️  ISSUES: Frontend may have problems!")

if __name__ == "__main__":
    test_admin_dashboard_sections()
    test_frontend_compatibility()
    
    print(f"\n🚀 ADMIN DASHBOARD TESTING COMPLETE!")
    print(f"📱 Frontend URL: http://localhost:3001/admin-dashboard")
    print(f"🔗 API URL: http://127.0.0.1:8000/api/ultra-fast-dashboard/")
