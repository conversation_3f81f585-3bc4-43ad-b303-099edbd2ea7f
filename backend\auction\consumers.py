import json
from urllib.parse import parse_qs

from channels.db import database_sync_to_async
from channels.generic.websocket import AsyncWebsocketConsumer
from django.contrib.auth.models import User
from django.utils import timezone

from .models import Auction, Bid, ChatMessage, ChatRoom
from .ai_chat_service import ai_chat_service
from auctions.websocket_service import auction_websocket_service


class BidConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.auction_id = self.scope["url_route"]["kwargs"]["auction_id"]
        self.auction_group_name = f"auction_{self.auction_id}"

        await self.channel_layer.group_add(self.auction_group_name, self.channel_name)
        await self.accept()

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.auction_group_name, self.channel_name
        )

    async def receive(self, text_data):
        data = json.loads(text_data)
        amount = data["amount"]
        username = data["username"]

        bid = await self.save_bid(self.auction_id, amount, username)

        if bid:
            await self.channel_layer.group_send(
                self.auction_group_name,
                {
                    "type": "send_bid",
                    "amount": bid.amount,
                    "username": bid.user.username,
                    "timestamp": bid.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                },
            )

    async def send_bid(self, event):
        await self.send(
            text_data=json.dumps(
                {
                    "amount": event["amount"],
                    "username": event["username"],
                    "timestamp": event["timestamp"],
                }
            )
        )

    @database_sync_to_async
    def save_bid(self, auction_id, amount, username):
        try:
            user = User.objects.get(username=username)
            auction = Auction.objects.get(id=auction_id)
            amount = float(amount)
            if amount > auction.current_bid:
                bid = Bid.objects.create(auction=auction, user=user, amount=amount)
                auction.current_bid = amount
                auction.save()
                return bid
        except Exception as e:
            print("Bid error:", e)
            return None


class ChatConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for real-time chat functionality"""

    async def connect(self):
        self.room_id = self.scope["url_route"]["kwargs"]["room_id"]
        self.room_group_name = f"chat_{self.room_id}"

        # Debug authentication
        user = self.scope.get("user")
        print(
            f"Chat WebSocket connection - User: {user}, Authenticated: {user.is_authenticated if user else 'No user'}"
        )

        # Join room group
        await self.channel_layer.group_add(self.room_group_name, self.channel_name)

        await self.accept()

        # Add user to chat room participants
        if self.scope["user"].is_authenticated:
            await self.add_user_to_room(self.room_id, self.scope["user"])
            print(
                f"User {self.scope['user'].username} added to chat room {self.room_id}"
            )

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(self.room_group_name, self.channel_name)

    async def receive(self, text_data):
        """Receive message from WebSocket"""
        try:
            data = json.loads(text_data)
            message_type = data.get("type", "chat_message")

            print(f"Chat WebSocket received: {data}")
            print(f"Message type: {message_type}")

            if message_type == "chat_message":
                await self.handle_chat_message(data)
            elif message_type == "typing":
                await self.handle_typing_indicator(data)
            elif message_type == "mark_read":
                await self.handle_mark_read(data)
            elif message_type == "ping":
                # Respond to ping with pong for heartbeat
                await self.send(
                    text_data=json.dumps(
                        {"type": "pong", "timestamp": timezone.now().isoformat()}
                    )
                )
            else:
                print(f"Unknown message type: {message_type}")

        except json.JSONDecodeError:
            print(f"Invalid JSON received: {text_data}")
            await self.send(text_data=json.dumps({"error": "Invalid JSON format"}))
        except Exception as e:
            print(f"Error handling message: {e}")
            await self.send(text_data=json.dumps({"error": str(e)}))

    async def handle_chat_message(self, data):
        """Handle incoming chat message"""
        message = data.get("message", "").strip()
        if not message:
            return

        user = self.scope["user"]
        if not user.is_authenticated:
            await self.send(text_data=json.dumps({"error": "Authentication required"}))
            return

        # Save message to database
        chat_message = await self.save_message(
            room_id=self.room_id,
            sender=user,
            message=message,
            message_type=data.get("message_type", "text"),
        )

        if chat_message:
            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    "type": "chat_message",
                    "message_id": chat_message.id,
                    "message": chat_message.message,
                    "sender_id": chat_message.sender.id,
                    "sender_username": chat_message.sender.username,
                    "message_type": chat_message.message_type,
                    "timestamp": chat_message.timestamp.isoformat(),
                    "time_ago": self.get_time_ago(chat_message.timestamp),
                },
            )

            # Check if AI should respond to this message
            try:
                if ai_chat_service.should_respond(message):
                    # Get auction ID from room (assuming room_id corresponds to auction_id)
                    auction_id = await self.get_auction_id_from_room(self.room_id)
                    if auction_id:
                        # Generate AI response
                        ai_response = await ai_chat_service.generate_response(
                            message, auction_id, user
                        )

                        if ai_response:
                            # Save AI response to database
                            ai_message = await self.save_ai_message(
                                room_id=self.room_id,
                                message=ai_response,
                            )

                            if ai_message:
                                # Send AI response to room group
                                await self.channel_layer.group_send(
                                    self.room_group_name,
                                    {
                                        "type": "chat_message",
                                        "message_id": ai_message.id,
                                        "message": ai_message.message,
                                        "sender_id": ai_message.sender.id if ai_message.sender else None,
                                        "sender_username": "AuctionStore AI",
                                        "message_type": "ai_response",
                                        "timestamp": ai_message.timestamp.isoformat(),
                                        "time_ago": self.get_time_ago(ai_message.timestamp),
                                        "is_ai": True,
                                    },
                                )
            except Exception as e:
                print(f"Error generating AI response: {e}")
                # Continue without AI response if there's an error

    async def handle_typing_indicator(self, data):
        """Handle typing indicator"""
        user = self.scope["user"]
        if not user.is_authenticated:
            return

        # Send typing indicator to room group (excluding sender)
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                "type": "typing_indicator",
                "user_id": user.id,
                "username": user.username,
                "is_typing": data.get("is_typing", False),
            },
        )

    async def handle_mark_read(self, data):
        """Handle mark messages as read"""
        user = self.scope["user"]
        if not user.is_authenticated:
            return

        message_ids = data.get("message_ids", [])
        if message_ids:
            await self.mark_messages_read(message_ids, user)

    # WebSocket message handlers
    async def chat_message(self, event):
        """Send chat message to WebSocket"""
        await self.send(
            text_data=json.dumps(
                {
                    "type": "chat_message",
                    "message_id": event["message_id"],
                    "message": event["message"],
                    "sender_id": event["sender_id"],
                    "sender_username": event["sender_username"],
                    "message_type": event["message_type"],
                    "timestamp": event["timestamp"],
                    "time_ago": event["time_ago"],
                }
            )
        )

    async def typing_indicator(self, event):
        """Send typing indicator to WebSocket"""
        # Don't send typing indicator back to the sender
        if event["user_id"] != self.scope["user"].id:
            await self.send(
                text_data=json.dumps(
                    {
                        "type": "typing_indicator",
                        "user_id": event["user_id"],
                        "username": event["username"],
                        "is_typing": event["is_typing"],
                    }
                )
            )

    async def system_message(self, event):
        """Send system message to WebSocket"""
        await self.send(
            text_data=json.dumps(
                {
                    "type": "system_message",
                    "message": event["message"],
                    "timestamp": event["timestamp"],
                }
            )
        )

    # Extended WebSocket message handlers for auction features
    async def trending_update(self, event):
        """Handle trending auction updates"""
        await self.send(text_data=json.dumps({
            "type": "trending_update",
            "data": event
        }))

    async def ending_soon_alert(self, event):
        """Handle ending soon alerts"""
        await self.send(text_data=json.dumps({
            "type": "ending_soon_alert",
            "data": event
        }))

    async def analytics_update(self, event):
        """Handle analytics updates"""
        await self.send(text_data=json.dumps({
            "type": "analytics_update",
            "data": event.get("data", {})
        }))

    async def dashboard_update(self, event):
        """Handle admin dashboard updates"""
        await self.send(text_data=json.dumps({
            "type": "dashboard_update",
            "data": event.get("data", {})
        }))

    async def user_stats_update(self, event):
        """Handle user statistics updates"""
        await self.send(text_data=json.dumps({
            "type": "user_stats_update",
            "data": event.get("data", {})
        }))

    # Database operations
    @database_sync_to_async
    def save_message(self, room_id, sender, message, message_type="text"):
        """Save chat message to database"""
        try:
            room = ChatRoom.objects.get(id=room_id)
            chat_message = ChatMessage.objects.create(
                room=room, sender=sender, message=message, message_type=message_type
            )

            # Update room's last activity
            room.updated_at = timezone.now()
            room.save()

            return chat_message
        except ChatRoom.DoesNotExist:
            return None
        except Exception as e:
            print(f"Error saving message: {e}")
            return None

    @database_sync_to_async
    def add_user_to_room(self, room_id, user):
        """Add user to chat room participants"""
        try:
            room = ChatRoom.objects.get(id=room_id)
            room.add_participant(user)
            return True
        except ChatRoom.DoesNotExist:
            return False
        except Exception as e:
            print(f"Error adding user to room: {e}")
            return False

    @database_sync_to_async
    def mark_messages_read(self, message_ids, user):
        """Mark messages as read"""
        try:
            ChatMessage.objects.filter(
                id__in=message_ids, room__participants=user
            ).exclude(sender=user).update(is_read=True)
            return True
        except Exception as e:
            print(f"Error marking messages as read: {e}")
            return False

    def get_time_ago(self, timestamp):
        """Get human-readable time ago"""
        from django.utils.timesince import timesince

        return timesince(timestamp)

    @database_sync_to_async
    def get_auction_id_from_room(self, room_id):
        """Get auction ID from chat room"""
        try:
            room = ChatRoom.objects.get(id=room_id)
            # Assuming the room is associated with an auction
            # You might need to adjust this based on your ChatRoom model
            return getattr(room, 'auction_id', room_id)
        except ChatRoom.DoesNotExist:
            return None
        except Exception as e:
            print(f"Error getting auction ID from room: {e}")
            return None

    @database_sync_to_async
    def save_ai_message(self, room_id, message):
        """Save AI response message to database"""
        try:
            room = ChatRoom.objects.get(id=room_id)
            # Create AI message without a sender (or with a special AI user)
            ai_message = ChatMessage.objects.create(
                room=room,
                sender=None,  # AI messages don't have a human sender
                message=message,
                message_type="ai_response"
            )

            # Update room's last activity
            room.updated_at = timezone.now()
            room.save()

            return ai_message
        except ChatRoom.DoesNotExist:
            return None
        except Exception as e:
            print(f"Error saving AI message: {e}")
            return None


class InvalidChatConsumer(AsyncWebsocketConsumer):
    """Consumer to handle invalid chat room connections gracefully"""

    async def connect(self):
        """Accept connection but immediately send error and close"""
        await self.accept()

        # Send error message
        await self.send(
            text_data=json.dumps(
                {
                    "type": "error",
                    "message": "Invalid chat room ID. Please refresh the page.",
                    "error_code": "INVALID_ROOM_ID",
                }
            )
        )

        # Close connection after a brief delay
        await self.close(code=4000)

    async def disconnect(self, close_code):
        """Handle disconnect"""
        pass

    async def receive(self, text_data):
        """Handle any incoming messages (should not happen)"""
        await self.send(
            text_data=json.dumps(
                {
                    "type": "error",
                    "message": "Invalid chat room. Connection will be closed.",
                    "error_code": "INVALID_ROOM_ID",
                }
            )
        )


class AuctionsListConsumer(AsyncWebsocketConsumer):
    """Consumer for real-time auction list updates"""

    async def connect(self):
        """Accept WebSocket connection for auction list updates"""
        self.group_name = "auctions_list"

        # Add to auctions list group
        await self.channel_layer.group_add(self.group_name, self.channel_name)

        await self.accept()
        print(f"AuctionsListConsumer: Client connected to auctions list updates")

    async def disconnect(self, close_code):
        """Handle WebSocket disconnect"""
        await self.channel_layer.group_discard(self.group_name, self.channel_name)
        print(f"AuctionsListConsumer: Client disconnected from auctions list updates")

    async def receive(self, text_data):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(text_data)
            message_type = data.get("type", "")

            if message_type == "ping":
                # Respond to ping with pong
                await self.send(
                    text_data=json.dumps(
                        {"type": "pong", "timestamp": timezone.now().isoformat()}
                    )
                )
            else:
                print(f"AuctionsListConsumer: Unknown message type: {message_type}")

        except json.JSONDecodeError:
            print(f"AuctionsListConsumer: Invalid JSON received: {text_data}")
        except Exception as e:
            print(f"AuctionsListConsumer: Error handling message: {e}")

    async def auction_created(self, event):
        """Send auction created notification to WebSocket"""
        await self.send(
            text_data=json.dumps(
                {
                    "type": "auction_created",
                    "auction": event["auction"],
                    "message": event.get("message", "New auction created"),
                    "timestamp": event.get("timestamp", timezone.now().isoformat()),
                }
            )
        )

    async def auction_updated(self, event):
        """Send auction updated notification to WebSocket"""
        await self.send(
            text_data=json.dumps(
                {
                    "type": "auction_updated",
                    "auction": event["auction"],
                    "message": event.get("message", "Auction updated"),
                    "timestamp": event.get("timestamp", timezone.now().isoformat()),
                }
            )
        )

    async def auction_deleted(self, event):
        """Send auction deleted notification to WebSocket"""
        await self.send(
            text_data=json.dumps(
                {
                    "type": "auction_deleted",
                    "auction_id": event["auction_id"],
                    "message": event.get("message", "Auction deleted"),
                    "timestamp": event.get("timestamp", timezone.now().isoformat()),
                }
            )
        )
