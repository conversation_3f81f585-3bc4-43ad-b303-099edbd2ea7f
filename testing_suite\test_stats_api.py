#!/usr/bin/env python3
"""
Test script to verify the stats API is working correctly
"""

import requests
import json

def test_stats_api():
    """Test the platform stats API endpoint"""
    print("🧪 Testing Platform Stats API")
    print("=" * 50)
    
    url = "http://127.0.0.1:8000/api/landing/stats/"
    
    try:
        print(f"📡 Making request to: {url}")
        response = requests.get(url, timeout=10)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response successful!")
            print(f"📄 Raw response: {json.dumps(data, indent=2)}")
            
            if data.get('success') and 'stats' in data:
                stats = data['stats']
                print(f"\n📈 Platform Statistics:")
                print(f"   👥 Total Users: {stats.get('total_users')}")
                print(f"   🏷️ Total Auctions: {stats.get('total_auctions')}")
                print(f"   🔥 Active Auctions: {stats.get('active_auctions')}")
                print(f"   💰 Total Bids: {stats.get('total_bids')}")
                print(f"   📂 Categories: {stats.get('categories')}")
                
                if 'recent_activity' in stats:
                    activity = stats['recent_activity']
                    print(f"\n🆕 Recent Activity (24h):")
                    print(f"   New Auctions: {activity.get('new_auctions_24h')}")
                    print(f"   New Bids: {activity.get('new_bids_24h')}")
                
                print(f"\n⏰ Timestamp: {data.get('timestamp')}")
                
                # Verify the user count is correct (should be 9, not 85)
                user_count = stats.get('total_users')
                if user_count == 9:
                    print(f"\n✅ SUCCESS: User count is correct ({user_count})")
                    print("   Home page will now show the real user count!")
                elif user_count == 85:
                    print(f"\n❌ ISSUE: Still showing old hardcoded count ({user_count})")
                else:
                    print(f"\n⚠️ UNEXPECTED: User count is {user_count}")
                
                return True
            else:
                print(f"❌ API returned unsuccessful response")
                return False
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON decode failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_home_page_integration():
    """Test what the home page will receive"""
    print("\n🏠 Testing Home Page Integration")
    print("=" * 50)
    
    # Simulate the exact call the home page makes
    url = "http://127.0.0.1:8000/api/landing/stats/"
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success') and 'stats' in data:
                stats = data['stats']
                
                # This is exactly what the home page will do
                new_stats = {
                    'totalAuctions': stats.get('total_auctions', 0),
                    'activeAuctions': stats.get('active_auctions', 0),
                    'totalUsers': stats.get('total_users', 0),
                    'totalBids': stats.get('total_bids', 0),
                }
                
                print(f"📊 Home page will display:")
                print(f"   Total Auctions: {new_stats['totalAuctions']}")
                print(f"   Active Auctions: {new_stats['activeAuctions']}")
                print(f"   Registered Users: {new_stats['totalUsers']}")
                print(f"   Total Bids: {new_stats['totalBids']}")
                
                # Check consistency
                if new_stats['totalUsers'] == 9:
                    print(f"\n🎉 SUCCESS: Home page will show correct user count!")
                    print(f"   ✅ Before: 85 users (hardcoded)")
                    print(f"   ✅ After: {new_stats['totalUsers']} users (real count)")
                    print(f"   ✅ Consistent with admin dashboard and analytics")
                else:
                    print(f"\n⚠️ User count: {new_stats['totalUsers']} (expected 9)")
                
                return new_stats
            else:
                print("❌ Invalid API response structure")
                return None
        else:
            print(f"❌ API call failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    print("🔍 Platform Stats API Testing")
    print("=" * 60)
    
    # Test the API
    api_success = test_stats_api()
    
    # Test home page integration
    home_stats = test_home_page_integration()
    
    print("\n" + "=" * 60)
    if api_success and home_stats:
        print("🎉 ALL TESTS PASSED!")
        print("✅ API is working correctly")
        print("✅ Home page will show real user counts")
        print("✅ Platform statistics are now consistent")
    else:
        print("❌ TESTS FAILED!")
        print("⚠️ Check the API endpoint and server status")
    
    print("=" * 60)
