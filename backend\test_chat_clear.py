#!/usr/bin/env python3
"""
Test script for Chat Message Clearing functionality
Tests the chat message clearing permissions and functionality
"""

import os
import sys
import django
import requests

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from django.contrib.auth.models import User
from auction.models import Auction, ChatRoom, ChatMessage
from rest_framework_simplejwt.tokens import RefreshToken

def test_chat_clear_permissions():
    """Test chat message clearing permissions"""
    
    print("🧪 Testing Chat Message Clearing")
    print("=" * 50)
    
    try:
        # Create test users
        auction_owner, created = User.objects.get_or_create(
            username='chat_owner',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Chat',
                'last_name': 'Owner'
            }
        )
        
        participant_user, created = User.objects.get_or_create(
            username='chat_participant',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Chat',
                'last_name': 'Participant'
            }
        )
        
        random_user, created = User.objects.get_or_create(
            username='random_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Random',
                'last_name': 'User'
            }
        )
        
        print(f"✅ Test users created/found")
        
        # Create test auction
        from django.utils import timezone
        from datetime import timedelta

        test_auction, created = Auction.objects.get_or_create(
            title='Chat Test Auction',
            defaults={
                'description': 'Test auction for chat clearing',
                'starting_bid': 100.00,
                'current_bid': 100.00,
                'start_time': timezone.now(),
                'end_time': timezone.now() + timedelta(days=7),
                'owner': auction_owner,
                'category': 'electronics',
                'approved': True
            }
        )
        
        # Create chat room
        chat_room, created = ChatRoom.objects.get_or_create(
            auction=test_auction,
            defaults={'is_active': True}
        )
        
        # Add participant to chat room
        chat_room.participants.add(participant_user)
        
        print(f"✅ Chat room created: {chat_room.id}")
        print(f"   Auction: {test_auction.title}")
        print(f"   Owner: {auction_owner.username}")
        print(f"   Participants: {[p.username for p in chat_room.participants.all()]}")
        
        # Create some test messages
        for i in range(3):
            ChatMessage.objects.create(
                room=chat_room,
                sender=participant_user,
                message=f"Test message {i+1}",
                message_type="text"
            )
        
        message_count = ChatMessage.objects.filter(room=chat_room).count()
        print(f"✅ Created {message_count} test messages")
        
        base_url = "http://127.0.0.1:8000/api"
        
        # Test 1: Auction owner can clear messages
        print("\n1️⃣ Testing auction owner clearing messages...")
        owner_token = str(RefreshToken.for_user(auction_owner).access_token)
        owner_headers = {'Authorization': f'Bearer {owner_token}'}
        
        try:
            response = requests.delete(
                f"{base_url}/chat-messages/clear-room/{chat_room.id}/",
                headers=owner_headers
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Owner cleared {data.get('deleted_count', 0)} messages")
            else:
                print(f"   ❌ Error: {response.text}")
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        # Recreate messages for next test
        for i in range(2):
            ChatMessage.objects.create(
                room=chat_room,
                sender=participant_user,
                message=f"New test message {i+1}",
                message_type="text"
            )
        
        # Test 2: Participant can clear messages
        print("\n2️⃣ Testing participant clearing messages...")
        participant_token = str(RefreshToken.for_user(participant_user).access_token)
        participant_headers = {'Authorization': f'Bearer {participant_token}'}
        
        try:
            response = requests.delete(
                f"{base_url}/chat-messages/clear-room/{chat_room.id}/",
                headers=participant_headers
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Participant cleared {data.get('deleted_count', 0)} messages")
            else:
                print(f"   ❌ Error: {response.text}")
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        # Recreate messages for next test
        for i in range(2):
            ChatMessage.objects.create(
                room=chat_room,
                sender=auction_owner,
                message=f"Owner message {i+1}",
                message_type="text"
            )
        
        # Test 3: Random user cannot clear messages
        print("\n3️⃣ Testing random user clearing messages (should fail)...")
        random_token = str(RefreshToken.for_user(random_user).access_token)
        random_headers = {'Authorization': f'Bearer {random_token}'}
        
        try:
            response = requests.delete(
                f"{base_url}/chat-messages/clear-room/{chat_room.id}/",
                headers=random_headers
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 403:
                print(f"   ✅ Correctly denied access for random user")
            elif response.status_code == 200:
                print(f"   ❌ Unexpected success - random user should not be able to clear")
            else:
                print(f"   ❌ Unexpected error: {response.text}")
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        # Final message count
        final_count = ChatMessage.objects.filter(room=chat_room).count()
        print(f"\n📊 Final message count: {final_count}")
        
        print("\n🎉 Chat clearing test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_chat_clear_permissions()
