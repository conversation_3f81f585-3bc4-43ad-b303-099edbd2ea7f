/**
 * Currency formatting utilities for the Online Auction System
 * Standardized for US market with USD as primary currency
 */

/**
 * Format amount as US Dollar currency
 * @param {number|string} amount - The amount to format
 * @param {boolean} showSymbol - Whether to show the $ symbol (default: true)
 * @returns {string} Formatted currency string
 */
export const formatUSD = (amount, showSymbol = true) => {
  const numAmount = parseFloat(amount) || 0;

  // Manual formatting to avoid browser locale issues
  const formatted = numAmount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  return showSymbol ? `$${formatted}` : formatted;
};

/**
 * Format amount with currency symbol based on currency code
 * @param {number|string} amount - The amount to format
 * @param {string} currency - Currency code (USD, EUR, GBP, INR, etc.)
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, currency = "USD") => {
  const numAmount = parseFloat(amount) || 0;

  switch (currency.toUpperCase()) {
    case "USD":
      return `$${numAmount.toFixed(2)}`;
    case "EUR":
      return `€${numAmount.toFixed(2)}`;
    case "GBP":
      return `£${numAmount.toFixed(2)}`;
    case "INR":
      return `₹${numAmount.toFixed(2)}`;
    default:
      // Default to USD for Stripe compatibility
      return `$${numAmount.toFixed(2)}`;
  }
};

/**
 * Format amount for auction display (always shows $ symbol for Stripe compatibility)
 * @param {number|string} amount - The amount to format
 * @returns {string} Formatted auction price
 */
export const formatAuctionPrice = (amount) => {
  // Ensure we always get a valid number
  const numAmount = parseFloat(amount) || 0;

  // Manual formatting to avoid browser locale issues
  const formatted = numAmount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  return `$${formatted}`;
};

/**
 * Format amount for bid display with proper USD formatting
 * @param {number|string} amount - The bid amount
 * @returns {string} Formatted bid amount
 */
export const formatBidAmount = (amount) => {
  const numAmount = parseFloat(amount) || 0;

  // For large amounts, use standard USD formatting
  if (numAmount >= 1000000) {
    // 1 million
    const millions = (numAmount / 1000000).toFixed(2);
    return `$${millions}M`;
  } else if (numAmount >= 1000) {
    // 1 thousand
    const thousands = (numAmount / 1000).toFixed(1);
    return `$${thousands}K`;
  }

  return `$${numAmount.toFixed(2)}`;
};

/**
 * Parse currency string to number (removes symbols and formatting)
 * @param {string} currencyString - Currency string like "₹1,23,456.78"
 * @returns {number} Parsed number
 */
export const parseCurrency = (currencyString) => {
  if (typeof currencyString === "number") return currencyString;

  // Remove currency symbols and commas
  const cleaned = currencyString
    .toString()
    .replace(/[₹$€£,]/g, "")
    .replace(/\s/g, "");

  return parseFloat(cleaned) || 0;
};

/**
 * Convert INR to USD (for legacy data or international users)
 * @param {number|string} inrAmount - Amount in INR
 * @param {number} exchangeRate - INR to USD exchange rate (default: 0.012)
 * @returns {string} Formatted USD amount
 */
export const convertINRToUSD = (inrAmount, exchangeRate = 0.012) => {
  const inr = parseFloat(inrAmount) || 0;
  const usd = inr * exchangeRate;
  return formatUSD(usd);
};

/**
 * Get currency symbol for a given currency code
 * @param {string} currency - Currency code
 * @returns {string} Currency symbol
 */
export const getCurrencySymbol = (currency = "USD") => {
  const symbols = {
    USD: "$",
    EUR: "€",
    GBP: "£",
    INR: "₹",
    JPY: "¥",
  };

  return symbols[currency.toUpperCase()] || "$"; // Default to USD for Stripe
};

/**
 * Validate if amount is a valid currency value
 * @param {any} amount - Amount to validate
 * @returns {boolean} True if valid currency amount
 */
export const isValidCurrencyAmount = (amount) => {
  const num = parseFloat(amount);
  return !isNaN(num) && num >= 0 && isFinite(num);
};

/**
 * Format amount for payment display (always 2 decimal places)
 * @param {number|string} amount - Payment amount
 * @returns {string} Formatted payment amount
 */
export const formatPaymentAmount = (amount) => {
  const numAmount = parseFloat(amount) || 0;
  return `$${numAmount.toFixed(2)}`;
};

// Export default formatting function for backward compatibility
export default formatUSD;
