from django.utils import timezone

from ..models import Auction, Bid, Payment
from .notifications import notify_user


def close_ended_auctions():
    now = timezone.now()
    auctions = Auction.objects.filter(end_time__lte=now, is_closed=False)

    for auction in auctions:
        top_bid = auction.bids.order_by("-amount", "-created_at").first()
        if top_bid:
            # Set the winner immediately when auction ends
            auction.winner = top_bid.user
            auction.current_bid = top_bid.amount
            auction.save()  # Save the auction with winner set

            # Create payment record
            payment, created = Payment.objects.get_or_create(
                user=top_bid.user,
                auction=auction,
                defaults={
                    'amount': top_bid.amount,
                    'payment_status': "pending",
                }
            )

            # Set payment deadline
            auction.set_payment_deadline()

            notify_user(
                top_bid.user,
                f"Congratulations! You won '{auction.title}' with a bid of ${top_bid.amount}. Please proceed with the payment.",
                "Auction Won",
            )

        auction.is_closed = True
        auction.save()
