import logging

from celery import shared_task
from django.conf import settings
from django.core.mail import send_mail
from django.utils import timezone

from .models import Auction, Bid, Notification, Payment
from .payment_timeout_service import PaymentTimeoutService
from .utils.auction_closer import close_ended_auctions
from .utils.notifications import notify_user

logger = logging.getLogger(__name__)


@shared_task
def close_auctions_task():
    """
    Celery task to automatically close ended auctions
    """
    try:
        close_ended_auctions()
        logger.info("Successfully closed ended auctions")
    except Exception as e:
        logger.error(f"Error closing auctions: {str(e)}")


@shared_task
def send_auction_ending_reminders():
    """
    Send reminders to users about auctions ending soon
    """
    try:
        # Get auctions ending in the next hour
        one_hour_from_now = timezone.now() + timezone.timedelta(hours=1)
        ending_soon_auctions = Auction.objects.filter(
            end_time__lte=one_hour_from_now,
            end_time__gt=timezone.now(),
            is_closed=False,
        )

        for auction in ending_soon_auctions:
            # Notify all bidders
            bidders = auction.bids.values_list("user", flat=True).distinct()
            for user_id in bidders:
                notify_user(
                    user_id,
                    f"Auction '{auction.title}' is ending soon! Don't miss your chance to bid.",
                    "Auction Ending Soon",
                )

        logger.info(
            f"Sent ending reminders for {ending_soon_auctions.count()} auctions"
        )
    except Exception as e:
        logger.error(f"Error sending auction ending reminders: {str(e)}")


@shared_task
def cleanup_old_notifications():
    """
    Clean up old read notifications (older than 30 days)
    """
    try:
        thirty_days_ago = timezone.now() - timezone.timedelta(days=30)
        deleted_count = Notification.objects.filter(
            status="read", created_at__lt=thirty_days_ago
        ).delete()[0]

        logger.info(f"Cleaned up {deleted_count} old notifications")
    except Exception as e:
        logger.error(f"Error cleaning up notifications: {str(e)}")


@shared_task
def process_pending_payments():
    """
    Process pending payments and send reminders
    """
    try:
        # Get payments pending for more than 24 hours
        twenty_four_hours_ago = timezone.now() - timezone.timedelta(hours=24)
        pending_payments = Payment.objects.filter(
            payment_status="pending", created_at__lt=twenty_four_hours_ago
        )

        for payment in pending_payments:
            notify_user(
                payment.user,
                f"Payment reminder: Your payment of ${payment.amount} for '{payment.auction.title}' is still pending.",
                "Payment Reminder",
            )

        logger.info(f"Sent payment reminders for {pending_payments.count()} payments")
    except Exception as e:
        logger.error(f"Error processing pending payments: {str(e)}")


@shared_task(bind=True, max_retries=3)
def check_payment_timeouts(self):
    """
    Periodic task to check payment timeouts and handle re-auctions
    Should be run every hour
    """
    try:
        logger.info("Starting payment timeout check task")

        service = PaymentTimeoutService()
        service.run_payment_timeout_check()

        logger.info("Payment timeout check task completed successfully")
        return "Payment timeout check completed"

    except Exception as e:
        logger.error(f"Payment timeout check task failed: {e}")

        # Retry the task
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying payment timeout check (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=300, exc=e)  # Retry after 5 minutes

        # Send alert email to admin if all retries failed
        try:
            admin_email = getattr(settings, "ADMIN_EMAIL", "<EMAIL>")
            send_mail(
                subject="[Auction System Alert] Payment Timeout Check Failed",
                message=f"Payment timeout check task failed after {self.max_retries} retries. Error: {e}",
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[admin_email],
                fail_silently=False,
            )
        except Exception as mail_error:
            logger.error(f"Failed to send admin alert: {mail_error}")

        raise


@shared_task
def set_auction_payment_deadlines():
    """
    Task to set payment deadlines for newly ended auctions
    Should be run every 15 minutes
    """
    try:
        logger.info("Setting payment deadlines for ended auctions")

        service = PaymentTimeoutService()
        service.process_ended_auctions()

        logger.info("Payment deadlines set successfully")
        return "Payment deadlines set"

    except Exception as e:
        logger.error(f"Failed to set payment deadlines: {e}")
        raise
