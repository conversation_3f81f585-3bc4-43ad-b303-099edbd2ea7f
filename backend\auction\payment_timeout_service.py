"""
Payment Timeout Service
Handles payment deadlines, reminders, and re-auctions
"""

import logging
from datetime import timedelta

from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.html import strip_tags

from .models import Auction, Notification, Payment

logger = logging.getLogger(__name__)


class PaymentTimeoutService:
    """Service to handle payment timeouts and re-auctions"""

    def __init__(self):
        self.payment_window_hours = 24  # 24 hours to pay
        self.reminder_hours = 12  # Send reminder after 12 hours

    def process_ended_auctions(self):
        """Process auctions that have just ended"""
        # Find auctions that ended but don't have payment deadline set
        ended_auctions = Auction.objects.filter(
            is_closed=True, payment_deadline__isnull=True, bids__isnull=False
        ).distinct()

        for auction in ended_auctions:
            self.set_payment_deadline(auction)

    def set_payment_deadline(self, auction):
        """Set payment deadline for auction winner"""
        winner = auction.get_winner()
        if winner:
            # Set payment deadline
            auction.set_payment_deadline()

            # Create payment record
            payment = Payment.objects.create(
                user=winner,
                auction=auction,
                amount=auction.current_bid,
                payment_deadline=auction.payment_deadline,
                payment_status="pending",
            )

            # Send winner notification
            self.send_winner_notification(auction, winner)

            logger.info(
                f"Payment deadline set for auction {auction.id}, winner: {winner.username}"
            )

    def check_payment_reminders(self):
        """Send payment reminders to winners who haven't paid"""
        reminder_time = timezone.now() - timedelta(hours=self.reminder_hours)

        # Find auctions where payment deadline was set 12+ hours ago
        auctions_needing_reminder = Auction.objects.filter(
            payment_deadline__lte=timezone.now() - timedelta(hours=self.reminder_hours),
            payment_deadline__gt=timezone.now(),  # Not yet overdue
            payment_reminder_sent=False,
            winner__isnull=False,
        )

        for auction in auctions_needing_reminder:
            self.send_payment_reminder(auction)

    def check_payment_timeouts(self):
        """Check for payment timeouts and re-list auctions"""
        overdue_auctions = Auction.objects.filter(
            payment_deadline__lt=timezone.now(),
            winner__isnull=False,
            payment_timeout_count=0,  # First timeout
        )

        for auction in overdue_auctions:
            # Check if payment was actually made
            payment = Payment.objects.filter(
                auction=auction, user=auction.winner, payment_status="completed"
            ).first()

            if not payment:
                self.handle_payment_timeout(auction)

    def send_winner_notification(self, auction, winner):
        """Send notification to auction winner"""
        try:
            subject = f"🎉 Congratulations! You won: {auction.title}"

            context = {
                "user": winner,
                "auction": auction,
                "amount": auction.current_bid,
                "deadline": auction.payment_deadline,
                "payment_url": f"{settings.FRONTEND_URL}/payment/{auction.id}/",
            }

            html_message = render_to_string("emails/auction_winner.html", context)
            plain_message = strip_tags(html_message)

            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[winner.email],
                html_message=html_message,
                fail_silently=False,
            )

            # Create notification
            Notification.objects.create(
                user=winner,
                title=subject,
                message=f"You won the auction for {auction.title}! Please complete payment within 24 hours.",
                notification_type="auction_end",
                auction=auction,
            )

            logger.info(
                f"Winner notification sent to {winner.email} for auction {auction.id}"
            )

        except Exception as e:
            logger.error(f"Failed to send winner notification: {e}")

    def send_payment_reminder(self, auction):
        """Send payment reminder to winner"""
        try:
            winner = auction.winner
            subject = f"⏰ Payment Reminder: {auction.title}"

            hours_remaining = (
                auction.payment_deadline - timezone.now()
            ).total_seconds() / 3600

            context = {
                "user": winner,
                "auction": auction,
                "amount": auction.current_bid,
                "deadline": auction.payment_deadline,
                "hours_remaining": int(hours_remaining),
                "payment_url": f"{settings.FRONTEND_URL}/payment/{auction.id}/",
            }

            html_message = render_to_string("emails/payment_reminder.html", context)
            plain_message = strip_tags(html_message)

            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[winner.email],
                html_message=html_message,
                fail_silently=False,
            )

            # Create notification
            Notification.objects.create(
                user=winner,
                title=subject,
                message=f"Reminder: Please complete payment for {auction.title} within {int(hours_remaining)} hours.",
                notification_type="payment_reminder",
                auction=auction,
            )

            # Mark reminder as sent
            auction.payment_reminder_sent = True
            auction.save()

            logger.info(
                f"Payment reminder sent to {winner.email} for auction {auction.id}"
            )

        except Exception as e:
            logger.error(f"Failed to send payment reminder: {e}")

    def handle_payment_timeout(self, auction):
        """Handle payment timeout - send notification and re-list auction"""
        try:
            winner = auction.winner

            # Send timeout notification
            self.send_timeout_notification(auction, winner)

            # Create re-auction
            re_auction = auction.create_re_auction()

            # Mark original auction payment as timeout
            Payment.objects.filter(
                auction=auction, user=winner, payment_status="pending"
            ).update(payment_status="timeout")

            # Send re-auction notification to watchers
            self.notify_re_auction(re_auction, auction)

            logger.info(
                f"Payment timeout handled for auction {auction.id}, re-listed as {re_auction.id}"
            )

        except Exception as e:
            logger.error(f"Failed to handle payment timeout: {e}")

    def send_timeout_notification(self, auction, winner):
        """Send timeout notification to winner"""
        try:
            subject = f"❌ Payment Timeout: {auction.title}"

            context = {
                "user": winner,
                "auction": auction,
                "amount": auction.current_bid,
            }

            html_message = render_to_string("emails/payment_timeout.html", context)
            plain_message = strip_tags(html_message)

            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[winner.email],
                html_message=html_message,
                fail_silently=False,
            )

            # Create notification
            Notification.objects.create(
                user=winner,
                title=subject,
                message=f"Payment timeout for {auction.title}. The item has been re-listed for auction.",
                notification_type="payment_timeout",
                auction=auction,
            )

            logger.info(
                f"Timeout notification sent to {winner.email} for auction {auction.id}"
            )

        except Exception as e:
            logger.error(f"Failed to send timeout notification: {e}")

    def notify_re_auction(self, re_auction, original_auction):
        """Notify watchers about re-auction"""
        try:
            # Get all watchers of the original auction
            watchers = original_auction.watchers.all()

            for watcher in watchers:
                subject = f"🔄 Item Re-listed: {re_auction.title}"

                context = {
                    "user": watcher,
                    "auction": re_auction,
                    "original_auction": original_auction,
                    "auction_url": f"{settings.FRONTEND_URL}/auctions/{re_auction.id}/",
                }

                html_message = render_to_string(
                    "emails/re_auction_notification.html", context
                )
                plain_message = strip_tags(html_message)

                send_mail(
                    subject=subject,
                    message=plain_message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[watcher.email],
                    html_message=html_message,
                    fail_silently=False,
                )

                # Create notification
                Notification.objects.create(
                    user=watcher,
                    title=subject,
                    message=f"The item {re_auction.title} has been re-listed due to payment timeout. You can bid again!",
                    notification_type="re_auction",
                    auction=re_auction,
                )

            logger.info(f"Re-auction notifications sent for auction {re_auction.id}")

        except Exception as e:
            logger.error(f"Failed to send re-auction notifications: {e}")

    def run_payment_timeout_check(self):
        """Main method to run all payment timeout checks"""
        logger.info("Starting payment timeout check...")

        # Process ended auctions
        self.process_ended_auctions()

        # Send payment reminders
        self.check_payment_reminders()

        # Handle payment timeouts
        self.check_payment_timeouts()

        logger.info("Payment timeout check completed.")


# Convenience function for management commands
def run_payment_timeout_service():
    """Run the payment timeout service"""
    service = PaymentTimeoutService()
    service.run_payment_timeout_check()
