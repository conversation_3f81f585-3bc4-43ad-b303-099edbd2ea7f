"""
Custom validators for the auction system
"""

import re

from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _


class StrongPasswordValidator:
    """
    Validate that the password contains at least:
    - One uppercase letter
    - One lowercase letter
    - One digit
    - One special character
    """

    def validate(self, password, user=None):
        if not re.search(r"[A-Z]", password):
            raise ValidationError(
                _("Password must contain at least one uppercase letter."),
                code="password_no_upper",
            )

        if not re.search(r"[a-z]", password):
            raise ValidationError(
                _("Password must contain at least one lowercase letter."),
                code="password_no_lower",
            )

        if not re.search(r"\d", password):
            raise ValidationError(
                _("Password must contain at least one digit."),
                code="password_no_digit",
            )

        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            raise ValidationError(
                _(
                    'Password must contain at least one special character (!@#$%^&*(),.?":{}|<>).'
                ),
                code="password_no_special",
            )

    def get_help_text(self):
        return _(
            "Your password must contain at least one uppercase letter, "
            "one lowercase letter, one digit, and one special character."
        )


def validate_auction_title(value):
    """Validate auction title for XSS and inappropriate content"""
    # Check for script tags and javascript
    dangerous_patterns = [
        r"<script.*?>.*?</script>",
        r"javascript:",
        r"on\w+\s*=",
        r"<iframe",
        r"<object",
        r"<embed",
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, value, re.IGNORECASE):
            raise ValidationError(
                _("Title contains potentially dangerous content."),
                code="dangerous_content",
            )

    # Check length
    if len(value) > 200:
        raise ValidationError(
            _("Title cannot be longer than 200 characters."),
            code="title_too_long",
        )


def validate_auction_description(value):
    """Validate auction description for XSS and inappropriate content"""
    # Check for script tags and javascript
    dangerous_patterns = [
        r"<script.*?>.*?</script>",
        r"javascript:",
        r"on\w+\s*=",
        r"<iframe",
        r"<object",
        r"<embed",
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, value, re.IGNORECASE):
            raise ValidationError(
                _("Description contains potentially dangerous content."),
                code="dangerous_content",
            )

    # Check length
    if len(value) > 5000:
        raise ValidationError(
            _("Description cannot be longer than 5000 characters."),
            code="description_too_long",
        )


def validate_bid_amount(value):
    """Validate bid amount"""
    if value <= 0:
        raise ValidationError(
            _("Bid amount must be greater than zero."),
            code="invalid_bid_amount",
        )

    if value > 10000000:  # 1 crore limit
        raise ValidationError(
            _("Bid amount cannot exceed ₹1,00,00,000."),
            code="bid_amount_too_high",
        )


def validate_phone_number(value):
    """Validate Indian phone number"""
    # Indian phone number pattern
    pattern = r"^(\+91|91|0)?[6-9]\d{9}$"

    if not re.match(pattern, str(value)):
        raise ValidationError(
            _("Enter a valid Indian phone number."),
            code="invalid_phone",
        )


def validate_file_size(value):
    """Validate uploaded file size (max 5MB)"""
    filesize = value.size

    if filesize > 5 * 1024 * 1024:  # 5MB
        raise ValidationError(
            _("File size cannot exceed 5MB."),
            code="file_too_large",
        )


def validate_image_file(value):
    """Validate that uploaded file is an image"""
    import os

    from django.core.exceptions import ValidationError

    valid_extensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"]
    ext = os.path.splitext(value.name)[1].lower()

    if ext not in valid_extensions:
        raise ValidationError(
            _("Only image files (JPG, PNG, GIF, WebP) are allowed."),
            code="invalid_image_format",
        )
