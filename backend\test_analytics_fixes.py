#!/usr/bin/env python
"""
Test script to verify all analytics fixes are working properly
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from auction.models import Auction, Bid, User, Analytics
from auction.analytics_services import advanced_analytics_service
from auction.signals import send_admin_dashboard_update
from django.db.models import F
from django.utils import timezone
import json

def test_analytics_system():
    """Comprehensive test of the analytics system"""
    
    print("🔍 Testing Analytics System Fixes...")
    print("=" * 60)
    
    # Test 1: Analytics Service
    print("\n📊 Test 1: Analytics Service")
    print("-" * 30)
    
    try:
        # Test fresh data calculation
        print("Testing fresh data calculation...")
        fresh_data = advanced_analytics_service.get_comprehensive_dashboard_data(use_cache=False)
        print(f"✅ Fresh data retrieved: {len(fresh_data)} sections")
        
        # Test caching
        print("Testing caching...")
        cached_data = advanced_analytics_service.get_comprehensive_dashboard_data(use_cache=True)
        print(f"✅ Cached data retrieved: {len(cached_data)} sections")
        
        # Test cache invalidation
        print("Testing cache invalidation...")
        advanced_analytics_service.invalidate_cache()
        print("✅ Cache invalidated successfully")
        
        # Display key metrics
        basic_metrics = fresh_data.get('basic_metrics', {})
        print(f"\n📈 Current Metrics:")
        print(f"   Total Auctions: {basic_metrics.get('total_auctions', 0)}")
        print(f"   Active Auctions: {basic_metrics.get('active_auctions', 0)}")
        print(f"   Total Users: {basic_metrics.get('total_users', 0)}")
        print(f"   Total Bids: {basic_metrics.get('total_bids', 0)}")
        print(f"   Total Views: {basic_metrics.get('total_views', 0)}")
        
    except Exception as e:
        print(f"❌ Analytics service error: {e}")
    
    # Test 2: Signal Integration
    print("\n🔔 Test 2: Signal Integration")
    print("-" * 30)
    
    try:
        # Test creating a test auction
        print("Creating test auction...")
        test_auction = Auction.objects.create(
            title="Test Analytics Auction",
            description="Testing analytics updates",
            starting_bid=100.00,
            current_bid=100.00,
            start_time=timezone.now(),
            end_time=timezone.now() + timezone.timedelta(days=1),
            owner=User.objects.first() or User.objects.create_user(
                username='test_analytics_user',
                email='<EMAIL>',
                password='testpass123'
            )
        )
        print(f"✅ Test auction created: {test_auction.title}")
        
        # Test placing a test bid
        print("Placing test bid...")
        test_user = User.objects.exclude(id=test_auction.owner.id).first()
        if test_user:
            test_bid = Bid.objects.create(
                auction=test_auction,
                user=test_user,
                amount=150.00
            )
            print(f"✅ Test bid placed: ₹{test_bid.amount}")
        else:
            print("⚠️ No other users available for test bid")
        
        # Test view increment
        print("Testing view increment...")
        Auction.objects.filter(id=test_auction.id).update(
            views_count=F('views_count') + 5
        )
        test_auction.refresh_from_db()
        print(f"✅ Views incremented: {test_auction.views_count}")
        
    except Exception as e:
        print(f"❌ Signal integration error: {e}")
    
    # Test 3: WebSocket Integration
    print("\n🌐 Test 3: WebSocket Integration")
    print("-" * 30)
    
    try:
        print("Testing WebSocket dashboard update...")
        send_admin_dashboard_update()
        print("✅ WebSocket dashboard update sent")
        
    except Exception as e:
        print(f"❌ WebSocket integration error: {e}")
    
    # Test 4: Analytics Storage
    print("\n💾 Test 4: Analytics Storage")
    print("-" * 30)
    
    try:
        # Check stored analytics
        today = timezone.now().date()
        stored_analytics = Analytics.objects.filter(date=today)
        print(f"Analytics records for today: {stored_analytics.count()}")
        
        for analytic in stored_analytics:
            print(f"   {analytic.metric_name}: {analytic.metric_value} ({analytic.metric_type})")
        
        if stored_analytics.exists():
            print("✅ Analytics storage working")
        else:
            print("⚠️ No analytics stored today")
            
    except Exception as e:
        print(f"❌ Analytics storage error: {e}")
    
    # Test 5: Cache Performance
    print("\n⚡ Test 5: Cache Performance")
    print("-" * 30)
    
    try:
        import time
        
        # Test without cache
        start_time = time.time()
        fresh_data = advanced_analytics_service.get_comprehensive_dashboard_data(use_cache=False)
        fresh_time = time.time() - start_time
        
        # Test with cache
        start_time = time.time()
        cached_data = advanced_analytics_service.get_comprehensive_dashboard_data(use_cache=True)
        cached_time = time.time() - start_time
        
        print(f"Fresh data time: {fresh_time:.3f}s")
        print(f"Cached data time: {cached_time:.3f}s")
        print(f"Cache speedup: {fresh_time/cached_time:.1f}x faster")
        
        if cached_time < fresh_time:
            print("✅ Caching is working and improving performance")
        else:
            print("⚠️ Caching may not be working optimally")
            
    except Exception as e:
        print(f"❌ Cache performance error: {e}")
    
    # Test 6: Data Consistency
    print("\n🔄 Test 6: Data Consistency")
    print("-" * 30)
    
    try:
        # Get data from different sources
        analytics_data = advanced_analytics_service.get_comprehensive_dashboard_data(use_cache=False)
        direct_count = Auction.objects.count()
        
        analytics_count = analytics_data['basic_metrics']['total_auctions']
        
        print(f"Analytics service count: {analytics_count}")
        print(f"Direct database count: {direct_count}")
        
        if analytics_count == direct_count:
            print("✅ Data consistency verified")
        else:
            print(f"⚠️ Data inconsistency detected: {abs(analytics_count - direct_count)} difference")
            
    except Exception as e:
        print(f"❌ Data consistency error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Analytics System Test Complete!")
    print("\n💡 Next Steps:")
    print("1. Start your system: ./start_complete.bat")
    print("2. Open admin dashboard in browser")
    print("3. Create auctions/bids to see real-time updates")
    print("4. Check browser console for WebSocket messages")
    print("5. Verify analytics update automatically")

if __name__ == "__main__":
    test_analytics_system()
