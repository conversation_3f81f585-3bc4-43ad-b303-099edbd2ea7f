# 🏛️ Online Auction System

## ✅ PRODUCTION-READY AUCTION PLATFORM

A comprehensive, feature-rich online auction platform with real-time bidding, advanced search, secure payments, and professional UI/UX. Built with modern web technologies and optimized for performance.

## 🌟 Key Features

### 🔐 **User Management & Security**

- Secure user registration and authentication system
- Role-based access control (Admin, Regular Users)
- JWT token-based authentication
- Profile management with auction history
- Email verification and password reset functionality

### ⚡ **Real-time Auction System**

- Live bidding with WebSocket connections (Django Channels)
- Real-time auction status updates
- Automatic auction closure and winner determination
- Live countdown timers for auction endings
- Instant bid validation and notifications

### 🏺 **Advanced Auction Management**

- Multi-image auction listings with carousel display
- Category-based organization (Electronics, Fashion, Art, etc.)
- Reserve price and Buy Now options
- Auction status tracking (Active, Ending Soon, Ended)
- Featured auctions and popularity tracking
- Admin-controlled auction approval system

### 🔍 **Comprehensive Search & Filtering**

- Advanced search with auto-complete suggestions
- Multi-criteria filtering (category, price, condition, status)
- Sort options (price, date, popularity, ending time)
- Real-time search results with proper formatting
- Category-based filtering with professional UI

### 💰 **Payment & Transaction System**

- Integrated Stripe payment processing
- INR currency support with ₹ symbol
- 24-hour payment deadline for winners
- Automatic re-listing for unpaid auctions
- Transaction history and receipt management
- Secure payment verification system

### 📱 **Modern UI/UX Design**

- Fully responsive design for all devices
- Professional color scheme and layout
- Interactive components with loading states
- Error handling and user feedback
- Accessibility-compliant interface

## 🛠️ Technology Stack

### **Frontend Technologies**

- **React.js 18** - Modern component-based architecture
- **React Router v6** - Client-side routing and navigation
- **Bootstrap 5** - Responsive UI framework
- **Axios** - HTTP client for API communication
- **WebSocket** - Real-time bidding updates
- **React Hooks** - State management and lifecycle

### **Backend Technologies**

- **Django 4.2** - Robust web framework
- **Django REST Framework** - RESTful API development
- **Django Channels** - WebSocket support for real-time features
- **PostgreSQL** - Production-grade database
- **Redis** - Caching and session management
- **Celery** - Background task processing
- **Daphne** - ASGI server for WebSocket handling

### **Infrastructure & Deployment**

- **Docker** - Containerization for consistent deployment
- **Render** - Cloud hosting platform
- **Stripe** - Payment processing integration
- **Email Services** - Automated notification system

## 🚀 Quick Setup Guide

### **Prerequisites**

- Python 3.8+
- Node.js 16+
- PostgreSQL 12+
- Redis 6+

### **🎯 One-Command Launch (Recommended)**

```bash
# Clone the repository
git clone <repository-url>
cd Online_Auction_System

# Start everything with one command
start_complete.bat
```

**The launcher automatically:**

- ✅ Starts Redis server
- ✅ Launches Django backend (port 8000)
- ✅ Starts WebSocket server (port 8001)
- ✅ Launches React frontend (port 3001)
- ✅ Opens browser to http://localhost:3001

### **🔧 Manual Setup (Alternative)**

#### **1. Backend Setup**

```bash
cd backend
python -m venv venv
venv\Scripts\activate          # Windows
# source venv/bin/activate     # macOS/Linux

pip install -r requirements.txt
python manage.py migrate
python manage.py createsuperuser
```

#### **2. Frontend Setup**

```bash
cd frontend
npm install
npm start
```

#### **3. Start Services**

```bash
# Terminal 1: Django Backend
python manage.py runserver 127.0.0.1:8000

# Terminal 2: WebSocket Server
daphne -b 127.0.0.1 -p 8001 OnlineAuctionSystem.asgi:application

# Terminal 3: React Frontend
npm start
```

#### **4. Stop All Services**

```bash
stop.bat
```

### **🌐 Access Points**

- **Main Application**: http://localhost:3001
- **Admin Panel**: http://127.0.0.1:8000/admin
- **API Documentation**: http://127.0.0.1:8000/api/
- **WebSocket Endpoint**: ws://127.0.0.1:8001/ws/

## ⚙️ Environment Configuration

### **Backend Environment (.env)**

```env
# Django Settings
DEBUG=True
SECRET_KEY=your-secret-key-here-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/auction_db

# Stripe Payment Integration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_CURRENCY=usd

# Email Configuration (Gmail)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-gmail-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# CORS and URLs
FRONTEND_URL=http://localhost:3001
CORS_ALLOWED_ORIGINS=http://localhost:3001,http://127.0.0.1:3001

# WebSocket Settings
CHANNEL_LAYERS_REDIS_URL=redis://localhost:6379/1
```

### **Frontend Environment (.env)**

```env
# API Configuration
REACT_APP_API_URL=http://127.0.0.1:8000/api
REACT_APP_WEBSOCKET_URL=ws://127.0.0.1:8001/ws

# Stripe Configuration
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Currency Settings
REACT_APP_DEFAULT_CURRENCY=USD
REACT_APP_CURRENCY_SYMBOL=$

# Application Settings
REACT_APP_APP_NAME=AuctionStore
REACT_APP_ITEMS_PER_PAGE=12
```

## 🔑 Getting API Keys

### Stripe Setup

1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Create account or login
3. Get API keys from Developers > API keys
4. Copy Publishable key and Secret key
5. Set up webhooks for payment verification

### Gmail App Password

1. Enable 2-Factor Authentication on Gmail
2. Go to Google Account Settings
3. Security > App passwords
4. Generate app password for "Mail"
5. Use this password in EMAIL_HOST_PASSWORD

## 📚 API Documentation

### **Authentication Endpoints**

```
POST   /api/auth/register/           # User registration
POST   /api/auth/login/              # User login
POST   /api/auth/logout/             # User logout
POST   /api/auth/refresh/            # Token refresh
GET    /api/auth/user/               # Get current user
```

### **Auction Management**

```
GET    /api/auctions/               # List all auctions
POST   /api/auctions/               # Create new auction
GET    /api/auctions/{id}/          # Get auction details
PUT    /api/auctions/{id}/          # Update auction
DELETE /api/auctions/{id}/          # Delete auction
GET    /api/auctions/featured/      # Get featured auctions
GET    /api/auctions/categories/    # Get auction categories
```

### **Bidding System**

```
GET    /api/bids/                   # List all bids
POST   /api/bids/                   # Place a new bid
GET    /api/auctions/{id}/bids/     # Get bids for specific auction
GET    /api/bids/user/              # Get user's bid history
```

### **Advanced Search**

```
GET    /api/search/advanced/        # Advanced search with filters
GET    /api/search/filters/         # Get available filter options
GET    /api/search/suggestions/     # Get search suggestions
GET    /api/search/categories/      # Get search categories
```

### **User Management**

```
GET    /api/users/profile/          # Get user profile
PUT    /api/users/profile/          # Update user profile
GET    /api/users/watchlist/        # Get user watchlist
POST   /api/users/watchlist/        # Add to watchlist
DELETE /api/users/watchlist/{id}/   # Remove from watchlist
GET    /api/users/auctions/         # Get user's auctions
```

### **Payment Processing**

```
POST   /api/payments/create/        # Create payment intent
POST   /api/payments/verify/        # Verify payment
GET    /api/payments/history/       # Payment history
POST   /api/payments/refund/        # Process refund
```

### **WebSocket Endpoints**

```
ws://127.0.0.1:8001/ws/auctions/    # Real-time auction updates
ws://127.0.0.1:8001/ws/bids/{id}/   # Real-time bidding for specific auction
ws://127.0.0.1:8001/ws/notifications/ # Real-time notifications
```

## 🧪 Testing & Quality Assurance

### **🚀 Quick Testing (One Command)**

```bash
# Start all services
start_complete.bat

# Run comprehensive tests
cd testing_suite
python run_tests.py
```

### **📋 Manual Testing Flow**

#### **1. User Registration & Authentication**

- ✅ Register new user account
- ✅ Email verification (if enabled)
- ✅ Login with credentials
- ✅ Profile management
- ✅ Password reset functionality

#### **2. Auction Management**

- ✅ Create new auction with images
- ✅ Edit auction details
- ✅ Set reserve price and Buy Now options
- ✅ Category assignment
- ✅ Admin approval process

#### **3. Real-time Bidding**

- ✅ Place bids on active auctions
- ✅ Real-time bid updates via WebSocket
- ✅ Bid validation and error handling
- ✅ Automatic auction closure
- ✅ Winner determination

#### **4. Advanced Search & Filtering**

- ✅ Category-based filtering
- ✅ Price range filtering
- ✅ Search suggestions
- ✅ Sort functionality
- ✅ Real-time search results

#### **5. Payment Processing**

- ✅ Stripe payment integration
- ✅ Payment deadline management
- ✅ Transaction verification
- ✅ Receipt generation

### **🔬 Automated Testing Suite**

```bash
# Backend Unit Tests
cd backend
python manage.py test

# Frontend Component Tests
cd frontend
npm test

# Integration Tests
cd testing_suite
python comprehensive_feature_test.py

# Selenium Automation Tests
python selenium_automation_tests.py

# API Testing
python test_api.py
```

### **📊 Test Coverage**

- ✅ **Unit Tests**: Models, Views, Components
- ✅ **Integration Tests**: API endpoints, Database operations
- ✅ **Frontend Tests**: React components, User interactions
- ✅ **Selenium Tests**: End-to-end user workflows
- ✅ **Performance Tests**: Load testing, Response times
- ✅ **Security Tests**: Authentication, Authorization

## 📁 Project Structure

```
Online_Auction_System/
├── 🗂️ backend/                     # Django Backend Application
│   ├── 🏗️ OnlineAuctionSystem/     # Main Django project settings
│   │   ├── settings.py             # Django configuration
│   │   ├── urls.py                 # URL routing
│   │   ├── asgi.py                 # ASGI configuration for WebSockets
│   │   └── wsgi.py                 # WSGI configuration
│   ├── 🏺 auction/                 # Core auction functionality
│   │   ├── models.py               # Database models
│   │   ├── views.py                # API views
│   │   ├── serializers.py          # Data serialization
│   │   ├── urls.py                 # Auction URLs
│   │   ├── consumers.py            # WebSocket consumers
│   │   └── search_services.py      # Advanced search logic
│   ├── 👥 auctions/                # Extended auction features
│   ├── 🔧 middleware/              # Custom middleware
│   ├── 📊 static/                  # Static files
│   ├── 🗄️ db.sqlite3              # Development database
│   ├── 📋 requirements.txt         # Python dependencies
│   └── 🔧 manage.py                # Django management script
├── 🎨 frontend/                    # React Frontend Application
│   ├── 📱 src/                     # Source code
│   │   ├── 🧩 components/          # Reusable React components
│   │   │   ├── AdvancedSearch.js   # Advanced search component
│   │   │   ├── AuctionCard.js      # Auction display card
│   │   │   ├── BiddingInterface.js # Real-time bidding
│   │   │   └── Navigation.js       # Navigation component
│   │   ├── 📄 pages/               # Page components
│   │   │   ├── Home.js             # Homepage
│   │   │   ├── Auctions.js         # Auction listings
│   │   │   ├── AuctionDetail.js    # Auction details
│   │   │   └── Profile.js          # User profile
│   │   ├── 🔧 utils/               # Utility functions
│   │   │   ├── currency.js         # Currency formatting
│   │   │   ├── websocket.js        # WebSocket utilities
│   │   │   └── api.js              # API helpers
│   │   ├── 🎨 styles/              # CSS styles
│   │   └── 🌐 api/                 # API integration
│   ├── 🌍 public/                  # Public assets
│   │   ├── index.html              # Main HTML template
│   │   └── favicon.ico             # Application icon
│   ├── 📦 package.json             # Node.js dependencies
│   └── 📦 package-lock.json        # Dependency lock file
├── 📚 docs/                        # Documentation
│   ├── 📖 README.md                # Documentation index
│   ├── 🛠️ development/             # Development guides
│   └── 💾 backup_scripts/          # Backup utilities
├── 🧪 testing_suite/               # Comprehensive test suite
│   ├── 🔬 comprehensive_feature_test.py  # Feature tests
│   ├── 🤖 selenium_automation_tests.py  # Automation tests
│   ├── 🔌 test_api.py              # API tests
│   └── 📊 run_tests.py             # Test runner
├── 📊 Research_Paper_Online_Auction_System/  # Academic documentation
│   ├── 📄 Academic_Paper/          # Research paper
│   ├── 📋 Documentation/           # Technical docs
│   ├── 📸 Screenshots/             # System screenshots
│   └── 📈 Testing_Reports/         # Test results
├── 🚀 start_complete.bat           # Complete system launcher
├── 🛑 stop.bat                     # System stopper
├── 🐳 docker-compose.yml           # Docker configuration
├── ☁️ render.yaml                  # Render deployment config
└── 📖 README.md                    # This file
```

## 🚀 Deployment

### Using Docker

```bash
# Build and run
docker-compose up --build

# Access:
# Frontend: http://localhost:3000
# Backend: http://localhost:8000
```

### Deploy to Render

1. Fork this repository
2. Create Render account
3. Connect GitHub repository
4. Set environment variables
5. Deploy web service

## 🔧 Troubleshooting

### Common Issues

**Backend won't start:**

- Check Python version (3.9+)
- Install missing dependencies: `pip install -r requirements.txt`
- Check database connection
- Verify environment variables

**Frontend won't start:**

- Check Node.js version (16+)
- Clear cache: `npm cache clean --force`
- Delete node_modules: `rm -rf node_modules && npm install`
- Use legacy peer deps: `npm install --legacy-peer-deps`

**Payment not working:**

- Verify Stripe keys in both frontend and backend
- Check webhook configuration
- Ensure HTTPS in production
- Test with Stripe test cards

**WebSocket issues:**

- Check Redis connection
- Verify CORS settings
- Check firewall/proxy settings

### Getting Help

- Check logs: `python manage.py runserver --verbosity=2`
- Enable debug mode: `DEBUG=True` in .env
- Check browser console for frontend errors
- Verify all services are running

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -m 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit Pull Request

## 📞 Support

For issues and questions:

- Create GitHub issue
- Email: <EMAIL>
- Documentation: Check README and code comments

---

**🎉 Ready to start your auction platform? Follow the Quick Setup Guide above!**

## 📚 Documentation

Detailed documentation is organized in the `docs/` folder:

- **Development Guides**: `docs/development/`
- **Backup Scripts**: `docs/backup_scripts/`
- **Research Papers**: `Research_Paper_Online_Auction_System/`
- **Testing Suite**: `testing_suite/`

See `docs/README.md` for complete documentation index.
