#!/usr/bin/env python3
"""
Script to find and fix broken image URLs in auction data
"""

import os
import sys
import django
import requests
from urllib.parse import urlparse

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OnlineAuctionSystem.settings')
django.setup()

from auction.models import Auction

def check_image_url(url):
    """Check if an image URL is accessible"""
    if not url or url.strip() == "":
        return False, "Empty URL"
    
    try:
        response = requests.head(url, timeout=10, allow_redirects=True)
        if response.status_code == 200:
            return True, "OK"
        else:
            return False, f"HTTP {response.status_code}"
    except requests.exceptions.RequestException as e:
        return False, str(e)

def get_placeholder_image():
    """Return a working placeholder image URL"""
    return "https://via.placeholder.com/400x300/3498db/ffffff?text=Auction+Item"

def fix_broken_images():
    """Find and fix broken image URLs in auctions"""
    print("🔍 Checking auction images for broken URLs...")
    print("=" * 60)
    
    auctions = Auction.objects.all()
    broken_count = 0
    fixed_count = 0
    
    for auction in auctions:
        print(f"\n📦 Checking auction: {auction.title} (ID: {auction.id})")
        
        # Check main image
        if auction.image:
            is_valid, status = check_image_url(auction.image)
            if not is_valid:
                print(f"❌ Broken main image: {auction.image}")
                print(f"   Status: {status}")
                
                # Fix the broken image
                auction.image = get_placeholder_image()
                broken_count += 1
                
                # Check if it's the Nike URL specifically
                if "static.nike.com" in auction.image:
                    print("🏃‍♂️ Found Nike URL - replacing with placeholder")
            else:
                print(f"✅ Main image OK: {auction.image[:50]}...")
        
        # Check additional images
        if auction.additional_images:
            new_additional_images = []
            for i, img_url in enumerate(auction.additional_images):
                if img_url and img_url.strip() != "":
                    is_valid, status = check_image_url(img_url)
                    if not is_valid:
                        print(f"❌ Broken additional image {i+1}: {img_url}")
                        print(f"   Status: {status}")
                        new_additional_images.append(get_placeholder_image())
                        broken_count += 1
                    else:
                        print(f"✅ Additional image {i+1} OK: {img_url[:50]}...")
                        new_additional_images.append(img_url)
            
            auction.additional_images = new_additional_images
        
        # Save if any changes were made
        try:
            auction.save()
            if broken_count > fixed_count:
                fixed_count = broken_count
                print(f"💾 Updated auction {auction.id}")
        except Exception as e:
            print(f"❌ Error saving auction {auction.id}: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎉 Image check completed!")
    print(f"📊 Total auctions checked: {auctions.count()}")
    print(f"🔧 Broken images found: {broken_count}")
    print(f"✅ Images fixed: {fixed_count}")
    
    if broken_count > 0:
        print(f"\n💡 All broken images have been replaced with placeholder images.")
        print(f"🔄 Please refresh your browser to see the changes.")

def find_nike_urls():
    """Specifically find Nike URLs that are causing the 404 error"""
    print("🔍 Searching for Nike URLs...")
    
    nike_auctions = Auction.objects.filter(image__icontains="static.nike.com")
    
    for auction in nike_auctions:
        print(f"🏃‍♂️ Found Nike URL in auction: {auction.title} (ID: {auction.id})")
        print(f"   URL: {auction.image}")
        
        # Replace with placeholder
        auction.image = get_placeholder_image()
        auction.save()
        print(f"✅ Replaced with placeholder image")
    
    # Check additional images too
    all_auctions = Auction.objects.all()
    for auction in all_auctions:
        if auction.additional_images:
            new_images = []
            changed = False
            for img_url in auction.additional_images:
                if "static.nike.com" in img_url:
                    print(f"🏃‍♂️ Found Nike URL in additional images: {auction.title} (ID: {auction.id})")
                    print(f"   URL: {img_url}")
                    new_images.append(get_placeholder_image())
                    changed = True
                else:
                    new_images.append(img_url)
            
            if changed:
                auction.additional_images = new_images
                auction.save()
                print(f"✅ Updated additional images for auction {auction.id}")

if __name__ == "__main__":
    print("🛠️  Auction Image Fixer")
    print("=" * 30)
    
    # First, specifically fix Nike URLs
    find_nike_urls()
    
    print("\n" + "=" * 30)
    
    # Then check all images
    fix_broken_images()
