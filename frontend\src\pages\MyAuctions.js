import React from "react";
import { useAuth } from "../context/AuthContext";
import { useAuction } from "../context/AuctionContext";

// Display seller's auctions
function MyAuctions() {
  const { user } = useAuth();
  const { auctions } = useAuction();
  const myAuctions = auctions.filter((a) => a.owner === user.username);

  return (
    <div className="container mt-5">
      <h3>My Auctions</h3>
      {myAuctions.length === 0 ? (
        <p>No auctions posted yet.</p>
      ) : (
        myAuctions.map((a) => (
          <div key={a.id} className="card mb-3">
            <div className="card-body">
              <h5>{a.title}</h5>
              <p>Current Bid: ${a.currentBid}</p>
              <p>
                Status: {new Date(a.endTime) > new Date() ? "Active" : "Ended"}
              </p>
            </div>
          </div>
        ))
      )}
    </div>
  );
}

export default MyAuctions;
