{"ast": null, "code": "var baseFindIndex = require('./_baseFindIndex'),\n  baseIsNaN = require('./_baseIsNaN'),\n  strictIndexOf = require('./_strictIndexOf');\n\n/**\n * The base implementation of `_.indexOf` without `fromIndex` bounds checks.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseIndexOf(array, value, fromIndex) {\n  return value === value ? strictIndexOf(array, value, fromIndex) : baseFindIndex(array, baseIsNaN, fromIndex);\n}\nmodule.exports = baseIndexOf;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}