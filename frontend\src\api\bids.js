import axiosInstance from "./axiosInstance";

// Fetch all bids for a specific auction
export async function fetchBids(auctionId, token) {
  try {
    const res = await axiosInstance.get(
      `bids/?auction=${auctionId}&ordering=-amount`
    );
    // Django REST Framework returns paginated data with 'results' array
    return res.data.results || [];
  } catch (error) {
    console.error("Failed to fetch bids:", error);
    throw new Error("Failed to fetch bids");
  }
}

// Place a new bid on an auction
export async function placeBid(auctionId, amount, token) {
  try {
    const res = await axiosInstance.post("bids/", {
      auction: auctionId,
      amount,
    });
    return res.data;
  } catch (error) {
    // Use backend-provided error if available
    const errorMessage =
      error.response?.data?.error ||
      error.response?.data?.detail ||
      error.message ||
      "Failed to place bid";
    throw new Error(errorMessage);
  }
}
