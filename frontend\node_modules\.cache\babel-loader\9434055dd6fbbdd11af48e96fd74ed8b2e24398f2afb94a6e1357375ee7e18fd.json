{"ast": null, "code": "var mapCacheClear = require('./_mapCacheClear'),\n  mapCacheDelete = require('./_mapCacheDelete'),\n  mapCacheGet = require('./_mapCacheGet'),\n  mapCacheHas = require('./_mapCacheHas'),\n  mapCacheSet = require('./_mapCacheSet');\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n    length = entries == null ? 0 : entries.length;\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\nmodule.exports = MapCache;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}